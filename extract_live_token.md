#!/usr/bin/env python3
"""
Extract Live Token from Running Application

This script extracts a real MSAL token from the running application
and tests it with our debugging tools.
"""

import os
import sys
import requests
import json
import time
from typing import Optional

# Add the project root to the Python path
sys.path.insert(0, '/workspaces/ai-scope-app2-branch1')

def setup_environment():
    """Load environment variables from .env file"""
    from dotenv import load_dotenv
    load_dotenv()
    return True

def extract_token_from_logs():
    """Extract the most recent token from application logs"""
    print("🔍 EXTRACTING TOKEN FROM LOGS")
    print("=" * 50)
    
    log_files = [
        'logs/deploy_project_resources_detailed_20250523_094705.log',
        'logs/deploy_project_resources_detailed_20250523_094704.log'
    ]
    
    latest_token = None
    latest_timestamp = None
    
    for log_file in log_files:
        if not os.path.exists(log_file):
            continue
            
        print(f"📄 Checking {log_file}")
        
        try:
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            for line in lines:
                if 'Bearer eyJ' in line and 'TOKEN_EXTRACTION_DEBUG' in line:
                    # Extract timestamp
                    timestamp_str = line.split(' - ')[0]
                    
                    # Extract token
                    bearer_start = line.find('Bearer eyJ')
                    if bearer_start != -1:
                        # Find the end of the token (look for space or truncation marker)
                        token_part = line[bearer_start + 7:]  # Skip 'Bearer '
                        
                        # Handle truncated tokens in logs
                        if '...' in token_part:
                            # This is a truncated token, skip it
                            continue
                        
                        # Find the end of the token
                        token_end = token_part.find(' ')
                        if token_end == -1:
                            token_end = token_part.find('\n')
                        if token_end == -1:
                            token_end = len(token_part)
                        
                        token = token_part[:token_end].strip()
                        
                        # Basic validation - JWT should have 3 parts
                        if len(token.split('.')) == 3:
                            latest_token = token
                            latest_timestamp = timestamp_str
                            print(f"✅ Found token at {timestamp_str}")
                            print(f"   Token length: {len(token)}")
                            print(f"   Token preview: {token[:20]}...{token[-20:]}")
        
        except Exception as e:
            print(f"❌ Error reading {log_file}: {e}")
    
    if latest_token:
        print(f"\n✅ Latest token found: {latest_timestamp}")
        return latest_token
    else:
        print("❌ No valid tokens found in logs")
        return None

def test_token_with_backend(token: str) -> bool:
    """Test the token with the backend validation"""
    print(f"\n🧪 TESTING TOKEN WITH BACKEND")
    print("=" * 50)
    
    try:
        # Import backend validation function
        from backend.auth.token_utils import validate_token
        
        print("📡 Calling backend validate_token function...")
        
        # Run the backend validation
        import asyncio
        is_valid, claims = asyncio.run(validate_token(token))
        
        print(f"📋 Backend validation result: {is_valid}")
        if claims:
            print(f"📋 Backend claims: {list(claims.keys())}")
            print(f"📋 User email: {claims.get('email', claims.get('preferred_username', 'N/A'))}")
            print(f"📋 User name: {claims.get('name', 'N/A')}")
            print(f"📋 Issuer: {claims.get('iss', 'N/A')}")
            print(f"📋 Audience: {claims.get('aud', 'N/A')}")
        else:
            print("📋 Backend claims: None")
            
        return is_valid
        
    except Exception as e:
        print(f"❌ Error calling backend validation: {e}")
        return False

def test_api_endpoint_with_token(token: str):
    """Test the API endpoint with the extracted token"""
    print(f"\n🌐 TESTING API ENDPOINT WITH TOKEN")
    print("=" * 50)
    
    try:
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        
        print("📡 Calling /api/user-context/me endpoint...")
        
        response = requests.get(
            'http://localhost:50508/api/user-context/me',
            headers=headers,
            timeout=10
        )
        
        print(f"📋 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API call successful")
            print(f"📋 User: {data.get('user', {}).get('name', 'N/A')}")
            print(f"📋 Email: {data.get('user', {}).get('email', 'N/A')}")
            print(f"📋 Role: {data.get('user', {}).get('role', 'N/A')}")
            return True
        else:
            print(f"❌ API call failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling API endpoint: {e}")
        return False

def analyze_token_claims(token: str):
    """Analyze token claims without verification"""
    print(f"\n🔍 ANALYZING TOKEN CLAIMS")
    print("=" * 50)
    
    try:
        import jwt
        
        # Decode without verification
        header = jwt.get_unverified_header(token)
        payload = jwt.decode(token, options={"verify_signature": False})
        
        print(f"📋 Header:")
        print(f"   Algorithm: {header.get('alg')}")
        print(f"   Key ID: {header.get('kid')}")
        print(f"   Type: {header.get('typ')}")
        
        print(f"\n📋 Payload:")
        print(f"   Issuer: {payload.get('iss')}")
        print(f"   Audience: {payload.get('aud')}")
        print(f"   Subject: {payload.get('sub')}")
        print(f"   Email: {payload.get('email', payload.get('preferred_username', 'N/A'))}")
        print(f"   Name: {payload.get('name', 'N/A')}")
        print(f"   Scopes: {payload.get('scp', 'N/A')}")
        print(f"   App ID: {payload.get('appid', 'N/A')}")
        
        # Check expiration
        exp_time = payload.get('exp', 0)
        current_time = time.time()
        if exp_time < current_time:
            print(f"⚠️  Token is EXPIRED (expired {current_time - exp_time:.0f} seconds ago)")
        else:
            print(f"✅ Token is valid (expires in {exp_time - current_time:.0f} seconds)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing token: {e}")
        return False

def main():
    """Main function"""
    print("🔍 LIVE TOKEN EXTRACTION AND TESTING")
    print("=" * 60)
    print("This tool extracts a real token from the running application and tests it")
    print()
    
    # Setup environment
    setup_environment()
    
    # Extract token from logs
    token = extract_token_from_logs()
    if not token:
        print("\n❌ Could not extract token from logs")
        print("💡 Try refreshing the browser page to generate new token logs")
        return
    
    # Analyze token claims
    analyze_token_claims(token)
    
    # Test with backend validation
    backend_valid = test_token_with_backend(token)
    
    # Test with API endpoint
    api_valid = test_api_endpoint_with_token(token)
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 50)
    print(f"🔐 Backend validation: {'✅ PASSED' if backend_valid else '❌ FAILED'}")
    print(f"🌐 API endpoint test: {'✅ PASSED' if api_valid else '❌ FAILED'}")
    
    if not backend_valid and not api_valid:
        print("❌ Both tests failed - signature verification issue confirmed")
    elif backend_valid and not api_valid:
        print("⚠️  Backend validation passed but API failed - possible integration issue")
    elif not backend_valid and api_valid:
        print("⚠️  Backend validation failed but API passed - unexpected behavior")
    else:
        print("✅ All tests passed - authentication is working correctly")

if __name__ == "__main__":
    main()

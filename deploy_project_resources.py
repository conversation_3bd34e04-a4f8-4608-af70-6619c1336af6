#!/usr/bin/env python3
"""
Deploy project resources using Bicep templates and ACR-based Function App.
This script is called by app.py when a new project is created.

This script is responsible for:
1. Deploying the core infrastructure via Bicep (storage, search, etc.)
2. Deploying the Function App from ACR
3. Updating the deployment status in CosmosDB
"""

import os
import sys

script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(script_dir, ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

import json
import time
import logging
import subprocess
import uuid
import re
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Tuple, Callable
import asyncio
import aiohttp
import requests
from azure.identity import DefaultAzureCredential
from azure.mgmt.resource import ResourceManagementClient
from azure.mgmt.storage import StorageManagementClient
from azure.mgmt.web import WebSiteManagementClient
from azure.mgmt.storage.models import (
    StorageAccountCreateParameters,
    Sku,
    SkuName,
    Kind,
    Identity,
    StorageAccountUpdateParameters,
)
from azure.mgmt.resource.resources.models import (
    DeploymentMode,
    Deployment,
    DeploymentProperties,
    TemplateLink,
    DeploymentWhatIf,
    DeploymentWhatIfProperties,
    DeploymentMode,
)
from azure.core.exceptions import ResourceNotFoundError
from dotenv import load_dotenv
from backend.services.project_service import ProjectDataService
from backend.utils.logging_config import get_configured_logger

# Load environment variables early
load_dotenv()

# Default API port
API_PORT = os.getenv("API_PORT", "50505")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("logs/cosmos_db.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)
project_service = ProjectDataService()

# Try to import the deployment status module if available
try:
    from backend.deployments.deployment_status import DeploymentSummary

    using_deployment_status_module = True
except ImportError:
    DeploymentSummary = None
    using_deployment_status_module = False
    logger.warning(
        "Could not import DeploymentSummary from backend.deployments.deployment_status. Using fallback status updates."
    )

# Try to import the RBAC client if available
try:
    from backend.rbac.rbac_routes import rbac_client

    using_rbac_client = True
except ImportError:
    rbac_client = None
    using_rbac_client = False


# Try to import the project creation function if available
try:
    from backend.rbac.rbac_routes import create_project

    create_project_available = True
except ImportError as e:
    create_project = None
    create_project_available = False
    logger.warning(
        f"Could not import create_project: {e}. Project creation will be skipped."
    )


def is_api_available(api_url: str) -> bool:
    """Check if the deployment API is reachable."""
    logger.info("API availability check skipped, defaulting to direct DB update.")
    return False


def is_valid_uuid(uuid_to_test: str, version: int = 4) -> bool:
    """
    Check if uuid_to_test is a valid UUID.

    Args:
        uuid_to_test (str): String to test
        version (int): UUID version (1, 3, 4, or 5)

    Returns:
        bool: True if valid UUID, False otherwise
    """
    try:
        uuid_obj = uuid.UUID(str(uuid_to_test), version=version)
        return str(uuid_obj) == str(uuid_to_test).lower()
    except (ValueError, AttributeError, TypeError) as e:
        logger.warning(f"Invalid UUID format for '{uuid_to_test}': {e}")
        return False


def validate_project_parameters(
    project_id: str, project_name: str, region_id: str
) -> bool:
    """
    Validate project parameters before processing.

    Args:
        project_id (str): The project ID to validate
        project_name (str): The project name to validate
        region_id (str): The region ID to validate

    Returns:
        bool: True if all parameters are valid, False otherwise
    """
    if not is_valid_uuid(project_id):
        logger.error(
            f"Invalid project_id format: {project_id}. Must be a valid UUID v4."
        )
        return False

    if (
        not project_name
        or not isinstance(project_name, str)
        or len(project_name.strip()) == 0
    ):
        logger.error("Project name cannot be empty")
        return False

    if not region_id or not isinstance(region_id, str) or len(region_id.strip()) == 0:
        logger.error("Region ID cannot be empty")
        return False

    return True


async def check_api_server(port: str, timeout: int = 30) -> bool:
    """Check if the local API server is reachable on the given port."""
    try:
        await asyncio.wait_for(
            asyncio.open_connection("127.0.0.1", int(port)), timeout=timeout
        )
        return True
    except Exception as e:
        logger.error(f"API server not reachable on port {port}: {e}")
        return False


# Constants for ACR deployment
ACR_NAME = "functionappaiscope"
FUNCTIONS_CONTAINER_IMAGE_NAME = "functionapp"
FUNCTIONS_CONTAINER_IMAGE_TAG = "latest"
SHARED_SEARCH_SERVICE_NAME = "search-shared-service"
SHARED_OPENAI_SERVICE_NAME = "your-openai-service"

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,  # Changed to DEBUG level for more detailed logs
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)

# Create a file handler for detailed logs
os.makedirs("logs", exist_ok=True)
detailed_log_file = f"logs/deploy_project_resources_detailed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
file_handler = logging.FileHandler(detailed_log_file)
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
file_handler.setFormatter(file_formatter)
logging.getLogger().addHandler(file_handler)

logging.info(f"Detailed logs will be written to {detailed_log_file}")

# ACR Configuration
ACR_NAME = os.environ.get("ACR_NAME", "functionappaiscope")
FUNCTIONS_CONTAINER_IMAGE_NAME = os.environ.get(
    "FUNCTIONS_CONTAINER_IMAGE_NAME", "functionapp"
)
FUNCTIONS_CONTAINER_IMAGE_TAG = os.environ.get(
    "FUNCTIONS_CONTAINER_IMAGE_TAG", "latest"
)
SHARED_SEARCH_SERVICE_NAME = os.environ.get("SHARED_SEARCH_SERVICE_NAME", "")
SHARED_OPENAI_SERVICE_NAME = os.environ.get(
    "SHARED_OPENAI_SERVICE_NAME", "openai-service"
)


async def update_deployment_status(
    project_id, status_data, api_url=f"http://localhost:{API_PORT}"
):
    """Update the deployment status using ProjectDataService."""
    return await project_service.update_deployment_status(
        project_id,
        status_data.get("status"),
        status_data.get("message"),
        status_data.get("details"),
        status_data.get("error"),
    )

    return result


async def update_project_resources(
    project_id, resource_data, api_url=f"http://localhost:{API_PORT}"
):
    """
    Update the project in CosmosDB with the actual resource names from Azure deployment.

    Args:
        project_id (str): The ID of the project
        resource_data (dict): Dictionary containing the resource names
        api_url (str): The base URL of the API (not used for direct update)

    Returns:
        bool: True if update was successful, False otherwise
    """
    # Validate project_id
    if not project_id or project_id == "11111111-1111-1111-1111-111111111111":
        logging.error("Invalid project ID for update")
        return False

    logging.info(
        f"Updating project {project_id} with resource data: {json.dumps(resource_data, indent=2)}"
    )

    try:
        project = await project_service.get_project(project_id)
        if not project:
            logging.error(f"Project {project_id} not found for resource update")
            return False
        project.update(resource_data)
        return await project_service.update_project(project_id, project)
    except Exception as e:
        logging.error(f"Error during resource update: {e}")
        return False


def run_main_bicep_deployment(
    project_id,
    project_name,
    region_id,
    resource_group="rg-internal-ai",
    location="westeurope",
    function_app_id=None,
    template_file=None,
    deploy_event_grid_only=False,
    storage_account_id=None,
):
    """
    Runs the main Bicep deployment or a specific template deployment.
    Returns the deployment outputs.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The Azure region ID (for tagging)
        resource_group (str): The resource group name
        location (str): The Azure region to deploy to
        function_app_id (str, optional): The resource ID of the function app to connect to the event grid system topic
        template_file (str, optional): The specific template file to deploy (default: None, which uses the default template)
        deploy_event_grid_only (bool, optional): Whether to deploy only the event grid system topic (default: False)
        storage_account_id (str, optional): The storage account ID to use for the event grid system topic (required if deploy_event_grid_only is True)
    """
    logging.info(
        f"Starting Bicep deployment for project {project_id} in RG {resource_group}"
    )
    if deploy_event_grid_only:
        logging.info("Deploying only the event grid system topic")

    script_dir = os.path.dirname(os.path.abspath(__file__))
    deploy_script = os.path.join(script_dir, "deploy_project_resources.sh")

    # Make sure the script is executable
    try:
        os.chmod(deploy_script, 0o755)
    except Exception as e:
        error_msg = f"Error making deployment script executable: {e}"
        logging.error(error_msg)
        raise Exception(error_msg)

    # Build the command
    cmd = [deploy_script, project_id, project_name, "--region-id", region_id]

    # Add function app ID if provided
    if function_app_id:
        cmd.extend(["--function-app-id", function_app_id])
        logging.info(f"Function app ID provided: {function_app_id}")
    else:
        logging.info(
            "No function app ID provided. Event grid system topic will be created without a subscription to a function app."
        )

    # Add template file if provided
    if template_file:
        cmd.extend(["--template-file", template_file])
        logging.info(f"Using template file: {template_file}")

    # Add storage account ID if provided (for event grid system topic deployment)
    if deploy_event_grid_only and storage_account_id:
        cmd.extend(["--storage-account-id", storage_account_id])
        logging.info(f"Using storage account ID: {storage_account_id}")
    elif deploy_event_grid_only and not storage_account_id:
        error_msg = "Storage account ID is required when deploying only the event grid system topic"
        logging.error(error_msg)
        raise Exception(error_msg)

    # Set environment variables for the script
    env = os.environ.copy()
    env["RESOURCE_GROUP"] = resource_group
    env["LOCATION"] = location

    logging.info(f"Running command: {' '.join(cmd)}")

    try:
        # Log the command and environment variables
        logging.debug(f"Command to execute: {' '.join(cmd)}")
        logging.debug(
            f"Environment variables: RESOURCE_GROUP={resource_group}, LOCATION={location}"
        )

        # Run the deployment script and capture output
        logging.info(
            f"Starting subprocess for Bicep deployment at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        process = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            env=env,
            check=False,
        )
        logging.info(
            f"Subprocess completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} with return code: {process.returncode}"
        )

        # Save the full output to log files for detailed analysis
        stdout_log_file = f"logs/deployment_stdout_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        stderr_log_file = f"logs/deployment_stderr_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        with open(stdout_log_file, "w") as f:
            f.write(process.stdout)
        logging.info(f"Saved full stdout to {stdout_log_file}")

        with open(stderr_log_file, "w") as f:
            f.write(process.stderr if process.stderr else "No stderr output")
        logging.info(f"Saved full stderr to {stderr_log_file}")

        # Log summary of the output
        stdout_lines = process.stdout.splitlines()
        stderr_lines = process.stderr.splitlines() if process.stderr else []

        logging.debug(f"Stdout has {len(stdout_lines)} lines")
        if len(stdout_lines) > 0:
            logging.debug(f"First 5 lines of stdout: {stdout_lines[:5]}")
            logging.debug(
                f"Last 5 lines of stdout: {stdout_lines[-5:] if len(stdout_lines) >= 5 else stdout_lines}"
            )

        logging.debug(f"Stderr has {len(stderr_lines)} lines")
        if len(stderr_lines) > 0:
            logging.debug(f"First 5 lines of stderr: {stderr_lines[:5]}")
            logging.debug(
                f"Last 5 lines of stderr: {stderr_lines[-5:] if len(stderr_lines) >= 5 else stderr_lines}"
            )

        # Check if resources were actually created despite a non-zero return code
        resources_created = False
        resource_data = {}

        # Extract resource names from the output regardless of return code
        for line in process.stdout.splitlines():
            if "Storage Account:" in line and "not found" not in line.lower():
                storage_account_name = line.split("Storage Account:")[1].strip()
                if storage_account_name:
                    resource_data["storage_account_name"] = storage_account_name
                    logging.info(
                        f"Found storage account name in output: {storage_account_name}"
                    )
            elif "Search Service:" in line and "not found" not in line.lower():
                search_service_name = line.split("Search Service:")[1].strip()
                if search_service_name:
                    resource_data["search_service_name"] = search_service_name
                    logging.info(
                        f"Found search service name in output: {search_service_name}"
                    )
            elif (
                "Function App:" in line
                and "URL" not in line
                and "not found" not in line.lower()
            ):
                function_app_name = line.split("Function App:")[1].strip()
                if function_app_name:
                    resource_data["function_app_name"] = function_app_name
                    logging.info(
                        f"Found function app name in output: {function_app_name}"
                    )

        # Check if critical resources exist in Azure
        if (
            "storage_account_name" in resource_data
            and resource_data["storage_account_name"]
        ):
            try:
                # Check if storage account exists
                check_storage_cmd = [
                    "az",
                    "storage",
                    "account",
                    "show",
                    "--name",
                    resource_data["storage_account_name"],
                    "--resource-group",
                    resource_group,
                    "--query",
                    "name",
                    "-o",
                    "tsv",
                ]
                storage_check = subprocess.run(
                    check_storage_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False,
                )
                if storage_check.returncode == 0 and storage_check.stdout.strip():
                    logging.info(
                        f"Verified storage account exists: {resource_data['storage_account_name']}"
                    )
                    resources_created = True
                else:
                    logging.warning(
                        f"Storage account {resource_data['storage_account_name']} not found in Azure"
                    )
            except Exception as e:
                logging.warning(f"Error checking storage account: {e}")

        if (
            "search_service_name" in resource_data
            and resource_data["search_service_name"]
        ):
            try:
                # Check if search service exists
                check_search_cmd = [
                    "az",
                    "search",
                    "service",
                    "show",
                    "--name",
                    resource_data["search_service_name"],
                    "--resource-group",
                    resource_group,
                    "--query",
                    "name",
                    "-o",
                    "tsv",
                ]
                search_check = subprocess.run(
                    check_search_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False,
                )
                if search_check.returncode == 0 and search_check.stdout.strip():
                    logging.info(
                        f"Verified search service exists: {resource_data['search_service_name']}"
                    )
                    resources_created = True
                else:
                    logging.warning(
                        f"Search service {resource_data['search_service_name']} not found in Azure"
                    )
            except Exception as e:
                logging.warning(f"Error checking search service: {e}")

        # Handle deployment result - no retries
        if process.returncode != 0:
            error_message = f"Main Bicep deployment script returned non-zero code: {process.returncode}"
            if process.stderr:
                error_message += (
                    f"\nStderr summary: {process.stderr[:500]}..."
                    if len(process.stderr) > 500
                    else f"\nStderr: {process.stderr}"
                )

            # Check if any resources were created despite the failure
            if resources_created:
                logging.warning(error_message)
                logging.warning(
                    "Some resources were created in Azure despite deployment failure."
                )
                logging.warning(
                    f"See full logs in {stdout_log_file} and {stderr_log_file}"
                )
                # We'll continue but mark the deployment as failed in the summary
            else:
                logging.error(error_message)
                logging.error(
                    f"See full logs in {stdout_log_file} and {stderr_log_file}"
                )
                raise Exception(error_message)

        logging.info(f"Main Bicep deployment successful for project {project_id}")

        # Extract resource names from the output
        resource_data = {}
        for line in process.stdout.splitlines():
            if "Storage Account:" in line:
                resource_data["storage_account_name"] = line.split("Storage Account:")[
                    1
                ].strip()
                logging.info(
                    f"Extracted storage account name: {resource_data['storage_account_name']}"
                )
            elif "Search Service:" in line:
                resource_data["search_service_name"] = line.split("Search Service:")[
                    1
                ].strip()
                logging.info(
                    f"Extracted search service name: {resource_data['search_service_name']}"
                )
            elif "Function App:" in line and "URL" not in line:
                resource_data["function_app_name"] = line.split("Function App:")[
                    1
                ].strip()
                logging.info(
                    f"Extracted function app name: {resource_data['function_app_name']}"
                )

        # Look for JSON output markers in the stdout
        json_start_marker = "JSON_OUTPUT_START"
        json_end_marker = "JSON_OUTPUT_END"

        json_start = process.stdout.find(json_start_marker)
        json_end = process.stdout.find(json_end_marker)

        if json_start >= 0 and json_end > json_start:
            # Extract the JSON between the markers
            json_content = process.stdout[
                json_start + len(json_start_marker) : json_end
            ].strip()
            try:
                deployment_outputs = json.loads(json_content)
                logging.info(f"Successfully parsed JSON output from deployment script")

                # Extract resource names from the JSON output if available
                if "resources" in deployment_outputs:
                    resources = deployment_outputs["resources"]
                    if "storage_account_name" in resources:
                        resource_data["storage_account_name"] = resources[
                            "storage_account_name"
                        ]
                    if "search_service_name" in resources:
                        resource_data["search_service_name"] = resources[
                            "search_service_name"
                        ]
                    if "function_app_name" in resources:
                        resource_data["function_app_name"] = resources[
                            "function_app_name"
                        ]
                    if "uploads_container" in resources:
                        resource_data["storage_container_uploads"] = resources[
                            "uploads_container"
                        ]
                    if "input_container" in resources:
                        resource_data["storage_container_input"] = resources[
                            "input_container"
                        ]
                    if "output_container" in resources:
                        resource_data["storage_container_output"] = resources[
                            "output_container"
                        ]

                    logging.info(
                        "Captured container names: uploads=%s, input=%s, output=%s",
                        resource_data.get("storage_container_uploads"),
                        resource_data.get("storage_container_input"),
                        resource_data.get("storage_container_output"),
                    )

                return deployment_outputs, resource_data
            except json.JSONDecodeError as e:
                logging.warning(f"Could not parse JSON output between markers: {e}")

        # Fallback: Try to find any JSON in the output
        try:
            # Look for JSON output in the stdout
            json_start = process.stdout.find("{")
            json_end = process.stdout.rfind("}")
            if json_start >= 0 and json_end > json_start:
                json_str = process.stdout[json_start : json_end + 1]
                try:
                    deployment_outputs = json.loads(json_str)
                    logging.info(
                        "Successfully parsed JSON output from deployment script (fallback method)"
                    )
                    if "resources" in deployment_outputs:
                        resources = deployment_outputs["resources"]
                        if "uploads_container" in resources:
                            resource_data["storage_container_uploads"] = resources[
                                "uploads_container"
                            ]
                        if "input_container" in resources:
                            resource_data["storage_container_input"] = resources[
                                "input_container"
                            ]
                        if "output_container" in resources:
                            resource_data["storage_container_output"] = resources[
                                "output_container"
                            ]

                        logging.info(
                            "Captured container names (fallback): uploads=%s, input=%s, output=%s",
                            resource_data.get("storage_container_uploads"),
                            resource_data.get("storage_container_input"),
                            resource_data.get("storage_container_output"),
                        )

                    return deployment_outputs, resource_data
                except json.JSONDecodeError:
                    logging.warning(
                        f"Could not parse JSON output from main Bicep deployment (fallback method)"
                    )
            else:
                logging.warning(
                    "Could not find JSON output in deployment script stdout"
                )

            # If we got here, we couldn't parse JSON but deployment was successful
            # Return what we have
            return {}, resource_data
        except Exception as json_error:
            logging.warning(f"Error parsing JSON output: {json_error}")
            return {}, resource_data
    except Exception as e:
        error_message = f"Error running main Bicep deployment: {e}"
        logging.error(error_message)
        raise Exception(error_message)


async def generate_deployment_summary(
    project_id: str,
    project_name: str,
    region_id: str,
    resource_group: str,
    start_time: datetime,
    resource_data: Dict[str, Any],
    main_bicep_outputs: Dict[str, Any],
    resource_durations: Dict[str, float],
    status: str = "success",
    auto_update_project: bool = True,
    api_url: str = f"http://localhost:{API_PORT}",
    sas_token_attempts: int = 3,
    sas_token_delay: int = 5,
) -> Optional[str]:
    """
    Generate a deployment summary JSON file with all the deployment information and update the project in CosmosDB.

    Args:
        project_id (str): The ID of the project (must be a valid UUID v4)
        project_name (str): The name of the project
        region_id (str): The Azure region ID
        resource_group (str): The resource group name
        start_time (datetime): The start time of the deployment
        resource_data (dict): Dictionary containing resource names from deployment
        main_bicep_outputs (dict): The outputs from the main Bicep deployment
        resource_durations (dict): Duration in seconds for each resource step
        status (str): The deployment status (success, partial_success, or failed)
        auto_update_project (bool): Whether to automatically update the project in CosmosDB
        api_url (str): The base URL of the API
        sas_token_attempts (int): Number of attempts to generate the SAS token
        sas_token_delay (int): Delay in seconds between SAS token generation attempts

    Returns:
        Optional[str]: Path to the generated summary file, or None if failed
    """
    # Validate input parameters
    if not validate_project_parameters(project_id, project_name, region_id):
        logger.error(
            "Invalid project parameters provided to generate_deployment_summary"
        )
        return None

    logger.info(
        f"[generate_deployment_summary] Starting deployment summary generation for project {project_id}"
    )
    logger.debug(
        f"[generate_deployment_summary] Project name: {project_name}, Region: {region_id}, Status: {status}"
    )
    logging.info(
        f"Generating deployment summary for project {project_id} with status {status}"
    )

    # Calculate deployment time
    end_time = datetime.now(timezone.utc)
    deployment_time = end_time - start_time
    deployment_time_str = f"{deployment_time.total_seconds():.1f}s"

    # Initialize summary dictionary
    summary = {
        "project_id": project_id,
        "project_name": project_name,
        "region_id": region_id,
        "resources": {
            "storage_account_name": "",
            "storage_account_sas_token": "",
            "uploads_container": "",
            "input_container": "",
            "output_container": "",
            "search_service_name": "",
            "search_index_name": "",
            "search_indexer_name": "",
            "search_key": "",
            "search_api_version": "2023-11-01",
            "search_datasource_name": "",
            "function_app_name": "",
            "function_app_url": None,
            "function_key_maturity": None,
            "function_key_executive_summary": None,
            "function_key_powerpoint": None,
            "event_grid_subscription_name": "",
            "azure_function_maturity_assessment_url": None,
            "azure_function_executive_summary_url": None,
        },
        "status": status,
        "deployment_time": deployment_time_str,
        "resource_durations": resource_durations,
        "timestamp": end_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
    }

    # Fill in resource data primarily from main_bicep_outputs['resources'] (using snake_case keys from shell script JSON)
    bicep_resources = main_bicep_outputs.get("resources", {})
    logging.debug(
        f"Populating summary from main_bicep_outputs resources: {bicep_resources}"
    )
    summary["resources"]["storage_account_name"] = bicep_resources.get(
        "storage_account_name", ""
    )
    summary["resources"]["uploads_container"] = bicep_resources.get(
        "uploads_container", ""
    )
    summary["resources"]["input_container"] = bicep_resources.get("input_container", "")
    summary["resources"]["output_container"] = bicep_resources.get(
        "output_container", ""
    )
    summary["resources"]["search_service_name"] = bicep_resources.get(
        "search_service_name", ""
    )
    summary["resources"]["search_index_name"] = bicep_resources.get(
        "search_index_name", ""
    )
    summary["resources"]["search_indexer_name"] = bicep_resources.get(
        "search_indexer_name", ""
    )
    summary["resources"]["search_datasource_name"] = bicep_resources.get(
        "search_datasource_name", ""
    )
    # Note: search_key is typically not an output of the main bicep, retrieved later if needed

    # Fill in/overwrite with data collected during the Python script execution (resource_data)
    # This is important for Function App and Event Grid details populated after the main Bicep run
    logging.debug(f"Populating/overwriting summary from resource_data: {resource_data}")
    if resource_data.get("storage_account_name"):
        summary["resources"]["storage_account_name"] = resource_data[
            "storage_account_name"
        ]
    if resource_data.get("search_service_name"):
        summary["resources"]["search_service_name"] = resource_data[
            "search_service_name"
        ]
    if resource_data.get("function_app_name"):
        summary["resources"]["function_app_name"] = resource_data["function_app_name"]
        summary["resources"][
            "function_app_url"
        ] = f"https://{resource_data['function_app_name']}.azurewebsites.net"
    if resource_data.get("event_grid_subscription_name"):
        summary["resources"]["event_grid_subscription_name"] = resource_data[
            "event_grid_subscription_name"
        ]

    # Generate SAS token for the main project storage account
    if summary["resources"]["storage_account_name"]:
        try:
            # Generate SAS token with all permissions for all services, valid for 1 year
            expiry_date = (datetime.now(timezone.utc) + timedelta(days=365)).strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            )
            cmd = [
                "az",
                "storage",
                "account",
                "generate-sas",
                "--account-name",
                summary["resources"]["storage_account_name"],
                "--resource-types",
                "sco",
                "--services",
                "bfqt",
                "--permissions",
                "rwdlacuptf",
                "--expiry",
                expiry_date,
                "--https-only",
                "--output",
                "tsv",
            ]

            sas_token = None
            for attempt in range(1, sas_token_attempts + 1):
                logging.info(
                    f"Generating SAS token for storage account {summary['resources']['storage_account_name']} (attempt {attempt}/{sas_token_attempts})"
                )
                process = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False,
                )
                if process.returncode == 0 and process.stdout.strip():
                    sas_token = process.stdout.strip()
                    logging.info(
                        f"Generated SAS token for storage account {summary['resources']['storage_account_name']} on attempt {attempt}"
                    )
                    break
                else:
                    logging.warning(
                        f"Failed to generate SAS token on attempt {attempt}: {process.stderr}"
                    )
                    if attempt < sas_token_attempts:
                        logging.info(
                            f"Retrying SAS token generation in {sas_token_delay} seconds..."
                        )
                        await asyncio.sleep(sas_token_delay)

            if sas_token:
                summary["resources"]["storage_account_sas_token"] = sas_token
                resource_data["storage_account_sas_token"] = sas_token
                logging.debug("Added storage_account_sas_token to resource_data")
                try:
                    await update_project_resources(project_id, resource_data, api_url)
                except Exception as e:
                    logging.error(f"Error updating project with SAS token: {e}")
            else:
                logging.warning(
                    f"Failed to generate SAS token after {sas_token_attempts} attempts"
                )
        except Exception as e:
            logging.error(f"Error generating SAS token: {e}")
        finally:
            # Always log the SAS token generation result
            logging.info(
                f"SAS token generation for storage account {summary['resources']['storage_account_name']} completed."
            )

    # Get function keys if function app exists
    if summary["resources"]["function_app_name"]:
        try:
            # Use correct function names as provided by user
            function_names = [
                "HttpTriggerAppMaturityAssessment",
                "HttpTriggerAppExecutiveSummary",
                "HttpTriggerPowerPointGenerator",
            ]
            function_key_fields = [
                "function_key_maturity",
                "function_key_executive_summary",
                "function_key_powerpoint",
            ]
            function_url_fields = [
                "azure_function_maturity_assessment_url",
                "azure_function_executive_summary_url",
                None,
            ]
            function_endpoints = [
                "HttpTriggerAppMaturityAssessment",
                "HttpTriggerAppExecutiveSummary",
                None,
            ]

            # Ensure search key is populated (might be needed by functions, retrieve if not in bicep outputs)
            if (
                not summary["resources"]["search_key"]
                and summary["resources"]["search_service_name"]
            ):
                try:
                    logging.info(
                        f"Retrieving search key for summary for service: {summary['resources']['search_service_name']}"
                    )
                    search_key_cmd = [
                        "az",
                        "search",
                        "admin-key",
                        "show",
                        "--service-name",
                        summary["resources"]["search_service_name"],
                        "--resource-group",
                        resource_group,
                        "--query",
                        "primaryKey",
                        "-o",
                        "tsv",
                    ]
                    search_key_proc = subprocess.run(
                        search_key_cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        check=False,
                    )
                    if (
                        search_key_proc.returncode == 0
                        and search_key_proc.stdout.strip()
                    ):
                        search_key = search_key_proc.stdout.strip()
                        summary["resources"]["search_key"] = search_key
                        logging.info("Successfully retrieved search key for summary.")
                    else:
                        logging.warning(
                            f"Failed to retrieve search key: {search_key_proc.stderr}"
                        )
                except Exception as e:
                    logging.error(f"Error retrieving search key: {e}")
                finally:
                    logging.info(
                        f"Search key retrieval for service {summary['resources']['search_service_name']} completed."
                    )

            # Get function keys
            for i, function_name in enumerate(function_names):
                if function_name:
                    try:
                        logging.info(f"Getting function key for {function_name}")
                        key_cmd = [
                            "az",
                            "functionapp",
                            "function",
                            "keys",
                            "list",
                            "--function-name",
                            function_name,
                            "--name",
                            summary["resources"]["function_app_name"],
                            "--resource-group",
                            resource_group,
                            "--query",
                            "[0].value",
                            "-o",
                            "tsv",
                        ]
                        try:
                            key_proc = subprocess.run(
                                key_cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                text=True,
                                check=False,
                            )
                            if key_proc.returncode == 0 and key_proc.stdout.strip():
                                key = key_proc.stdout.strip()
                                summary["resources"][function_key_fields[i]] = key
                                logging.info(
                                    f"Successfully retrieved function key for {function_name}"
                                )
                            else:
                                error_msg = (
                                    key_proc.stderr.strip()
                                    if key_proc.stderr
                                    else "Unknown error"
                                )
                                raise RuntimeError(error_msg)
                        except Exception as e:
                            logging.exception(
                                f"Error retrieving function key for {function_name}: {e}"
                            )
                            summary["resources"][function_key_fields[i]] = None
                        finally:
                            logging.info(
                                f"Function key retrieval for {function_name} completed."
                            )
                    except Exception as e:
                        logging.error(f"Error retrieving function keys: {e}")
        except Exception as e:
            logging.error(f"Error retrieving function keys: {e}")

        try:
            # Save deployment status summary to Cosmos DB
            # Re-initialize the project service to ensure a fresh auth token for the final update.
            # This prevents 403 Forbidden errors on long-running deployments.
            final_update_project_service = ProjectDataService()
            await final_update_project_service.update_final_deployment_summary(
                project_id, summary
            )

            # First save the summary to a file with timestamp
            resource_count = len([v for v in summary["resources"].values() if v])
            summary["deployment_total"] = resource_count

            timestamp_str = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            summary_file = f"deployment-summary-{timestamp_str}-{project_id}.json"
            with open(summary_file, "w") as f:
                json.dump(summary, f, indent=2)
            logging.info(f"Saved deployment summary to {summary_file}")

            summary_file_path = None
            try:
                timestamp_str = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
                logs_dir = os.path.join(os.path.dirname(__file__), "logs")
                os.makedirs(logs_dir, exist_ok=True)
                summary_file_path = os.path.join(
                    logs_dir, f"deployment_summary_{project_id}_{timestamp_str}.json"
                )
                with open(summary_file_path, "w") as f:
                    json.dump(summary, f, indent=2)
                logging.info(f"Saved final deployment summary to {summary_file_path}")
                return summary_file_path
            except Exception as e:
                logging.error(
                    f"An error occurred during the final project update step: {e}",
                    exc_info=True,
                )
                if summary_file_path and os.path.exists(summary_file_path):
                    return summary_file_path
                return None

        except Exception as e:
            logging.error(f"Error in deployment summary generation: {e}")
            return None


async def verify_function_app_health(
    function_app_name: str, max_attempts: int = 10, interval_seconds: int = 10
) -> bool:
    """
    Verify the Function App is healthy by checking its health endpoint.
    
    Args:
        function_app_name (str): Name of the function app
        max_attempts (int): Maximum number of attempts
        interval_seconds (int): Seconds between attempts
        
    Returns:
        bool: True if health check passes, False otherwise
    """
    function_app_url = f"https://{function_app_name}.azurewebsites.net"
    health_endpoint = f"{function_app_url}/api/health"
    
    logging.info(f"Checking Function App health at {health_endpoint}")
    
    for attempt in range(1, max_attempts + 1):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(health_endpoint, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        logging.info(f"Health check passed on attempt {attempt}")
                        return True
                    elif response.status == 404:
                        # Health endpoint might not exist, check if app responds at all
                        logging.warning(f"Health endpoint returned 404, checking base URL")
                        async with session.get(function_app_url, timeout=aiohttp.ClientTimeout(total=30)) as base_response:
                            if base_response.status in [200, 401, 403]:
                                logging.info(f"Function App is responding (status: {base_response.status})")
                                return True
                    else:
                        logging.warning(f"Health check attempt {attempt} returned status {response.status}")
        except asyncio.TimeoutError:
            logging.warning(f"Health check attempt {attempt} timed out")
        except Exception as e:
            logging.warning(f"Health check attempt {attempt} failed: {str(e)}")
        
        if attempt < max_attempts:
            logging.info(f"Waiting {interval_seconds} seconds before next health check attempt")
            await asyncio.sleep(interval_seconds)
    
    logging.error(f"Health check failed after {max_attempts} attempts")
    return False


async def verify_function_endpoints(
    function_app_name: str,
    function_names: List[str],
    max_attempts: int = 5,
    interval_seconds: int = 10
) -> Dict[str, bool]:
    """
    Verify that function endpoints are responding (expecting 401 Unauthorized).
    
    Args:
        function_app_name (str): Name of the function app
        function_names (List[str]): List of function names to check
        max_attempts (int): Maximum number of attempts per function
        interval_seconds (int): Seconds between attempts
        
    Returns:
        Dict[str, bool]: Dictionary mapping function name to verification status
    """
    function_app_url = f"https://{function_app_name}.azurewebsites.net"
    results = {}
    
    logging.info(f"Verifying function endpoints for {function_app_name}")
    
    for function_name in function_names:
        endpoint_url = f"{function_app_url}/api/{function_name}"
        logging.info(f"Checking endpoint: {endpoint_url}")
        
        endpoint_ready = False
        for attempt in range(1, max_attempts + 1):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(endpoint_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                        if response.status == 401:
                            logging.info(f"Function {function_name} endpoint returned 401 (expected) on attempt {attempt}")
                            endpoint_ready = True
                            break
                        elif response.status == 200:
                            logging.info(f"Function {function_name} endpoint returned 200 on attempt {attempt}")
                            endpoint_ready = True
                            break
                        else:
                            logging.warning(f"Function {function_name} endpoint returned unexpected status {response.status} on attempt {attempt}")
            except asyncio.TimeoutError:
                logging.warning(f"Function {function_name} endpoint check timed out on attempt {attempt}")
            except Exception as e:
                logging.warning(f"Function {function_name} endpoint check failed on attempt {attempt}: {str(e)}")
            
            if attempt < max_attempts and not endpoint_ready:
                logging.info(f"Waiting {interval_seconds} seconds before next attempt for {function_name}")
                await asyncio.sleep(interval_seconds)
        
        results[function_name] = endpoint_ready
        if not endpoint_ready:
            logging.error(f"Function {function_name} endpoint verification failed after {max_attempts} attempts")
    
    return results


async def deploy_event_grid_with_manual_validation(
    project_id: str,
    project_name: str,
    resource_group: str,
    location: str,
    storage_account_id: str,
    function_app_id: str,
    function_app_name: str,
    region_id: str,
    max_validation_attempts: int = 5,
    validation_interval: int = 10
) -> Dict[str, Any]:
    """
    Deploy Event Grid with manual validation support.
    
    This function creates an Event Grid system topic and subscription with manual validation,
    allowing for better error handling and recovery.
    
    Args:
        project_id: Project ID
        project_name: Project name
        resource_group: Resource group name
        location: Azure location
        storage_account_id: Storage account resource ID
        function_app_id: Function app resource ID
        function_app_name: Function app name
        region_id: Region ID for tagging
        max_validation_attempts: Maximum attempts to validate
        validation_interval: Seconds between validation attempts
        
    Returns:
        Dictionary with deployment results and resource names
    """
    import tempfile
    import hashlib
    import re
    
    logging.info("Starting Event Grid deployment with manual validation")
    
    # Generate event grid system topic name
    sanitized_name = project_name.lower().replace(" ", "-")
    sanitized_name = re.sub(r"[^a-z0-9-]", "", sanitized_name)
    unique_suffix = hashlib.md5(project_id.encode()).hexdigest()[:4]
    event_grid_system_topic_name = f"evgt-{sanitized_name}-{unique_suffix}"
    event_subscription_name = f"evgs-{sanitized_name}-{unique_suffix}"
    
    logging.info(f"Event Grid System Topic Name: {event_grid_system_topic_name}")
    logging.info(f"Event Subscription Name: {event_subscription_name}")
    
    # First, create the Event Grid System Topic
    try:
        logging.info("Creating Event Grid System Topic...")
        create_topic_cmd = [
            "az", "eventgrid", "system-topic", "create",
            "--name", event_grid_system_topic_name,
            "--resource-group", resource_group,
            "--location", location,
            "--topic-type", "Microsoft.Storage.StorageAccounts",
            "--source", storage_account_id,
            "--tags", f"project-id={project_id}", f"region-id={region_id}", f"project-name={project_name}"
        ]
        
        topic_result = subprocess.run(
            create_topic_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False
        )
        
        if topic_result.returncode != 0:
            logging.error(f"Failed to create Event Grid System Topic: {topic_result.stderr}")
            return {"success": False, "error": topic_result.stderr}
            
        logging.info("Event Grid System Topic created successfully")
        
    except Exception as e:
        logging.error(f"Error creating Event Grid System Topic: {e}")
        return {"success": False, "error": str(e)}
    
    # Create the Event Subscription with manual validation
    try:
        logging.info("Creating Event Grid Subscription with manual validation...")
        
        # Construct the webhook endpoint URL
        webhook_endpoint = f"https://{function_app_name}.azurewebsites.net/runtime/webhooks/EventGrid?functionName=EventGridTriggerBlobIndexer"
        
        # Create subscription with manual validation
        create_subscription_cmd = [
            "az", "eventgrid", "system-topic", "event-subscription", "create",
            "--name", event_subscription_name,
            "--system-topic-name", event_grid_system_topic_name,
            "--resource-group", resource_group,
            "--endpoint", webhook_endpoint,
            "--endpoint-type", "webhook",
            "--included-event-types", "Microsoft.Storage.BlobCreated",
            "--advanced-filter", "subject", "StringContains", "/uploads/",
            "--event-delivery-schema", "eventgridschema",
            "--max-delivery-attempts", "30",
            "--event-ttl", "1440",
            "--deadletter-endpoint", f"{storage_account_id}/blobServices/default/containers/deadletter"
        ]
        
        subscription_result = subprocess.run(
            create_subscription_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False
        )
        
        if subscription_result.returncode != 0:
            error_msg = subscription_result.stderr
            
            # Check if it's a validation error
            if "validation" in error_msg.lower():
                logging.warning("Event Grid validation required. Attempting manual validation...")
                
                # Get validation details
                validation_url = None
                validation_code = None
                
                # Try to extract validation URL from the error
                if "validation URL" in error_msg:
                    # Parse validation URL from error message
                    import re
                    url_match = re.search(r'https://[^\s]+validationCode=[^\s]+', error_msg)
                    if url_match:
                        validation_url = url_match.group(0)
                        logging.info(f"Found validation URL: {validation_url}")
                
                # If we have a validation URL, attempt to validate
                if validation_url:
                    validated = await perform_event_grid_validation(validation_url, max_validation_attempts, validation_interval)
                    if validated:
                        logging.info("Event Grid validation successful")
                        
                        # Retry subscription creation
                        retry_result = subprocess.run(
                            create_subscription_cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            check=False
                        )
                        
                        if retry_result.returncode == 0:
                            logging.info("Event Grid Subscription created successfully after validation")
                        else:
                            logging.error(f"Failed to create subscription after validation: {retry_result.stderr}")
                            return {"success": False, "error": retry_result.stderr}
                    else:
                        logging.error("Event Grid validation failed")
                        return {"success": False, "error": "Validation failed"}
                else:
                    logging.error("Could not extract validation URL from error")
                    return {"success": False, "error": "Validation URL not found"}
            else:
                logging.error(f"Failed to create Event Grid Subscription: {error_msg}")
                return {"success": False, "error": error_msg}
        else:
            logging.info("Event Grid Subscription created successfully")
            
    except Exception as e:
        logging.error(f"Error creating Event Grid Subscription: {e}")
        return {"success": False, "error": str(e)}
    
    # Return success with resource names
    return {
        "success": True,
        "event_grid_system_topic_name": event_grid_system_topic_name,
        "event_grid_subscription_name": event_subscription_name
    }


async def perform_event_grid_validation(validation_url: str, max_attempts: int = 5, interval: int = 10) -> bool:
    """
    Perform Event Grid validation by calling the validation URL.
    
    Args:
        validation_url: The validation URL provided by Event Grid
        max_attempts: Maximum validation attempts
        interval: Seconds between attempts
        
    Returns:
        True if validation successful, False otherwise
    """
    logging.info("Performing Event Grid validation...")
    
    for attempt in range(1, max_attempts + 1):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(validation_url, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        logging.info(f"Validation successful on attempt {attempt}")
                        return True
                    else:
                        logging.warning(f"Validation attempt {attempt} returned status {response.status}")
        except Exception as e:
            logging.warning(f"Validation attempt {attempt} failed: {str(e)}")
        
        if attempt < max_attempts:
            logging.info(f"Waiting {interval} seconds before next validation attempt")
            await asyncio.sleep(interval)
    
    logging.error(f"Validation failed after {max_attempts} attempts")
    return False


def check_function_app_exists(project_id, resource_group):
    """
    Check if a Function App exists for the given project ID.

    Args:
        project_id (str): The ID of the project
        resource_group (str): The resource group name

    Returns:
        str: The name of the Function App if found, None otherwise
    """
    logging.info(f"Checking if Function App exists for project {project_id}")

    try:
        # Run az functionapp list command
        cmd = [
            "az",
            "functionapp",
            "list",
            "--resource-group",
            resource_group,
            "--query",
            f"[?contains(name, '{project_id}')].name",
            "-o",
            "tsv",
        ]

        process = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=False
        )

        if process.returncode == 0 and process.stdout.strip():
            function_app_name = process.stdout.strip()
            logging.info(f"Found Function App: {function_app_name}")
            return function_app_name
        else:
            logging.warning(f"No Function App found for project {project_id}")
            return None

    except Exception as e:
        logging.error(f"Error checking Function App existence: {e}")
        return None


def deploy_acr_function_app(
    project_id,
    project_name,
    resource_group,
    location,
    main_bicep_outputs,
    resource_data,
):
    """
    Deploy the Function App from ACR.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        resource_group (str): The resource group name
        location (str): The Azure region
        main_bicep_outputs (dict): The outputs from the main Bicep deployment
        resource_data (dict): Dictionary containing resource names from main deployment

    Returns:
        str: The name of the deployed Function App
    """
    logging.info(f"Starting ACR Function App deployment for project {project_id}")
    logging.debug(f"ACR Function App deployment parameters:")
    logging.debug(f"  project_id: {project_id}")
    logging.debug(f"  project_name: {project_name}")
    logging.debug(f"  resource_group: {resource_group}")
    logging.debug(f"  location: {location}")
    logging.debug(
        f"  main_bicep_outputs keys: {list(main_bicep_outputs.keys()) if main_bicep_outputs else 'None'}"
    )
    logging.debug(f"  resource_data: {resource_data}")

    script_dir = os.path.dirname(os.path.abspath(__file__))
    acr_script_path = os.path.join(
        script_dir, "scripts/ACR_deployment/deploy_function_app_from_acr.sh"
    )
    # acr_params_file_path is the template, temp_acr_params_file_path is what we generate and use
    temp_acr_params_file_path = os.path.join(
        script_dir, f"scripts/ACR_deployment/parameters_{project_id}.json"
    )

    logging.info(f"Starting ACR Function App deployment for project {project_id}")

    # Make sure the ACR deployment script is executable
    try:
        os.chmod(acr_script_path, 0o755)
    except Exception as e:
        error_msg = f"Error making ACR deployment script executable: {e}"
        logging.error(error_msg)
        raise Exception(error_msg)

    # 1. Gather parameters for the ACR script's parameters.json

    # Generate Function App name and related names
    random_suffix = uuid.uuid4().hex[:4]
    function_app_name = f"func-{project_id}-{random_suffix}"
    app_service_plan_name = f"plan-{function_app_name}"

    # Generate Function App's dedicated storage account name
    # Ensure it's compliant: lowercase, no hyphens, 3-24 chars.
    clean_project_id_for_storage = project_id.replace("-", "").lower()
    func_storage_account_name_base = (
        f"stfunc{clean_project_id_for_storage}{random_suffix}"
    )
    func_storage_account_name = func_storage_account_name_base[:24]  # Max 24 chars
    logging.info(f"Generated Function App name: {function_app_name}")
    logging.info(
        f"Generated Function App's dedicated storage account name: {func_storage_account_name}"
    )

    # Create the Function App's dedicated storage account
    try:
        logging.info(
            f"Creating dedicated storage account {func_storage_account_name} for Function App {function_app_name} in RG {resource_group} and location {location}..."
        )
        storage_create_cmd = [
            "az",
            "storage",
            "account",
            "create",
            "--name",
            func_storage_account_name,
            "--resource-group",
            resource_group,
            "--location",
            location,
            "--sku",
            "Standard_LRS",
            "--kind",
            "StorageV2",
            "--output",
            "json",
        ]
        storage_create_process = subprocess.run(
            storage_create_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False,
        )
        if storage_create_process.returncode != 0:
            logging.error(
                f"Failed to create dedicated storage account {func_storage_account_name}: {storage_create_process.stderr}"
            )
            # Check if it was due to already existing (though unlikely with unique suffix)
            if (
                "StorageAccountAlreadyTaken" in storage_create_process.stderr
                or "NameNotAvailable" in storage_create_process.stderr
            ):
                logging.warning(
                    f"Storage account {func_storage_account_name} might already exist or name is taken. Attempting to proceed."
                )
            elif "ResourceGroupNotFound" in storage_create_process.stderr:
                logging.error(
                    f"Resource group {resource_group} not found. Cannot create storage account."
                )
                raise Exception(f"Resource group {resource_group} not found.")
            else:
                raise Exception(
                    f"Failed to create dedicated storage account {func_storage_account_name}: {storage_create_process.stderr}"
                )
        else:
            logging.info(
                f"Successfully created/verified dedicated storage account {func_storage_account_name}."
            )
            # Wait a bit for propagation if newly created
            time.sleep(10)

        # Retrieve connection string for the dedicated storage account
        logging.info(
            f"Retrieving connection string for storage account {func_storage_account_name}..."
        )
        conn_str_cmd = [
            "az",
            "storage",
            "account",
            "show-connection-string",
            "--name",
            func_storage_account_name,
            "--resource-group",
            resource_group,
            "--query",
            "connectionString",
            "-o",
            "tsv",
        ]
        conn_str_process = subprocess.run(
            conn_str_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True,
        )
        func_storage_connection_string = conn_str_process.stdout.strip()
        if not func_storage_connection_string:
            raise Exception(
                f"Failed to retrieve connection string for {func_storage_account_name}"
            )
        logging.info(
            f"Successfully retrieved connection string for {func_storage_account_name}."
        )

    except Exception as e:
        logging.error(
            f"Error during dedicated storage account setup for Function App: {e}"
        )
        raise

    # Retrieve Search API Key with better error handling and validation
    search_api_key = None
    main_search_service_name = main_bicep_outputs.get("searchServiceName", {}).get(
        "value", resource_data.get("search_service_name", SHARED_SEARCH_SERVICE_NAME)
    )

    if (
        main_search_service_name
        and main_search_service_name != SHARED_SEARCH_SERVICE_NAME
    ):
        try:
            logging.info(
                f"Retrieving Search API key for service: {main_search_service_name} in RG {resource_group}..."
            )
            search_key_cmd = [
                "az",
                "search",
                "admin-key",
                "show",
                "--service-name",
                main_search_service_name,
                "--resource-group",
                resource_group,
                "--query",
                "primaryKey",
                "-o",
                "tsv",
            ]
            search_key_process = subprocess.run(
                search_key_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False,
            )

            if search_key_process.returncode == 0 and search_key_process.stdout.strip():
                search_api_key = search_key_process.stdout.strip()
                resource_data["search_key"] = search_api_key
                logging.debug("Added search_key to resource_data")
                logging.info(
                    f"Successfully retrieved Search API key for {main_search_service_name}."
                )
            else:
                error_msg = search_key_process.stderr.strip()
                if "not found" in error_msg.lower():
                    logging.error(
                        f"Search service {main_search_service_name} not found in resource group {resource_group}"
                    )
                    raise ValueError(
                        f"Search service {main_search_service_name} not found in resource group {resource_group}"
                    )
                else:
                    logging.warning(
                        f"Could not retrieve Search API key for {main_search_service_name}: {error_msg}"
                    )
        except subprocess.CalledProcessError as e:
            logging.error(f"Error retrieving Search API key: {e}")
            raise e
        except Exception as e:
            logging.error(f"Unexpected error retrieving Search API key: {e}")
            raise e

    # Validate the API key
    if not search_api_key:
        logging.error(f"No valid Search API key found for {main_search_service_name}")
        raise ValueError("Search API key is required for Function App deployment")

    # Validate the API key format
    if len(search_api_key) < 32:  # Minimum length for Azure API keys
        logging.error(f"Invalid Search API key length: {len(search_api_key)}")
        raise ValueError("Invalid Search API key length")

    logging.info(
        f"Final Search API key status: {'valid key' if len(search_api_key) >= 32 else 'invalid'}"
    )

    # Retrieve OpenAI API Key (Using SHARED_OPENAI_SERVICE_NAME for now as per existing logic)
    openai_api_key = None

    try:
        # Try to get from environment first - check both OPENAI_API_KEY and AZURE_OPENAI_KEY
        openai_api_key = os.environ.get("OPENAI_API_KEY")
        if not openai_api_key:
            # Try AZURE_OPENAI_KEY as an alternative
            openai_api_key = os.environ.get("AZURE_OPENAI_KEY")
            if openai_api_key:
                logging.info("Using AZURE_OPENAI_KEY from environment variables")

        if not openai_api_key:
            # Try to get from key vault if configured
            if os.environ.get("KEY_VAULT_NAME"):
                key_vault_name = os.environ["KEY_VAULT_NAME"]
                logging.info(
                    f"Attempting to get OpenAI API key from Key Vault: {key_vault_name}"
                )
                key_vault_cmd = [
                    "az",
                    "keyvault",
                    "secret",
                    "show",
                    "--name",
                    "OpenAiApiKey",
                    "--vault-name",
                    key_vault_name,
                    "--query",
                    "value",
                    "-o",
                    "tsv",
                ]
                key_vault_process = subprocess.run(
                    key_vault_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False,
                )
                if (
                    key_vault_process.returncode == 0
                    and key_vault_process.stdout.strip()
                ):
                    openai_api_key = key_vault_process.stdout.strip()
                    logging.info("Successfully retrieved OpenAI API key from Key Vault")

        # If still not found, try to read from .env file directly
        if not openai_api_key:
            try:
                env_path = os.path.join(os.getcwd(), ".env")
                if os.path.exists(env_path):
                    logging.info("Attempting to read OpenAI API key from .env file")
                    with open(env_path, "r") as env_file:
                        for line in env_file:
                            if line.strip().startswith("AZURE_OPENAI_KEY="):
                                openai_api_key = (
                                    line.strip().split("=", 1)[1].strip().strip("\"'")
                                )
                                logging.info(
                                    "Successfully read AZURE_OPENAI_KEY from .env file"
                                )
                                break
                            elif line.strip().startswith("OPENAI_API_KEY="):
                                openai_api_key = (
                                    line.strip().split("=", 1)[1].strip().strip("\"'")
                                )
                                logging.info(
                                    "Successfully read OPENAI_API_KEY from .env file"
                                )
                                break
            except Exception as env_error:
                logging.warning(
                    f"Error reading OpenAI API key from .env file: {env_error}"
                )

        if not openai_api_key:
            logging.error(
                "No valid OpenAI API key found in environment, Key Vault, or .env file"
            )
            raise ValueError("OpenAI API key is required for Function App deployment")

        # Validate OpenAI API key format
        if len(openai_api_key) < 32:  # Minimum length for Azure OpenAI API keys
            logging.error(f"Invalid OpenAI API key length: {len(openai_api_key)}")
            raise ValueError("Invalid OpenAI API key length")

        logging.info(
            f"Final OpenAI API key status: {'valid key' if len(openai_api_key) >= 32 else 'invalid'}"
        )
    except Exception as e:
        logging.error(f"Error retrieving OpenAI API key: {e}")
        raise e

    # Log the OpenAI service name that will be used
    logging.info(
        f"OpenAI Service Name for Bicep parameters: {SHARED_OPENAI_SERVICE_NAME}"
    )

    # Prepare parameters for scripts/ACR_deployment/function_app_deployment.bicep
    # Extract values from the 'resources' key in the JSON output of the first script
    # The keys in the JSON output from deploy_project_resources.sh are snake_case
    bicep_resources = main_bicep_outputs.get("resources", {})
    logging.debug(
        f"Extracted resources from main Bicep output for Function App params: {bicep_resources}"
    )

    bicep_func_app_params = {
        "functionAppName": {"value": function_app_name},
        "location": {"value": location},
        "appServicePlanName": {"value": app_service_plan_name},
        "projectId": {"value": project_id},
        "projectName": {"value": project_name},
        # Correctly access nested resource values from the shell script's JSON output
        "uploadsContainer": {"value": bicep_resources.get("uploads_container", "")},
        "inputContainer": {"value": bicep_resources.get("input_container", "")},
        "outputContainer": {"value": bicep_resources.get("output_container", "")},
        "searchIndexName": {"value": bicep_resources.get("search_index_name", "")},
        "searchIndexerName": {"value": bicep_resources.get("search_indexer_name", "")},
        "searchDatasourceName": {
            "value": bicep_resources.get("search_datasource_name", "")
        },
        "searchServiceName": {
            "value": main_search_service_name
        },  # Already correctly derived above
        "searchApiKey": {
            "value": search_api_key if search_api_key else "placeholderSearchApiKey"
        },  # Pass placeholder if not found, Bicep should handle
        "openAiServiceName": {"value": SHARED_OPENAI_SERVICE_NAME},  # From constants
        "openAiApiKey": {
            "value": openai_api_key if openai_api_key else "placeholderOpenAiApiKey"
        },  # Pass placeholder
        "openAiModelDeployment": {
            "value": os.environ.get("SHARED_OPENAI_DEPLOYMENT", "gpt-35-turbo")
        },  # Example, adjust as needed
        "storageConnectionString": {
            "value": func_storage_connection_string
        },  # For Function App's own storage
        "acrName": {"value": ACR_NAME},  # From constants
        "containerImageName": {
            "value": FUNCTIONS_CONTAINER_IMAGE_NAME
        },  # From constants
        "containerImageTag": {"value": FUNCTIONS_CONTAINER_IMAGE_TAG},  # From constants
    }

    project_params_data_for_shell = {
        "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
        "contentVersion": "*******",
        "parameters": bicep_func_app_params,
    }

    # Write the temporary parameters file that the shell script will use
    # Ensure the directory exists
    os.makedirs(os.path.join(script_dir, "scripts/ACR_deployment"), exist_ok=True)
    with open(temp_acr_params_file_path, "w") as f_project_params:
        json.dump(project_params_data_for_shell, f_project_params, indent=2)

    logging.info(
        f"Generated temporary ACR parameters file with consolidated parameters: {temp_acr_params_file_path}"
    )

    # Prepare environment for the bash script
    script_env = os.environ.copy()
    script_env["TARGET_LOCATION"] = location
    script_env["RESOURCE_GROUP"] = resource_group
    # The shell script will read PARAMETERS_FILE to know which parameters JSON to use for its Bicep deployment
    script_env["PARAMETERS_FILE"] = temp_acr_params_file_path

    try:
        # Set up retry logic for ACR deployment - limit to 1 attempt
        max_retries = 1
        retry_count = 0
        success = False

        while retry_count < max_retries and not success:
            retry_count += 1

            # Run the ACR deployment script
            logging.info(
                f"Running ACR deployment script (attempt {retry_count}/{max_retries}): {acr_script_path}"
            )
            logging.debug(
                f"ACR deployment environment variables: TARGET_LOCATION={location}, RESOURCE_GROUP={resource_group}, PARAMETERS_FILE={temp_acr_params_file_path}"
            )

            # Verify the parameters file exists and is valid
            if not os.path.exists(temp_acr_params_file_path):
                logging.error(
                    f"Parameters file does not exist: {temp_acr_params_file_path}"
                )
                raise FileNotFoundError(
                    f"Parameters file not found: {temp_acr_params_file_path}"
                )

            # Validate the parameters file content
            try:
                with open(temp_acr_params_file_path, "r") as f:
                    params_content = json.load(f)
                    required_params = [
                        "functionAppName",
                        "location",
                        "appServicePlanName",
                        "projectId",
                        "projectName",
                    ]
                    missing_params = [
                        param
                        for param in required_params
                        if param not in params_content["parameters"]
                    ]
                    if missing_params:
                        logging.error(
                            f"Missing required parameters in file: {missing_params}"
                        )
                        raise ValueError(
                            f"Parameters file is missing required parameters: {missing_params}"
                        )
                    logging.info(
                        f"Parameters file validated successfully: {temp_acr_params_file_path}"
                    )
            except json.JSONDecodeError as e:
                logging.error(f"Invalid JSON in parameters file: {e}")
                raise ValueError(f"Parameters file contains invalid JSON: {e}")
            except Exception as e:
                logging.error(f"Error validating parameters file: {e}")
                raise e

            # Log the content of the parameters file for debugging
            try:
                with open(temp_acr_params_file_path, "r") as f:
                    params_content = f.read()
                logging.debug(f"Parameters file content: {params_content}")
            except Exception as e:
                logging.warning(f"Could not read parameters file: {e}")

            # Start time for ACR deployment
            acr_start_time = datetime.now()
            logging.info(
                f"Starting ACR deployment at {acr_start_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )

            # Set a timeout for the subprocess (30 minutes)
            timeout_seconds = 1800  # 30 minutes

            try:
                logging.info(
                    f"Running ACR deployment script with timeout of {timeout_seconds} seconds"
                )
                process_acr = subprocess.run(
                    [acr_script_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    env=script_env,
                    check=False,
                    timeout=timeout_seconds,
                )
            except subprocess.TimeoutExpired as timeout_error:
                logging.error(
                    f"ACR deployment script timed out after {timeout_seconds} seconds"
                )
                # Save timeout error to log files
                timeout_stdout_log_file = f"logs/acr_deployment_timeout_stdout_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_attempt{retry_count}.log"
                timeout_stderr_log_file = f"logs/acr_deployment_timeout_stderr_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_attempt{retry_count}.log"

                with open(timeout_stdout_log_file, "w") as f:
                    f.write(
                        timeout_error.stdout
                        if hasattr(timeout_error, "stdout") and timeout_error.stdout
                        else "No stdout available"
                    )
                with open(timeout_stderr_log_file, "w") as f:
                    f.write(
                        timeout_error.stderr
                        if hasattr(timeout_error, "stderr") and timeout_error.stderr
                        else "No stderr available"
                    )

                logging.info(
                    f"Saved timeout logs to {timeout_stdout_log_file} and {timeout_stderr_log_file}"
                )

                # Check if Function App was created despite the timeout
                function_app_name = check_function_app_exists(
                    project_id, resource_group
                )
                if function_app_name:
                    logging.warning(
                        f"Function App {function_app_name} exists despite deployment script timeout"
                    )
                    return function_app_name

                # If we should retry, continue to the next iteration
                if retry_count < max_retries:
                    logging.warning(
                        f"Retrying ACR deployment (attempt {retry_count+1}/{max_retries}) after timeout"
                    )
                    continue
                else:
                    error_message = f"ACR Function App deployment script timed out after {timeout_seconds} seconds for project {project_id} after {max_retries} attempts"
                    logging.error(error_message)
                    raise Exception(error_message)
            except Exception as e:
                logging.error(f"Error executing ACR deployment script: {str(e)}")

                # Check if Function App was created despite the error
                function_app_name = check_function_app_exists(
                    project_id, resource_group
                )
                if function_app_name:
                    logging.warning(
                        f"Function App {function_app_name} exists despite deployment script error"
                    )
                    return function_app_name

                # If we should retry, continue to the next iteration
                if retry_count < max_retries:
                    logging.warning(
                        f"Retrying ACR deployment (attempt {retry_count+1}/{max_retries}) after error: {str(e)}"
                    )
                    continue
                else:
                    error_message = f"Error executing ACR deployment script for project {project_id} after {max_retries} attempts: {str(e)}"
                    logging.error(error_message)
                    raise Exception(error_message)

            # End time for ACR deployment
            acr_end_time = datetime.now()
            acr_duration = (acr_end_time - acr_start_time).total_seconds()
            logging.info(
                f"ACR deployment completed at {acr_end_time.strftime('%Y-%m-%d %H:%M:%S')} (took {acr_duration} seconds)"
            )
            logging.info(f"ACR deployment return code: {process_acr.returncode}")

            # Save ACR deployment output to log files
            acr_stdout_log_file = f"logs/acr_deployment_stdout_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_attempt{retry_count}.log"
            acr_stderr_log_file = f"logs/acr_deployment_stderr_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_attempt{retry_count}.log"

            with open(acr_stdout_log_file, "w") as f:
                f.write(process_acr.stdout)
            logging.info(f"Saved ACR deployment stdout to {acr_stdout_log_file}")

            with open(acr_stderr_log_file, "w") as f:
                f.write(
                    process_acr.stderr if process_acr.stderr else "No stderr output"
                )
            logging.info(f"Saved ACR deployment stderr to {acr_stderr_log_file}")

            # Log summary of the output
            acr_stdout_lines = process_acr.stdout.splitlines()
            acr_stderr_lines = (
                process_acr.stderr.splitlines() if process_acr.stderr else []
            )

            logging.debug(f"ACR stdout has {len(acr_stdout_lines)} lines")
            if len(acr_stdout_lines) > 0:
                logging.debug(f"First 5 lines of ACR stdout: {acr_stdout_lines[:5]}")
                logging.debug(
                    f"Last 5 lines of ACR stdout: {acr_stdout_lines[-5:] if len(acr_stdout_lines) >= 5 else acr_stdout_lines}"
                )

            logging.debug(f"ACR stderr has {len(acr_stderr_lines)} lines")
            if len(acr_stderr_lines) > 0:
                logging.debug(f"First 5 lines of ACR stderr: {acr_stderr_lines[:5]}")
                logging.debug(
                    f"Last 5 lines of ACR stderr: {acr_stderr_lines[-5:] if len(acr_stderr_lines) >= 5 else acr_stderr_lines}"
                )

            if process_acr.returncode == 0:
                logging.info(
                    f"ACR Function App deployment script successful for project {project_id} on attempt {retry_count}"
                )
                success = True
                break
            else:
                error_message = f"ACR Function App deployment script failed for project {project_id}. Return code: {process_acr.returncode}"
                if process_acr.stderr:
                    error_message += (
                        f"\nStderr summary: {process_acr.stderr[:500]}..."
                        if len(process_acr.stderr) > 500
                        else f"\nStderr: {process_acr.stderr}"
                    )

                # Check if we should retry
                if retry_count < max_retries:
                    logging.warning(f"{error_message}")
                    logging.warning(
                        f"Retrying ACR deployment (attempt {retry_count+1}/{max_retries}) after a short delay..."
                    )

                    # Check if Function App was created despite the error
                    function_app_name = check_function_app_exists(
                        project_id, resource_group
                    )
                    if function_app_name:
                        logging.warning(
                            f"Function App {function_app_name} exists despite deployment script failure"
                        )
                        return function_app_name

                    # Wait before retrying
                    retry_delay = retry_count * 10  # Exponential backoff
                    logging.info(f"Waiting {retry_delay} seconds before retry...")
                    time.sleep(retry_delay)
                else:
                    logging.error(error_message)
                    logging.error(
                        f"See full logs in {acr_stdout_log_file} and {acr_stderr_log_file}"
                    )
                    logging.error(f"Failed after {max_retries} attempts")

                    # Check if Function App was created despite the error
                    function_app_name = check_function_app_exists(
                        project_id, resource_group
                    )
                    if function_app_name:
                        logging.warning(
                            f"Function App {function_app_name} exists despite deployment script failure"
                        )
                        return function_app_name

                    raise Exception(error_message)

        logging.info(
            f"ACR Function App deployment script successful for project {project_id}"
        )

        # Get the Function App name from the output file
        function_app_name = None
        func_app_name_file = "./scripts/ACR_deployment/function_app_name.txt"
        if os.path.exists(func_app_name_file):
            with open(func_app_name_file, "r") as f:
                function_app_name = f.read().strip()
            logging.info(
                f"Retrieved Function App Name from file: {function_app_name} for project {project_id}"
            )
            os.remove(func_app_name_file)  # Clean up
        else:
            logging.warning(
                f"Could not find {func_app_name_file} to retrieve Function App name for {project_id}"
            )

            # Fallback: Check if Function App exists in Azure
            logging.info(
                f"Attempting to find Function App for project {project_id} in Azure..."
            )
            function_app_name = check_function_app_exists(project_id, resource_group)
            if function_app_name:
                logging.info(f"Found Function App in Azure: {function_app_name}")
            else:
                logging.error(f"Could not find Function App for project {project_id}")
                return None

        # Verify the function app exists in Azure
        deployment_msg = ""
        if function_app_name:
            # Verify the function app is accessible
            try:
                # Use Azure CLI to check if the function app exists
                check_cmd = f"az functionapp show --name {function_app_name} --resource-group {resource_group}"
                logging.info(f"Verifying Function App exists: {check_cmd}")

                result = subprocess.run(
                    check_cmd, shell=True, capture_output=True, text=True
                )

                if result.returncode == 0:
                    deployment_msg = (
                        f"Function App {function_app_name} verified in Azure"
                    )
                    logging.info(deployment_msg)

                    # Get the function app URL
                    function_app_url = f"https://{function_app_name}.azurewebsites.net"

                    # Update resource data with function app details
                    resource_data["function_app_name"] = function_app_name
                    resource_data["function_app_url"] = function_app_url

                    logging.info(
                        f"Updated resource data with function app details: {function_app_name}, {function_app_url}"
                    )
                    return function_app_name
                else:
                    deployment_msg = (
                        f"Function App verification failed: {result.stderr}"
                    )
                    logging.error(deployment_msg)
                    return None
            except Exception as e:
                deployment_msg = f"Error verifying Function App: {e}"
                logging.error(deployment_msg)
                return None

        if not function_app_name:
            deployment_msg = "Function App deployment failed: name not found"
            logging.error(deployment_msg)

        return function_app_name

    # The finally block related to original_acr_params_content was removed as it's no longer needed.
    # Cleanup of temp_acr_params_file_path happens implicitly when the script exits or can be added here if needed.
    finally:
        # Clean up temporary project-specific params file
        if os.path.exists(temp_acr_params_file_path):
            try:
                os.remove(temp_acr_params_file_path)
                logging.info(
                    f"Cleaned up temporary parameters file: {temp_acr_params_file_path}"
                )
            except Exception as e:
                logging.warning(
                    f"Could not clean up temporary parameters file {temp_acr_params_file_path}: {e}"
                )


async def deploy_project_resources(
    project_id,
    project_name,
    region_id="westeurope",
    api_url=f"http://localhost:{API_PORT}",
    resource_group="rg-internal-ai",
    location="westeurope",
    function_app_id=None,
):
    # Note: function_app_id is used in the code below when setting up Event Grid
    """
    Deploy Azure resources for a project using Bicep templates and ACR-based Function App.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The Azure region ID (for tagging)
        api_url (str): The base URL of the API
        resource_group (str): The resource group name
        location (str): The Azure region to deploy to
        function_app_id (str, optional): The resource ID of the function app to connect to the event grid system topic

    Returns:
        bool: True if deployment was successful, False otherwise
    """
    # Check if the API server is running for local deployments
    if api_url.startswith("http://localhost") or api_url.startswith("http://127."):
        try:
            # Extract port from api_url
            import re

            port_match = re.search(r":(\d+)", api_url)
            port = port_match.group(1) if port_match else API_PORT

            # Set environment variable for the API port
            os.environ["API_PORT"] = port

            logging.info(f"Checking if API server is running on port {port}...")
            if not await check_api_server(port):
                logging.warning(
                    "API server check failed; continuing with deployment but status updates may fail"
                )
            else:
                logging.info(
                    "API server is running and ready to receive status updates"
                )
        except Exception as e:
            logging.warning(f"Error checking API server: {e}")
            logging.warning("Continuing with deployment, but status updates may fail")
    else:
        logging.info(
            f"Deployment targeting remote API at {api_url}; skipping local API server check"
        )
    deployment_success = False
    resource_data = {}  # Initialize resource_data
    main_bicep_outputs = {}  # Initialize main_bicep_outputs
    error_details = None

    # Record deployment start time for calculating total deployment time
    deployment_start_time = datetime.now(timezone.utc)

    # Generate deployment ID for tracking
    deployment_id = str(uuid.uuid4())

    # Initialize deployment logger
    logger = get_configured_logger(
        "deployment",
        project_id=project_id,
        deployment_id=deployment_id,
    )

    # Log deployment start
    logger.info(f"Starting deployment for project: {project_name} (ID: {project_id})")
    logger.info(f"Deployment ID: {deployment_id}")
    logger.info(f"Region: {region_id}, Resource Group: {resource_group}")

    # Track timing for individual resource deployments
    resource_durations = {}

    try:
        # Create a DeploymentSummary instance if the module is available
        deployment_summary = None
        if using_deployment_status_module and DeploymentSummary is not None:
            try:
                deployment_summary = DeploymentSummary(project_id, api_url)
                logging.info(
                    f"Created DeploymentSummary instance for project {project_id}"
                )
            except Exception as e:
                logging.error(f"Error creating DeploymentSummary instance: {e}")
                deployment_summary = None

        # Update deployment status to "in_progress"
        status_data = {
            "status": "in_progress",
            "message": "Starting infrastructure deployment...",
        }
        # If we have a deployment_summary instance, use it to update the status directly
        if deployment_summary:
            try:
                deployment_summary.update_status(
                    "in_progress", "Starting infrastructure deployment..."
                )
                deployment_summary.save_summary()
                logging.info(
                    "Updated deployment status to 'in_progress' using DeploymentSummary"
                )
            except Exception as e:
                logging.error(
                    f"Error updating deployment status using DeploymentSummary: {e}"
                )
                # Fall back to the old method
                await project_service.update_deployment_status(
                    project_id,
                    status_data["status"],
                    status_data["message"],
                    status_data.get("details"),
                    status_data.get("error"),
                )
                logging.info(
                    "Updated deployment status to 'in_progress' using fallback method"
                )
        else:
            # Use the old method
            await project_service.update_deployment_status(
                project_id,
                status_data["status"],
                status_data["message"],
                status_data.get("details"),
                status_data.get("error"),
            )
            logging.info("Updated deployment status to 'in_progress'")

        # Step 1: Deploy main Bicep (without Event Grid System Topic)
        try:
            logging.info(f"Starting main Bicep deployment for project {project_id}")

            storage_start_time = datetime.now(timezone.utc)

            # First deploy the main infrastructure without event grid
            main_bicep_outputs, resource_data = run_main_bicep_deployment(
                project_id,
                project_name,
                region_id,
                resource_group,
                location,
                None,  # No function app ID yet
            )

            storage_end_time = datetime.now(timezone.utc)
            duration = (storage_end_time - storage_start_time).total_seconds()
            resource_durations["storage"] = duration
            resource_durations["search"] = duration

            # Log the outputs and resource data for debugging
            logging.info(
                f"Main Bicep deployment outputs: {json.dumps(main_bicep_outputs, indent=2)}"
            )
            logging.info(
                f"Resource data extracted: {json.dumps(resource_data, indent=2)}"
            )

            # Update deployment status after main infrastructure deployment
            status_data = {
                "status": "in_progress",
                "message": "Main infrastructure deployed successfully.",
                "details": {
                    "infrastructure_complete": True,
                    "storage": {
                        "storage_account": "storage_account_name" in resource_data,
                        "containers": {
                            "uploads": "uploads_container"
                            in main_bicep_outputs.get("resources", {}),
                            "input": "input_container"
                            in main_bicep_outputs.get("resources", {}),
                            "output": "output_container"
                            in main_bicep_outputs.get("resources", {}),
                        },
                    },
                    "storage_complete": "storage_account_name" in resource_data,
                    "search": {
                        "search_service": "search_service_name" in resource_data,
                        "index": "search_index_name"
                        in main_bicep_outputs.get("resources", {}),
                        "indexer": "search_indexer_name"
                        in main_bicep_outputs.get("resources", {}),
                        "datasource": "search_datasource_name"
                        in main_bicep_outputs.get("resources", {}),
                    },
                    "search_complete": "search_service_name" in resource_data,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                        "maturity_assessment": False,
                        "executive_summary": False,
                    },
                    "function_complete": False,
                    "overall_complete": False,
                    "completion_percentage": 50,
                },
            }
            await project_service.update_deployment_status(
                project_id,
                status_data["status"],
                status_data["message"],
                status_data.get("details"),
                status_data.get("error"),
            )

            # Update project resources in CosmosDB
            if resource_data:
                update_result = await update_project_resources(
                    project_id, resource_data, api_url
                )
                if update_result:
                    logging.info(
                        f"Successfully updated project {project_id} with resource names in CosmosDB"
                    )
                else:
                    logging.warning(
                        f"Failed to update project {project_id} with resource names in CosmosDB, but continuing"
                    )

            # Mark deployment as successful since the main infrastructure was deployed
            deployment_success = True

            # Generate SAS token for the main project storage account
            sas_token_attempts = 3
            sas_token_delay = 5

            storage_account_name = resource_data.get("storage_account_name")
            if storage_account_name:
                expiry_date = (
                    datetime.now(timezone.utc) + timedelta(days=365)
                ).strftime("%Y-%m-%dT%H:%M:%SZ")
                cmd = [
                    "az",
                    "storage",
                    "account",
                    "generate-sas",
                    "--account-name",
                    storage_account_name,
                    "--resource-types",
                    "sco",
                    "--services",
                    "bfqt",
                    "--permissions",
                    "rwdlacuptf",
                    "--expiry",
                    expiry_date,
                    "--https-only",
                    "--output",
                    "tsv",
                ]

                logging.info(f"Running command to generate SAS token: {' '.join(cmd)}")
                sas_token = None
                for attempt in range(1, sas_token_attempts + 1):
                    process = subprocess.run(
                        cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        check=False,
                    )
                    masked_stdout = (
                        process.stdout.strip()[:5] + "..."
                        if process.stdout.strip()
                        else ""
                    )
                    logging.info(
                        f"Attempt {attempt} exit code {process.returncode}, stdout: {masked_stdout}, stderr: {process.stderr.strip()}"
                    )
                    if process.returncode == 0 and process.stdout.strip():
                        sas_token = process.stdout.strip()
                        logging.info(
                            f"Generated SAS token for storage account {storage_account_name} on attempt {attempt}"
                        )
                        break
                    if attempt < sas_token_attempts:
                        logging.info(
                            f"Retrying SAS token generation in {sas_token_delay} seconds..."
                        )
                        await asyncio.sleep(sas_token_delay)

                if sas_token:
                    resource_data["storage_account_sas_token"] = sas_token
                    try:
                        await update_project_resources(
                            project_id, resource_data, api_url
                        )
                    except Exception as e:
                        logging.error(f"Error updating project with SAS token: {e}")
                else:
                    error_message = f"Failed to generate SAS token for {storage_account_name} after {sas_token_attempts} attempts"
                    logging.error(error_message)
                    raise RuntimeError(error_message)

            # Step 2: Deploy ACR Function App
            try:
                logging.info(
                    f"Starting ACR Function App deployment for project {project_id}"
                )
                status_data = {
                    "status": "in_progress",
                    "message": "Main infrastructure deployed. Starting Function App deployment.",
                    "details": {
                        "infrastructure_complete": True,
                        "storage": {
                            "storage_account": "storage_account_name" in resource_data,
                            "containers": {
                                "uploads": "uploads_container"
                                in main_bicep_outputs.get("resources", {}),
                                "input": "input_container"
                                in main_bicep_outputs.get("resources", {}),
                                "output": "output_container"
                                in main_bicep_outputs.get("resources", {}),
                            },
                        },
                        "storage_complete": "storage_account_name" in resource_data,
                        "search": {
                            "search_service": "search_service_name" in resource_data,
                            "index": "search_index_name"
                            in main_bicep_outputs.get("resources", {}),
                            "indexer": "search_indexer_name"
                            in main_bicep_outputs.get("resources", {}),
                            "datasource": "search_datasource_name"
                            in main_bicep_outputs.get("resources", {}),
                        },
                        "search_complete": "search_service_name" in resource_data,
                        "function": {
                            "function_app": False,
                            "event_grid_topic": False,
                            "event_grid_system_topic": False,
                            "event_grid": False,
                            "maturity_assessment": False,
                            "executive_summary": False,
                        },
                        "function_app_deployment_started": True,
                        "function_complete": False,
                        "overall_complete": False,
                        "completion_percentage": 60,
                    },
                }
                await project_service.update_deployment_status(
                    project_id,
                    status_data["status"],
                    status_data["message"],
                    status_data.get("details"),
                    status_data.get("error"),
                )

                function_start_time = datetime.now(timezone.utc)
                deployed_function_app_name = deploy_acr_function_app(
                    project_id,
                    project_name,
                    resource_group,
                    location,
                    main_bicep_outputs,
                    resource_data,
                )
                function_end_time = datetime.now(timezone.utc)
                resource_durations["function_app"] = (
                    function_end_time - function_start_time
                ).total_seconds()

                if not deployed_function_app_name:
                    deployment_success = False
                    logging.error(
                        "Function App deployment failed or name could not be retrieved."
                    )

                if deployed_function_app_name:
                    logging.info(
                        f"Function App deployment succeeded: {deployed_function_app_name}"
                    )
                    function_app_message = f"Function App {deployed_function_app_name} deployed successfully."
                else:
                    logging.error("Function App deployment failed")
                    function_app_message = "Function App deployment failed."

                if deployed_function_app_name:
                    # Update resource data with function app name
                    resource_data["function_app_name"] = deployed_function_app_name
                    resource_data["function_app_url"] = (
                        f"https://{deployed_function_app_name}.azurewebsites.net"
                    )

                    # Update project resources in CosmosDB with basic function app details
                    logging.info(
                        f"Updating Cosmos DB with basic function app details for {project_id}"
                    )
                    update_result = await update_project_resources(
                        project_id, resource_data, api_url
                    )
                    if update_result:
                        logging.info(
                            f"Successfully updated project {project_id} with basic function app details in CosmosDB"
                        )
                    else:
                        logging.warning(
                            f"Failed to update project {project_id} with basic function app details in CosmosDB"
                        )

                    # Retrieve function keys
                    logging.info(
                        f"Starting function key retrieval for {deployed_function_app_name}"
                    )
                    function_names = [
                        "HttpTriggerAppMaturityAssessment",
                        "HttpTriggerAppExecutiveSummary",
                        "HttpTriggerPowerPointGenerator",
                    ]
                    keys_retrieved = False

                    # Poll the function app until it reports a Running state
                    logging.info(
                        f"Waiting for Function App '{deployed_function_app_name}' to be in 'Running' state..."
                    )
                    total_wait_time = 0
                    max_wait_seconds = 600  # 10 minutes timeout
                    poll_interval_seconds = 7
                    is_running = False

                    while total_wait_time < max_wait_seconds:
                        try:
                            check_cmd = [
                                "az",
                                "functionapp",
                                "show",
                                "--name",
                                deployed_function_app_name,
                                "--resource-group",
                                resource_group,
                                "--query",
                                "state",
                                "-o",
                                "tsv",
                            ]
                            result = subprocess.run(
                                check_cmd,
                                capture_output=True,
                                text=True,
                                check=True,
                            )
                            state = result.stdout.strip()
                            logging.info(f"Function App state is currently: '{state}'")

                            if state == "Running":
                                logging.info(
                                    "Function App is running. Proceeding to health checks."
                                )
                                is_running = True
                                break
                        except subprocess.CalledProcessError as e:
                            logging.warning(
                                f"Polling failed with an error, will retry: {e.stderr}"
                            )
                        except Exception as e:
                            logging.error(
                                f"An unexpected error occurred while polling: {e}"
                            )

                        time.sleep(poll_interval_seconds)
                        total_wait_time += poll_interval_seconds

                    if not is_running:
                        error_message = (
                            f"Function App '{deployed_function_app_name}' did not start within the {max_wait_seconds // 60} minute timeout."
                        )
                        logging.error(error_message)
                        raise RuntimeError(error_message)

                    # NEW: Perform health check
                    logging.info("Performing Function App health check...")
                    health_check_passed = await verify_function_app_health(
                        deployed_function_app_name,
                        max_attempts=10,
                        interval_seconds=10
                    )
                    
                    if not health_check_passed:
                        error_message = f"Function App '{deployed_function_app_name}' health check failed"
                        logging.error(error_message)
                        raise RuntimeError(error_message)
                    
                    # NEW: Verify function endpoints are responding
                    logging.info("Verifying function endpoints are accessible...")
                    endpoint_results = await verify_function_endpoints(
                        deployed_function_app_name,
                        function_names,
                        max_attempts=5,
                        interval_seconds=10
                    )
                    
                    # Check if all endpoints are ready
                    all_endpoints_ready = all(endpoint_results.values())
                    if not all_endpoints_ready:
                        failed_endpoints = [name for name, ready in endpoint_results.items() if not ready]
                        logging.warning(f"Some function endpoints are not ready: {failed_endpoints}")
                        # Continue anyway, but log the warning
                    else:
                        logging.info("All function endpoints verified and ready")

                    for function_name in function_names:
                        try:
                            function_key = ""
                            key_cmd = f"az functionapp function keys list -g {resource_group} -n {deployed_function_app_name} --function-name {function_name} --query 'default' -o tsv"
                            max_attempts = (
                                5
                                if function_name
                                in (
                                    "HttpTriggerAppMaturityAssessment",
                                    "HttpTriggerAppExecutiveSummary",
                                )
                                else 3
                            )

                            base_delay = 5
                            for attempt in range(1, max_attempts + 1):
                                logging.info(
                                    f"Retrieving function key for {function_name} (attempt {attempt}/{max_attempts}) with command: {key_cmd}"
                                )
                                result = subprocess.run(
                                    key_cmd,
                                    shell=True,
                                    capture_output=True,
                                    text=True,
                                )
                                if result.returncode == 0 and result.stdout.strip():
                                    function_key = result.stdout.strip()
                                    logging.info(
                                        f"Retrieved function key for {function_name} on attempt {attempt}: {function_key[:5]}..."
                                    )
                                    break
                                else:
                                    err = result.stderr.strip() if result.stderr else ""
                                    logging.warning(
                                        f"Attempt {attempt} failed to retrieve key for {function_name}: {err}"
                                    )
                                    if attempt < max_attempts:
                                        delay = base_delay * (2 ** (attempt - 1))
                                        logging.info(
                                            f"Waiting {delay} seconds before next attempt"
                                        )
                                        time.sleep(delay)

                            if not function_key:
                                fallback_cmd = f"az functionapp function keys list -g {resource_group} -n {deployed_function_app_name} --function-name {function_name}"
                                logging.info(
                                    f"Trying fallback approach for {function_name}: {fallback_cmd}"
                                )
                                fallback_result = subprocess.run(
                                    fallback_cmd,
                                    shell=True,
                                    capture_output=True,
                                    text=True,
                                )
                                if fallback_result.returncode == 0:
                                    try:
                                        keys_json = json.loads(fallback_result.stdout)
                                        if (
                                            "default" in keys_json
                                            and keys_json["default"]
                                        ):
                                            function_key = keys_json["default"]
                                            logging.info(
                                                f"Retrieved function key for {function_name} using fallback: {function_key[:5]}..."
                                            )
                                    except json.JSONDecodeError:
                                        logging.error(
                                            f"Failed to parse JSON from fallback approach for {function_name}: {fallback_result.stdout}"
                                        )
                                else:
                                    logging.error(
                                        f"Fallback command failed for {function_name}: {fallback_result.stderr}"
                                    )

                            if function_key:
                                if function_name == "HttpTriggerAppMaturityAssessment":
                                    resource_data["function_key_maturity"] = (
                                        function_key
                                    )
                                    resource_data[
                                        "azure_function_maturity_assessment_url"
                                    ] = f"{resource_data['function_app_url']}/api/{function_name}"
                                elif function_name == "HttpTriggerAppExecutiveSummary":
                                    resource_data["function_key_executive_summary"] = (
                                        function_key
                                    )
                                    resource_data[
                                        "azure_function_executive_summary_url"
                                    ] = f"{resource_data['function_app_url']}/api/{function_name}"
                                elif function_name == "HttpTriggerPowerPointGenerator":
                                    resource_data["function_key_powerpoint"] = (
                                        function_key
                                    )

                                keys_retrieved = True
                                logging.info(
                                    f"Successfully retrieved function key for {function_name}"
                                )
                            else:
                                logging.error(
                                    f"Failed to retrieve function key for {function_name} after all attempts"
                                )
                        except Exception as e:
                            logging.error(
                                f"Error retrieving function key for {function_name}: {e}"
                            )

                    # Log the keys that were retrieved
                    logging.info(
                        f"Completed function key retrieval. Keys retrieved: {list(k for k in resource_data.keys() if 'function_key' in k)}"
                    )

                    # Update Cosmos DB with function keys and endpoints
                    if keys_retrieved:
                        logging.info(
                            f"Updating Cosmos DB with function keys and endpoints for {project_id}"
                        )
                        update_result = await update_project_resources(
                            project_id, resource_data, api_url
                        )
                        if update_result:
                            logging.info(
                                f"Successfully updated project {project_id} with function keys and endpoints in CosmosDB"
                            )
                        else:
                            logging.warning(
                                f"Failed to update project {project_id} with function keys and endpoints in CosmosDB"
                            )
                    else:
                        logging.warning(
                            f"No function keys were retrieved for {project_id}, skipping Cosmos DB update"
                        )

                    # Send status update after function keys have been retrieved
                    if keys_retrieved:
                        status_data = {
                            "status": "function_app_keys_retrieved",
                            "message": f"{function_app_message} Function keys retrieved successfully.",
                            "details": {
                                "infrastructure_complete": True,
                                "storage": {
                                    "storage_account": "storage_account_name"
                                    in resource_data,
                                    "containers": {
                                        "uploads": "uploads_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "input": "input_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "output": "output_container"
                                        in main_bicep_outputs.get("resources", {}),
                                    },
                                },
                                "storage_complete": "storage_account_name"
                                in resource_data,
                                "search": {
                                    "search_service": "search_service_name"
                                    in resource_data,
                                    "index": "search_index_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "indexer": "search_indexer_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "datasource": "search_datasource_name"
                                    in main_bicep_outputs.get("resources", {}),
                                },
                                "search_complete": "search_service_name"
                                in resource_data,
                                "function": {
                                    "function_app": True,
                                    "event_grid_topic": False,
                                    "event_grid_system_topic": False,
                                    "event_grid": False,
                                    "maturity_assessment": True,
                                    "executive_summary": True,
                                },
                                "function_app_complete": True,
                                "function_complete": False,
                                "overall_complete": False,
                                "completion_percentage": 80,
                            },
                        }
                        await project_service.update_deployment_status(
                            project_id,
                            status_data["status"],
                            status_data["message"],
                            status_data.get("details"),
                            status_data.get("error"),
                        )

                    # Get the function app ID
                    retrieved_function_app_id = None
                    try:
                        cmd = [
                            "az",
                            "functionapp",
                            "show",
                            "--name",
                            deployed_function_app_name,
                            "--resource-group",
                            resource_group,
                            "--query",
                            "id",
                            "-o",
                            "tsv",
                        ]
                        process = subprocess.run(
                            cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            check=False,
                        )
                        if process.returncode == 0 and process.stdout.strip():
                            retrieved_function_app_id = process.stdout.strip()
                            logging.info(
                                f"Retrieved function app ID: {retrieved_function_app_id}"
                            )
                        else:
                            logging.warning(
                                f"Could not retrieve function app ID: {process.stderr}"
                            )
                    except Exception as e:
                        logging.error(f"Error retrieving function app ID: {e}")

                    # Step 3: Deploy Event Grid System Topic (only if function app ID was retrieved)
                    event_grid_complete = False
                    max_event_grid_retries = 3
                    event_grid_retry_count = 0

                    if retrieved_function_app_id:

                        logging.info(
                            f"Attempting to deploy event grid system topic and subscription using function app ID: {retrieved_function_app_id}"
                        )
                        event_grid_start_time = datetime.now(timezone.utc)

                        # Wait for Function App to be fully deployed and ready
                        logging.info(
                            "Waiting 30 seconds for Function App to be fully deployed before setting up Event Grid..."
                        )
                        await asyncio.sleep(30)

                        # Get the function app name from resource data
                        function_app_name = deployed_function_app_name

                        # Verify Function App exists and is running
                        try:
                            func_app_check_cmd = [
                                "az",
                                "functionapp",
                                "show",
                                "--name",
                                function_app_name,
                                "--resource-group",
                                resource_group,
                                "--query",
                                "state",
                                "-o",
                                "tsv",
                            ]
                            func_app_check = subprocess.run(
                                func_app_check_cmd,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                text=True,
                                check=False,
                            )
                            if (
                                func_app_check.returncode == 0
                                and func_app_check.stdout.strip() == "Running"
                            ):
                                logging.info(
                                    f"Function App {function_app_name} is running and ready for Event Grid setup"
                                )
                            else:
                                logging.warning(
                                    f"Function App {function_app_name} is not in 'Running' state: {func_app_check.stdout.strip()}"
                                )
                                logging.info(
                                    "Waiting additional 60 seconds for Function App to be ready..."
                                )
                                time.sleep(60)
                        except Exception as e:
                            logging.warning(f"Error checking Function App state: {e}")
                            logging.info("Continuing with Event Grid setup anyway...")

                        # Get storage account ID
                        storage_account_id = None
                        if (
                            "storage_account_name" in resource_data
                            and resource_data["storage_account_name"]
                        ):
                            try:
                                cmd = [
                                    "az",
                                    "storage",
                                    "account",
                                    "show",
                                    "--name",
                                    resource_data["storage_account_name"],
                                    "--resource-group",
                                    resource_group,
                                    "--query",
                                    "id",
                                    "-o",
                                    "tsv",
                                ]
                                process = subprocess.run(
                                    cmd,
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    text=True,
                                    check=False,
                                )
                                if process.returncode == 0 and process.stdout.strip():
                                    storage_account_id = process.stdout.strip()
                                    logging.info(
                                        f"Retrieved storage account ID: {storage_account_id}"
                                    )
                                else:
                                    logging.warning(
                                        f"Could not retrieve storage account ID: {process.stderr}"
                                    )

                                    # Try to get storage account ID using a different method
                                    logging.info(
                                        "Trying alternative method to get storage account ID..."
                                    )
                                    cmd = [
                                        "az",
                                        "storage",
                                        "account",
                                        "list",
                                        "--resource-group",
                                        resource_group,
                                        "--query",
                                        f"[?name=='{resource_data['storage_account_name']}'].id",
                                        "-o",
                                        "tsv",
                                    ]
                                    process = subprocess.run(
                                        cmd,
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE,
                                        text=True,
                                        check=False,
                                    )
                                    if (
                                        process.returncode == 0
                                        and process.stdout.strip()
                                    ):
                                        storage_account_id = process.stdout.strip()
                                        logging.info(
                                            f"Retrieved storage account ID using alternative method: {storage_account_id}"
                                        )
                            except Exception as e:
                                logging.error(
                                    f"Error retrieving storage account ID: {e}"
                                )

                            if storage_account_id:
                                # Use the new async Event Grid deployment function
                                logging.info("Deploying Event Grid with improved validation handling...")
                                event_grid_result = await deploy_event_grid_with_manual_validation(
                                    project_id=project_id,
                                    project_name=project_name,
                                    resource_group=resource_group,
                                    location=location,
                                    storage_account_id=storage_account_id,
                                    function_app_id=retrieved_function_app_id,
                                    function_app_name=deployed_function_app_name,
                                    region_id=region_id,
                                    max_validation_attempts=5,
                                    validation_interval=10
                                )
                                
                                if event_grid_result["success"]:
                                    event_grid_complete = True
                                    logging.info("Event Grid deployment successful")
                                    
                                    # Update resource data with Event Grid information
                                    resource_data["event_grid_system_topic_name"] = event_grid_result["event_grid_system_topic_name"]
                                    resource_data["event_grid_subscription_name"] = event_grid_result["event_grid_subscription_name"]
                                    
                                    # Update Cosmos DB with Event Grid details
                                    logging.info(f"Updating Cosmos DB with Event Grid details for {project_id}")
                                    update_result = await update_project_resources(
                                        project_id,
                                        resource_data,
                                        api_url,
                                    )
                                    if update_result:
                                        logging.info(f"Successfully updated project {project_id} with Event Grid details in CosmosDB")
                                    else:
                                        logging.warning(f"Failed to update project {project_id} with Event Grid details in CosmosDB")
                                else:
                                    event_grid_complete = False
                                    error_msg = event_grid_result.get("error", "Unknown error")
                                    logging.error(f"Event Grid deployment failed: {error_msg}")
                                    
                                    # Continue without Event Grid if it fails
                                    logging.warning("Continuing deployment without Event Grid functionality")
                                
                                # Record Event Grid deployment time
                                event_grid_end_time = datetime.now(timezone.utc)
                                resource_durations["event_grid"] = (
                                    event_grid_end_time - event_grid_start_time
                                ).total_seconds()
                            else:
                                logging.error(
                                    "Could not attempt event grid system topic/subscription deployment because storage account ID could not be retrieved"
                                )
                        else:
                            logging.error(
                                "Could not attempt event grid system topic/subscription deployment because storage account name was not found in resource data"
                            )
                    else:
                        logging.warning(
                            "Skipping Event Grid deployment because Function App ID was not retrieved."
                        )

                    # Update status to completed once Event Grid deployment is verified
                    if event_grid_complete:
                        event_grid_end_time = datetime.now(timezone.utc)
                        resource_durations["event_grid"] = (
                            event_grid_end_time - event_grid_start_time
                        ).total_seconds()

                        status_data = {
                            "status": "completed",
                            "message": f"{function_app_message}"
                            + (
                                " Event Grid deployment attempted."
                                if retrieved_function_app_id
                                else ""
                            ),
                            "details": {
                                "infrastructure_complete": True,
                                "storage": {
                                    "storage_account": "storage_account_name"
                                    in resource_data,
                                    "containers": {
                                        "uploads": "uploads_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "input": "input_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "output": "output_container"
                                        in main_bicep_outputs.get("resources", {}),
                                    },
                                },
                                "storage_complete": "storage_account_name"
                                in resource_data,
                                "search": {
                                    "search_service": "search_service_name"
                                    in resource_data,
                                    "index": "search_index_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "indexer": "search_indexer_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "datasource": "search_datasource_name"
                                    in main_bicep_outputs.get("resources", {}),
                                },
                                "search_complete": "search_service_name"
                                in resource_data,
                                "function": {
                                    "function_app": True,
                                    "event_grid_topic": event_grid_complete,
                                    "event_grid_system_topic": event_grid_complete,
                                    "event_grid": event_grid_complete,
                                    "maturity_assessment": True,
                                    "executive_summary": True,
                                },
                                "function_app_complete": True,
                                "event_grid_complete": event_grid_complete,
                                "function_complete": True,
                                "overall_complete": True,
                                "ready_for_function_deployment": False,
                                "completion_percentage": 100,
                            },
                        }
                        await project_service.update_deployment_status(
                            project_id,
                            status_data["status"],
                            status_data["message"],
                            status_data.get("details"),
                            status_data.get("error"),
                        )
                        logging.info(
                            "Deployment summary and final status written to Cosmos DB"
                        )
                    else:
                        # Function App deployment failed, but main infrastructure is still good
                        logging.warning(
                            "Function App deployment failed or name could not be retrieved"
                        )

                        status_data = {
                            "status": "failed",
                            "message": f"{function_app_message} Main infrastructure deployed successfully, but Function App deployment failed.",
                            "details": {
                                "infrastructure_complete": True,
                                "storage": {
                                    "storage_account": "storage_account_name"
                                    in resource_data,
                                    "containers": {
                                        "uploads": "uploads_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "input": "input_container"
                                        in main_bicep_outputs.get("resources", {}),
                                        "output": "output_container"
                                        in main_bicep_outputs.get("resources", {}),
                                    },
                                },
                                "storage_complete": "storage_account_name" in resource_data,
                                "search": {
                                    "search_service": "search_service_name"
                                    in resource_data,
                                    "index": "search_index_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "indexer": "search_indexer_name"
                                    in main_bicep_outputs.get("resources", {}),
                                    "datasource": "search_datasource_name"
                                    in main_bicep_outputs.get("resources", {}),
                                },
                                "search_complete": "search_service_name" in resource_data,
                                "function": {
                                    "function_app": False,
                                    "event_grid_topic": False,
                                    "event_grid_system_topic": False,
                                    "event_grid": False,
                                    "maturity_assessment": False,
                                    "executive_summary": False,
                                },
                                "function_app_complete": False,
                                "function_complete": False,
                                "overall_complete": False,
                                "error_type": "function_app_deployment_error",
                                "completion_percentage": 50,
                            },
                        }
                        await project_service.update_deployment_status(
                            project_id,
                            status_data["status"],
                            status_data["message"],
                            status_data.get("details"),
                            status_data.get("error"),
                        )
            except Exception as func_app_error:
                # Function App deployment exception
                logging.error(f"Error deploying Function App: {func_app_error}")
                error_details = str(func_app_error)

                status_data = {
                    "status": "failed",
                    "message": f"{function_app_message} Main infrastructure deployed successfully, but Function App deployment failed: {str(func_app_error)}",
                    "details": {
                        "infrastructure_complete": True,
                        "storage": {
                            "storage_account": "storage_account_name" in resource_data,
                            "containers": {
                                "uploads": "uploads_container"
                                in main_bicep_outputs.get("resources", {}),
                                "input": "input_container"
                                in main_bicep_outputs.get("resources", {}),
                                "output": "output_container"
                                in main_bicep_outputs.get("resources", {}),
                            },
                        },
                        "storage_complete": "storage_account_name" in resource_data,
                        "search": {
                            "search_service": "search_service_name" in resource_data,
                            "index": "search_index_name"
                            in main_bicep_outputs.get("resources", {}),
                            "indexer": "search_indexer_name"
                            in main_bicep_outputs.get("resources", {}),
                            "datasource": "search_datasource_name"
                            in main_bicep_outputs.get("resources", {}),
                        },
                        "search_complete": "search_service_name" in resource_data,
                        "function": {
                            "function_app": False,
                            "event_grid_topic": False,
                            "event_grid_system_topic": False,
                            "event_grid": False,
                            "maturity_assessment": False,
                            "executive_summary": False,
                        },
                        "function_app_complete": False,
                        "function_complete": False,
                        "overall_complete": False,
                        "error_type": "function_app_deployment_error",
                        "error_details": str(func_app_error),
                        "completion_percentage": 50,
                    },
                }
                await project_service.update_deployment_status(
                    project_id,
                    status_data["status"],
                    status_data["message"],
                    status_data.get("details"),
                    status_data.get("error"),
                )
                deployment_success = False

        except Exception as e:
            error_message = f"Error during main infrastructure deployment: {str(e)}"
            logging.error(error_message)
            error_details = str(e)

            # Update deployment status to failed
            status_data = {
                "status": "failed",
                "message": error_message,
                "details": {
                    "infrastructure_complete": False,
                    "storage": {
                        "storage_account": False,
                        "containers": {
                            "uploads": False,
                            "input": False,
                            "output": False,
                        },
                    },
                    "storage_complete": False,
                    "search": {
                        "search_service": False,
                        "index": False,
                        "indexer": False,
                        "datasource": False,
                    },
                    "search_complete": False,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                        "maturity_assessment": False,
                        "executive_summary": False,
                    },
                    "function_complete": False,
                    "overall_complete": False,
                    "error_type": "deployment_error",
                    "error_details": str(e),
                    "completion_percentage": 0,
                },
            }
            await project_service.update_deployment_status(
                project_id,
                status_data["status"],
                status_data["message"],
                status_data.get("details"),
                status_data.get("error"),
            )
            deployment_success = False

    except Exception as e:
        logging.error(f"Error in deploy_project_resources: {e}")
        error_details = str(e)

        # Update status to failed
        try:
            status_data = {
                "status": "failed",
                "message": f"Error in deployment process: {str(e)}",
                "details": {
                    "infrastructure_complete": False,
                    "storage": {
                        "storage_account": False,
                        "containers": {
                            "uploads": False,
                            "input": False,
                            "output": False,
                        },
                    },
                    "storage_complete": False,
                    "search": {
                        "search_service": False,
                        "index": False,
                        "indexer": False,
                        "datasource": False,
                    },
                    "search_complete": False,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                        "maturity_assessment": False,
                        "executive_summary": False,
                    },
                    "function_complete": False,
                    "overall_complete": False,
                    "error_type": "unexpected_error",
                    "error_details": str(e),
                    "completion_percentage": 0,
                },
            }
            await project_service.update_deployment_status(
                project_id,
                status_data["status"],
                status_data["message"],
                status_data.get("details"),
                status_data.get("error"),
            )
        except Exception as update_error:
            logging.error(f"Error updating deployment status: {update_error}")

        deployment_success = False

    finally:
        final_status = "completed" if deployment_success else "failed"
        final_message = (
            "Deployment completed successfully."
            if deployment_success
            else "Deployment failed."
        )
        try:
            await project_service.update_deployment_status(
                project_id,
                final_status,
                final_message,
            )
        except Exception as update_error:
            logging.error(f"Error updating final deployment status: {update_error}")

        if error_details:
            logging.info(
                f"Deployment finished with status '{final_status}'. Error details: {error_details}"
            )
        else:
            logging.info(f"Deployment finished with status '{final_status}'")

    # Calculate and log total deployment time
    deployment_end_time = datetime.now(timezone.utc)
    deployment_duration = (deployment_end_time - deployment_start_time).total_seconds()
    logger.info(f"Total deployment time: {deployment_duration:.2f} seconds")

    return deployment_success


# If run directly, execute the deployment
if __name__ == "__main__":
    # Set up logging to file for debugging
    debug_log_file = "logs/deploy_project_resources_debug.log"
    debug_handler = logging.FileHandler(debug_log_file)
    debug_handler.setLevel(logging.DEBUG)
    debug_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    debug_handler.setFormatter(debug_formatter)
    logging.getLogger().addHandler(debug_handler)

    logging.info(f"Starting deploy_project_resources.py with args: {sys.argv}")

    if len(sys.argv) < 3:
        print(
            "Usage: deploy_project_resources.py <project_id> <project_name> [--region-id <region_id>] [--resource-group <resource_group>] [--location <location>] [--function-app-id <function_app_id>] [api_url]"
        )
        print("Environment: API_PORT defines the local API port (default 50505; 8000 in Azure)")
        print(
            f"Example: deploy_project_resources.py my-project-id my-project-name --region-id westeurope --resource-group rg-internal-ai --location westeurope --function-app-id /subscriptions/xxx/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/func-my-project-id http://localhost:{API_PORT}"
        )
        sys.exit(1)

    project_id = sys.argv[1]
    project_name = sys.argv[2]

    # Parse remaining arguments
    region_id = "westeurope"  # Default value
    resource_group = "rg-internal-ai"  # Default value
    location = "westeurope"  # Default value
    api_url = f"http://localhost:{API_PORT}"  # Default value
    function_app_id = None  # Default value

    # Parse remaining arguments
    i = 3
    while i < len(sys.argv):
        if sys.argv[i] == "--region-id" and i + 1 < len(sys.argv):
            region_id = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--resource-group" and i + 1 < len(sys.argv):
            resource_group = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--location" and i + 1 < len(sys.argv):
            location = sys.argv[i + 1]
            i += 2
        elif sys.argv[i] == "--function-app-id" and i + 1 < len(sys.argv):
            function_app_id = sys.argv[i + 1]
            i += 2
        elif not sys.argv[i].startswith("--"):
            # Assume this is the API URL if it's not a flag
            api_url = sys.argv[i]
            i += 1
        else:
            # Skip unknown arguments
            i += 1

    # Ensure the API server is reachable before deployment
    if not asyncio.run(check_api_server(API_PORT)):
        logger.error(f"API server not reachable on port {API_PORT}. Exiting.")
        sys.exit(1)

    # Run the deployment
    asyncio.run(
        deploy_project_resources(
            project_id,
            project_name,
            region_id,
            api_url,
            resource_group,
            location,
            function_app_id,
        )
    )

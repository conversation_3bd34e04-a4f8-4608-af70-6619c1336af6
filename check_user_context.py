import os
import asyncio
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.rbac.user_context import get_role_permissions

async def check_user_context():
    load_dotenv()
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    print('Initializing CosmosDB client...')
    init_success = await cosmos_client.initialize()
    print(f'Initialization success: {init_success}')
    
    if init_success:
        print('Testing connection...')
        success, message = await cosmos_client.ensure()
        print(f'Connection test: {success}, Message: {message}')
        
        if success:
            # Get the user ID
            user_id = '7e43ed9b-4998-468e-bcd1-9b78f7c82977'
            
            # Get the user
            user = await cosmos_client.get_user(user_id)
            if user:
                print(f'Found user: {user}')
                
                # Get user permissions based on role
                permissions = get_role_permissions(user.get('role', 'REGULAR_USER'))
                print(f'User permissions: {permissions}')
                
                # Get accessible resources
                accessible_resources = {}
                
                # Get accessible projects
                try:
                    accessible_resources["projects"] = await cosmos_client.get_accessible_projects(user_id)
                    print(f'User has access to {len(accessible_resources["projects"])} projects')
                    for project in accessible_resources["projects"]:
                        print(f'  - Project: {project.get("name")} (ID: {project.get("id")})')
                except Exception as e:
                    print(f'Error getting accessible projects: {e}')
                    accessible_resources["projects"] = []
                
                # Get accessible teams
                try:
                    accessible_resources["teams"] = await cosmos_client.get_accessible_teams(user_id)
                    print(f'User has access to {len(accessible_resources["teams"])} teams')
                except Exception as e:
                    print(f'Error getting accessible teams: {e}')
                    accessible_resources["teams"] = []
                
                # Get accessible regions
                try:
                    if permissions["canViewAllRegions"]:
                        accessible_resources["regions"] = await cosmos_client.get_regions()
                    elif user.get("region"):
                        region = await cosmos_client.get_region(user["region"])
                        accessible_resources["regions"] = [region] if region else []
                    else:
                        accessible_resources["regions"] = []
                    print(f'User has access to {len(accessible_resources["regions"])} regions')
                except Exception as e:
                    print(f'Error getting accessible regions: {e}')
                    accessible_resources["regions"] = []
                
                # Get accessible users
                try:
                    accessible_resources["users"] = await cosmos_client.get_accessible_users(user_id)
                    print(f'User has access to {len(accessible_resources["users"])} users')
                except Exception as e:
                    print(f'Error getting accessible users: {e}')
                    accessible_resources["users"] = []
                
                # Construct the complete user context
                user_context = {
                    "user": user,
                    "permissions": permissions,
                    "accessibleResources": accessible_resources
                }
                
                print(f'User context: {user_context}')
            else:
                print(f'User not found: {user_id}')
            
    await cosmos_client.close()

if __name__ == "__main__":
    asyncio.run(check_user_context())

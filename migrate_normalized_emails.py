#!/usr/bin/env python3
"""
Migration script to add normalized_email field to existing users.
This ensures that existing users can be found by their normalized email addresses.
"""

import asyncio
import os
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.rbac.user_context import normalize_email

async def migrate_normalized_emails():
    """Add normalized_email field to all existing users"""
    load_dotenv()
    
    print("Starting email normalization migration...")
    
    # Initialize Cosmos client
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    try:
        # Initialize the client
        init_success = await cosmos_client.initialize()
        if not init_success:
            print("Failed to initialize Cosmos client")
            return
        
        # Get all users
        print("Fetching all users...")
        all_users = await cosmos_client.get_users()
        print(f"Found {len(all_users)} users")
        
        # Update each user with normalized email
        updated_count = 0
        for user in all_users:
            user_id = user.get("id")
            user_email = user.get("email")
            
            if user_email:
                normalized = normalize_email(user_email)
                current_normalized = user.get("normalized_email")
                
                # Only update if normalized_email is missing or different
                if current_normalized != normalized:
                    print(f"Updating user {user.get('name')} ({user_id})")
                    print(f"  Email: {user_email}")
                    print(f"  Normalized: {normalized}")
                    
                    # Update the user
                    update_data = {"normalized_email": normalized}
                    await cosmos_client.update_user(user_id, update_data)
                    updated_count += 1
                else:
                    print(f"User {user.get('name')} already has correct normalized_email")
            else:
                print(f"User {user.get('name')} ({user_id}) has no email address")
        
        print(f"\nMigration complete. Updated {updated_count} users.")
        
    finally:
        await cosmos_client.close()

def main():
    """Run the migration"""
    print("=== Email Normalization Migration ===\n")
    asyncio.run(migrate_normalized_emails())
    print("\n=== Migration Complete ===")

if __name__ == "__main__":
    main()
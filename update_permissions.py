import os
import asyncio
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

async def update_permissions():
    load_dotenv()
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint,
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'),
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )

    print('Initializing CosmosDB client...')
    init_success = await cosmos_client.initialize()
    print(f'Initialization success: {init_success}')

    if init_success:
        print('Testing connection...')
        success, message = await cosmos_client.ensure()
        print(f'Connection test: {success}, Message: {message}')

        if success:
            # Get the user ID
            user_id = '7e43ed9b-4998-468e-bcd1-9b78f7c82977'

            # Get the user
            user = await cosmos_client.get_user(user_id)
            if user:
                print(f'Found user: {user}')

                # Update the user's permissions
                user['permissions'] = {
                    "canCreateProject": True,
                    "canEditProject": True,
                    "canDeleteProject": True,
                    "canAssignUsers": True,
                    "canSetCostLimits": True,
                    "canAccessAdminPanel": True,
                    "canManageUsers": True,
                    "canManageGlobalSettings": True,
                    "canCreateTeams": True,
                    "canAssignProjects": True,
                    "canAssignTeams": True,
                    "canSetupRegionalAdmins": True,
                    "canTagUsers": True,
                    "canViewAllRegions": True
                }

                # Update the user in the database
                await cosmos_client.update_user(user['id'], user)
                print('User permissions updated successfully')

                # Verify the update
                updated_user = await cosmos_client.get_user(user_id)
                print(f'Updated user: {updated_user}')
            else:
                print(f'User not found: {user_id}')

    await cosmos_client.close()

if __name__ == "__main__":
    asyncio.run(update_permissions())

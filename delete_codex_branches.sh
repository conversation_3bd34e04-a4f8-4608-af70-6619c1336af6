#!/bin/bash

# Delete codex branches from GitHub
echo "Deleting codex branches from GitHub..."

# Get all codex branches
branches=$(git branch -r | grep "github/codex/" | sed 's/.*github\///')

# Convert to array
IFS=$'\n' read -rd '' -a branch_array <<< "$branches"

# Delete in batches of 5
batch_size=5
total=${#branch_array[@]}
current=0

for branch in "${branch_array[@]}"; do
    echo "Deleting $branch"
    git push github --delete "$branch" 2>/dev/null || echo "  Already deleted or error: $branch"
    
    current=$((current + 1))
    
    # Add a small delay every batch_size deletions
    if [ $((current % batch_size)) -eq 0 ]; then
        echo "Processed $current/$total branches. Pausing..."
        sleep 2
    fi
done

echo "Cleaning up local tracking branches..."
git remote prune github

echo "Done! Deleted $total codex branches."
# yaml-language-server: $schema=https://raw.githubusercontent.com/Azure/azure-dev/main/schemas/v1.0/azure.yaml.json

name: sample-app-aoai-chatgpt
metadata:
  template: sample-app-aoai-chatgpt@0.0.1-beta
services:
  backend:
    project: .
    language: py
    host: appservice
    hooks:
      prepackage:
        windows:
          shell: pwsh
          run:  cd ./frontend;npm install;npm run build
          interactive: true
          continueOnError: false
        posix:
          shell: sh
          run:  cd ./frontend;npm install;npm run build
          interactive: true
          continueOnError: false
hooks:
    preprovision:
      windows:
        shell: pwsh
        run: ./scripts/auth_init.ps1
        interactive: true
        continueOnError: false
      posix:
        shell: sh
        run: ./scripts/auth_init.sh
        interactive: true
        continueOnError: false
    postprovision:
      windows:
        shell: pwsh
        run: ./scripts/auth_update.ps1;./scripts/prepdocs.ps1;
        interactive: true
        continueOnError: false
      posix:
        shell: sh
        run: ./scripts/auth_update.sh;./scripts/prepdocs.sh;
        interactive: true
        continueOnError: false

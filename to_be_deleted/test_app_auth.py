#!/usr/bin/env python3
"""
Test script to validate the application's authentication flow.
This script tests the application's authentication endpoints using the Azure Entra ID credentials.
"""

import os
import sys
import json
import requests
from backend.utils.logging_config import configure_logger
from azure.identity import ClientSecretCredential

# Configure logging
logger = configure_logger("test_app_auth")

def get_entra_token():
    """Get an access token for Entra ID authentication."""
    # Get credentials from environment variables
    client_id = os.environ.get("AZURE_CLIENT_ID")
    tenant_id = os.environ.get("AZURE_TENANT_ID")
    client_secret = os.environ.get("AZURE_APP_SECRET") or os.environ.get("AZURE_CLIENT_SECRET")

    if not all([client_id, tenant_id, client_secret]):
        logger.error("Missing required environment variables for Azure Entra ID authentication")
        return None

    try:
        # Create a credential object
        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )

        # Get token for Microsoft Graph API
        token = credential.get_token("https://graph.microsoft.com/.default")
        return token.token
    except Exception as e:
        logger.error(f"Error getting Entra ID token: {e}")
        return None

def test_app_auth(base_url="http://localhost:50508"):
    """
    Test the application's authentication flow.
    
    Args:
        base_url: The base URL of the application
    
    Returns:
        bool: True if all tests pass, False otherwise
    """
    # Get an access token
    token = get_entra_token()
    if not token:
        logger.error("Failed to get Entra ID token")
        return False
    
    logger.info(f"Successfully obtained token: {token[:10]}...{token[-10:]}")
    
    # Test endpoints
    endpoints = [
        # Health check endpoint (should work without authentication)
        {"url": f"{base_url}/api/health", "auth_required": False, "name": "Health Check"},
        
        # Authentication endpoints
        {"url": f"{base_url}/api/auth/me", "auth_required": True, "name": "Current User"},
        
        # RBAC endpoints
        {"url": f"{base_url}/api/rbac/regions", "auth_required": True, "name": "Regions"},
        {"url": f"{base_url}/api/rbac/users", "auth_required": True, "name": "Users"},
        
        # Project endpoints
        {"url": f"{base_url}/api/projects", "auth_required": True, "name": "Projects"}
    ]
    
    all_passed = True
    
    for endpoint in endpoints:
        try:
            logger.info(f"Testing endpoint: {endpoint['name']} ({endpoint['url']})")
            
            headers = {}
            if endpoint["auth_required"]:
                headers["Authorization"] = f"Bearer {token}"
            
            response = requests.get(endpoint["url"], headers=headers)
            
            if response.status_code == 200:
                logger.info(f"Successfully accessed {endpoint['name']}")
                try:
                    response_data = response.json()
                    logger.info(f"Response: {json.dumps(response_data, indent=2)[:200]}...")
                except:
                    logger.info(f"Response: {response.text[:200]}...")
            else:
                logger.error(f"Failed to access {endpoint['name']}: {response.status_code}")
                logger.error(f"Response: {response.text}")
                if endpoint["auth_required"]:
                    all_passed = False
        except Exception as e:
            logger.error(f"Error testing endpoint {endpoint['name']}: {e}")
            if endpoint["auth_required"]:
                all_passed = False
    
    return all_passed

def main():
    """
    Main function to run the test.
    """
    # Set environment variables from command line arguments if provided
    if len(sys.argv) > 1:
        os.environ["AZURE_CLIENT_ID"] = sys.argv[1]
    if len(sys.argv) > 2:
        os.environ["AZURE_TENANT_ID"] = sys.argv[2]
    if len(sys.argv) > 3:
        os.environ["AZURE_APP_SECRET"] = sys.argv[3]
        os.environ["AZURE_CLIENT_SECRET"] = sys.argv[3]
    
    # Get base URL from command line or use default
    base_url = sys.argv[4] if len(sys.argv) > 4 else "http://localhost:50508"
    
    # Test the application's authentication flow
    success = test_app_auth(base_url)
    
    if success:
        logger.info("Application authentication test completed successfully!")
        return 0
    else:
        logger.error("Application authentication test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())

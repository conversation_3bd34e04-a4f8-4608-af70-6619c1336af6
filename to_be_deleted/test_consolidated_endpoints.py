#!/usr/bin/env python3
"""
Test script to verify the consolidated endpoints are working correctly.
This script tests the new consolidated endpoints that reduce API calls.
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:50505"  # Adjust as needed
TEST_TOKEN = "your-test-token-here"  # Replace with actual token

async def test_endpoint(session: aiohttp.ClientSession, endpoint: str, description: str) -> Dict[str, Any]:
    """Test a single endpoint and measure response time."""
    print(f"\n🧪 Testing {description}")
    print(f"   Endpoint: {endpoint}")
    
    start_time = time.time()
    
    try:
        headers = {
            "Authorization": f"Bearer {TEST_TOKEN}",
            "Content-Type": "application/json"
        }
        
        async with session.get(f"{BASE_URL}{endpoint}", headers=headers) as response:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            
            if response.status == 200:
                data = await response.json()
                print(f"   ✅ Success - {response.status} ({response_time:.2f}ms)")
                
                # Print data summary
                if isinstance(data, list):
                    print(f"   📊 Returned {len(data)} items")
                elif isinstance(data, dict):
                    if 'teams' in data and 'projects' in data:
                        # Dashboard data endpoint
                        print(f"   📊 Dashboard data:")
                        print(f"      - Teams: {len(data.get('teams', []))}")
                        print(f"      - Projects: {len(data.get('projects', []))}")
                        print(f"      - Users: {len(data.get('users', []))}")
                        print(f"      - Regions: {len(data.get('regions', []))}")
                    else:
                        print(f"   📊 Returned object with {len(data)} keys")
                
                return {
                    "success": True,
                    "status": response.status,
                    "response_time": response_time,
                    "data_size": len(json.dumps(data)),
                    "item_count": len(data) if isinstance(data, list) else 1
                }
            else:
                error_text = await response.text()
                print(f"   ❌ Failed - {response.status}")
                print(f"   Error: {error_text}")
                return {
                    "success": False,
                    "status": response.status,
                    "response_time": response_time,
                    "error": error_text
                }
                
    except Exception as e:
        end_time = time.time()
        response_time = (end_time - start_time) * 1000
        print(f"   ❌ Exception - {str(e)}")
        return {
            "success": False,
            "response_time": response_time,
            "error": str(e)
        }

async def test_old_vs_new_approach():
    """Compare the old approach (multiple calls) vs new approach (consolidated)."""
    print("🚀 Testing API Performance: Old vs New Approach")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        
        # Test new consolidated endpoints
        print("\n🆕 NEW APPROACH - Consolidated Endpoints")
        print("-" * 40)
        
        new_results = []
        
        # Test dashboard data endpoint (single call for everything)
        result = await test_endpoint(
            session, 
            "/api/rbac/dashboard-data", 
            "Dashboard Data (All-in-One)"
        )
        new_results.append(result)
        
        # Test teams consolidated endpoint
        result = await test_endpoint(
            session, 
            "/api/rbac/teams/consolidated", 
            "Teams with Members & Projects"
        )
        new_results.append(result)
        
        # Test projects consolidated endpoint
        result = await test_endpoint(
            session, 
            "/api/rbac/projects/consolidated", 
            "Projects with Teams"
        )
        new_results.append(result)
        
        # Test old approach (multiple individual calls)
        print("\n🔄 OLD APPROACH - Individual Endpoints")
        print("-" * 40)
        
        old_results = []
        
        # Simulate the old approach with multiple calls
        endpoints = [
            ("/api/rbac/teams", "Teams"),
            ("/api/rbac/projects", "Projects"),
            ("/api/rbac/users", "Users"),
            ("/api/rbac/regions", "Regions"),
        ]
        
        for endpoint, description in endpoints:
            result = await test_endpoint(session, endpoint, description)
            old_results.append(result)
        
        # Calculate and display results
        print("\n📊 PERFORMANCE COMPARISON")
        print("=" * 60)
        
        new_total_time = sum(r.get('response_time', 0) for r in new_results if r.get('success'))
        old_total_time = sum(r.get('response_time', 0) for r in old_results if r.get('success'))
        
        new_call_count = len([r for r in new_results if r.get('success')])
        old_call_count = len([r for r in old_results if r.get('success')])
        
        print(f"New Approach:")
        print(f"  - API Calls: {new_call_count}")
        print(f"  - Total Time: {new_total_time:.2f}ms")
        print(f"  - Avg Time per Call: {new_total_time/max(new_call_count,1):.2f}ms")
        
        print(f"\nOld Approach:")
        print(f"  - API Calls: {old_call_count}")
        print(f"  - Total Time: {old_total_time:.2f}ms")
        print(f"  - Avg Time per Call: {old_total_time/max(old_call_count,1):.2f}ms")
        
        if old_total_time > 0:
            improvement = ((old_total_time - new_total_time) / old_total_time) * 100
            print(f"\n🎯 Performance Improvement: {improvement:.1f}%")
            print(f"   Time Saved: {old_total_time - new_total_time:.2f}ms")
        
        print(f"\n📉 API Call Reduction: {old_call_count - new_call_count} fewer calls")

if __name__ == "__main__":
    print("⚡ Consolidated Endpoints Performance Test")
    print("This script tests the new consolidated endpoints that reduce API calls.")
    print("\nNote: Make sure the backend server is running and update the TEST_TOKEN.")
    
    try:
        asyncio.run(test_old_vs_new_approach())
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")

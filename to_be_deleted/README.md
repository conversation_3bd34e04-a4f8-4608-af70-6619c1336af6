# Files Not Currently Used by app.py

This folder contains files that are not directly used by the main application (`app.py`). These files have been moved here for organization purposes but are preserved in case they are needed in the future.

## File Categories

### Test Files
- `test_update_project.py` - Tests for project updates
- `test_rbac_update_project.py` - Tests for RBAC project updates
- `test_cosmos_connection.py` - Tests for CosmosDB connections
- `test_deployment_api.py` - Tests for deployment API
- `test_deployment.py` - Tests for deployments
- `test_deployment_process.py` - Tests for deployment processes
- `test_event_grid.sh` - Tests for Event Grid
- `test_full_deployment.sh` - Tests for full deployments
- `test_priorityplot.py` - Tests for PriorityPlot
- `test_update_with_deployment.py` - Tests for updates with deployments
- `test_validate_deployment.sh` - Tests for deployment validation

### Utility Scripts
- `fetch_project.py` - Script to fetch project details
- `check_project_region.py` - Script to check project regions
- `check_rbac_data.py` - <PERSON><PERSON>t to check RBAC data
- `check_env.sh` - <PERSON>ript to check environment variables
- `check_api_server.py` - <PERSON>ript to check API server status
- `fix_project_retrieval.py` - <PERSON>ript to fix project retrieval
- `update_project_from_json.py` - Script to update projects from JSON
- `update_project_deployment.py` - Script to update project deployments

### Deployment Scripts
- `deploy_local_test.sh` - Script for local deployment testing
- `run_deployment_test.sh` - Script to run deployment tests
- `run_test_update.sh` - Script to run test updates
- `local_mock_deployment.py` - Script for local mock deployments

### Configuration Files
- `gunicorn.conf.py` - Gunicorn configuration
- `host.json` - Host configuration
- `local.settings.json` - Local settings
- `mock_deployment_summary.json` - Mock deployment summary
- `WebApp.Dockerfile` - Dockerfile for the web app
- `WebApp.dockerignore` - Dockerignore file for the web app

## Note
These files may still be useful for development, testing, or reference purposes. They have been moved to this folder to keep the root directory clean and focused on files that are actively used by the main application.

#!/usr/bin/env python3
"""
Test the updated CostManagementService with correct tag filtering
"""

import os
import asyncio
import sys

# Load environment variables from .env file
def load_env_file():
    """Load environment variables from .env file"""
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    value = value.strip('"').strip("'")
                    os.environ[key] = value
        print("✅ Environment variables loaded from .env file")
    except Exception as e:
        print(f"⚠️ Error loading .env file: {e}")

load_env_file()

# Add backend to path
sys.path.append('/workspaces/ai-scope-app2/backend')

try:
    from backend.cost_management.cost_management_service import CostManagementService
    print("✅ CostManagementService import successful")
except ImportError as e:
    print(f"❌ Import failed: {e}")
    exit(1)

async def test_updated_service():
    """Test the updated CostManagementService"""
    
    print("🔧 Testing Updated CostManagementService")
    print("=" * 50)
    
    # Initialize service
    service = CostManagementService()
    
    if not service.client:
        print("❌ Service client not available")
        return
    
    print("✅ Service initialized successfully")
    
    # Test project ID
    project_id = "35a7db33-b409-4873-b3bc-54e42931bd3d"
    time_range = "month"
    
    print(f"\n🔧 TEST 1: Query all project costs (project-id tag)")
    print("-" * 40)
    
    try:
        all_projects_response = await service.query_all_project_costs(time_range)
        
        if hasattr(all_projects_response, 'rows') and all_projects_response.rows:
            rows = all_projects_response.rows
            print(f"✅ Found {len(rows)} project-id tag entries")
            
            for i, row in enumerate(rows, 1):
                print(f"  {i}. {row}")
                
        else:
            print("⚠️ No project-id tags found")
            
    except Exception as e:
        print(f"❌ All projects query failed: {e}")
    
    print(f"\n🔧 TEST 2: Query specific project costs")
    print(f"Project ID: {project_id}")
    print("-" * 40)
    
    try:
        project_response = await service.query_project_costs(time_range, project_id)
        
        if hasattr(project_response, 'rows') and project_response.rows:
            rows = project_response.rows
            print(f"✅ Found {len(rows)} resources for project {project_id}")
            
            total_cost = 0
            for i, row in enumerate(rows, 1):
                print(f"  {i}. {row}")
                if len(row) > 0:
                    try:
                        cost = float(row[0])
                        total_cost += cost
                    except (ValueError, TypeError):
                        pass
            
            print(f"\n💰 Total cost for project: €{total_cost:.2f}")
            
        else:
            print(f"⚠️ No resources found for project {project_id}")
            
    except Exception as e:
        print(f"❌ Project query failed: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n🔧 TEST 3: Query by generic tag")
    print("-" * 40)
    
    try:
        # Test the generic tag method
        generic_response = await service.query_cost_by_tag(time_range, "project-id", None)
        
        if hasattr(generic_response, 'rows') and generic_response.rows:
            rows = generic_response.rows
            print(f"✅ Generic tag query found {len(rows)} entries")
            
            for i, row in enumerate(rows[:3], 1):  # Show first 3
                print(f"  {i}. {row}")
                
        else:
            print("⚠️ Generic tag query returned no results")
            
    except Exception as e:
        print(f"❌ Generic tag query failed: {e}")
    
    print(f"\n🎯 SUMMARY")
    print("=" * 50)
    print("✅ Updated CostManagementService tested")
    print("✅ Using correct SDK models (QueryFilter, QueryComparisonExpression)")
    print("✅ Using project-id tag name")
    print("✅ Proper error handling and logging")

if __name__ == "__main__":
    asyncio.run(test_updated_service())

#!/usr/bin/env python3

import asyncio
import json
from backend.utils.logging_config import configure_logger
import os
import sys
from datetime import datetime, timezone
from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

# Configure logging
logger = configure_logger("test_rbac_update_project")

async def test_rbac_update_project(project_id, region_id):
    """
    Test the RBAC client's update_project method with the fixed implementation.
    
    Args:
        project_id (str): The project ID.
        region_id (str): The region ID of the project (used as partition key).
    """
    logger.info(f"Testing RBAC update_project for project {project_id} in region {region_id}")
    
    # Get CosmosDB connection details from environment variables
    cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT")
    cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
    cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE")
    
    if not cosmos_account or not cosmos_key or not cosmos_database:
        logger.error("AZURE_COSMOSDB_ACCOUNT, AZURE_COSMOSDB_ACCOUNT_KEY, and AZURE_COSMOSDB_DATABASE environment variables must be set")
        return False
    
    # Construct the CosmosDB endpoint URL
    cosmos_endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
    
    logger.info(f"Using CosmosDB endpoint: {cosmos_endpoint}")
    logger.info(f"Using CosmosDB database: {cosmos_database}")
    
    # Create a CosmosRbacClient
    rbac_client = CosmosRbacClient(cosmos_endpoint, cosmos_key, cosmos_database)
    
    # Initialize the client
    success = await rbac_client.initialize()
    if not success:
        logger.error("Failed to initialize RBAC client")
        return False
    
    try:
        # Get the project
        project = await rbac_client.get_project_by_id(project_id)
        if not project:
            logger.error(f"Project {project_id} not found")
            return False
        
        logger.info(f"Found project {project_id} with name {project.get('name')}")
        
        # Create a test update
        test_field = f"test_field_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        project["test_field"] = test_field
        project["updated_at"] = datetime.now(timezone.utc).isoformat()
        
        # Update the project using the RBAC client
        updated_project = await rbac_client.update_project(
            project_id=project_id,
            region=region_id,
            project_data=project
        )
        
        if not updated_project:
            logger.error(f"Failed to update project {project_id}")
            return False
        
        logger.info(f"Successfully updated project {project_id} with test field {test_field}")
        return True
    
    except Exception as e:
        logger.error(f"Error testing RBAC update_project: {e}")
        return False
    finally:
        # Close the RBAC client
        await rbac_client.close()

async def main():
    if len(sys.argv) < 3:
        print("Usage: python test_rbac_update_project.py <project_id> <region_id>")
        sys.exit(1)
    
    project_id = sys.argv[1]
    region_id = sys.argv[2]
    
    success = await test_rbac_update_project(project_id, region_id)
    
    if success:
        print(f"✅ Successfully tested RBAC update_project for project {project_id}")
        sys.exit(0)
    else:
        print(f"❌ Failed to test RBAC update_project for project {project_id}")
        sys.exit(1)

if __name__ == "__main__":
    # Load environment variables from .env file
    load_dotenv()
    
    asyncio.run(main())

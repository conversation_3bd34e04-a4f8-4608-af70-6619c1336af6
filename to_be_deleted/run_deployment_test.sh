#!/bin/bash
# Script to run the deployment test

# Set up colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print header
echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}   Testing Project Deployment Process    ${NC}"
echo -e "${BLUE}=========================================${NC}"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: Python 3 is not installed${NC}"
    exit 1
fi

# Load environment variables from .env file if it exists
if [ -f .env ]; then
    echo -e "${BLUE}Loading environment variables from .env file...${NC}"
    export $(grep -v '^#' .env | xargs)
    echo -e "${GREEN}Environment variables loaded successfully${NC}"
else
    echo -e "${YELLOW}Warning: .env file not found${NC}"
fi

# Check if required environment variables are set
if [ -z "$AZURE_COSMOSDB_ACCOUNT_KEY" ] && [ -z "$COSMOSDB_KEY" ]; then
    echo -e "${YELLOW}Warning: AZURE_COSMOSDB_ACCOUNT_KEY or COSMOSDB_KEY environment variable is not set${NC}"
    echo -e "${YELLOW}The test may fail if CosmosDB authentication is required${NC}"
fi

# Check if python-dotenv is installed
if ! python3 -c "import dotenv" &> /dev/null; then
    echo -e "${YELLOW}Warning: python-dotenv is not installed. Installing it now...${NC}"
    pip install python-dotenv
    echo -e "${GREEN}python-dotenv installed successfully${NC}"
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Get current timestamp for log file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/deployment_test_${TIMESTAMP}.log"

echo -e "${BLUE}Logs will be written to ${LOG_FILE}${NC}"

# Function to run the mock test
run_mock_test() {
    echo -e "${BLUE}Running mock test of update_project_resources...${NC}"
    python3 test_deployment_process.py --test-type mock 2>&1 | tee -a "$LOG_FILE"

    # Check if the test was successful
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        echo -e "${GREEN}Mock test completed successfully!${NC}"
        return 0
    else
        echo -e "${RED}Mock test failed!${NC}"
        return 1
    fi
}

# Function to run the real deployment test
run_real_test() {
    local project_name=$1
    local region_id=$2
    local resource_group=$3
    local location=$4

    echo -e "${BLUE}Running real deployment test for project ${project_name}...${NC}"
    python3 test_deployment_process.py --test-type real \
        --project-name "$project_name" \
        --region-id "$region_id" \
        --resource-group "$resource_group" \
        --location "$location" 2>&1 | tee -a "$LOG_FILE"

    # Check if the test was successful
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        echo -e "${GREEN}Real deployment test completed successfully!${NC}"
        return 0
    else
        echo -e "${RED}Real deployment test failed!${NC}"
        return 1
    fi
}

# Parse command line arguments
TEST_TYPE="mock"
PROJECT_NAME="test-$(date +"%Y%m%d%H%M%S")"
REGION_ID="westeurope"
RESOURCE_GROUP="rg-internal-ai"
LOCATION="westeurope"

while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --test-type)
            TEST_TYPE="$2"
            shift
            shift
            ;;
        --project-name)
            PROJECT_NAME="$2"
            shift
            shift
            ;;
        --region-id)
            REGION_ID="$2"
            shift
            shift
            ;;
        --resource-group)
            RESOURCE_GROUP="$2"
            shift
            shift
            ;;
        --location)
            LOCATION="$2"
            shift
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            exit 1
            ;;
    esac
done

# Run the appropriate test
if [ "$TEST_TYPE" == "mock" ]; then
    run_mock_test
    exit $?
elif [ "$TEST_TYPE" == "real" ]; then
    run_real_test "$PROJECT_NAME" "$REGION_ID" "$RESOURCE_GROUP" "$LOCATION"
    exit $?
else
    echo -e "${RED}Invalid test type: ${TEST_TYPE}${NC}"
    echo -e "${YELLOW}Valid options are 'mock' or 'real'${NC}"
    exit 1
fi

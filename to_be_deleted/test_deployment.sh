#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log messages
log() {
    echo -e "$1"
}

# Function to log errors
log_error() {
    echo -e "${RED}ERROR: $1${NC}"
}

# Function to log success
log_success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
}

# Function to log warnings
log_warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
}

# Function to log info
log_info() {
    echo -e "${BLUE}INFO: $1${NC}"
}

# Check if OpenAI API key is set in .env
log_info "Checking for OpenAI API key in .env file..."
if grep -q "AZURE_OPENAI_KEY" .env; then
    OPENAI_KEY=$(grep "AZURE_OPENAI_KEY" .env | cut -d '=' -f2)
    if [ -n "$OPENAI_KEY" ]; then
        log_success "OpenAI API key found in .env file"
    else
        log_error "OpenAI API key is empty in .env file"
        exit 1
    fi
else
    log_error "OpenAI API key not found in .env file"
    exit 1
fi

# Check if ACR exists and is accessible
log_info "Checking if ACR exists and is accessible..."
ACR_NAME="functionappaiscope"
if az acr list --query "[?name=='$ACR_NAME']" -o tsv | grep -q "$ACR_NAME"; then
    log_success "ACR $ACR_NAME exists in the current subscription"
    
    # Check if we have access to it
    if az acr show --name "$ACR_NAME" > /dev/null 2>&1; then
        log_success "ACR $ACR_NAME is accessible"
        
        # Check if the required image exists
        CONTAINER_IMAGE_NAME="functionapp"
        CONTAINER_IMAGE_TAG="latest"
        if az acr repository show --name "$ACR_NAME" --image "${CONTAINER_IMAGE_NAME}:${CONTAINER_IMAGE_TAG}" > /dev/null 2>&1; then
            log_success "Image ${CONTAINER_IMAGE_NAME}:${CONTAINER_IMAGE_TAG} found in ACR $ACR_NAME"
        else
            log_error "Image ${CONTAINER_IMAGE_NAME}:${CONTAINER_IMAGE_TAG} not found in ACR $ACR_NAME"
            log_info "Available images in repository ${CONTAINER_IMAGE_NAME}:"
            az acr repository show-tags --name "$ACR_NAME" --repository "$CONTAINER_IMAGE_NAME" --output table
            exit 1
        fi
    else
        log_error "You don't have access to ACR $ACR_NAME"
        exit 1
    fi
else
    log_error "ACR $ACR_NAME does not exist in the current subscription"
    log_info "Available ACRs in subscription:"
    az acr list --query "[].name" -o tsv
    exit 1
fi

# Check if we're logged in to Azure
log_info "Checking Azure login status..."
if ! az account show > /dev/null 2>&1; then
    log_warning "Not logged in to Azure. Please log in."
    az login
fi

# Check if the resource group exists
RESOURCE_GROUP="rg-internal-ai"
log_info "Checking if resource group $RESOURCE_GROUP exists..."
if az group show --name "$RESOURCE_GROUP" > /dev/null 2>&1; then
    log_success "Resource group $RESOURCE_GROUP exists"
else
    log_error "Resource group $RESOURCE_GROUP does not exist"
    exit 1
fi

# Create a test project
PROJECT_ID="test-$(date +%Y%m%d%H%M%S)"
PROJECT_NAME="Test Project $(date +%Y-%m-%d)"
log_info "Creating test project with ID: $PROJECT_ID, Name: $PROJECT_NAME"

# Run the deployment script
log_info "Running deployment script..."
python deploy_project_resources.py "$PROJECT_ID" "$PROJECT_NAME" "westeurope" "http://localhost:50505" "$RESOURCE_GROUP" "westeurope"

# Check the result
if [ $? -eq 0 ]; then
    log_success "Deployment script completed successfully"
else
    log_error "Deployment script failed"
    exit 1
fi

# Check if the function app was created
log_info "Checking if function app was created..."
FUNCTION_APP_NAME=$(az functionapp list --resource-group "$RESOURCE_GROUP" --query "[?contains(name, '$PROJECT_ID')].name" -o tsv)
if [ -n "$FUNCTION_APP_NAME" ]; then
    log_success "Function app $FUNCTION_APP_NAME was created"
else
    log_error "Function app was not created"
    exit 1
fi

# Check if the event grid system topic was created
log_info "Checking if event grid system topic was created..."
EVENT_GRID_SYSTEM_TOPIC_NAME=$(az eventgrid system-topic list --resource-group "$RESOURCE_GROUP" --query "[?contains(name, '$PROJECT_ID')].name" -o tsv)
if [ -n "$EVENT_GRID_SYSTEM_TOPIC_NAME" ]; then
    log_success "Event grid system topic $EVENT_GRID_SYSTEM_TOPIC_NAME was created"
    
    # Check if the event subscription was created
    log_info "Checking if event subscription was created..."
    EVENT_SUBSCRIPTION_NAME=$(az eventgrid system-topic event-subscription list --resource-group "$RESOURCE_GROUP" --system-topic-name "$EVENT_GRID_SYSTEM_TOPIC_NAME" --query "[].name" -o tsv)
    if [ -n "$EVENT_SUBSCRIPTION_NAME" ]; then
        log_success "Event subscription $EVENT_SUBSCRIPTION_NAME was created"
    else
        log_error "Event subscription was not created"
    fi
else
    log_error "Event grid system topic was not created"
fi

log_info "Test completed"

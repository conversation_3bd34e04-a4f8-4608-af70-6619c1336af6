#!/bin/bash
# Script to validate an existing deployment

# Set variables
PROJECT_ID="050806-test"
RESOURCE_GROUP="rg-internal-ai"

echo "Validating deployment for project $PROJECT_ID in resource group $RESOURCE_GROUP"

# Get the storage account name
STORAGE_ACCOUNT_NAME=$(az storage account list --resource-group $RESOURCE_GROUP --query "[?tags.\"project-id\" == '$PROJECT_ID'].name" --output tsv)
echo "Storage Account Name: $STORAGE_ACCOUNT_NAME"

if [ -z "$STORAGE_ACCOUNT_NAME" ]; then
  echo "ERROR: Storage Account not found!"
else
  echo "Storage Account exists: $STORAGE_ACCOUNT_NAME"

  # Get the storage account ID
  STORAGE_ACCOUNT_ID=$(az storage account show --name $STORAGE_ACCOUNT_NAME --resource-group $RESOURCE_GROUP --query "id" --output tsv)
  echo "Storage Account ID: $STORAGE_ACCOUNT_ID"
fi

# Get the function app name
FUNCTION_APP_NAME=$(az functionapp list --resource-group $RESOURCE_GROUP --query "[?tags.\"project-id\" == '$PROJECT_ID'].name" --output tsv)
echo "Function App Name: $FUNCTION_APP_NAME"

if [ -z "$FUNCTION_APP_NAME" ]; then
  echo "ERROR: Function App not found!"
else
  echo "Function App exists: $FUNCTION_APP_NAME"

  # Get the function app ID
  FUNCTION_APP_ID=$(az functionapp show --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query "id" --output tsv)
  echo "Function App ID: $FUNCTION_APP_ID"

  # Check if the EventGridTriggerBlobIndexer function exists
  echo "Checking for EventGridTriggerBlobIndexer function..."
  az functionapp function list --name $FUNCTION_APP_NAME --resource-group $RESOURCE_GROUP --query "[?name=='EventGridTriggerBlobIndexer']" --output json
fi

# Get the event grid system topic name
EVENT_GRID_SYSTEM_TOPIC_NAME=$(az eventgrid system-topic list --resource-group $RESOURCE_GROUP --query "[?tags.\"project-id\" == '$PROJECT_ID'].name" --output tsv)
echo "Event Grid System Topic Name: $EVENT_GRID_SYSTEM_TOPIC_NAME"

if [ -z "$EVENT_GRID_SYSTEM_TOPIC_NAME" ]; then
  echo "ERROR: Event Grid System Topic not found!"

  # Try to create the event grid system topic
  if [ -n "$STORAGE_ACCOUNT_ID" ] && [ -n "$FUNCTION_APP_ID" ]; then
    echo "Attempting to create Event Grid System Topic..."

    # Generate a sanitized name for Azure resources
    SANITIZED_NAME=$(echo "$PROJECT_ID" | tr '[:upper:]' '[:lower:]' | tr -d ' ' | tr -d '[^a-z0-9-]')
    UNIQUE_SUFFIX=$(echo "$PROJECT_ID" | md5sum | cut -c1-4)
    NEW_EVENT_GRID_SYSTEM_TOPIC_NAME="evgt-${SANITIZED_NAME}-${UNIQUE_SUFFIX}"

    echo "Creating Event Grid System Topic: $NEW_EVENT_GRID_SYSTEM_TOPIC_NAME"
    az eventgrid system-topic create \
      --name $NEW_EVENT_GRID_SYSTEM_TOPIC_NAME \
      --resource-group $RESOURCE_GROUP \
      --source $STORAGE_ACCOUNT_ID \
      --topic-type "Microsoft.Storage.StorageAccounts" \
      --tags project-id=$PROJECT_ID

    if [ $? -eq 0 ]; then
      echo "Event Grid System Topic created successfully!"
      EVENT_GRID_SYSTEM_TOPIC_NAME=$NEW_EVENT_GRID_SYSTEM_TOPIC_NAME

      # Create the event subscription
      echo "Creating Event Subscription..."
      az eventgrid system-topic event-subscription create \
        --name "blob-to-function-subscription" \
        --resource-group $RESOURCE_GROUP \
        --system-topic-name $EVENT_GRID_SYSTEM_TOPIC_NAME \
        --endpoint-type azurefunction \
        --endpoint $FUNCTION_APP_ID/functions/EventGridTriggerBlobIndexer \
        --included-event-types "Microsoft.Storage.BlobCreated" "Microsoft.Storage.BlobDeleted" \
        --subject-begins-with "/blobServices/default/containers/" \
        --subject-ends-with ".pdf" \
        --max-delivery-attempts 30 \
        --event-ttl 1440 \
        --max-events-per-batch 1 \
        --preferred-batch-size-in-kilobytes 64

      if [ $? -eq 0 ]; then
        echo "Event Subscription created successfully!"
      else
        echo "ERROR: Failed to create Event Subscription!"
      fi
    else
      echo "ERROR: Failed to create Event Grid System Topic!"
    fi
  else
    echo "Cannot create Event Grid System Topic: Missing Storage Account ID or Function App ID"
  fi
else
  echo "Event Grid System Topic exists: $EVENT_GRID_SYSTEM_TOPIC_NAME"

  # Get the event subscription
  EVENT_SUBSCRIPTION_NAME=$(az eventgrid system-topic event-subscription list --resource-group $RESOURCE_GROUP --system-topic-name "$EVENT_GRID_SYSTEM_TOPIC_NAME" --query "[].name" --output tsv)
  echo "Event Subscription Name: $EVENT_SUBSCRIPTION_NAME"

  # Check if the event subscription exists
  if [ -z "$EVENT_SUBSCRIPTION_NAME" ]; then
    echo "ERROR: Event Subscription not found!"

    # Try to create the event subscription
    if [ -n "$FUNCTION_APP_ID" ]; then
      echo "Attempting to create Event Subscription..."
      az eventgrid system-topic event-subscription create \
        --name "blob-to-function-subscription" \
        --resource-group $RESOURCE_GROUP \
        --system-topic-name $EVENT_GRID_SYSTEM_TOPIC_NAME \
        --endpoint-type azurefunction \
        --endpoint $FUNCTION_APP_ID/functions/EventGridTriggerBlobIndexer \
        --included-event-types "Microsoft.Storage.BlobCreated" "Microsoft.Storage.BlobDeleted" \
        --subject-begins-with "/blobServices/default/containers/" \
        --subject-ends-with ".pdf" \
        --max-delivery-attempts 30 \
        --event-ttl 1440 \
        --max-events-per-batch 1 \
        --preferred-batch-size-in-kilobytes 64

      if [ $? -eq 0 ]; then
        echo "Event Subscription created successfully!"
      else
        echo "ERROR: Failed to create Event Subscription!"
      fi
    else
      echo "Cannot create Event Subscription: Missing Function App ID"
    fi
  else
    echo "Event Subscription exists: $EVENT_SUBSCRIPTION_NAME"

    # Get the event subscription details
    echo "Event Subscription Details:"
    az eventgrid system-topic event-subscription show --resource-group $RESOURCE_GROUP --system-topic-name "$EVENT_GRID_SYSTEM_TOPIC_NAME" --name "$EVENT_SUBSCRIPTION_NAME"
  fi
fi

echo "Validation complete!"

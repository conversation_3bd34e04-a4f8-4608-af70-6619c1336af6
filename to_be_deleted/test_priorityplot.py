#!/usr/bin/env python
"""
Test script for the PriorityPlot functionality.
This script will create the necessary directories and test the data_utils.py functionality.
"""

import os
import sys
import json
from backend.priorityplot import data_utils

def main():
    """Main function to test the PriorityPlot functionality."""
    print("Testing PriorityPlot functionality...")
    
    # Ensure the uploads and public directories exist
    uploads_dir = data_utils.ensure_uploads_directory()
    public_dir = data_utils.ensure_public_directory()
    
    print(f"Uploads directory: {uploads_dir}")
    print(f"Public directory: {public_dir}")
    
    # Check if an Excel file was provided as an argument
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
        print(f"Processing file: {excel_file}")
        
        # Check if the file exists
        if not os.path.exists(excel_file):
            print(f"File does not exist: {excel_file}")
            return
        
        # Process the file
        file_info, data = data_utils.process_uploaded_file(excel_file)
        
        if file_info and data:
            print("File processed successfully!")
            print(f"File info: {json.dumps(file_info, indent=2)}")
            print(f"Number of initiatives: {len(data['initiatives'])}")
        else:
            print("Failed to process file.")
    else:
        print("No Excel file provided. Usage: python test_priorityplot.py <excel_file>")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3

import asyncio
import json
import logging
import os
import sys
from azure.cosmos.aio import CosmosClient
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

async def check_project_region(project_id):
    """
    Check the region of a project in Cosmos DB.
    
    Args:
        project_id (str): The project ID.
    """
    logger.info(f"Checking region for project {project_id}")
    
    # Get CosmosDB connection details from environment variables
    cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT")
    cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
    cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE")
    
    if not cosmos_account or not cosmos_key or not cosmos_database:
        logger.error("AZURE_COSMOSDB_ACCOUNT, AZURE_COSMOSDB_ACCOUNT_KEY, and AZURE_COSMOSDB_DATABASE environment variables must be set")
        return False
    
    # Construct the CosmosDB endpoint URL
    cosmos_endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
    
    logger.info(f"Using CosmosDB endpoint: {cosmos_endpoint}")
    logger.info(f"Using CosmosDB database: {cosmos_database}")
    
    # Create a CosmosDB client
    async with CosmosClient(cosmos_endpoint, credential=cosmos_key) as client:
        # Get the database
        database = client.get_database_client(cosmos_database)
        
        # Get the projects container
        projects_container = database.get_container_client("projects")
        
        try:
            # Try to find the project using a query
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            parameters = [{"name": "@projectId", "value": project_id}]
            
            projects = []
            try:
                # First try with enable_cross_partition_query parameter
                async for item in projects_container.query_items(
                    query=query,
                    parameters=parameters,
                    enable_cross_partition_query=True
                ):
                    projects.append(item)
            except TypeError as e:
                if "unexpected keyword argument 'enable_cross_partition_query'" in str(e):
                    # If that fails, try without the parameter
                    logger.info("Retrying query without enable_cross_partition_query parameter")
                    async for item in projects_container.query_items(
                        query=query,
                        parameters=parameters
                    ):
                        projects.append(item)
                else:
                    # Re-raise if it's a different TypeError
                    raise
            
            if not projects:
                logger.error(f"Project {project_id} not found in database")
                return False
            
            project_doc = projects[0]
            region = project_doc.get("region")
            
            logger.info(f"Project {project_id} has region: {region}")
            logger.info(f"Project details: {json.dumps(project_doc, indent=2)}")
            
            return True
        
        except Exception as e:
            logger.error(f"Error checking project region: {e}")
            return False

async def main():
    if len(sys.argv) < 2:
        print("Usage: python check_project_region.py <project_id>")
        sys.exit(1)
    
    project_id = sys.argv[1]
    
    success = await check_project_region(project_id)
    
    if success:
        print(f"✅ Successfully checked region for project {project_id}")
        sys.exit(0)
    else:
        print(f"❌ Failed to check region for project {project_id}")
        sys.exit(1)

if __name__ == "__main__":
    # Load environment variables from .env file
    load_dotenv()
    
    asyncio.run(main())

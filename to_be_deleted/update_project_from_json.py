#!/usr/bin/env python3
"""
Update a project with resource information from a JSON file.

This script takes a project ID and a JSON file containing resource information
and updates the project in the database.
"""

import json
import os
import sys
import subprocess
import logging
import argparse
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Update a project with resource information from a JSON file")
    parser.add_argument("project_id", help="The UUID of the project")
    parser.add_argument("json_file", help="Path to a JSON file containing resource information")
    parser.add_argument("--api-url", default="http://localhost:50505", help="Base URL of the API")
    parser.add_argument("--update-status", action="store_true", help="Update the deployment status to success")
    
    args = parser.parse_args()
    
    logger.info(f"Updating project {args.project_id} with resource information from {args.json_file}")
    
    # Check if the JSON file exists
    if not os.path.isfile(args.json_file):
        logger.error(f"JSON file {args.json_file} does not exist")
        sys.exit(1)
    
    # Load the JSON file
    try:
        with open(args.json_file, 'r') as f:
            resource_data = json.load(f)
        logger.info(f"Loaded resource data from {args.json_file}")
    except Exception as e:
        logger.error(f"Error loading JSON file: {e}")
        sys.exit(1)
    
    # Run the update_project_deployment.py script
    cmd = [
        sys.executable,
        "update_project_deployment.py",
        args.project_id,
        "--deployment-file", args.json_file,
        "--api-url", args.api_url
    ]
    
    if args.update_status:
        cmd.append("--update-status")
    
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        check=False
    )
    
    if result.returncode == 0:
        logger.info(f"Successfully updated project {args.project_id}")
        logger.info(result.stdout)
    else:
        logger.error(f"Failed to update project {args.project_id}")
        logger.error(f"Return code: {result.returncode}")
        logger.error(f"Stdout: {result.stdout}")
        logger.error(f"Stderr: {result.stderr}")
        sys.exit(1)
    
    logger.info(f"Project {args.project_id} update completed")
    sys.exit(0)

if __name__ == "__main__":
    main()

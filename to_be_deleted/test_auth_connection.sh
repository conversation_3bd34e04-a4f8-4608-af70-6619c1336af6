#!/bin/bash
# <PERSON>ript to test and validate Azure Entra ID authentication

# Set environment variables from .env file if it exists
if [ -f .env ]; then
    echo "Loading environment variables from .env file"
    export $(grep -v '^#' .env | xargs)
else
    echo "No .env file found, using provided credentials"
fi

# Check if Python virtual environment exists
if [ ! -d ".venv" ]; then
    echo "Creating Python virtual environment"
    python3 -m venv .venv
fi

# Activate virtual environment
source .venv/bin/activate

# Install required packages
echo "Installing required packages"
pip install azure-identity requests

# Set environment variables from command line arguments if provided
if [ $# -ge 3 ]; then
    export AZURE_CLIENT_ID=$1
    export AZURE_TENANT_ID=$2
    export AZURE_APP_SECRET=$3
    export AZURE_CLIENT_SECRET=$3
fi

# Display the credentials being used
echo "Using the following Azure Entra ID credentials:"
echo "  AZURE_CLIENT_ID: ${AZURE_CLIENT_ID}"
echo "  AZURE_TENANT_ID: ${AZURE_TENANT_ID}"
echo "  AZURE_APP_SECRET: ${AZURE_APP_SECRET:0:5}...${AZURE_APP_SECRET: -4}"
echo "  AZURE_CLIENT_SECRET: ${AZURE_CLIENT_SECRET:0:5}...${AZURE_CLIENT_SECRET: -4}"

# Run the Entra ID authentication test
echo ""
echo "Testing Azure Entra ID authentication..."
python test_entra_auth.py
ENTRA_TEST_RESULT=$?

# Check if the application is running
echo ""
echo "Checking if the application is running..."
curl -s http://localhost:50508/api/health > /dev/null
APP_RUNNING=$?

if [ $APP_RUNNING -ne 0 ]; then
    echo "Application is not running. Starting the application..."
    # Check if start.sh exists and is executable
    if [ -f "./start.sh" ] && [ -x "./start.sh" ]; then
        # Start the application in the background
        ./start.sh &
        APP_PID=$!
        echo "Application started with PID: $APP_PID"
        
        # Wait for the application to start
        echo "Waiting for the application to start..."
        sleep 10
        
        # Check if the application is running now
        curl -s http://localhost:50508/api/health > /dev/null
        APP_RUNNING=$?
        
        if [ $APP_RUNNING -ne 0 ]; then
            echo "Failed to start the application. Please start it manually and run this script again."
            exit 1
        fi
    else
        echo "start.sh not found or not executable. Please start the application manually and run this script again."
        exit 1
    fi
fi

# Run the API authentication test
echo ""
echo "Testing API authentication..."
python test_api_auth.py
API_TEST_RESULT=$?

# Check the results
echo ""
echo "Test Results:"
echo "  Entra ID Authentication Test: $([ $ENTRA_TEST_RESULT -eq 0 ] && echo 'PASSED' || echo 'FAILED')"
echo "  API Authentication Test: $([ $API_TEST_RESULT -eq 0 ] && echo 'PASSED' || echo 'FAILED')"

# Overall result
if [ $ENTRA_TEST_RESULT -eq 0 ] && [ $API_TEST_RESULT -eq 0 ]; then
    echo ""
    echo "All tests PASSED! Azure Entra ID authentication is working correctly."
    exit 0
else
    echo ""
    echo "Some tests FAILED. Please check the logs for details."
    exit 1
fi

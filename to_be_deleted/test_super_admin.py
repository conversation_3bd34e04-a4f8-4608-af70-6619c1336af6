import os
import asyncio
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

async def test_super_admin_projects():
    load_dotenv()
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    print('Initializing CosmosDB client...')
    init_success = await cosmos_client.initialize()
    print(f'Initialization success: {init_success}')
    
    if init_success:
        print('Testing connection...')
        success, message = await cosmos_client.ensure()
        print(f'Connection test: {success}, Message: {message}')
        
        if success:
            print('Getting all projects...')
            all_projects = await cosmos_client.get_projects()
            print(f'Found {len(all_projects)} projects')
            
            print('Getting accessible projects for SuperAdmin...')
            super_admin_projects = await cosmos_client.get_accessible_projects('1')
            print(f'Found {len(super_admin_projects)} accessible projects for SuperAdmin (ID: 1)')
            
            print('Getting accessible projects for the authenticated user...')
            user_projects = await cosmos_client.get_accessible_projects('7e43ed9b-4998-468e-bcd1-9b78f7c82977')
            print(f'Found {len(user_projects)} accessible projects for user 7e43ed9b-4998-468e-bcd1-9b78f7c82977')
            
            # Get user details
            print('Getting user details for the authenticated user...')
            user = await cosmos_client.get_user('7e43ed9b-4998-468e-bcd1-9b78f7c82977')
            print(f'User details: {user}')
            
    await cosmos_client.close()

if __name__ == "__main__":
    asyncio.run(test_super_admin_projects())

#!/usr/bin/env python
"""
<PERSON><PERSON>t to fix project retrieval by adding a bridge between the two Cosmos DB clients.
"""

import asyncio
import logging
from azure.cosmos.aio import CosmosClient
from backend.settings import app_settings
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.history.cosmosdbservice import CosmosConversationClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

async def fix_project_retrieval():
    """Fix project retrieval by adding a bridge between the two Cosmos DB clients."""
    logging.info("Fixing project retrieval...")

    # Check if app_settings.chat_history is configured
    if not app_settings.chat_history:
        logging.error("CosmosDB settings not configured.")
        return False

    # Construct the CosmosDB endpoint
    cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
    logging.info(f"CosmosDB endpoint: {cosmos_endpoint}")

    # Create the clients
    try:
        # Initialize the RBAC client
        rbac_client = CosmosRbacClient(
            cosmos_endpoint,
            app_settings.chat_history.account_key,
            app_settings.chat_history.database
        )
        await rbac_client.initialize()
        
        # Initialize the conversation client
        conversation_client = CosmosConversationClient(
            cosmos_endpoint,
            app_settings.chat_history.account_key,
            app_settings.chat_history.database,
            app_settings.chat_history.conversations_container
        )
        
        # Get all projects from the RBAC client
        rbac_projects = await rbac_client.get_projects()
        logging.info(f"Found {len(rbac_projects)} projects in RBAC 'projects' container")
        
        # For each project in the RBAC client, create a corresponding project in the conversation client
        for project in rbac_projects:
            project_id = project.get('id')
            user_id = project.get('owner', '1')  # Default to user ID 1 if owner not specified
            
            # Check if the project already exists in the conversation client
            existing_project = await conversation_client.get_project(user_id, project_id)
            
            if existing_project:
                logging.info(f"Project {project_id} already exists in conversation container for user {user_id}")
            else:
                # Create a new project document for the conversation client
                conversation_project = {
                    'id': project_id,
                    'type': 'project',
                    'userId': user_id,
                    'name': project.get('name'),
                    'description': project.get('description', ''),
                    'storage_container_uploads': project.get('storage_container_uploads'),
                    'storage_container_input': project.get('storage_container_input'),
                    'storage_container_output': project.get('storage_container_output'),
                    'search_index_name': project.get('search_index_name'),
                    'search_datasource_name': project.get('search_datasource_name'),
                    'createdAt': project.get('created_at')
                }
                
                # Create the project in the conversation client
                try:
                    await conversation_client.container_client.create_item(conversation_project)
                    logging.info(f"Created project {project_id} in conversation container for user {user_id}")
                except Exception as e:
                    logging.error(f"Error creating project {project_id} in conversation container: {e}")
        
        # Close the clients
        await rbac_client.close()
        await conversation_client.close()
        
        logging.info("Project retrieval fix completed successfully")
        return True
    except Exception as e:
        logging.error(f"Error fixing project retrieval: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(fix_project_retrieval())

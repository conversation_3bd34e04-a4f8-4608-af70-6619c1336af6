#!/bin/bash

# Check if required environment variables are set
if [ -z "$AZURE_COSMOSDB_ACCOUNT" ]; then
    echo "AZURE_COSMOSDB_ACCOUNT is not set. Please set it to your Cosmos DB account name."
    echo "Example: export AZURE_COSMOSDB_ACCOUNT=internal-ai-conversation-history-db"
    exit 1
fi

if [ -z "$AZURE_COSMOSDB_ACCOUNT_KEY" ]; then
    echo "AZURE_COSMOSDB_ACCOUNT_KEY is not set. Please set it to your Cosmos DB account key."
    echo "Example: export AZURE_COSMOSDB_ACCOUNT_KEY=your-cosmos-db-key"
    exit 1
fi

if [ -z "$AZURE_COSMOSDB_DATABASE" ]; then
    echo "AZURE_COSMOSDB_DATABASE is not set. Please set it to your Cosmos DB database name."
    echo "Example: export AZURE_COSMOSDB_DATABASE=db_conversation_history"
    exit 1
fi

echo "All required environment variables are set:"
echo "AZURE_COSMOSDB_ACCOUNT: $AZURE_COSMOSDB_ACCOUNT"
echo "AZURE_COSMOSDB_DATABASE: $AZURE_COSMOSDB_DATABASE"
echo "AZURE_COSMOSDB_ACCOUNT_KEY: [REDACTED]"

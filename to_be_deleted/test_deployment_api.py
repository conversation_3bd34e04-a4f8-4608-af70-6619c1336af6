#!/usr/bin/env python3
"""
Test script for the deployment API.
"""

import argparse
import asyncio
import json
import logging
import sys
import uuid
from datetime import datetime, timezone

import httpx

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def create_deployment(
    project_id: str,
    project_name: str,
    region_id: str,
    api_url: str = "http://localhost:50505"
) -> dict:
    """
    Create a new deployment.
    
    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The ID of the region
        api_url (str): The base URL of the API
    
    Returns:
        dict: The deployment response
    """
    url = f"{api_url}/api/system/deployments"
    
    # Create deployment request
    deployment_request = {
        "project_id": project_id,
        "project_name": project_name,
        "region_id": region_id,
        "resource_group": "rg-internal-ai",
        "location": "westeurope",
        "deployment_type": "full",
        "priority": 3,
        "timeout_minutes": 60
    }
    
    # Add headers
    headers = {
        "Content-Type": "application/json",
        "X-Azure-CLI-Credentials": "true"
    }
    
    # Send the request
    logger.info(f"Creating deployment for project {project_id} at {url}")
    async with httpx.AsyncClient() as client:
        response = await client.post(
            url,
            json=deployment_request,
            headers=headers
        )
        
    if response.status_code == 202:
        logger.info(f"Deployment created successfully for project {project_id}")
        return response.json()
    else:
        logger.error(f"Failed to create deployment: {response.status_code} {response.text}")
        return None


async def get_deployment(
    deployment_id: str,
    api_url: str = "http://localhost:50505"
) -> dict:
    """
    Get a deployment.
    
    Args:
        deployment_id (str): The ID of the deployment
        api_url (str): The base URL of the API
    
    Returns:
        dict: The deployment response
    """
    url = f"{api_url}/api/system/deployments/{deployment_id}"
    
    # Add headers
    headers = {
        "Content-Type": "application/json",
        "X-Azure-CLI-Credentials": "true"
    }
    
    # Send the request
    logger.info(f"Getting deployment {deployment_id} from {url}")
    async with httpx.AsyncClient() as client:
        response = await client.get(
            url,
            headers=headers
        )
        
    if response.status_code == 200:
        logger.info(f"Got deployment {deployment_id} successfully")
        return response.json()
    else:
        logger.error(f"Failed to get deployment: {response.status_code} {response.text}")
        return None


async def list_deployments(
    project_id: str = None,
    api_url: str = "http://localhost:50505"
) -> dict:
    """
    List deployments.
    
    Args:
        project_id (str): The ID of the project
        api_url (str): The base URL of the API
    
    Returns:
        dict: The deployments response
    """
    url = f"{api_url}/api/system/deployments"
    
    # Add query parameters
    params = {}
    if project_id:
        params["project_id"] = project_id
        
    # Add headers
    headers = {
        "Content-Type": "application/json",
        "X-Azure-CLI-Credentials": "true"
    }
    
    # Send the request
    logger.info(f"Listing deployments from {url}")
    async with httpx.AsyncClient() as client:
        response = await client.get(
            url,
            params=params,
            headers=headers
        )
        
    if response.status_code == 200:
        logger.info(f"Listed deployments successfully")
        return response.json()
    else:
        logger.error(f"Failed to list deployments: {response.status_code} {response.text}")
        return None


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test the deployment API")
    parser.add_argument("--api-url", default="http://localhost:50505", help="The base URL of the API")
    parser.add_argument("--project-id", help="The ID of the project")
    parser.add_argument("--project-name", help="The name of the project")
    parser.add_argument("--region-id", help="The ID of the region")
    parser.add_argument("--deployment-id", help="The ID of the deployment")
    parser.add_argument("--action", choices=["create", "get", "list"], required=True, help="The action to perform")
    
    args = parser.parse_args()
    
    if args.action == "create":
        if not args.project_id or not args.project_name or not args.region_id:
            logger.error("Project ID, project name, and region ID are required for create action")
            sys.exit(1)
            
        deployment = await create_deployment(
            project_id=args.project_id,
            project_name=args.project_name,
            region_id=args.region_id,
            api_url=args.api_url
        )
        
        if deployment:
            print(json.dumps(deployment, indent=2))
            
    elif args.action == "get":
        if not args.deployment_id:
            logger.error("Deployment ID is required for get action")
            sys.exit(1)
            
        deployment = await get_deployment(
            deployment_id=args.deployment_id,
            api_url=args.api_url
        )
        
        if deployment:
            print(json.dumps(deployment, indent=2))
            
    elif args.action == "list":
        deployments = await list_deployments(
            project_id=args.project_id,
            api_url=args.api_url
        )
        
        if deployments:
            print(json.dumps(deployments, indent=2))


if __name__ == "__main__":
    asyncio.run(main())

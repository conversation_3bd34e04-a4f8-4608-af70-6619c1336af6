#!/bin/bash
# <PERSON>ript to test Azure CLI connectivity and diagnose SSL issues

# Create logs directory if it doesn't exist
LOGS_DIR="logs"
mkdir -p $LOGS_DIR

# Generate a unique log file name based on timestamp
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
LOG_FILE="${LOGS_DIR}/azure_cli_test_${TIMESTAMP}.log"

# Function to log with timestamp
log() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

# Function to log errors with more visibility
log_error() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1"
    echo -e "\e[31m$message\e[0m"  # Red color in terminal
    echo "$message" >> "$LOG_FILE"
}

# Function to log success messages with more visibility
log_success() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1"
    echo -e "\e[32m$message\e[0m"  # Green color in terminal
    echo "$message" >> "$LOG_FILE"
}

log "Starting Azure CLI connectivity test - Logging to $LOG_FILE"

# Check Azure CLI version
log "Checking Azure CLI version..."
AZ_VERSION=$(az version --query '"azure-cli"' -o tsv)
log "Azure CLI version: $AZ_VERSION"

# Check if Azure CLI is logged in
log "Checking Azure CLI login status..."
if ! az account show > /dev/null 2>&1; then
    log_error "Not logged in to Azure CLI. Please run 'az login' first."
    exit 1
fi
log_success "Azure CLI login verified"

# Get current subscription
SUBSCRIPTION_ID=$(az account show --query id -o tsv)
SUBSCRIPTION_NAME=$(az account show --query name -o tsv)
log "Using Azure subscription: $SUBSCRIPTION_NAME ($SUBSCRIPTION_ID)"

# Test resource group access
RESOURCE_GROUP="rg-internal-ai"
log "Testing access to resource group: $RESOURCE_GROUP"
if ! az group show --name $RESOURCE_GROUP > /dev/null 2>&1; then
    log_error "Resource group $RESOURCE_GROUP not found or you don't have access to it."
    exit 1
fi
log_success "Resource group $RESOURCE_GROUP access verified"

# Test network connectivity to Azure
log "Testing network connectivity to Azure..."
if ! curl -s https://management.azure.com/subscriptions/$SUBSCRIPTION_ID?api-version=2020-01-01 -o /dev/null; then
    log_error "Network connectivity test to Azure failed. Check your network connection."
    exit 1
fi
log_success "Network connectivity to Azure verified"

# Test a simple deployment to verify deployment functionality
log "Testing a simple deployment to verify Azure deployment functionality..."
DEPLOYMENT_NAME="test-deployment-${TIMESTAMP}"
DEPLOYMENT_LOG_FILE="${LOGS_DIR}/test_deployment_${TIMESTAMP}.log"

# Create a simple template file
TEMPLATE_FILE=$(mktemp)
cat > $TEMPLATE_FILE << EOF
{
  "\$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "*******",
  "resources": [],
  "outputs": {
    "test": {
      "type": "string",
      "value": "Deployment test successful"
    }
  }
}
EOF

log "Using test template file: $TEMPLATE_FILE"
log "Executing test deployment..."

# Run the test deployment with increased timeout and retry
export AZURE_CLI_DISABLE_CONNECTION_VERIFICATION=1
export AZURE_CLI_EXPERIMENTAL_CONNECTION_VERIFY_OFF=1

# Try the deployment with retries
MAX_RETRIES=3
RETRY_COUNT=0
SUCCESS=false

while [ $RETRY_COUNT -lt $MAX_RETRIES ] && [ "$SUCCESS" != "true" ]; do
    log "Deployment attempt $((RETRY_COUNT+1)) of $MAX_RETRIES..."
    
    # Run the deployment with a timeout
    timeout 60 az deployment group create \
        --resource-group $RESOURCE_GROUP \
        --name $DEPLOYMENT_NAME \
        --template-file $TEMPLATE_FILE \
        --verbose 2>&1 | tee -a "$DEPLOYMENT_LOG_FILE"
    
    DEPLOYMENT_STATUS=$?
    
    if [ $DEPLOYMENT_STATUS -eq 0 ]; then
        log_success "Test deployment completed successfully"
        SUCCESS=true
    else
        log_error "Test deployment failed with status code $DEPLOYMENT_STATUS"
        RETRY_COUNT=$((RETRY_COUNT+1))
        
        if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
            log "Waiting 10 seconds before retry..."
            sleep 10
        fi
    fi
done

# Clean up
rm $TEMPLATE_FILE

if [ "$SUCCESS" = "true" ]; then
    log_success "Azure CLI connectivity test completed successfully"
    echo "To deploy your project resources, run:"
    echo "./deploy_project_resources.sh <project_id> <project_name> --region-id westeurope"
    exit 0
else
    log_error "Azure CLI connectivity test failed after $MAX_RETRIES attempts"
    log "Please check your network connection and Azure CLI configuration"
    log "You may need to run 'az login' again to refresh your authentication token"
    exit 1
fi

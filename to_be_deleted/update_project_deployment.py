#!/usr/bin/env python3
"""
Update project deployment resources and status.

This script is used to update a project's resources and deployment status in the database.
It can be used to update a project after deployment or to fix issues with a project's deployment.
"""

import asyncio
import json
import logging
import os
import sys
import aiohttp
import argparse
from datetime import datetime, timezone
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

async def update_project_resources(project_id: str, resource_data: Dict[str, Any], api_url: str = "http://localhost:50505") -> bool:
    """
    Update the project in CosmosDB with the actual resource names from Azure deployment.
    
    Args:
        project_id (str): The ID of the project
        resource_data (dict): Dictionary containing the resource names
        api_url (str): The base URL of the API
    
    Returns:
        bool: True if update was successful, False otherwise
    """
    logger.info(f"Updating project {project_id} with resource information")
    logger.info(f"Resource data: {json.dumps(resource_data, indent=2)}")
    
    # Endpoint for updating project resources
    update_url = f"{api_url}/api/projects/{project_id}/resources"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Send the update
            logger.info(f"Sending resource update to {update_url}")
            async with session.post(
                update_url, 
                json=resource_data, 
                headers={"X-Azure-CLI-Credentials": "true", "Content-Type": "application/json"}
            ) as response:
                if response.status != 200:
                    response_text = await response.text()
                    logger.error(f"Error updating project resources: {response.status} - {response_text}")
                    return False
                else:
                    response_json = await response.json()
                    logger.info(f"Successfully updated project resources: {json.dumps(response_json, indent=2)}")
                    return True
    except Exception as e:
        logger.error(f"Error updating project resources: {e}")
        return False

async def update_deployment_status(project_id: str, status_data: Dict[str, Any], api_url: str = "http://localhost:50505") -> bool:
    """
    Update the deployment status via the API endpoint.

    Args:
        project_id (str): The ID of the project
        status_data (dict): Dictionary containing the status update
        api_url (str): The base URL of the API
        
    Returns:
        bool: True if update was successful, False otherwise
    """
    update_url = f"{api_url}/api/projects/{project_id}/deployment-status"

    try:
        # Add headers to indicate we're using Azure CLI credentials
        headers = {
            "Content-Type": "application/json",
            "X-Azure-CLI-Credentials": "true"
        }

        # Send the update
        logger.info(f"Sending status update to {update_url}")
        logger.info(f"Status data: {json.dumps(status_data, indent=2)}")

        async with aiohttp.ClientSession() as session:
            async with session.post(update_url, json=status_data, headers=headers) as response:
                if response.status != 200:
                    response_text = await response.text()
                    logger.error(f"Error updating deployment status: {response.status} - {response_text}")
                    return False
                else:
                    response_json = await response.json()
                    logger.info(f"Successfully updated deployment status: {json.dumps(response_json, indent=2)}")
                    return True
    except Exception as e:
        logger.error(f"Error updating deployment status: {e}")
        return False

def create_success_deployment_status() -> Dict[str, Any]:
    """
    Create a deployment status object for a successful deployment.
    
    Returns:
        dict: A deployment status object
    """
    now = datetime.now(timezone.utc).isoformat()
    
    return {
        "status": "success",
        "message": "Project deployment completed successfully",
        "updated_at": now,
        "details": {
            "storage": {
                "storage_account": True,
                "containers": {
                    "uploads": True,
                    "input": True,
                    "output": True
                }
            },
            "storage_complete": True,
            "search": {
                "search_service": True,
                "index": True,
                "indexer": True,
                "datasource": True
            },
            "search_complete": True,
            "function": {
                "function_app": True,
                "event_grid_topic": True,
                "event_grid_system_topic": True,
                "event_grid": True,
                "maturity_assessment": True,
                "executive_summary": True
            },
            "function_complete": True,
            "overall_complete": True,
            "completion_percentage": 100
        }
    }

def map_deployment_to_db_format(deployment_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Map deployment data to the format expected by the database.
    
    Args:
        deployment_data (dict): The deployment data from the deployment output
        
    Returns:
        dict: The mapped data in the format expected by the database
    """
    # Create a new dictionary for the mapped data
    db_format = {}
    
    # Map the fields from deployment format to database format
    if "resources" in deployment_data:
        resources = deployment_data["resources"]
        
        # Map storage account and containers
        if "storage_account_name" in resources:
            db_format["storage_account_name"] = resources["storage_account_name"]
        if "uploads_container" in resources:
            db_format["storage_container_uploads"] = resources["uploads_container"]
        if "input_container" in resources:
            db_format["storage_container_input"] = resources["input_container"]
        if "output_container" in resources:
            db_format["storage_container_output"] = resources["output_container"]
            
        # Map search service and related resources
        if "search_service_name" in resources:
            db_format["search_service_name"] = resources["search_service_name"]
        if "search_index_name" in resources:
            db_format["search_index_name"] = resources["search_index_name"]
        if "search_indexer_name" in resources:
            db_format["search_indexer_name"] = resources["search_indexer_name"]
        if "search_datasource_name" in resources:
            db_format["search_datasource_name"] = resources["search_datasource_name"]
            
        # Map function app and related resources
        if "function_app_name" in resources:
            db_format["function_app_name"] = resources["function_app_name"]
        if "function_app_url" in resources:
            db_format["function_app_url"] = resources["function_app_url"]
    else:
        # If no resources object, assume the input is already in the right format
        db_format = deployment_data.copy()
    
    return db_format

async def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Update project deployment in CosmosDB")
    parser.add_argument("project_id", help="The UUID of the project")
    parser.add_argument("--api-url", default="http://localhost:50505", help="Base URL of the API")
    parser.add_argument("--deployment-file", help="Path to a JSON file containing deployment data")
    parser.add_argument("--update-status", action="store_true", help="Update the deployment status to success")
    
    # Add arguments for individual resource fields
    parser.add_argument("--storage-account", help="Storage account name")
    parser.add_argument("--uploads-container", help="Uploads container name")
    parser.add_argument("--input-container", help="Input container name")
    parser.add_argument("--output-container", help="Output container name")
    parser.add_argument("--search-service", help="Search service name")
    parser.add_argument("--search-index", help="Search index name")
    parser.add_argument("--search-indexer", help="Search indexer name")
    parser.add_argument("--search-datasource", help="Search datasource name")
    parser.add_argument("--function-app", help="Function app name")
    parser.add_argument("--function-app-url", help="Function app URL")
    
    args = parser.parse_args()
    
    # Initialize resource data
    resource_data = {}
    
    # If a deployment file is provided, load it
    if args.deployment_file:
        try:
            with open(args.deployment_file, 'r') as f:
                deployment_data = json.load(f)
                resource_data = map_deployment_to_db_format(deployment_data)
                logger.info(f"Loaded deployment data from {args.deployment_file}")
        except Exception as e:
            logger.error(f"Error loading deployment file: {e}")
            sys.exit(1)
    
    # Override with any explicitly provided arguments
    if args.storage_account:
        resource_data["storage_account_name"] = args.storage_account
    if args.uploads_container:
        resource_data["storage_container_uploads"] = args.uploads_container
    if args.input_container:
        resource_data["storage_container_input"] = args.input_container
    if args.output_container:
        resource_data["storage_container_output"] = args.output_container
    if args.search_service:
        resource_data["search_service_name"] = args.search_service
    if args.search_index:
        resource_data["search_index_name"] = args.search_index
    if args.search_indexer:
        resource_data["search_indexer_name"] = args.search_indexer
    if args.search_datasource:
        resource_data["search_datasource_name"] = args.search_datasource
    if args.function_app:
        resource_data["function_app_name"] = args.function_app
    if args.function_app_url:
        resource_data["function_app_url"] = args.function_app_url
    
    # Update resources if we have any
    if resource_data:
        success = await update_project_resources(
            args.project_id,
            resource_data,
            args.api_url
        )
        
        if not success:
            logger.error(f"Failed to update project {args.project_id} resources")
            sys.exit(1)
    
    # Update deployment status if requested
    if args.update_status:
        status_data = create_success_deployment_status()
        success = await update_deployment_status(
            args.project_id,
            status_data,
            args.api_url
        )
        
        if not success:
            logger.error(f"Failed to update project {args.project_id} deployment status")
            sys.exit(1)
    
    logger.info(f"Successfully updated project {args.project_id}")
    sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Test script to directly test the application's endpoints.
This script tests various endpoints with and without authentication.
"""

import os
import sys
import json
import logging
import requests
import argparse
from azure.identity import ClientSecretCredential

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def get_client_credentials_token(client_id, tenant_id, client_secret):
    """Get a token using client credentials flow."""
    try:
        # Create a credential object
        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )

        # Get token for Microsoft Graph API using .default scope
        token = credential.get_token("https://graph.microsoft.com/.default")
        return token.token
    except Exception as e:
        logger.error(f"Error getting client credentials token: {e}")
        return None

def test_endpoints(base_url="http://localhost:50508", token=None):
    """
    Test various endpoints of the application.
    
    Args:
        base_url: The base URL of the application
        token: Optional token for authenticated requests
    """
    # Define endpoints to test
    endpoints = [
        # Public endpoints (no authentication required)
        {"url": f"{base_url}/", "method": "GET", "auth_required": False, "name": "Home Page"},
        {"url": f"{base_url}/api/health", "method": "GET", "auth_required": False, "name": "Health Check"},
        
        # Authentication endpoints
        {"url": f"{base_url}/api/auth/me", "method": "GET", "auth_required": True, "name": "Current User (Auth)"},
        {"url": f"{base_url}/api/user-context/me", "method": "GET", "auth_required": True, "name": "Current User (Context)"},
        
        # RBAC endpoints
        {"url": f"{base_url}/api/rbac/regions", "method": "GET", "auth_required": True, "name": "Regions"},
        {"url": f"{base_url}/api/rbac/users", "method": "GET", "auth_required": True, "name": "Users"},
        {"url": f"{base_url}/api/rbac/projects", "method": "GET", "auth_required": True, "name": "Projects"},
        {"url": f"{base_url}/api/rbac/me", "method": "GET", "auth_required": True, "name": "Current User (RBAC)"},
    ]
    
    # Test each endpoint
    for endpoint in endpoints:
        try:
            logger.info(f"Testing endpoint: {endpoint['name']} ({endpoint['url']})")
            
            headers = {}
            if endpoint["auth_required"] and token:
                headers["Authorization"] = f"Bearer {token}"
            
            response = requests.request(
                method=endpoint["method"],
                url=endpoint["url"],
                headers=headers
            )
            
            logger.info(f"Status code: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"Successfully accessed {endpoint['name']}")
                try:
                    response_data = response.json()
                    logger.info(f"Response: {json.dumps(response_data, indent=2)[:200]}...")
                except:
                    logger.info(f"Response: {response.text[:200]}...")
            else:
                logger.warning(f"Failed to access {endpoint['name']}: {response.status_code}")
                logger.warning(f"Response: {response.text[:200]}...")
            
            logger.info("-" * 50)
        except Exception as e:
            logger.error(f"Error testing endpoint {endpoint['name']}: {e}")
            logger.info("-" * 50)

def main():
    """
    Main function to run the test.
    """
    parser = argparse.ArgumentParser(description='Test application endpoints')
    parser.add_argument('--url', default="http://localhost:50508", help='The base URL of the application')
    parser.add_argument('--client-id', help='The client ID for authentication')
    parser.add_argument('--tenant-id', help='The tenant ID for authentication')
    parser.add_argument('--client-secret', help='The client secret for authentication')
    parser.add_argument('--no-auth', action='store_true', help='Test without authentication')
    
    args = parser.parse_args()
    
    # Get credentials from arguments or environment variables
    client_id = args.client_id or os.environ.get("AZURE_CLIENT_ID")
    tenant_id = args.tenant_id or os.environ.get("AZURE_TENANT_ID")
    client_secret = args.client_secret or os.environ.get("AZURE_CLIENT_SECRET") or os.environ.get("AZURE_APP_SECRET")
    
    # Get token if authentication is required
    token = None
    if not args.no_auth and all([client_id, tenant_id, client_secret]):
        logger.info("Getting token using client credentials flow...")
        token = get_client_credentials_token(client_id, tenant_id, client_secret)
        
        if token:
            logger.info("Successfully obtained token.")
        else:
            logger.warning("Failed to get token. Testing without authentication.")
    elif args.no_auth:
        logger.info("Testing without authentication as requested.")
    else:
        logger.warning("Missing credentials. Testing without authentication.")
    
    # Test endpoints
    test_endpoints(args.url, token)
    
    logger.info("Testing completed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())

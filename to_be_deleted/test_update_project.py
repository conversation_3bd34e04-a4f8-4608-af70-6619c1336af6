#!/usr/bin/env python3

import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

async def update_project_status(project_id, region_id, project_name=None, resources=None):
    """
    Update the project status in CosmosDB using the fixed implementation.

    Args:
        project_id (str): The project ID.
        region_id (str): The region ID of the project (used as partition key).
        project_name (str, optional): The name of the project.
        resources (dict, optional): Dictionary containing resource details.
    """
    logger.info(f"Updating project {project_id} in region {region_id}")

    # Default resources if not provided
    if resources is None:
        resources = {
            "storage_account_name": "sttestvansh07055gbl",
            "uploads_container": "uploads-testvansh0705-5gbl",
            "input_container": "input-testvansh0705-5gbl",
            "output_container": "output-testvansh0705-5gbl",
            "search_service_name": "search-testvansh0705-5gbl",
            "search_index_name": "project-testvansh0705-index",
            "search_indexer_name": "project-testvansh0705-indexer",
            "search_datasource_name": "project-testvansh0705-ds",
            "function_app_name": "",
            "function_app_url": ""
        }

    # Get CosmosDB connection details from environment variables
    cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT")
    cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
    cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE")

    if not cosmos_account or not cosmos_key or not cosmos_database:
        logger.error("AZURE_COSMOSDB_ACCOUNT, AZURE_COSMOSDB_ACCOUNT_KEY, and AZURE_COSMOSDB_DATABASE environment variables must be set")
        return False

    # Construct the CosmosDB endpoint URL
    cosmos_endpoint = f"https://{cosmos_account}.documents.azure.com:443/"

    logger.info(f"Using CosmosDB endpoint: {cosmos_endpoint}")
    logger.info(f"Using CosmosDB database: {cosmos_database}")

    # Create a CosmosDB client
    async with CosmosClient(cosmos_endpoint, credential=cosmos_key) as client:
        # Get the database
        database = client.get_database_client(cosmos_database)

        # Get the projects container
        projects_container = database.get_container_client("projects")

        try:
            # Try to find the project using a query first
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            parameters = [{"name": "@projectId", "value": project_id}]

            projects = []
            try:
                # First try with enable_cross_partition_query parameter
                async for item in projects_container.query_items(
                    query=query,
                    parameters=parameters,
                    enable_cross_partition_query=True
                ):
                    projects.append(item)
            except TypeError as e:
                if "unexpected keyword argument 'enable_cross_partition_query'" in str(e):
                    # If that fails, try without the parameter
                    logger.info("Retrying query without enable_cross_partition_query parameter")
                    async for item in projects_container.query_items(
                        query=query,
                        parameters=parameters
                    ):
                        projects.append(item)
                else:
                    # Re-raise if it's a different TypeError
                    raise

            if not projects:
                logger.error(f"Project {project_id} not found in database")
                return False

            project_doc = projects[0]
            logger.info(f"Found project {project_id} in database")

            # Update the project document with new status
            now = datetime.now(timezone.utc).isoformat()

            # Create a test update to the deployment status
            deployment_status = project_doc.get("deployment_status", {})
            deployment_status.update({
                "status": "completed",
                "message": "Test update completed successfully",
                "updated_at": now,
                "details": {
                    "storage": {
                        "storage_account": True,
                        "containers": {
                            "uploads": True,
                            "input": True,
                            "output": True
                        }
                    },
                    "storage_complete": True,
                    "search": {
                        "search_service": True,
                        "index": True,
                        "indexer": True,
                        "datasource": True
                    },
                    "search_complete": True,
                    "function": {
                        "function_app": False,
                        "event_grid_topic": False,
                        "event_grid_system_topic": False,
                        "event_grid": False,
                        "maturity_assessment": False,
                        "executive_summary": False
                    },
                    "function_complete": False,
                    "overall_complete": True,
                    "completion_percentage": 100,
                    "test_update": True
                }
            })

            # Update the project document with resources
            project_doc["deployment_status"] = deployment_status
            project_doc["updated_at"] = now
            project_doc["test_update"] = True  # Add a field to verify the update

            # Update resource information
            if "storage_account_name" not in project_doc or not project_doc["storage_account_name"]:
                project_doc["storage_account_name"] = resources["storage_account_name"]

            if "storage_container_uploads" not in project_doc or not project_doc["storage_container_uploads"]:
                project_doc["storage_container_uploads"] = resources["uploads_container"]

            if "storage_container_input" not in project_doc or not project_doc["storage_container_input"]:
                project_doc["storage_container_input"] = resources["input_container"]

            if "storage_container_output" not in project_doc or not project_doc["storage_container_output"]:
                project_doc["storage_container_output"] = resources["output_container"]

            if "search_service_name" not in project_doc or not project_doc["search_service_name"]:
                project_doc["search_service_name"] = resources["search_service_name"]

            if "search_index_name" not in project_doc or not project_doc["search_index_name"]:
                project_doc["search_index_name"] = resources["search_index_name"]

            if "search_indexer_name" not in project_doc or not project_doc["search_indexer_name"]:
                project_doc["search_indexer_name"] = resources["search_indexer_name"]

            if "search_datasource_name" not in project_doc or not project_doc["search_datasource_name"]:
                project_doc["search_datasource_name"] = resources["search_datasource_name"]

            logger.info(f"Updated project document with resources: {json.dumps(resources, indent=2)}")

            # Get the region from the document
            region = project_doc.get("region", region_id)
            logger.info(f"Using region from document: {region}")

            # Replace the item in the container using our fixed approach
            try:
                # First try without partition_key parameter since we're using SDK 4.5.1
                logger.info("Using replace_item without partition_key parameter")
                updated_doc = await projects_container.replace_item(
                    item=project_id,
                    body=project_doc
                )
                logger.info(f"Successfully updated project {project_id} without partition_key")
                return True
            except Exception as e:
                logger.error(f"Error replacing item without partition_key: {e}")
                # If that fails, try with partition_key parameter as a fallback
                try:
                    logger.info("Retrying with partition_key parameter")
                    updated_doc = await projects_container.replace_item(
                        item=project_id,
                        body=project_doc,
                        partition_key=region
                    )
                    logger.info(f"Successfully updated project {project_id} with partition_key")
                    return True
                except Exception as e2:
                    logger.error(f"Error with fallback approach: {e2}")
                    return False

        except Exception as e:
            logger.error(f"Error updating project: {e}")
            return False

async def main():
    if len(sys.argv) < 3:
        print("Usage: python test_update_project.py <project_id> <region_id> [project_name]")
        sys.exit(1)

    project_id = sys.argv[1]
    region_id = sys.argv[2]
    project_name = sys.argv[3] if len(sys.argv) > 3 else "testvansh0705"

    # Define resources based on the deployment summary
    resources = {
        "storage_account_name": "sttestvansh07055gbl",
        "uploads_container": "uploads-testvansh0705-5gbl",
        "input_container": "input-testvansh0705-5gbl",
        "output_container": "output-testvansh0705-5gbl",
        "search_service_name": "search-testvansh0705-5gbl",
        "search_index_name": "project-testvansh0705-index",
        "search_indexer_name": "project-testvansh0705-indexer",
        "search_datasource_name": "project-testvansh0705-ds",
        "function_app_name": "",
        "function_app_url": ""
    }

    success = await update_project_status(project_id, region_id, project_name, resources)

    if success:
        print(f"✅ Successfully updated project {project_id}")
    else:
        print(f"❌ Failed to update project {project_id}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

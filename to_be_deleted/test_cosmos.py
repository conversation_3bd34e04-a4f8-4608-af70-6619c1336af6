import os
import asyncio
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

async def test_cosmos():
    load_dotenv()
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    print('Initializing CosmosDB client...')
    init_success = await cosmos_client.initialize()
    print(f'Initialization success: {init_success}')
    
    if init_success:
        print('Testing connection...')
        success, message = await cosmos_client.ensure()
        print(f'Connection test: {success}, Message: {message}')
        
        if success:
            print('Getting projects...')
            projects = await cosmos_client.get_projects()
            print(f'Found {len(projects)} projects')
            
            print('Getting accessible projects for SuperAdmin...')
            accessible_projects = await cosmos_client.get_accessible_projects('1')  # SuperAdmin ID
            print(f'Found {len(accessible_projects)} accessible projects for SuperAdmin')
            
            print('Getting users...')
            users = []
            query = "SELECT * FROM c WHERE c.type = 'user'"
            async for item in cosmos_client.users_container.query_items(query=query, parameters=[]):
                users.append(item)
            print(f'Found {len(users)} users')
            
    await cosmos_client.close()

if __name__ == "__main__":
    asyncio.run(test_cosmos())

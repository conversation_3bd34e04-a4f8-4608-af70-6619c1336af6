#!/usr/bin/env python3
import asyncio
import os
import json
import sys
from azure.cosmos import CosmosClient, exceptions
from azure.identity import DefaultAzureCredential

class CosmosRbacClient:
    def __init__(self, cosmosdb_endpoint: str, credential, database_name: str):
        self.cosmosdb_endpoint = cosmosdb_endpoint
        self.credential = credential
        self.database_name = database_name
        self.cosmosdb_client = None
        self.database_client = None
        self.projects_container = None

    async def initialize(self):
        """Initialize the Cosmos DB client and containers"""
        try:
            self.cosmosdb_client = CosmosClient(self.cosmosdb_endpoint, credential=self.credential)
            self.database_client = self.cosmosdb_client.get_database_client(self.database_name)

            # Initialize the projects container
            self.projects_container = self.database_client.get_container_client("projects")

            # Verify connection by reading database info
            try:
                self.database_client.read()
                print(f"Successfully connected to CosmosDB database {self.database_name}")
            except exceptions.CosmosHttpResponseError as db_error:
                print(f"Failed to connect to CosmosDB database {self.database_name}: {db_error}")
                return False

            return True
        except exceptions.CosmosHttpResponseError as e:
            print(f"Error initializing Cosmos DB client: {e}")
            return False
        except Exception as e:
            print(f"Unexpected error initializing Cosmos DB client: {e}")
            return False

    async def get_project(self, project_id: str):
        """Get a project by ID"""
        try:
            # Try to find the project using a query
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.type = 'project'"
            parameters = [{"name": "@projectId", "value": project_id}]

            projects = []
            items = list(self.projects_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True
            ))

            for item in items:
                projects.append(item)

            if projects:
                return projects[0]

            return None
        except exceptions.CosmosHttpResponseError as e:
            print(f"Error getting project by ID {project_id}: {e}")
            return None

async def main():
    if len(sys.argv) < 2:
        print("Usage: python fetch_project.py <project_id>")
        sys.exit(1)

    project_id = sys.argv[1]

    # Load environment variables from .env file if it exists
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

    # Get CosmosDB connection details from environment variables
    cosmosdb_endpoint = os.environ.get('AZURE_COSMOSDB_ACCOUNT', '')
    if cosmosdb_endpoint and not cosmosdb_endpoint.startswith('https://'):
        cosmosdb_endpoint = f"https://{cosmosdb_endpoint}.documents.azure.com:443/"
    else:
        cosmosdb_endpoint = os.environ.get('COSMOSDB_ENDPOINT')

    cosmosdb_key = os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY') or os.environ.get('COSMOSDB_KEY')
    database_name = os.environ.get('AZURE_COSMOSDB_DATABASE') or os.environ.get('COSMOSDB_DATABASE')

    print(f"Using CosmosDB endpoint: {cosmosdb_endpoint}")
    print(f"Using database name: {database_name}")

    if not cosmosdb_endpoint or not cosmosdb_key or not database_name:
        print("Error: Missing required environment variables.")
        print("Please set AZURE_COSMOSDB_ACCOUNT/COSMOSDB_ENDPOINT, AZURE_COSMOSDB_ACCOUNT_KEY/COSMOSDB_KEY, and AZURE_COSMOSDB_DATABASE/COSMOSDB_DATABASE.")
        sys.exit(1)

    # Initialize the CosmosDB client
    client = CosmosRbacClient(
        cosmosdb_endpoint=cosmosdb_endpoint,
        credential=cosmosdb_key,
        database_name=database_name
    )

    # Initialize the client
    init_success = await client.initialize()
    if not init_success:
        print("Failed to initialize CosmosDB client")
        sys.exit(1)

    # Get the project
    project = await client.get_project(project_id)
    if project:
        print(json.dumps(project, indent=2))
    else:
        print(f"Project with ID {project_id} not found")

if __name__ == "__main__":
    asyncio.run(main())

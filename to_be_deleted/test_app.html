<!DOCTYPE html>
<html>
<head>
    <title>Test App Connection</title>
</head>
<body>
    <h1>Testing Connection to App</h1>
    <div id="result"></div>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const resultDiv = document.getElementById('result');
            
            try {
                // Test basic connectivity
                const response = await fetch('http://localhost:50505/');
                const text = await response.text();
                
                resultDiv.innerHTML += `<p>Basic connectivity: SUCCESS</p>`;
                
                // Test API endpoint
                try {
                    const apiResponse = await fetch('http://localhost:50505/frontend_settings');
                    const apiData = await apiResponse.json();
                    resultDiv.innerHTML += `<p>API connectivity: SUCCESS</p>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(apiData, null, 2)}</pre>`;
                } catch (apiError) {
                    resultDiv.innerHTML += `<p>API connectivity: FAILED - ${apiError.message}</p>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<p>Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>

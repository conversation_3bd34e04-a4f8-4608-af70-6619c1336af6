#!/bin/bash
# Script to test the event_grid.bicep module

# Set variables
PROJECT_ID="050806-test"
PROJECT_NAME="050806"
REGION_ID="westeurope"
LOCATION="westeurope"
RESOURCE_GROUP="rg-internal-ai"
FUNCTION_APP_ID="/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Web/sites/func-050806-test-24cc"
STORAGE_ACCOUNT_ID="/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.Storage/storageAccounts/st050806hq5g"
TEMPLATE_FILE="modules/event_grid.bicep"

# Generate a sanitized name for Azure resources
SANITIZED_NAME=$(echo "$PROJECT_NAME" | tr '[:upper:]' '[:lower:]' | tr -d ' ' | tr -d '[^a-z0-9-]')
UNIQUE_SUFFIX=$(echo "$PROJECT_ID" | md5sum | cut -c1-4)
EVENT_GRID_SYSTEM_TOPIC_NAME="evgt-${SANITIZED_NAME}-${UNIQUE_SUFFIX}"

# Create a parameters file
PARAMS_FILE="event_grid_params.json"
cat > "$PARAMS_FILE" << EOF
{
  "\$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
  "contentVersion": "*******",
  "parameters": {
    "eventGridSystemTopicName": {
      "value": "${EVENT_GRID_SYSTEM_TOPIC_NAME}"
    },
    "location": {
      "value": "${LOCATION}"
    },
    "functionAppResourceId": {
      "value": "${FUNCTION_APP_ID}"
    },
    "storageAccountId": {
      "value": "${STORAGE_ACCOUNT_ID}"
    },
    "tags": {
      "value": {
        "project-id": "${PROJECT_ID}",
        "region-id": "${REGION_ID}",
        "project-name": "${PROJECT_NAME}"
      }
    }
  }
}
EOF

echo "Created parameters file: $PARAMS_FILE"
echo "Event Grid System Topic Name: $EVENT_GRID_SYSTEM_TOPIC_NAME"

# Deploy the event grid system topic
echo "Deploying event grid system topic..."
DEPLOYMENT_NAME="event-grid-test-$(date +%Y%m%d%H%M%S)"
az deployment group create \
  --resource-group "$RESOURCE_GROUP" \
  --name "$DEPLOYMENT_NAME" \
  --template-file "$TEMPLATE_FILE" \
  --parameters "@$PARAMS_FILE" \
  --verbose

# Check the deployment status
if [ $? -eq 0 ]; then
  echo "Deployment successful!"
  
  # Get the outputs
  echo "Retrieving deployment outputs..."
  EVENT_GRID_SYSTEM_TOPIC_NAME=$(az deployment group show \
    --resource-group "$RESOURCE_GROUP" \
    --name "$DEPLOYMENT_NAME" \
    --query "properties.outputs.eventGridSystemTopicName.value" \
    --output tsv)
  
  EVENT_GRID_SYSTEM_TOPIC_ID=$(az deployment group show \
    --resource-group "$RESOURCE_GROUP" \
    --name "$DEPLOYMENT_NAME" \
    --query "properties.outputs.eventGridSystemTopicId.value" \
    --output tsv)
  
  EVENT_SUBSCRIPTION_NAME=$(az deployment group show \
    --resource-group "$RESOURCE_GROUP" \
    --name "$DEPLOYMENT_NAME" \
    --query "properties.outputs.eventSubscriptionName.value" \
    --output tsv)
  
  echo "Event Grid System Topic Name: $EVENT_GRID_SYSTEM_TOPIC_NAME"
  echo "Event Grid System Topic ID: $EVENT_GRID_SYSTEM_TOPIC_ID"
  echo "Event Subscription Name: $EVENT_SUBSCRIPTION_NAME"
  
  # Verify the resources exist
  echo "Verifying resources..."
  az eventgrid system-topic show \
    --name "$EVENT_GRID_SYSTEM_TOPIC_NAME" \
    --resource-group "$RESOURCE_GROUP"
  
  az eventgrid system-topic event-subscription show \
    --name "$EVENT_SUBSCRIPTION_NAME" \
    --resource-group "$RESOURCE_GROUP" \
    --system-topic-name "$EVENT_GRID_SYSTEM_TOPIC_NAME"
else
  echo "Deployment failed!"
fi

# Clean up
echo "Cleaning up..."
rm "$PARAMS_FILE"

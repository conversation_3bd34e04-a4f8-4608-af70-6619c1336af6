#!/usr/bin/env python3
"""
Test script to validate authentication using client credentials flow.
This script uses the client credentials flow to get a token and test the API endpoints.
"""

import os
import sys
import json
import requests
from backend.utils.logging_config import configure_logger
from azure.identity import ClientSecretCredential
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logger = configure_logger("test_client_credentials")

def get_client_credentials_token(client_id, tenant_id, client_secret):
    """Get a token using client credentials flow."""
    try:
        # Create a credential object
        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )

        # Get token for Microsoft Graph API using .default scope
        # Note: For client credentials flow, we use .default scope
        token = credential.get_token("https://graph.microsoft.com/.default")
        return token.token
    except Exception as e:
        logger.error(f"Error getting client credentials token: {e}")
        return None

def test_api_with_token(token, base_url="http://localhost:50508"):
    """
    Test the API endpoints using the provided token.

    Args:
        token: The access token to use for authentication
        base_url: The base URL of the API

    Returns:
        bool: True if all tests pass, False otherwise
    """
    if not token:
        logger.error("No token provided")
        return False

    logger.info(f"Using token: {token[:10]}...{token[-10:]}")

    # Test endpoints
    endpoints = [
        # RBAC endpoints
        {"url": f"{base_url}/api/rbac/regions", "auth_required": True, "name": "Regions"},
        {"url": f"{base_url}/api/rbac/users", "auth_required": True, "name": "Users"},
        {"url": f"{base_url}/api/rbac/projects", "auth_required": True, "name": "Projects"},

        # User info endpoint
        {"url": f"{base_url}/api/rbac/me", "auth_required": True, "name": "Current User"},
    ]

    all_passed = True

    for endpoint in endpoints:
        try:
            logger.info(f"Testing endpoint: {endpoint['name']} ({endpoint['url']})")

            headers = {}
            if endpoint["auth_required"]:
                headers["Authorization"] = f"Bearer {token}"

            response = requests.get(endpoint["url"], headers=headers)

            if response.status_code == 200:
                logger.info(f"Successfully accessed {endpoint['name']}")
                try:
                    response_data = response.json()
                    logger.info(f"Response: {json.dumps(response_data, indent=2)[:200]}...")
                except:
                    logger.info(f"Response: {response.text[:200]}...")
            else:
                logger.error(f"Failed to access {endpoint['name']}: {response.status_code}")
                logger.error(f"Response: {response.text}")
                if endpoint["auth_required"] and endpoint["name"] != "Current User":  # We expect /me to fail
                    all_passed = False
        except Exception as e:
            logger.error(f"Error testing endpoint {endpoint['name']}: {e}")
            if endpoint["auth_required"] and endpoint["name"] != "Current User":  # We expect /me to fail
                all_passed = False

    return all_passed

def main():
    """
    Main function to run the test.
    """
    # Get credentials from command line arguments or environment variables
    client_id = sys.argv[1] if len(sys.argv) > 1 else os.environ.get("AZURE_CLIENT_ID")
    tenant_id = sys.argv[2] if len(sys.argv) > 2 else os.environ.get("AZURE_TENANT_ID")
    client_secret = sys.argv[3] if len(sys.argv) > 3 else os.environ.get("AZURE_CLIENT_SECRET") or os.environ.get("AZURE_APP_SECRET")
    base_url = sys.argv[4] if len(sys.argv) > 4 else "http://localhost:50508"

    # Check if we have all required credentials
    if not all([client_id, tenant_id, client_secret]):
        logger.error("Missing required credentials. Please provide client_id, tenant_id, and client_secret.")
        logger.error("Usage: python test_client_credentials.py <client_id> <tenant_id> <client_secret> [base_url]")
        return 1

    # Get a token using client credentials flow
    logger.info("Getting token using client credentials flow...")
    token = get_client_credentials_token(client_id, tenant_id, client_secret)

    if not token:
        logger.error("Failed to get token. Check your credentials and try again.")
        return 1

    logger.info("Successfully obtained token.")

    # Test the API endpoints
    success = test_api_with_token(token, base_url)

    if success:
        logger.info("API test completed successfully!")
        return 0
    else:
        logger.error("API test failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())

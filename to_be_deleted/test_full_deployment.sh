#!/bin/bash
# Script to test the full deployment process

# Set variables
PROJECT_ID="050806-test"
PROJECT_NAME="050806"
REGION_ID="westeurope"
LOCATION="westeurope"

echo "Starting full deployment test for project $PROJECT_ID ($PROJECT_NAME)"
echo "Region ID: $REGION_ID"
echo "Location: $LOCATION"

# Run the deployment script
echo "Running deployment script..."
python deploy_project_resources.py "$PROJECT_ID" "$PROJECT_NAME" --region-id "$REGION_ID" --location "$LOCATION"

# Check the result
if [ $? -eq 0 ]; then
  echo "Deployment script executed successfully!"
else
  echo "Deployment script failed!"
  exit 1
fi

# Verify the resources
echo "Verifying resources..."

# Get the storage account name
STORAGE_ACCOUNT_NAME=$(az storage account list --resource-group rg-internal-ai --query "[?tags.\"project-id\" == '$PROJECT_ID'].name" --output tsv)
echo "Storage Account Name: $STORAGE_ACCOUNT_NAME"

# Get the function app name
FUNCTION_APP_NAME=$(az functionapp list --resource-group rg-internal-ai --query "[?tags.\"project-id\" == '$PROJECT_ID'].name" --output tsv)
echo "Function App Name: $FUNCTION_APP_NAME"

# Get the event grid system topic name
EVENT_GRID_SYSTEM_TOPIC_NAME=$(az eventgrid system-topic list --resource-group rg-internal-ai --query "[?tags.\"project-id\" == '$PROJECT_ID'].name" --output tsv)
echo "Event Grid System Topic Name: $EVENT_GRID_SYSTEM_TOPIC_NAME"

# Check if the event grid system topic exists
if [ -z "$EVENT_GRID_SYSTEM_TOPIC_NAME" ]; then
  echo "ERROR: Event Grid System Topic not found!"
  exit 1
else
  echo "Event Grid System Topic exists: $EVENT_GRID_SYSTEM_TOPIC_NAME"

  # Get the event subscription
  EVENT_SUBSCRIPTION_NAME=$(az eventgrid system-topic event-subscription list --resource-group rg-internal-ai --system-topic-name "$EVENT_GRID_SYSTEM_TOPIC_NAME" --query "[].name" --output tsv)
  echo "Event Subscription Name: $EVENT_SUBSCRIPTION_NAME"

  # Check if the event subscription exists
  if [ -z "$EVENT_SUBSCRIPTION_NAME" ]; then
    echo "ERROR: Event Subscription not found!"
    exit 1
  else
    echo "Event Subscription exists: $EVENT_SUBSCRIPTION_NAME"

    # Get the event subscription details
    echo "Event Subscription Details:"
    az eventgrid system-topic event-subscription show --resource-group rg-internal-ai --system-topic-name "$EVENT_GRID_SYSTEM_TOPIC_NAME" --name "$EVENT_SUBSCRIPTION_NAME"
  fi
fi

echo "Verification complete!"

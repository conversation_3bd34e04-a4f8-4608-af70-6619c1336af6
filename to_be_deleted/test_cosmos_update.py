#!/usr/bin/env python3
"""
Test script for direct Cosmos DB updates.
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timezone
from direct_cosmos_update import update_project_resources_direct

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_update_project(project_id):
    """Test updating a project directly in Cosmos DB."""
    # Create test resource data
    resource_data = {
        "function_app_name": f"func-test-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "storage_account_name": f"sttest{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "search_service_name": f"srch-test-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "test_timestamp": datetime.now(timezone.utc).isoformat(),
    }

    # Update the project
    logger.info(f"Updating project {project_id} with test resource data")
    result = update_project_resources_direct(project_id, resource_data)

    if result:
        logger.info(f"Successfully updated project {project_id}")
    else:
        logger.error(f"Failed to update project {project_id}")

    return result


if __name__ == "__main__":
    import sys

    if len(sys.argv) < 2:
        print("Usage: python test_cosmos_update.py <project_id>")
        sys.exit(1)

    project_id = sys.argv[1]

    # Run the test
    asyncio.run(test_update_project(project_id))

#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to check RBAC data in Cosmos DB.
"""

import asyncio
import logging
from azure.cosmos.aio import CosmosClient
from backend.settings import app_settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

async def check_rbac_data():
    """Check RBAC data in Cosmos DB."""
    logging.info("Checking RBAC data in Cosmos DB...")

    # Check if app_settings.chat_history is configured
    if not app_settings.chat_history:
        logging.error("CosmosDB settings not configured. RBAC requires CosmosDB.")
        return False

    # Construct the CosmosDB endpoint
    cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
    logging.info(f"CosmosDB endpoint: {cosmos_endpoint}")

    # Create a new Cosmos DB client
    try:
        async with CosmosClient(cosmos_endpoint, credential=app_settings.chat_history.account_key) as client:
            # Get the database
            database_name = app_settings.chat_history.database
            database = client.get_database_client(database_name)
            
            # Check users container
            users_container = database.get_container_client("users")
            users_query = "SELECT * FROM c WHERE c.type = 'user'"
            users = []
            async for item in users_container.query_items(query=users_query):
                users.append(item)
            
            logging.info(f"Found {len(users)} users:")
            for user in users:
                logging.info(f"- User ID: {user.get('id')}, Name: {user.get('name')}, Role: {user.get('role')}")
            
            # Check regions container
            regions_container = database.get_container_client("regions")
            regions_query = "SELECT * FROM c WHERE c.type = 'region'"
            regions = []
            async for item in regions_container.query_items(query=regions_query):
                regions.append(item)
            
            logging.info(f"Found {len(regions)} regions:")
            for region in regions:
                logging.info(f"- Region ID: {region.get('id')}, Name: {region.get('name')}")
            
            # Check projects container
            projects_container = database.get_container_client("projects")
            projects_query = "SELECT * FROM c WHERE c.type = 'project'"
            projects = []
            async for item in projects_container.query_items(query=projects_query):
                projects.append(item)
            
            logging.info(f"Found {len(projects)} projects:")
            for project in projects:
                logging.info(f"- Project ID: {project.get('id')}, Name: {project.get('name')}")
            
            return True
    except Exception as e:
        logging.error(f"Error checking RBAC data: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(check_rbac_data())

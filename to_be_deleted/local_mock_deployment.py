#!/usr/bin/env python3
"""
Script to simulate a successful deployment locally without actually deploying Azure resources.
This is useful for testing the deployment status update functionality.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import the DeploymentSummary class if available, otherwise mock it
try:
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from backend.deployments.deployment_status import DeploymentSummary, update_project_deployment_status
    DEPLOYMENT_SUMMARY_AVAILABLE = True
except ImportError:
    logger.warning("Could not import DeploymentSummary class, using mock implementation")
    DEPLOYMENT_SUMMARY_AVAILABLE = False

    class DeploymentSummary:
        """Mock implementation of DeploymentSummary class"""
        def __init__(self, project_id: str, api_url: Optional[str] = None):
            self.project_id = project_id
            self.api_url = api_url
            self.summary_file = f"deployment_summary_{project_id}.json"
            self.status = "pending"
            self.resources = {}
            self.errors = []
            self.timestamp = datetime.now(timezone.utc).isoformat()
            self.summary = None

        def save_summary(self) -> None:
            """Save the deployment summary to the summary file."""
            try:
                summary = {
                    "status": self.status,
                    "resources": self.resources,
                    "errors": self.errors,
                    "timestamp": self.timestamp
                }
                self.summary = summary

                with open(self.summary_file, 'w') as f:
                    json.dump(summary, f, indent=2)

                logger.info(f"Deployment summary saved to {self.summary_file}")
            except Exception as e:
                logger.error(f"Error saving deployment summary: {str(e)}")

        def load_summary(self) -> Dict[str, Any]:
            """Load the deployment summary from the summary file."""
            if os.path.exists(self.summary_file):
                try:
                    with open(self.summary_file, 'r') as f:
                        summary = json.load(f)
                        self.status = summary.get("status", self.status)
                        self.resources = summary.get("resources", self.resources)
                        self.errors = summary.get("errors", self.errors)
                        self.timestamp = summary.get("timestamp", self.timestamp)
                        self.summary = summary
                        return summary
                except Exception as e:
                    logger.error(f"Error loading deployment summary: {str(e)}")
                    summary = {
                        "status": self.status,
                        "resources": self.resources,
                        "errors": self.errors,
                        "timestamp": self.timestamp
                    }
                    self.summary = summary
                    return summary
            else:
                summary = {
                    "status": self.status,
                    "resources": self.resources,
                    "errors": self.errors,
                    "timestamp": self.timestamp
                }
                self.summary = summary
                return summary

        def update_status(self, status: str, message: Optional[str] = None) -> None:
            """Update the deployment status."""
            self.status = status
            self.save_summary()
            logger.info(f"Updated deployment status to '{status}' for project {self.project_id}")

            # Simulate API call
            if self.api_url:
                logger.info(f"Sending status update to {self.api_url}/api/projects/{self.project_id}/deployment-status")

    async def update_project_deployment_status(
        project_id: str,
        status: str,
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        api_url: Optional[str] = None
    ) -> None:
        """Mock implementation of update_project_deployment_status function"""
        logger.info(f"Updating deployment status for project {project_id} to {status}")
        if api_url:
            logger.info(f"Would send status update to {api_url}/api/projects/{project_id}/deployment-status")

async def simulate_deployment(project_id: str, project_name: str, api_url: Optional[str] = None) -> None:
    """Simulate a deployment process with status updates."""
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    # Create a DeploymentSummary instance
    deployment_summary = DeploymentSummary(project_id, api_url)
    logger.info(f"Created DeploymentSummary instance for project {project_id}")

    # Save initial summary
    deployment_summary.save_summary()
    logger.info(f"Deployment summary saved to {deployment_summary.summary_file}")

    # Update status to in_progress
    deployment_summary.update_status("in_progress")
    logger.info(f"Updated deployment status to 'in_progress' for project {project_id}")

    # Simulate deployment steps
    logger.info(f"Simulating deployment for project {project_id} ({project_name})...")

    # Step 1: Storage account
    logger.info("Step 1: Simulating storage account deployment...")
    time.sleep(2)  # Simulate work

    # Update resources
    deployment_summary.resources["storage_account_name"] = f"st{project_name.lower()}{project_id[:4]}"
    deployment_summary.resources["uploads_container"] = f"uploads-{project_name.lower()}-{project_id[:4]}"
    deployment_summary.resources["input_container"] = f"input-{project_name.lower()}-{project_id[:4]}"
    deployment_summary.resources["output_container"] = f"output-{project_name.lower()}-{project_id[:4]}"
    deployment_summary.save_summary()

    # Step 2: Search service
    logger.info("Step 2: Simulating search service deployment...")
    time.sleep(2)  # Simulate work

    # Update resources
    deployment_summary.resources["search_service_name"] = f"search-{project_name.lower()}-{project_id[:4]}"
    deployment_summary.resources["search_index_name"] = f"project-{project_name.lower()}-index"
    deployment_summary.resources["search_indexer_name"] = f"project-{project_name.lower()}-indexer"
    deployment_summary.resources["search_datasource_name"] = f"project-{project_name.lower()}-ds"
    deployment_summary.save_summary()

    # Step 3: Function app
    logger.info("Step 3: Simulating function app deployment...")
    time.sleep(2)  # Simulate work

    # Update resources
    deployment_summary.resources["function_app_name"] = f"func-{project_id}-{project_id[:4]}"
    deployment_summary.resources["event_grid_system_topic_name"] = f"evgt-{project_name.lower()}-{project_id[:4]}"
    deployment_summary.save_summary()

    # Generate a detailed deployment summary
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    summary_file = f"logs/deployment_summary_{timestamp}_{project_id}.json"

    # Create a detailed summary
    details = {
        "storage": {
            "storage_account": True,
            "containers": {
                "uploads": True,
                "input": True,
                "output": True
            }
        },
        "storage_complete": True,
        "search": {
            "search_service": True,
            "index": True,
            "indexer": True,
            "datasource": True
        },
        "search_complete": True,
        "function": {
            "function_app": True,
            "event_grid_topic": True,
            "event_grid_system_topic": True,
            "event_grid": True,
            "maturity_assessment": True,
            "executive_summary": True
        },
        "function_complete": True,
        "overall_complete": True,
        "completion_percentage": 100
    }

    # Update status to completed
    deployment_summary.update_status("completed")
    logger.info(f"Updated deployment status to 'completed' for project {project_id}")

    # Update deployment status in CosmosDB
    if DEPLOYMENT_SUMMARY_AVAILABLE:
        await update_project_deployment_status(
            project_id=project_id,
            status="completed",
            message="Deployment completed successfully",
            details=details,
            api_url=api_url
        )

    logger.info(f"Deployment simulation completed for project {project_id}")

def main():
    parser = argparse.ArgumentParser(description="Simulate a deployment process with status updates")
    parser.add_argument("project_id", help="Project ID (UUID)")
    parser.add_argument("project_name", help="Project name")
    parser.add_argument("--api-url", help="API URL for status updates", default="http://localhost:50505")

    args = parser.parse_args()

    # Run the simulation
    asyncio.run(simulate_deployment(args.project_id, args.project_name, args.api_url))

if __name__ == "__main__":
    main()

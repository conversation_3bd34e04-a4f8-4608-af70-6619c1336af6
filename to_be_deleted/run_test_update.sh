#!/bin/bash

# Project details from the deployment summary
PROJECT_ID="29edc487-1c86-44aa-b183-c26c84eab470"
REGION_ID="25aed46f-1196-8502-3d250-251252253254255256"
PROJECT_NAME="testvansh0705"

# Load environment variables from .env file
if [ -f .env ]; then
    echo "Loading environment variables from .env file"
    export $(grep -v '^#' .env | xargs)
else
    echo ".env file not found"
    exit 1
fi

# Set the required environment variables explicitly
export AZURE_COSMOSDB_ACCOUNT="internal-ai-conversation-history-db"
export AZURE_COSMOSDB_DATABASE="db_conversation_history"
# Note: We're not printing the actual key for security reasons
echo "AZURE_COSMOSDB_ACCOUNT set to: $AZURE_COSMOSDB_ACCOUNT"
echo "AZURE_COSMOSDB_DATABASE set to: $AZURE_COSMOSDB_DATABASE"
echo "AZURE_COSMOSDB_ACCOUNT_KEY is set (value hidden)"

# Make the script executable
chmod +x test_update_project.py

# Run the test script
echo "Running test update for project $PROJECT_ID in region $REGION_ID with name $PROJECT_NAME"
python test_update_project.py "$PROJECT_ID" "$REGION_ID" "$PROJECT_NAME"

# Check the result
if [ $? -eq 0 ]; then
    echo "Test completed successfully!"
else
    echo "Test failed!"
    exit 1
fi

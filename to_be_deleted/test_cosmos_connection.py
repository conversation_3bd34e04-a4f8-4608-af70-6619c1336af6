#!/usr/bin/env python3
"""
Test script to validate Azure Cosmos DB connection using environment variables.
"""

import os
import sys
import logging
import asyncio
from azure.cosmos import CosmosClient, exceptions
from azure.identity import DefaultAzureCredential
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_cosmos_connection():
    """Test connection to Azure Cosmos DB using environment variables."""
    # Load environment variables
    load_dotenv()
    
    # Get Cosmos DB settings from environment variables
    cosmos_account = os.getenv("AZURE_COSMOSDB_ACCOUNT")
    cosmos_key = os.getenv("AZURE_COSMOSDB_ACCOUNT_KEY")
    cosmos_database = os.getenv("AZURE_COSMOSDB_DATABASE")
    cosmos_container = os.getenv("AZURE_COSMOSDB_PROJECTS_CONTAINER")
    
    # Check if required environment variables are set
    if not cosmos_account:
        logger.error("AZURE_COSMOSDB_ACCOUNT environment variable not set")
        return False
    
    if not cosmos_database:
        logger.error("AZURE_COSMOSDB_DATABASE environment variable not set")
        return False
    
    if not cosmos_container:
        logger.error("AZURE_COSMOSDB_PROJECTS_CONTAINER environment variable not set")
        return False
    
    # Construct the Cosmos DB endpoint
    cosmos_endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
    logger.info(f"Connecting to Cosmos DB endpoint: {cosmos_endpoint}")
    
    try:
        # Determine authentication method
        if cosmos_key:
            logger.info("Using account key authentication")
            credential = cosmos_key
        else:
            logger.info("Using Azure identity authentication")
            credential = DefaultAzureCredential()
        
        # Create the Cosmos DB client
        client = CosmosClient(cosmos_endpoint, credential=credential)
        
        # Get database client
        database_client = client.get_database_client(cosmos_database)
        
        # Test database connection
        try:
            database_properties = database_client.read()
            logger.info(f"Successfully connected to database: {database_properties['id']}")
        except exceptions.CosmosHttpResponseError as e:
            logger.error(f"Failed to connect to database: {e}")
            return False
        
        # Get container client
        container_client = database_client.get_container_client(cosmos_container)
        
        # Test container connection
        try:
            container_properties = container_client.read()
            logger.info(f"Successfully connected to container: {container_properties['id']}")
        except exceptions.CosmosHttpResponseError as e:
            logger.error(f"Failed to connect to container: {e}")
            return False
        
        # Test query (list first 5 items)
        try:
            logger.info("Testing query...")
            items = list(container_client.query_items(
                query="SELECT TOP 5 * FROM c",
                enable_cross_partition_query=True
            ))
            logger.info(f"Query successful, found {len(items)} items")
            
            # Print item IDs
            for item in items:
                logger.info(f"Item ID: {item.get('id')}")
            
        except exceptions.CosmosHttpResponseError as e:
            logger.error(f"Failed to query container: {e}")
            return False
        
        logger.info("All Cosmos DB connection tests passed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_cosmos_connection())
    sys.exit(0 if result else 1)

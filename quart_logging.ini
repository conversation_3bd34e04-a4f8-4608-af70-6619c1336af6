[loggers]
keys=root,quart,hypercorn

[handlers]
keys=console

[formatters]
keys=generic

[logger_root]
level=INFO
handlers=console
qualname=

[logger_quart]
level=WARNING
handlers=console
qualname=quart
propagate=0

[logger_hypercorn]
level=WARNING
handlers=console
qualname=hypercorn
propagate=0

[handler_console]
class=StreamHandler
formatter=generic
args=(sys.stdout,)

[formatter_generic]
format=%(asctime)s [%(process)d] [%(levelname)s] %(message)s
datefmt=%Y-%m-%d %H:%M:%S %z

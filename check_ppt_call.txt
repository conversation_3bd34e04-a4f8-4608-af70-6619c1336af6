import React, { useState, useEffect, useCallback, useRef, useContext, ChangeEvent } from 'react'
import {
  Trash2,
  Download,
  File,
  ChevronRight,
  Upload,
  Beaker,
  FileText,
  ChevronDown,
  RefreshCw,
  FileSpreadsheet,
  FileEdit,
  BarChart2,
  Presentation
} from 'lucide-react'
import { PriorityPlotButton, PriorityPlotModal } from '../PriorityPlot'
import { BlobServiceClient } from '@azure/storage-blob'
// Removed dropzone import as we're using direct file inputs
import './FileManagement.css'
import axios, { AxiosError } from 'axios'
import {
  useWebSocketUpdates,
  BlobUpdatesData,
  // RightPanelUpdatesData, // This type seems unused, commenting out
  // isInputContainerData, // This type seems unused, commenting out
  // isOutputContainerData, // This type seems unused, commenting out
  IndexUpdatesData
} from '../../hooks/websockets'
import { ProjectContext } from '../../pages/layout/ProjectLayout'; // Ensure ProjectContext is imported
import ValidationErrorModal from './ValidationErrorModal';

interface RightFileManagementProps {
  onToggle?: (isOpen: boolean) => void
}

// List of protected files that cannot be deleted
const PROTECTED_FILES = [
  'AI_Readiness_Assessment_Methodology2_0.xlsx',
  'Maturity Model Assessment Methodology2_0.xlsx',
  'business_data_discovery1_2.docx'
]

interface BlobFile {
  name: string
  size: number
  type: string
  url: string // Base URL without SAS
  lastModified: Date
}

// Interface for the global storage config (account name, SAS token)
interface GlobalAzureStorageConfig {
  account_name: string
  container_sas_token: string
  // Removed container names as they are now project-specific
}

// Helper function to format SAS token
const formatSasToken = (sasToken: string): string => {
  if (!sasToken) return ''
  const trimmedToken = sasToken.trim()
  const cleanToken = trimmedToken.startsWith('?') ? trimmedToken.substring(1) : trimmedToken
  return `?${cleanToken}`
}

// Helper function to get the appropriate file icon
const getFileIcon = (fileName: string) => {
  if (fileName.toLowerCase().endsWith('.xlsx')) {
    return <FileSpreadsheet className="file-icon excel" />
  } else if (fileName.toLowerCase().endsWith('.docx')) {
    return <FileEdit className="file-icon word" />
  }
  return <FileText className="file-icon" />
}

const RightFileManagement: React.FC<RightFileManagementProps> = ({ onToggle }): JSX.Element => {
  const [files, setFiles] = useState<BlobFile[]>([]) // Input container files
  const [outputFiles, setOutputFiles] = useState<BlobFile[]>([]) // Output container files
  const [error, setError] = useState<string | null>(null)
  const [isOpen, setIsOpen] = useState(true)
  const [uploading, setUploading] = useState(false)
  const [isMaturityLoading, setIsMaturityLoading] = useState(false)
  const [isExecSummaryLoading, setIsExecSummaryLoading] = useState(false)
  const [isPowerPointLoading, setIsPowerPointLoading] = useState(false)
  // const [isDragging, setIsDragging] = useState(false); // isDragging state seems unused
  const [globalStorageConfig, setGlobalStorageConfig] = useState<GlobalAzureStorageConfig>({ // Renamed state
    account_name: '',
    container_sas_token: ''
  })
  const [isInitialized, setIsInitialized] = useState(false)
  const wrapperRef = useRef<HTMLDivElement>(null)
  const [showMaturityDropdown, setShowMaturityDropdown] = useState(false)
  const [selectedMaturityFile, setSelectedMaturityFile] = useState<string | null>(null)
  const [showExecSummaryDropdown, setShowExecSummaryDropdown] = useState(false)
  const [selectedExecSummaryFile, setSelectedExecSummaryFile] = useState<string | null>(null)
  const [showPowerPointDropdown, setShowPowerPointDropdown] = useState(false)
  const [selectedPowerPointFile, setSelectedPowerPointFile] = useState<string | null>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const execSummaryDropdownRef = useRef<HTMLDivElement>(null)
  const powerPointDropdownRef = useRef<HTMLDivElement>(null)
  const [isRefreshingToken, setIsRefreshingToken] = useState(false)
  const [indexedFiles, setIndexedFiles] = useState<string[]>([]) // Still needed to check if templates are indexed for button states
  const [progressPercentage, setProgressPercentage] = useState(0)
  const [execSummaryProgressPercentage, setExecSummaryProgressPercentage] = useState(0)
  const [powerPointProgressPercentage, setPowerPointProgressPercentage] = useState(0)
  const projectContext = useContext(ProjectContext); // Get project context
  const projectId = projectContext?.projectId; // Extract projectId
  const [isPriorityPlotModalOpen, setIsPriorityPlotModalOpen] = useState(false)
  const [selectedPriorityPlotFile, setSelectedPriorityPlotFile] = useState<BlobFile | null>(null)
  const [isValidationErrorModalOpen, setIsValidationErrorModalOpen] = useState(false)
  const [validationErrorMessage, setValidationErrorMessage] = useState('')

  // File input references for each section
  const excelFileInputRef = useRef<HTMLInputElement>(null)
  const wordFileInputRef = useRef<HTMLInputElement>(null)
  const pptxFileInputRef = useRef<HTMLInputElement>(null)

  // WebSocket hooks for input and output containers (assuming server sends project-specific data)
  const {
    data: inputContainerData,
    isConnected: isInputContainerConnected,
    error: inputContainerError,
    lastUpdate: inputContainerLastUpdate,
    wsRef: inputWsRef // Get the WebSocket reference so we can send commands
  } = useWebSocketUpdates<BlobUpdatesData>({
    type: 'input-container', // Server needs to route project-specific data here
    projectId: projectId // Pass projectId
  })

  const {
    data: outputContainerData,
    isConnected: isOutputContainerConnected,
    error: outputContainerError,
    lastUpdate: outputContainerLastUpdate,
    wsRef: outputWsRef // Get the WebSocket reference so we can send commands
  } = useWebSocketUpdates<BlobUpdatesData>({
    type: 'output-container', // Server needs to route project-specific data here
    projectId: projectId // Pass projectId
  })

  // Use WebSocket for indexed files updates (assuming server sends project-specific index data)
  const {
    data: indexData,
    isConnected: isIndexWsConnected,
    error: indexWsError,
    lastUpdate: indexLastUpdate,
    wsRef: indexWsRef // Get the WebSocket reference so we can send commands
  } = useWebSocketUpdates<IndexUpdatesData>({
    type: 'index', // Server needs to route project-specific data here
    projectId: projectId // Pass projectId
  })

  // Track if WebSockets are connected
  const isWsConnected = isInputContainerConnected || isOutputContainerConnected

  useEffect(() => {
    onToggle?.(isOpen)
  }, [isOpen, onToggle])

  // Initialize configuration using project-specific storage account and SAS token
  useEffect(() => {
    const initializeStorageConfig = async () => {
      try {
        // Use project-specific storage account and SAS token from ProjectContext
        if (projectContext && projectContext.storageAccountName && projectContext.storageAccountSasToken) {
          console.log('Using project-specific storage configuration');

          // Create a storage config object using project-specific values
          setGlobalStorageConfig({
            account_name: projectContext.storageAccountName,
            container_sas_token: projectContext.storageAccountSasToken
          });

          console.log('RightFileManagement: Project-specific storage config initialized.', {
            account_name: projectContext.storageAccountName,
            hasSasToken: Boolean(projectContext.storageAccountSasToken)
          });

          setIsInitialized(true);
          setError(null);
        } else {
          console.warn('Project-specific storage configuration not available, falling back to global config');

          // Fall back to global config if project-specific values are not available
          const response = await axios.get('/frontend_settings');
          const config = response.data;

          if (config.azure_storage) {
            setGlobalStorageConfig({
              account_name: config.azure_storage.account_name || '',
              container_sas_token: config.azure_storage.container_sas_token || ''
            });
            setIsInitialized(true);
            console.log("RightFileManagement: Global storage config initialized as fallback.", {
              account_name: config.azure_storage.account_name,
              hasSasToken: Boolean(config.azure_storage.container_sas_token)
            });
          } else {
            setError('Storage configuration not found');
            console.error("RightFileManagement: Storage config missing in both project context and global settings.");
            setIsInitialized(true); // Still set initialized to true to prevent infinite loading
          }
        }
      } catch (err) {
        console.error('Error initializing storage configuration:', err);
        setError('Failed to initialize storage configuration');
        setIsInitialized(true); // Still set initialized to true to prevent infinite loading
      }
    };

    // Only initialize when ProjectContext is available
    if (projectContext) {
      initializeStorageConfig();
    } else {
      // Set a timeout to prevent infinite loading state
      const timer = setTimeout(() => {
        if (!isInitialized) {
          console.warn('No project context available after timeout, initializing anyway');
          setIsInitialized(true);
          setError('Project configuration not available. Some features may be limited.');
        }
      }, 3000); // 3 seconds timeout

      return () => clearTimeout(timer);
    }
  }, [projectContext, isInitialized])

  // Helper function to create a BlobServiceClient with proper error handling
  const createBlobServiceClient = async (storageAccountName: string, sasToken: string): Promise<BlobServiceClient> => {
    console.log(`Creating BlobServiceClient with storage account: ${storageAccountName}`);

    // Log SAS token details for debugging (without revealing the full token)
    const sasTokenLength = sasToken.length;
    console.log(`SAS token length: ${sasTokenLength} characters`);
    console.log(`SAS token starts with: ${sasToken.substring(0, 10)}...`);
    console.log(`SAS token ends with: ...${sasToken.substring(sasTokenLength - 10)}`);
    console.log(`SAS token contains '?' character: ${sasToken.includes('?')}`);
    console.log(`SAS token starts with '?': ${sasToken.startsWith('?')}`);

    // Ensure SAS token starts with '?'
    const formattedSasToken = sasToken.startsWith('?') ? sasToken : `?${sasToken}`;

    // Log the URL format (without the actual SAS token)
    console.log(`Using URL format: https://${storageAccountName}.blob.core.windows.net${formattedSasToken.substring(0, 5)}...`);

    try {
      const blobServiceClient = new BlobServiceClient(
        `https://${storageAccountName}.blob.core.windows.net${formattedSasToken}`
      );

      console.log(`BlobServiceClient created successfully for account: ${storageAccountName}`);
      return blobServiceClient;
    } catch (error) {
      console.error(`Error creating BlobServiceClient: ${error}`);
      throw error;
    }
  };

  // Fetch files when config and project context are ready, or when WebSocket isn't providing data
  const fetchFiles = useCallback(async () => {
    if (!isInitialized || !projectContext?.storageContainerInput || !projectContext?.storageContainerOutput || !projectId) {
      console.warn("Skipping fetchFiles: Not initialized, project context incomplete, or project ID missing.");
      return;
    }

    // Only skip HTTP fetch if WebSockets are connected AND we have received data
    const hasInputData = inputContainerData && Array.isArray(inputContainerData) && inputContainerData.length > 0;
    const hasOutputData = outputContainerData && Array.isArray(outputContainerData) && outputContainerData.length > 0;

    if (isWsConnected && hasInputData && hasOutputData) {
        console.log("WebSockets connected and providing data, skipping HTTP fetch for files.");
        return;
    }

    console.log("Fetching files via HTTP because WebSockets are not connected or not providing data...");
    setError(null); // Clear previous errors

    try {
      // Use project-specific storage account name and SAS token from ProjectContext
      const storageAccountName = projectContext.storageAccountName;
      const sasToken = projectContext.storageAccountSasToken;

      if (!storageAccountName || !sasToken) {
        throw new Error("Missing storage account name or SAS token in project context");
      }

      console.log(`Connecting directly to storage account: ${storageAccountName}`);

      // Get the BlobServiceClient
      const blobServiceClient = await createBlobServiceClient(storageAccountName, sasToken);

      // Fetch input container files directly from Azure Storage
      const inputContainerName = projectContext.storageContainerInput;
      if (!inputContainerName) {
        throw new Error("Missing input container name in project context");
      }

      console.log(`Fetching files directly from INPUT container: ${inputContainerName}`);
      try {
        const inputContainerClient = blobServiceClient.getContainerClient(inputContainerName);
        console.log(`Successfully created container client for ${inputContainerName}`);

        // List blobs in the input container
        const inputBlobs = [];
        try {
          console.log(`Listing blobs in container: ${inputContainerName}`);
          for await (const blob of inputContainerClient.listBlobsFlat()) {
            inputBlobs.push(blob);
          }
          console.log(`Successfully listed ${inputBlobs.length} blobs in ${inputContainerName}`);
        } catch (listError) {
          console.error(`Error listing blobs in ${inputContainerName}: ${listError}`);
          throw listError;
        }

        // Transform the data to match our expected format
        const fetchedInputFiles: BlobFile[] = inputBlobs.map((blob) => ({
          name: blob.name,
          size: blob.properties.contentLength || 0,
          type: blob.properties.contentType || 'unknown',
          url: `https://${storageAccountName}.blob.core.windows.net/${inputContainerName}/${blob.name}`,
          lastModified: blob.properties.lastModified || new Date()
        }));

        setFiles(fetchedInputFiles);
        console.log(`Fetched ${fetchedInputFiles.length} input files directly from Azure Storage.`);
      } catch (inputError) {
        console.error(`Error accessing input container: ${inputError}`);
        // Continue to try output container even if input fails
      }

      // Fetch output container files directly from Azure Storage
      const outputContainerName = projectContext.storageContainerOutput;
      if (!outputContainerName) {
        throw new Error("Missing output container name in project context");
      }

      console.log(`Fetching files directly from OUTPUT container: ${outputContainerName}`);
      try {
        const outputContainerClient = blobServiceClient.getContainerClient(outputContainerName);
        console.log(`Successfully created container client for ${outputContainerName}`);

        // List blobs in the output container
        const outputBlobs = [];
        try {
          console.log(`Listing blobs in container: ${outputContainerName}`);
          for await (const blob of outputContainerClient.listBlobsFlat()) {
            outputBlobs.push(blob);
          }
          console.log(`Successfully listed ${outputBlobs.length} blobs in ${outputContainerName}`);
        } catch (listError) {
          console.error(`Error listing blobs in ${outputContainerName}: ${listError}`);
          throw listError;
        }

        // Transform the data to match our expected format
        const fetchedOutputFiles: BlobFile[] = outputBlobs.map((blob) => ({
          name: blob.name,
          size: blob.properties.contentLength || 0,
          type: blob.properties.contentType || 'unknown',
          url: `https://${storageAccountName}.blob.core.windows.net/${outputContainerName}/${blob.name}`,
          lastModified: blob.properties.lastModified || new Date()
        }));

        const sortedOutputFiles = fetchedOutputFiles.sort((a, b) =>
          (b.lastModified?.getTime() || 0) - (a.lastModified?.getTime() || 0)
        );

        setOutputFiles(sortedOutputFiles);
        console.log(`Fetched ${sortedOutputFiles.length} output files directly from Azure Storage.`);
      } catch (outputError) {
        console.error(`Error accessing output container: ${outputError}`);
      }

    } catch (err) {
      console.error('Error fetching files via HTTP:', err)
      setError('Error fetching files')
    }
  }, [isInitialized, projectId, projectContext?.storageAccountName, projectContext?.storageContainerInput, projectContext?.storageContainerOutput, isWsConnected, inputContainerData, outputContainerData]) // Depend on project ID, project context containers and WebSocket data

  // Initial fetch and periodic refresh
  useEffect(() => {
    // Initial fetch
    fetchFiles();

    // Set up periodic refresh every 10 seconds
    const refreshInterval = setInterval(() => {
      console.log('Periodic refresh of file list...');
      fetchFiles();
    }, 10000);

    // Clean up interval on unmount
    return () => clearInterval(refreshInterval);
  }, [fetchFiles]); // fetchFiles has dependencies including WebSocket data

  // Generic file upload handler
  const uploadFile = useCallback(
    async (file: File) => {
      if (!projectId) {
        setError("Project ID is missing. Cannot upload template files.");
        console.error("Template upload error: Missing projectId in context");
        return;
      }

      setUploading(true);
      setError(null);
      console.log(`Uploading template file "${file.name}" for project ${projectId}...`);

      try {
        // Use direct Azure Storage upload instead of backend API
        if (!projectContext.storageAccountName || !projectContext.storageAccountSasToken) {
          throw new Error("Missing storage account name or SAS token in project context");
        }

        const storageAccountName = projectContext.storageAccountName;
        const sasToken = projectContext.storageAccountSasToken;
        const containerName = projectContext.storageContainerInput;

        if (!containerName) {
          throw new Error("Missing input container name in project context");
        }

        console.log(`Uploading directly to Azure Storage account: ${storageAccountName}, container: ${containerName}`);

        // Create a BlobServiceClient with proper SAS token formatting
        console.log(`Creating BlobServiceClient for upload with storage account: ${storageAccountName}`);
        console.log(`Upload SAS token starts with: ${sasToken.substring(0, 10)}...`);

        // Ensure SAS token starts with '?'
        const formattedSasToken = sasToken.startsWith('?') ? sasToken : `?${sasToken}`;

        const blobServiceClient = new BlobServiceClient(
          `https://${storageAccountName}.blob.core.windows.net${formattedSasToken}`
        );

        console.log(`BlobServiceClient for upload created successfully for account: ${storageAccountName}`);

        // Get a reference to the container
        const containerClient = blobServiceClient.getContainerClient(containerName);

        // Upload the file
        const blobClient = containerClient.getBlockBlobClient(file.name);
        console.log(`Uploading file "${file.name}" to Azure Storage...`);

        const uploadResponse = await blobClient.uploadData(await file.arrayBuffer(), {
          blobHTTPHeaders: { blobContentType: file.type }
        });

        console.log('Upload result:', {
          name: file.name,
          success: true,
          etag: uploadResponse.etag
        });

        // Manually refresh the file list after successful upload
        // This ensures we see the new files even if WebSocket updates don't arrive
        console.log('Manually refreshing file list after template upload...');
        setTimeout(async () => {
          try {
            // First try to send a refresh message to the WebSocket for input container
            if (inputWsRef?.current && inputWsRef.current.readyState === WebSocket.OPEN) {
              console.log('Sending refresh request to input container WebSocket');
              inputWsRef.current.send('refresh');
            }

            // Then also do a direct HTTP fetch as a fallback
            await fetchFiles();
            console.log('File list refreshed after template upload');
          } catch (refreshError) {
            console.error('Error refreshing file list after template upload:', refreshError);
          }
        }, 1000); // Wait 1 second after upload to refresh

      } catch (err) {
        console.error('Error uploading template file via API:', err);
        setError(`Error uploading template: ${err instanceof Error ? err.message : 'Unknown network error'}`);
      } finally {
        setUploading(false);
      }
    },
    [projectId, fetchFiles, inputWsRef] // Depend on projectId, fetchFiles, and inputWsRef
  );

  // Handle Excel file upload
  const handleExcelFileUpload = useCallback((event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      uploadFile(files[0]);
    }
    // Reset the input value so the same file can be uploaded again if needed
    if (event.target) {
      event.target.value = '';
    }
  }, [uploadFile]);

  // Handle Word file upload
  const handleWordFileUpload = useCallback((event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      uploadFile(files[0]);
    }
    // Reset the input value so the same file can be uploaded again if needed
    if (event.target) {
      event.target.value = '';
    }
  }, [uploadFile]);

  // Handle PowerPoint file upload
  const handlePptxFileUpload = useCallback((event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      uploadFile(files[0]);
    }
    // Reset the input value so the same file can be uploaded again if needed
    if (event.target) {
      event.target.value = '';
    }
  }, [uploadFile]);

  // Click handlers for upload buttons
  const handleExcelUploadClick = useCallback(() => {
    if (excelFileInputRef.current) {
      excelFileInputRef.current.click();
    }
  }, []);

  const handleWordUploadClick = useCallback(() => {
    if (wordFileInputRef.current) {
      wordFileInputRef.current.click();
    }
  }, []);

  const handlePptxUploadClick = useCallback(() => {
    if (pptxFileInputRef.current) {
      pptxFileInputRef.current.click();
    }
  }, []);

  // Handle file deletion using project-specific containers and direct Azure Storage access
  const handleDelete = async (fileName: string, isOutputFile: boolean = false) => {
    try {
      // Use project-specific storage account name and SAS token from ProjectContext
      const storageAccountName = projectContext.storageAccountName;
      const sasToken = projectContext.storageAccountSasToken;

      if (!storageAccountName || !sasToken) {
        throw new Error("Missing storage account name or SAS token in project context");
      }

      // Determine the container name based on isOutputFile
      const containerName = isOutputFile ? projectContext.storageContainerOutput : projectContext.storageContainerInput;

      if (!containerName) {
        throw new Error(`Project ${isOutputFile ? 'output' : 'input'} container name not found in context.`);
      }

      console.log(`Deleting file "${fileName}" directly from Azure Storage, container: ${containerName}`);

      // Create a BlobServiceClient
      const blobServiceClient = new BlobServiceClient(
        `https://${storageAccountName}.blob.core.windows.net${sasToken.startsWith('?') ? sasToken : '?' + sasToken}`
      );

      // Get a reference to the container and blob
      const containerClient = blobServiceClient.getContainerClient(containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(fileName);

      // Delete the blob
      await blockBlobClient.delete();
      console.log(`Successfully deleted file "${fileName}" from ${isOutputFile ? 'OUTPUT' : 'INPUT'} container`);

      // Update UI immediately (WebSocket should confirm shortly)
      if (isOutputFile) {
        setOutputFiles(prev => prev.filter(file => file.name !== fileName))
      } else {
        setFiles(prev => prev.filter(file => file.name !== fileName))
      }
    } catch (err) {
      console.error('Error deleting file:', err)
      if (err instanceof Error) {
        setError(`Error: ${err.message}`)
      } else {
        setError('An unknown error occurred')
      }
    }
  }

  // Refresh SAS token (remains the same, fetches global settings)
  const refreshSasToken = async (): Promise<boolean> => {
    try {
      setIsRefreshingToken(true)
      setError(null)
      const response = await axios.get('/frontend_settings')
      const config = response.data
      if (config.azure_storage && config.azure_storage.container_sas_token) {
        setGlobalStorageConfig(prevConfig => ({
          ...prevConfig,
          container_sas_token: config.azure_storage.container_sas_token
        }))
        // Refetch files might be needed if the token was the issue
        // await fetchFiles(); // Consider if needed after token refresh
        console.log("SAS Token refreshed.");
        return true
      } else {
        throw new Error('Could not refresh SAS token from settings')
      }
    } catch (err) {
      console.error('Error refreshing SAS token:', err)
      setError('Failed to refresh authentication token')
      return false
    } finally {
      setIsRefreshingToken(false)
    }
  }

  // Handle download (uses direct Azure Storage access)
  const handleDownload = async (fileUrl: string, fileName: string, containerType: string = 'input'): Promise<void> => {
    if (!projectId) {
      setError('Project ID is missing. Cannot download the file.')
      console.error('Project ID is missing when trying to download:', fileName)
      return
    }

    try {
      setError(null)

      // Use project-specific storage account name and SAS token from ProjectContext
      const storageAccountName = projectContext.storageAccountName;
      const sasToken = projectContext.storageAccountSasToken;

      if (!storageAccountName || !sasToken) {
        throw new Error("Missing storage account name or SAS token in project context");
      }

      // Determine the container name based on containerType
      let containerName = '';
      if (containerType === 'input') {
        containerName = projectContext.storageContainerInput;
      } else if (containerType === 'output') {
        containerName = projectContext.storageContainerOutput;
      } else {
        containerName = projectContext.storageContainerUploads;
      }

      if (!containerName) {
        throw new Error(`Missing ${containerType} container name in project context`);
      }

      console.log(`Downloading file "${fileName}" directly from Azure Storage, container: ${containerName}`);

      // Create a direct download URL with SAS token
      const formattedSasToken = sasToken.startsWith('?') ? sasToken : `?${sasToken}`;
      const downloadUrl = `https://${storageAccountName}.blob.core.windows.net/${containerName}/${encodeURIComponent(fileName)}${formattedSasToken}`;

      // Create a download link
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      link.target = '_blank'; // Open in new tab to handle large files better
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`Download initiated for file "${fileName}"`);
    } catch (err) {
      console.error('Error downloading file:', err)
      if (err instanceof Error) {
        setError(`Download error: ${err.message}`)
      } else {
        setError('An unknown error occurred during download')
      }
    }
  }

  // Helper function to convert a ReadableStream to a Blob
  const streamToBlob = async (stream: ReadableStream<Uint8Array>, size?: number): Promise<Blob> => {
    const chunks: Uint8Array[] = [];
    const reader = (stream as any).getReader();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }
    } finally {
      reader.releaseLock();
    }

    return new Blob(chunks);
  }

  // Filter files by type (no changes needed)
  const getExcelFiles = useCallback(() => {
    console.log('Filtering Excel files from:', files);
    return files.filter(file => file.name.toLowerCase().endsWith('.xlsx'))
  }, [files])

  const getWordFiles = useCallback(() => {
    console.log('Filtering Word files from:', files);
    return files.filter(file => file.name.toLowerCase().endsWith('.docx'))
  }, [files])

  const getPowerPointFiles = useCallback(() => {
    console.log('Filtering PowerPoint files from:', files);
    return files.filter(file => file.name.toLowerCase().endsWith('.pptx'))
  }, [files])

  // Check if a file is protected (no changes needed)
  const isProtectedFile = useCallback((fileName: string) => {
    return PROTECTED_FILES.includes(fileName)
  }, [])

  // Handle Maturity Assessment with manual refresh
  const handleMaturityAssessment = async (fileName: string) => {
    setIsMaturityLoading(true)
    setShowMaturityDropdown(false)
    try {
      // Use project-specific function URL and key from ProjectContext
      if (projectContext && projectContext.projectEnv?.AZURE_FUNCTION_MATURITY_ASSESSMENT_URL && projectContext.projectEnv?.FUNCTION_KEY_MATURITY) {
        const url = `${projectContext.projectEnv.AZURE_FUNCTION_MATURITY_ASSESSMENT_URL}?filename=${encodeURIComponent(fileName)}`
        console.log('Sending maturity assessment request to project-specific function:', url)
        await axios.post(
          url,
          {},
          { headers: { 'x-functions-key': projectContext.projectEnv.FUNCTION_KEY_MATURITY } }
        )
      } else {
        // Fall back to global config if project-specific values are not available
        console.warn('Project-specific function configuration not available, falling back to global config');
        const response = await axios.get('/frontend_settings')
        const config = response.data
        if (config.azure_function?.maturity_assessment_url && config.azure_function?.maturity_assessment_key) {
          const url = `${config.azure_function.maturity_assessment_url}?filename=${encodeURIComponent(fileName)}`
          console.log('Sending maturity assessment request to global function:', url)
          await axios.post(
            url,
            {},
            { headers: { 'x-functions-key': config.azure_function.maturity_assessment_key } }
          )
        } else {
          throw new Error('Maturity assessment function configuration not found');
        }
      }

        // Manually refresh the file list after successful processing
        console.log('Manually refreshing file list after maturity assessment...');
        setTimeout(async () => {
          try {
            // First try to send a refresh message to the WebSocket for output container
            if (outputWsRef?.current && outputWsRef.current.readyState === WebSocket.OPEN) {
              console.log('Sending refresh request to output container WebSocket');
              outputWsRef.current.send('refresh');
            }

            // Then also do a direct HTTP fetch as a fallback
            await fetchFiles();
            console.log('File list refreshed after maturity assessment');
          } catch (refreshError) {
            console.error('Error refreshing file list after maturity assessment:', refreshError);
          }
        }, 1000); // Wait 1 second after processing to refresh

    } catch (err) {
      console.error('Error in maturity assessment:', err)
      if (axios.isAxiosError(err)) {
        const axiosError = err as AxiosError
        if (axiosError.response) {
          console.error('Error response:', axiosError.response.status, axiosError.response.data)

          // Check if this is a validation error (status code 400)
          if (axiosError.response.status === 400) {
            // Extract the validation error message
            let errorMessage = typeof axiosError.response.data === 'string'
              ? axiosError.response.data
              : 'Excel file structure validation failed. Please upload a valid template.';

            // Format the error message if it contains "Excel file structure validation failed:"
            if (errorMessage.includes('Excel file structure validation failed:')) {
              // Split the message to separate the main error from the details
              const parts = errorMessage.split('Excel file structure validation failed:');
              if (parts.length > 1) {
                // Format the error message with proper line breaks
                errorMessage = 'Excel file structure validation failed:' + parts[1];
              }
            }

            // Show the validation error modal
            setValidationErrorMessage(errorMessage);
            setIsValidationErrorModalOpen(true);
            return; // Don't set the general error message
          }
        }
      }
      setError('Failed to run maturity assessment')
    } finally {
      setIsMaturityLoading(false)
      setSelectedMaturityFile(null)
    }
  }

  // Handle Executive Summary with manual refresh
  const handleExecSummary = async (fileName: string) => {
    setIsExecSummaryLoading(true)
    setShowExecSummaryDropdown(false)
    try {
      // Use project-specific function URL and key from ProjectContext
      if (projectContext && projectContext.projectEnv?.AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL && projectContext.projectEnv?.FUNCTION_KEY_EXECUTIVE_SUMMARY) {
        const inputContainerName = projectContext.storageContainerInput;
        if (!inputContainerName) {
          throw new Error("Project input container name not found in context for executive summary.");
        }
        const payload = {
          input_blob_path: `${inputContainerName}/${fileName}` // Use project-specific container
        }
        console.log('Sending executive summary request to project-specific function with payload:', payload)
        await axios.post(
          projectContext.projectEnv.AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL,
          payload,
          { headers: { 'x-functions-key': projectContext.projectEnv.FUNCTION_KEY_EXECUTIVE_SUMMARY } }
        )
      } else {
        // Fall back to global config if project-specific values are not available
        console.warn('Project-specific function configuration not available, falling back to global config');
        const response = await axios.get('/frontend_settings')
        const config = response.data
        if (config.azure_function?.executive_summary_url && config.azure_function?.executive_summary_key) {
          const inputContainerName = projectContext?.storageContainerInput;
          if (!inputContainerName) {
            throw new Error("Project input container name not found in context for executive summary.");
          }
          const payload = {
            input_blob_path: `${inputContainerName}/${fileName}` // Use project-specific container
          }
          console.log('Sending executive summary request to global function with payload:', payload)
          await axios.post(
            config.azure_function.executive_summary_url,
            payload,
            { headers: { 'x-functions-key': config.azure_function.executive_summary_key } }
          )
        } else {
          throw new Error('Executive summary function configuration not found');
        }
      }

        // Manually refresh the file list after successful processing
        console.log('Manually refreshing file list after executive summary generation...');
        setTimeout(async () => {
          try {
            // First try to send a refresh message to the WebSocket for output container
            if (outputWsRef?.current && outputWsRef.current.readyState === WebSocket.OPEN) {
              console.log('Sending refresh request to output container WebSocket');
              outputWsRef.current.send('refresh');
            }

            // Then also do a direct HTTP fetch as a fallback
            await fetchFiles();
            console.log('File list refreshed after executive summary generation');
          } catch (refreshError) {
            console.error('Error refreshing file list after executive summary generation:', refreshError);
          }
        }, 1000); // Wait 1 second after processing to refresh

    } catch (err) {
      console.error('Error generating executive summary:', err)
      if (axios.isAxiosError(err)) {
        const axiosError = err as AxiosError
        if (axiosError.response) {
          console.error('Error response:', axiosError.response.status, axiosError.response.data)
        }
      }
      setError('Failed to generate executive summary')
    } finally {
      setIsExecSummaryLoading(false)
      setSelectedExecSummaryFile(null)
    }
  }

  // Handle PowerPoint generation with manual refresh
  const handlePowerPoint = async (excelFileName: string) => {
    setIsPowerPointLoading(true)
    setShowPowerPointDropdown(false)
    try {
      // Get the first PowerPoint template file
      const pptxTemplates = getPowerPointFiles();
      if (pptxTemplates.length === 0) {
        throw new Error("No PowerPoint template files found. Please upload a PowerPoint template first.");
      }

      const pptxTemplate = pptxTemplates[0].name;
      const projectName = projectContext.projectName || "Data Maturity Assessment";

      // Extract just the filename if it contains a path
      const cleanExcelFileName = excelFileName.includes('/')
        ? excelFileName.substring(excelFileName.lastIndexOf('/') + 1)
        : excelFileName;

      // Use project-specific function URL and key from ProjectContext
      if (projectContext && projectContext.functionAppUrl && projectContext.functionKeyPowerPoint) {
        const url = `${projectContext.functionAppUrl}/api/pptxGenerator?code=${projectContext.functionKeyPowerPoint}`

        // Create the payload with excel file, pptx template, and project name
        // Only send the filename without container path
        const payload = {
          excel_file: cleanExcelFileName,
          pptx_template: pptxTemplate,
          project_name: projectName
        };

        console.log('Sending PowerPoint generation request to project-specific function:', url);
        console.log('Payload:', payload);

        await axios.post(
          url,
          payload,
          { headers: { 'Content-Type': 'application/json' } }
        )
      } else {
        // Fall back to global config if project-specific values are not available
        console.warn('Project-specific function configuration not available, falling back to global config');
        const response = await axios.get('/frontend_settings')
        const config = response.data
        if (config.azure_function?.function_app_url && config.azure_function?.function_key_powerpoint) {
          const url = `${config.azure_function.function_app_url}/api/pptxGenerator?code=${config.azure_function.function_key_powerpoint}`

          // Create the payload with excel file, pptx template, and project name
          // Only send the filename without container path
          const payload = {
            excel_file: cleanExcelFileName,
            pptx_template: pptxTemplate,
            project_name: projectName
          };

          console.log('Sending PowerPoint generation request to global function:', url);
          console.log('Payload:', payload);

          await axios.post(
            url,
            payload,
            { headers: { 'Content-Type': 'application/json' } }
          )
        } else {
          throw new Error('PowerPoint generation function configuration not found');
        }
      }

      // Manually refresh the file list after successful processing
      console.log('Manually refreshing file list after PowerPoint generation...');
      setTimeout(async () => {
        try {
          // First try to send a refresh message to the WebSocket for output container
          if (outputWsRef?.current && outputWsRef.current.readyState === WebSocket.OPEN) {
            console.log('Sending refresh request to output container WebSocket');
            outputWsRef.current.send('refresh');
          }

          // Then also do a direct HTTP fetch as a fallback
          await fetchFiles();
          console.log('File list refreshed after PowerPoint generation');
        } catch (refreshError) {
          console.error('Error refreshing file list after PowerPoint generation:', refreshError);
        }
      }, 1000); // Wait 1 second after processing to refresh

    } catch (err) {
      console.error('Error generating PowerPoint:', err)
      if (axios.isAxiosError(err)) {
        const axiosError = err as AxiosError
        if (axiosError.response) {
          console.error('Error response:', axiosError.response.status, axiosError.response.data)
        }
      }
      setError('Failed to generate PowerPoint')
    } finally {
      setIsPowerPointLoading(false)
      setSelectedPowerPointFile(null)
    }
  }

  // Fetch indexed files (remains the same, relies on project context index name)
  const fetchIndexedFiles = useCallback(async () => {
    const projectIndexName = projectContext?.projectEnv?.SEARCH_INDEX;
    if (!projectIndexName) {
      console.warn('Project index name not found in context, skipping fetchIndexedFiles');
      setIndexedFiles([]);
      return;
    }
    if (isIndexWsConnected && indexData && Array.isArray(indexData)) {
      console.log(`Using WebSocket data for indexed files: ${indexData.length} files found`)
      setIndexedFiles(indexData)
      return
    }
    console.log('No WebSocket data available for indexed files');
    setIndexedFiles([]);
  }, [projectContext?.projectEnv?.SEARCH_INDEX, indexData, isIndexWsConnected]);

  // Use WebSocket data for indexed files
  useEffect(() => {
    if (indexData && Array.isArray(indexData)) {
      console.log('Received indexed files data from WebSocket:', indexData.length)
      setIndexedFiles(indexData)
    }
  }, [indexData])

  // Initial check for indexed files
  useEffect(() => {
    if (projectContext?.projectEnv?.SEARCH_INDEX) {
      console.log('Initial check for indexed files using project context');
      fetchIndexedFiles();
    } else {
      console.warn('Project context not ready for initial indexed file check.');
    }
  }, [projectContext?.projectEnv?.SEARCH_INDEX, fetchIndexedFiles]);

  // Use WebSocket data for input files
  useEffect(() => {
    if (inputContainerData && Array.isArray(inputContainerData)) {
      console.log(`Received ${inputContainerData.length} input files from WebSocket`)
      // Use project-specific storage account name from ProjectContext
      const storageAccountName = projectContext.storageAccountName || globalStorageConfig.account_name;
      const baseUrl = `https://${storageAccountName}.blob.core.windows.net`
      const inputContainerName = projectContext?.storageContainerInput;
      const convertedFiles = inputContainerData.map(item => {
        if (!item.url && storageAccountName && inputContainerName) {
          console.log(`Adding missing URL for file: ${item.name} in container ${inputContainerName}`);
          return {
            ...item,
            url: `${baseUrl}/${inputContainerName}/${item.name}`,
            lastModified: item.lastModified ? new Date(item.lastModified) : new Date()
          };
        }
        return {
          ...item,
          lastModified: item.lastModified ? new Date(item.lastModified) : new Date()
        }
      })
      setFiles(convertedFiles);
    }
  }, [inputContainerData, globalStorageConfig.account_name, projectContext?.storageAccountName, projectContext?.storageContainerInput]);

  // Use WebSocket data for output files
  useEffect(() => {
    if (outputContainerData && Array.isArray(outputContainerData)) {
      console.log(`Received ${outputContainerData.length} output files from WebSocket`)
      // Use project-specific storage account name from ProjectContext
      const storageAccountName = projectContext.storageAccountName || globalStorageConfig.account_name;
      const baseUrl = `https://${storageAccountName}.blob.core.windows.net`
      const outputContainerName = projectContext?.storageContainerOutput;
      const sortedOutputFiles = [...outputContainerData].sort((a, b) => {
        const dateA = a.lastModified ? new Date(a.lastModified).getTime() : 0
        const dateB = b.lastModified ? new Date(b.lastModified).getTime() : 0
        return dateB - dateA
      })
      const convertedFiles = sortedOutputFiles.map(item => {
        if (!item.url && storageAccountName && outputContainerName) {
          console.log(`Adding missing URL for file: ${item.name} in container ${outputContainerName}`);
          return {
            ...item,
            url: `${baseUrl}/${outputContainerName}/${item.name}`,
            lastModified: item.lastModified ? new Date(item.lastModified) : new Date()
          };
        }
        return {
          ...item,
          lastModified: item.lastModified ? new Date(item.lastModified) : new Date()
        }
      })
      setOutputFiles(convertedFiles);
    }
  }, [outputContainerData, globalStorageConfig.account_name, projectContext?.storageAccountName, projectContext?.storageContainerOutput]);

  // Handle click outside dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowMaturityDropdown(false)
      }
      if (execSummaryDropdownRef.current && !execSummaryDropdownRef.current.contains(event.target as Node)) {
        setShowExecSummaryDropdown(false)
      }
      if (powerPointDropdownRef.current && !powerPointDropdownRef.current.contains(event.target as Node)) {
        setShowPowerPointDropdown(false)
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Handle click on collapsed panel (no changes needed)
  const handleCollapsedPanelClick = () => {
    if (!isOpen) {
      setIsOpen(true)
    }
  }

  // Progress bar effects (no changes needed)
  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (isMaturityLoading) {
      setProgressPercentage(0)
      interval = setInterval(() => {
        setProgressPercentage(prev => {
          const newProgress = prev + 100 / 240
          return newProgress >= 99 ? 99 : newProgress
        })
      }, 1000)
    } else {
      setProgressPercentage(100)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isMaturityLoading])

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (isExecSummaryLoading) {
      setExecSummaryProgressPercentage(0)
      interval = setInterval(() => {
        setExecSummaryProgressPercentage(prev => {
          const newProgress = prev + 100 / 180
          return newProgress >= 99 ? 99 : newProgress
        })
      }, 1000)
    } else {
      setExecSummaryProgressPercentage(100)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isExecSummaryLoading])

  // Progress bar effect for PowerPoint generation
  useEffect(() => {
    let interval: NodeJS.Timeout | undefined;
    if (isPowerPointLoading) {
      setPowerPointProgressPercentage(0)
      interval = setInterval(() => {
        setPowerPointProgressPercentage(prev => {
          const newProgress = prev + 100 / 200
          return newProgress >= 99 ? 99 : newProgress
        })
      }, 1000)
    } else {
      setPowerPointProgressPercentage(100)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isPowerPointLoading])

  // Render logic (no structural changes, just uses updated state/functions)

  // Add loading state check
  if (!isInitialized) {
    return <div className="file-management-loading">Loading...</div>
  }

  return (
    <div
      ref={wrapperRef}
      className={`file-management-wrapper right-panel ${!isOpen ? 'collapsed' : ''}`}
      onClick={!isOpen ? handleCollapsedPanelClick : undefined}>
      <div className={`file-management ${isOpen ? 'expanded' : 'collapsed'}`}>
        {!isOpen && (
          <>
            <FileText className="collapsed-icon" size={24} color="#0F6CBD" />
            <span className="second-arrow">«</span>
          </>
        )}
        {isOpen && (
          <>
            <div className="file-management-header" onClick={() => setIsOpen(!isOpen)}>
              <div className="header-content">
                <button
                  className={`toggle-button ${isOpen ? 'open' : ''}`}
                  onClick={e => { e.stopPropagation(); setIsOpen(!isOpen); }}>
                  <ChevronRight />
                </button>
                <h2>Template Files</h2>
                <div className="right-panel-status">
                  <button
                    className={`refresh-button ${isRefreshingToken ? 'loading' : ''}`}
                    onClick={e => { e.stopPropagation(); refreshSasToken(); }}
                    title="Refresh authentication token"
                    disabled={isRefreshingToken}>
                    <RefreshCw size={16} />
                  </button>
                  {isWsConnected ? (
                    <span style={{ color: '#44ef5c' }} className="pulsating" title="Live updates connected">●</span>
                  ) : (
                    <span style={{ color: '#ef4444' }} title="Live updates disconnected">●</span>
                  )}
                </div>
              </div>
            </div>
            <div className="file-management-content">
              <div className="section-container">
                <div className="upload-section">
                  <div className="subsection-header">
                    <h4 className="subsection-title">Assessment Templates</h4>
                    <button
                      className="upload-icon-button"
                      onClick={handleExcelUploadClick}
                      title="Upload Assessment Template (.xlsx)">
                      <Upload className="upload-icon" />
                    </button>
                    <input
                      type="file"
                      ref={excelFileInputRef}
                      onChange={handleExcelFileUpload}
                      accept=".xlsx"
                      style={{ display: 'none' }}
                    />
                  </div>
                  <div className="files-list">
                    {getExcelFiles().map(file => (
                      <div key={file.name} className="file-item">
                        <div className="file-info">
                          {getFileIcon(file.name)}
                          <span className="file-name">{file.name}</span>
                        </div>
                        <div className="file-actions">
                          <button
                            onClick={() => handleDownload(file.url, file.name)}
                            className="action-button download-button"
                            title="Download file"
                            disabled={!file.url}>
                            <Download className="action-icon" />
                          </button>
                          {!isProtectedFile(file.name) && (
                            <button
                              onClick={() => handleDelete(file.name)}
                              className="action-button delete-button"
                              title="Delete file">
                              <Trash2 className="delete-icon" />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  {isMaturityLoading && (
                    <div className="progress-bar-container">
                      <div className="progress-bar" style={{ width: `${progressPercentage}%` }}></div>
                      <div className="progress-percentage">{Math.round(progressPercentage)}%</div>
                    </div>
                  )}
                  <div className="dropdown-container" ref={dropdownRef}>
                    <button
                      className={`action-button-large ${isMaturityLoading ? 'loading' : ''}`}
                      onClick={() => setShowMaturityDropdown(!showMaturityDropdown)}
                      disabled={isMaturityLoading || getExcelFiles().length === 0}>
                      <Beaker className="action-icon" />
                      <span>{isMaturityLoading ? 'Processing...' : 'Run AI Maturity Assessment'}</span>
                      <ChevronDown className="dropdown-icon" />
                    </button>
                    {showMaturityDropdown && (
                      <div className="dropdown-menu">
                        {getExcelFiles().length > 0 ? (
                          getExcelFiles().map(file => (
                            <div
                              key={file.name}
                              className="dropdown-item"
                              onClick={() => handleMaturityAssessment(file.name)}>
                              {file.name}
                            </div>
                          ))
                        ) : (
                          <div className="dropdown-item disabled">No Excel files available</div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="upload-section">
                  <div className="subsection-header">
                    <h4 className="subsection-title">Business and Data Discovery Templates</h4>
                    <button
                      className="upload-icon-button"
                      onClick={handleWordUploadClick}
                      title="Upload Business and Data Discovery Template (.docx)">
                      <Upload className="upload-icon" />
                    </button>
                    <input
                      type="file"
                      ref={wordFileInputRef}
                      onChange={handleWordFileUpload}
                      accept=".docx"
                      style={{ display: 'none' }}
                    />
                  </div>
                  <div className="files-list">
                    {getWordFiles().map(file => (
                      <div key={file.name} className="file-item">
                        <div className="file-info">
                          {getFileIcon(file.name)}
                          <span className="file-name">{file.name}</span>
                        </div>
                        <div className="file-actions">
                          <button
                            onClick={() => handleDownload(file.url, file.name, 'input')}
                            className="action-button download-button"
                            title="Download file"
                            disabled={!projectId}>
                            <Download className="action-icon" />
                          </button>
                          {!isProtectedFile(file.name) && (
                            <button
                              onClick={() => handleDelete(file.name)}
                              className="action-button delete-button"
                              title="Delete file">
                              <Trash2 className="delete-icon" />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  {isExecSummaryLoading && (
                    <div className="progress-bar-container">
                      <div className="progress-bar" style={{ width: `${execSummaryProgressPercentage}%` }}></div>
                      <div className="progress-percentage">{Math.round(execSummaryProgressPercentage)}%</div>
                    </div>
                  )}
                  <div className="dropdown-container" ref={execSummaryDropdownRef}>
                    <button
                      className={`action-button-large ${isExecSummaryLoading ? 'loading' : ''}`}
                      onClick={() => setShowExecSummaryDropdown(!showExecSummaryDropdown)}
                      disabled={isExecSummaryLoading || getWordFiles().length === 0}>
                      <FileText className="action-icon" />
                      <span>{isExecSummaryLoading ? 'Generating...' : 'Generate Business and Data Discovery'}</span>
                      <ChevronDown className="dropdown-icon" />
                    </button>
                    {showExecSummaryDropdown && (
                      <div className="dropdown-menu">
                        {getWordFiles().length > 0 ? (
                          getWordFiles().map(file => (
                            <div key={file.name} className="dropdown-item" onClick={() => handleExecSummary(file.name)}>
                              {file.name}
                            </div>
                          ))
                        ) : (
                          <div className="dropdown-item disabled">No Word files available</div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <div className="upload-section">
                  <div className="subsection-header">
                    <h4 className="subsection-title">PowerPoint Templates</h4>
                    <button
                      className="upload-icon-button"
                      onClick={handlePptxUploadClick}
                      title="Upload PowerPoint Template (.pptx)">
                      <Upload className="upload-icon" />
                    </button>
                    <input
                      type="file"
                      ref={pptxFileInputRef}
                      onChange={handlePptxFileUpload}
                      accept=".pptx"
                      style={{ display: 'none' }}
                    />
                  </div>
                  <div className="files-list">
                    {getPowerPointFiles().map(file => (
                      <div key={file.name} className="file-item">
                        <div className="file-info">
                          {getFileIcon(file.name)}
                          <span className="file-name">{file.name}</span>
                        </div>
                        <div className="file-actions">
                          <button
                            onClick={() => handleDownload(file.url, file.name, 'input')}
                            className="action-button download-button"
                            title="Download file"
                            disabled={!projectId}>
                            <Download className="action-icon" />
                          </button>
                          {!isProtectedFile(file.name) && (
                            <button
                              onClick={() => handleDelete(file.name)}
                              className="action-button delete-button"
                              title="Delete file">
                              <Trash2 className="delete-icon" />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="section-separator"></div>
              <div className="section-container actions-section">
                {/* Removed indexedFiles check as it's not relevant for templates */}
                {error && <div className="status-message error">{error}</div>}
              </div>
              <div className="section-container">
                <div className="section-header">
                  <h3 className="section-title">Generated Files</h3>
                  <div className="section-actions">
                    <button
                      className="view-priority-plot-button"
                      onClick={() => {
                        // Find the first Excel file in the output files
                        const excelFile = outputFiles.find(file => file.name.toLowerCase().endsWith('.xlsx'));
                        if (excelFile) {
                          setSelectedPriorityPlotFile(excelFile);
                        } else {
                          setSelectedPriorityPlotFile(null);
                        }
                        setIsPriorityPlotModalOpen(true);
                      }}
                      title="Priority Plot"
                    >
                      <span>Priority Plot</span>
                    </button>
                    <div className="dropdown-container" ref={powerPointDropdownRef}>
                      <button
                        className={`view-powerpoint-button ${isPowerPointLoading ? 'loading' : ''}`}
                        onClick={() => {
                          // Check if PowerPoint templates are available
                          const pptxTemplates = getPowerPointFiles();
                          if (pptxTemplates.length === 0) {
                            setError("No PowerPoint template files found. Please upload a PowerPoint template first.");
                            setTimeout(() => setError(null), 5000);
                          } else {
                            setShowPowerPointDropdown(!showPowerPointDropdown);
                          }
                        }}
                        disabled={isPowerPointLoading || getExcelFiles().length === 0 || getPowerPointFiles().length === 0}
                        title="Generate PowerPoint"
                      >
                        <Presentation className="button-icon" />
                        <span>{isPowerPointLoading ? 'Generating...' : 'PowerPoint'}</span>
                        <ChevronDown className="dropdown-icon" />
                      </button>
                      {showPowerPointDropdown && (
                        <div className="dropdown-menu">
                          {getExcelFiles().length > 0 && getPowerPointFiles().length > 0 ? (
                            getExcelFiles().map(file => (
                              <div
                                key={file.name}
                                className="dropdown-item"
                                onClick={() => handlePowerPoint(file.name)}>
                                {file.name}
                              </div>
                            ))
                          ) : getPowerPointFiles().length === 0 ? (
                            <div className="dropdown-item disabled">No PowerPoint templates available</div>
                          ) : (
                            <div className="dropdown-item disabled">No Excel files available</div>
                          )}
                        </div>
                      )}
                      {isPowerPointLoading && (
                        <div className="progress-bar-container">
                          <div className="progress-bar" style={{ width: `${powerPointProgressPercentage}%` }}></div>
                          <div className="progress-percentage">{Math.round(powerPointProgressPercentage)}%</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="files-list">
                  {outputFiles.map(file => (
                    <div key={file.name} className="file-item">
                      <div className="file-info">
                        <File className="file-icon" />
                        <span className="file-name">{file.name}</span>
                      </div>
                      <div className="file-actions">
                        <button
                          onClick={() => handleDownload(file.url, file.name, 'output')}
                          className="action-button download-button"
                          title="Download file"
                          disabled={!projectId}>
                          <Download className="action-icon" />
                        </button>
                        {file.name.toLowerCase().endsWith('.xlsx') && (
                          <>
                            <PriorityPlotButton
                              onClick={(file) => {
                                setSelectedPriorityPlotFile(file);
                                setIsPriorityPlotModalOpen(true);
                              }}
                              file={file}
                            />
                            <button
                              onClick={() => {
                                // Check if PowerPoint templates are available
                                const pptxTemplates = getPowerPointFiles();
                                if (pptxTemplates.length === 0) {
                                  setError("No PowerPoint template files found. Please upload a PowerPoint template first.");
                                  setTimeout(() => setError(null), 5000);
                                } else {
                                  handlePowerPoint(file.name);
                                }
                              }}
                              className="action-button powerpoint-button"
                              title="Generate PowerPoint"
                              disabled={isPowerPointLoading || getPowerPointFiles().length === 0}
                            >
                              <Presentation className={`action-icon ${isPowerPointLoading ? 'spinning' : ''}`} />
                            </button>
                          </>
                        )}
                        <button
                          onClick={() => handleDelete(file.name, true)}
                          className="action-button delete-button"
                          title="Delete file">
                          <Trash2 className="delete-icon" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              {uploading && <div className="status-message uploading">Uploading...</div>}
            </div>
          </>
        )}
      </div>

      {/* Priority Plot Modal */}
      <PriorityPlotModal
        isOpen={isPriorityPlotModalOpen}
        onClose={() => {
          setIsPriorityPlotModalOpen(false);
          setSelectedPriorityPlotFile(null);
        }}
        selectedFile={selectedPriorityPlotFile}
      />

      {/* Validation Error Modal */}
      <ValidationErrorModal
        isOpen={isValidationErrorModalOpen}
        onClose={() => setIsValidationErrorModalOpen(false)}
        errorMessage={validationErrorMessage}
      />
    </div>
  )
}
export default RightFileManagement

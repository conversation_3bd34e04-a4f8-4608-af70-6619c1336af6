DEPLOY-173 - Refactor Deployment Authentication to Prioritize Service Principal Credentials
Assignee: Third-Party Senior Azure Engineer
Reporter: Azure Mentor Copilot
Priority: High

1. Background
Our automated resource deployment process, orchestrated by deploy_project_resources.py, currently uses DefaultAzureCredential for authentication. While this is flexible, it produces confusing errors in non-Azure environments (e.g., local development, CI/CD runners).

The logs consistently show a benign but alarming error: ERROR: Failed to connect to MSI. This occurs because DefaultAzureCredential attempts to find a Managed Service Identity (MSI) endpoint first. When it fails, it correctly falls back to other methods like the logged-in Azure CLI user's context.

To improve reliability and create a more predictable authentication flow for automated scenarios, we need to modify the logic to directly use an Entra ID App Registration's credentials (Service Principal) when they are available.

2. Task Description & Acceptance Criteria
The goal is to modify the authentication mechanism within our backend services to explicitly use ClientSecretCredential when the necessary environment variables are provided.

Definition of Done:

The ProjectDataService class in backend/services/project_service.py must be updated to prioritize ClientSecretCredential over DefaultAzureCredential.
When the deployment script runs in an environment with AZURE_CLIENT_ID, AZURE_TENANT_ID, and AZURE_CLIENT_SECRET defined, authentication must use these credentials directly.
The deployment logs must no longer show the Failed to connect to MSI error in this configuration.
A log entry confirming Using ClientSecretCredential for authentication. should be present.
If the specified environment variables are not present, the authentication logic must gracefully fall back to using DefaultAzureCredential to support other environments and local development workflows.
The end-to-end resource deployment must continue to function successfully after the change.
3. Prerequisites
Before starting development, the engineer must verify the following:

RBAC Permissions: The Service Principal associated with the AZURE_CLIENT_ID (bb1ebfc1-47d8-4273-9206-3acc107c1e35) must have the "Contributor" role assigned over the target resource group (rg-internal-ai). This is required to create, manage, and delete resources during the deployment.

Environment Variables: The deployment environment must be configured with the following environment variables:

AZURE_CLIENT_ID
AZURE_TENANT_ID
AZURE_CLIENT_SECRET
4. Implementation Steps
The primary code change should be implemented in the ProjectDataService class, as it is the central service for handling Azure resource interactions.

File to Modify: backend/services/project_service.py

Proposed Implementation:
Replace the current direct instantiation of DefaultAzureCredential with a helper method that implements the desired credential selection logic.

Current Logic:

Python

from azure.identity import DefaultAzureCredential
# ... other imports

class ProjectDataService:
    def __init__(self):
        # ...
        self._credential = DefaultAzureCredential()
        # ...
New Logic:

Python

import os
import logging
from azure.identity import DefaultAzureCredential, ClientSecretCredential
# ... other imports

class ProjectDataService:
    def __init__(self):
        # ...
        self._credential = self._get_credential()
        # ...

    def _get_credential(self):
        """
        Get the appropriate Azure credential.
        Prefers ClientSecretCredential if environment variables are set,
        otherwise falls back to DefaultAzureCredential.
        """
        client_id = os.getenv("AZURE_CLIENT_ID")
        client_secret = os.getenv("AZURE_CLIENT_SECRET")
        tenant_id = os.getenv("AZURE_TENANT_ID")

        if client_id and client_secret and tenant_id:
            logging.info("Using ClientSecretCredential for authentication.")
            return ClientSecretCredential(
                tenant_id=tenant_id,
                client_id=client_id,
                client_secret=client_secret
            )
        else:
            logging.info("Client secret credentials not found, falling back to DefaultAzureCredential.")
            return DefaultAzureCredential()

5. Expected Outcome
Upon completion, the deployment script's authentication will be more deterministic. It will bypass the MSI check when run with Service Principal credentials, resulting in cleaner logs and a more robust, predictable process for all automated deployment scenarios.
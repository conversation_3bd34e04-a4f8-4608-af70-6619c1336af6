#!/bin/bash

echo ""
echo "Restoring frontend npm packages"
echo ""
cd frontend
npm install
npm install uuid @types/uuid chart.js react-chartjs-2 @azure/storage-blob xlsx
if [ $? -ne 0 ]; then
    echo "Failed to restore frontend npm packages"
    exit $?
fi

echo ""
echo "Building frontend with increased memory allocation"
echo ""
# Increase Node.js memory limit to prevent build from being killed
export NODE_OPTIONS="--max-old-space-size=4096"
# Create a CommonJS version of the node-polyfill.js file
cat > node-polyfill.cjs << 'EOL'
// Node.js polyfill script
const crypto = require('crypto');

// Add getRandomValues to the global crypto object if it doesn't exist
if (!crypto.getRandomValues) {
  crypto.getRandomValues = function getRandomValues(array) {
    const bytes = crypto.randomBytes(array.length);
    for (let i = 0; i < bytes.length; i++) {
      array[i] = bytes[i];
    }
    return array;
  };
}

// Make crypto available globally
global.crypto = crypto;

console.log('Node.js crypto polyfill applied');
EOL

# Run the polyfill before building
node node-polyfill.cjs
npm run build
if [ $? -ne 0 ]; then
    echo "Failed to build frontend"
    exit $?
fi

cd ..
. ./scripts/loadenv.sh


echo ""
if [ ! -z "$RBAC_LOG_LEVEL" ]; then
    echo "RBAC logging level: $RBAC_LOG_LEVEL"
else
    echo "RBAC logging level not set in .env file"
fi

echo ""
echo "Installing Python dependencies from requirements.txt"
echo ""
./.venv/bin/pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Failed to install Python dependencies"
    exit $?
fi

echo ""
echo "Starting main backend with WebSocket support"
echo ""
# Explicitly expose the API port used for local development
export API_PORT=50505
./.venv/bin/gunicorn app:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:${API_PORT}

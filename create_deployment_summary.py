#!/usr/bin/env python3
"""
Create a deployment summary JSON file for a project.
This script is called at the end of the deployment process to save a summary of the deployment.
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timezone
import asyncio

# Import the ProjectDataService
try:
    from backend.services.project_service import ProjectDataService
    project_service = ProjectDataService()
    project_service_available = True
except ImportError:
    logging.warning(
        "ProjectDataService not available. Project updates with deployment data will be disabled."
    )
    project_service = None
    project_service_available = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


def create_deployment_summary(project_id, project_name, region_id, resources, status="success", deployment_time=None, resource_durations=None, auto_update_project=True):

    """
    Create a deployment summary JSON file for a project and optionally update the project in CosmosDB.

    Args:
        project_id (str): The ID of the project
        project_name (str): The name of the project
        region_id (str): The ID of the region
        resources (dict): Dictionary containing resource names
        status (str): The status of the deployment (success, failed)
        deployment_time (str): The time taken for deployment (e.g., "13s")
        resource_durations (dict): Optional mapping of resource step to duration in seconds
        auto_update_project (bool): Whether to automatically update the project in CosmosDB

    Returns:
        str: Path to the created summary file
    """
    # Create the summary data
    resource_count = len([v for v in resources.values() if v])

    summary = {
        "project_id": project_id,
        "project_name": project_name,
        "region_id": region_id,
        "resources": resources,
        "status": status,
        "deployment_time": deployment_time,
        "resource_durations": resource_durations or {},
        "timestamp": datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")


    }

    # Create the logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    # Create the summary file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f"logs/deployment_summary_{timestamp}_{project_id}.json"

    with open(summary_file, "w") as f:
        json.dump(summary, f, indent=2)

    logger.info(f"Created deployment summary file: {summary_file}")

    # Automatically update the project in CosmosDB if requested
    if auto_update_project and project_service_available:
        try:
            logger.info(
                f"Automatically updating project {project_id} with deployment summary"
            )
            # Run the async update in a sync context
            success = asyncio.run(project_service.update_final_deployment_summary(project_id, summary))
            if success:
                logger.info(
                    f"Successfully updated project {project_id} with deployment summary"
                )
            else:
                logger.error(
                    f"Failed to update project {project_id} with deployment summary"
                )
        except Exception as e:
            logger.error(f"Error updating project with deployment summary: {e}")

    return summary_file


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(
        description="Create a deployment summary JSON file for a project"
    )
    parser.add_argument("project_id", help="The UUID of the project")
    parser.add_argument("project_name", help="The name of the project")
    parser.add_argument("region_id", help="The ID of the region")
    parser.add_argument(
        "--status",
        default="success",
        help="The status of the deployment (success, failed)",
    )
    parser.add_argument(
        "--deployment-time", help="The time taken for deployment (e.g., '13s')"
    )
    parser.add_argument("--storage-account", help="Storage account name")
    parser.add_argument("--uploads-container", help="Uploads container name")
    parser.add_argument("--input-container", help="Input container name")
    parser.add_argument("--output-container", help="Output container name")
    parser.add_argument("--search-service", help="Search service name")
    parser.add_argument("--search-index", help="Search index name")
    parser.add_argument("--search-indexer", help="Search indexer name")
    parser.add_argument("--search-datasource", help="Search datasource name")
    parser.add_argument("--function-app", help="Function app name")
    parser.add_argument("--function-app-url", help="Function app URL")
    parser.add_argument(
        "--no-update",
        action="store_true",
        help="Do not automatically update the project in CosmosDB",
    )
    parser.add_argument("--storage-account-sas-token", help="Storage account SAS token")
    parser.add_argument("--search-key", help="Search service API key")

    parser.add_argument(
        "--function-key-maturity", help="Function key for maturity assessment"
    )
    parser.add_argument(
        "--function-key-executive-summary", help="Function key for executive summary"
    )
    parser.add_argument(
        "--function-key-powerpoint", help="Function key for PowerPoint generation"
    )
    parser.add_argument(
        "--azure-function-maturity-assessment-url",
        help="Azure Function URL for maturity assessment",
    )
    parser.add_argument(
        "--azure-function-executive-summary-url",
        help="Azure Function URL for executive summary",
    )

    parser.add_argument("--resource-durations", help="JSON mapping of resource step durations")


    args = parser.parse_args()

    # Create the resources dictionary
    resources = {
        "storage_account_name": args.storage_account,
        "storage_account_sas_token": args.storage_account_sas_token,
        "uploads_container": args.uploads_container,
        "input_container": args.input_container,
        "output_container": args.output_container,
        "search_service_name": args.search_service,
        "search_index_name": args.search_index,
        "search_indexer_name": args.search_indexer,
        "search_datasource_name": args.search_datasource,
        "function_app_name": args.function_app,
        "function_app_url": args.function_app_url,
        "azure_search_key": args.search_key,
        "function_key_maturity": args.function_key_maturity,
        "function_key_executive_summary": args.function_key_executive_summary,
        "function_key_powerpoint": args.function_key_powerpoint,
        "azure_function_maturity_assessment_url": args.azure_function_maturity_assessment_url,
        "azure_function_executive_summary_url": args.azure_function_executive_summary_url,
    }

    # Remove None values
    resources = {k: v for k, v in resources.items() if v is not None}

    resource_durations = None
    if args.resource_durations:
        try:
            resource_durations = json.loads(args.resource_durations)
        except json.JSONDecodeError:
            logger.error("Invalid JSON for --resource-durations")
            resource_durations = None

    # Create the summary file and optionally update the project
    summary_file = create_deployment_summary(
        args.project_id,
        args.project_name,
        args.region_id,
        resources,
        args.status,
        args.deployment_time,

        resource_durations,
        not args.no_update  # Auto-update unless --no-update is specified

    )

    print(f"Deployment summary created: {summary_file}")
    if not args.no_update and project_service_available:
        print(
            f"Project {args.project_id} has been automatically updated with deployment data"
        )
    elif args.no_update:
        print("Project was not updated with deployment data (--no-update specified)")
    elif not project_service_available:
        print(
            "Project was not updated with deployment data (ProjectDataService not available)"
        )

    return 0


if __name__ == "__main__":
    sys.exit(main())

#!/bin/bash

# Function to be called on script exit
cleanup() {
    echo ""
    echo "Signal received, stopping background server..."
    # Kill the server process using its PID
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID
        echo "Server with PID $SERVER_PID stopped."
    fi
    exit 0
}

# Trap SIGINT (Ctrl+C) and SIGTERM signals and call the cleanup function
trap cleanup SIGINT SIGTERM

echo ""
echo "Restoring frontend npm packages"
echo ""
cd frontend
npm install
npm install uuid @types/uuid chart.js react-chartjs-2 @azure/storage-blob xlsx
if [ $? -ne 0 ]; then
    echo "Failed to restore frontend npm packages"
    exit $?
fi

echo ""
echo "Building frontend with increased memory allocation"
echo ""
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
# Create and run a Node.js polyfill for crypto
cat > node-polyfill.cjs << 'EOL'
const crypto = require('crypto');
if (!crypto.getRandomValues) {
  crypto.getRandomValues = function getRandomValues(array) {
    const bytes = crypto.randomBytes(array.length);
    for (let i = 0; i < bytes.length; i++) {
      array[i] = bytes[i];
    }
    return array;
  };
}
global.crypto = crypto;
console.log('Node.js crypto polyfill applied');
EOL
node node-polyfill.cjs
npm run build
if [ $? -ne 0 ]; then
    echo "Failed to build frontend"
    exit $?
fi

cd ..
. ./scripts/loadenv.sh

echo ""
if [ ! -z "$RBAC_LOG_LEVEL" ]; then
    echo "RBAC logging level: $RBAC_LOG_LEVEL"
else
    echo "RBAC logging level not set in .env file"
fi

echo ""
echo "Installing Python dependencies from requirements.txt"
echo ""
./.venv/bin/pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Failed to install Python dependencies"
    exit $?
fi

echo ""
echo "Starting main backend with WebSocket support in the background..."
echo ""

# Explicitly expose the API port used for local development
export API_PORT=50505

# === BEGIN CHANGE ===

# Run Gunicorn in the background and capture its Process ID (PID)
./.venv/bin/gunicorn app:app --bind 0.0.0.0:${API_PORT} -k uvicorn.workers.UvicornWorker &
SERVER_PID=$!

echo "Server started in the background with PID: $SERVER_PID"
echo "Press Ctrl+C to stop the server and exit."

# Wait for the background process to finish. The 'trap' will handle the exit.
wait $SERVER_PID

# === END CHANGE ===

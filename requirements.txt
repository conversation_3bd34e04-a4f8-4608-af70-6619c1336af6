# Core Web Frameworks (Hybrid Architecture)
quart==0.20.0                    # Primary ASGI framework for main app
fastapi==0.104.1                 # Secondary framework for RBAC/WebSocket APIs
hypercorn>=0.14.4                # ASGI server for Quart (used in start.sh)

# Session Management
quart-session>=0.8.0             # Server-side session management for Quart

# Azure Identity & Management
azure-identity==1.15.0
azure-mgmt-resource>=21.0.0      # ResourceManagementClient
azure-mgmt-storage>=21.0.0       # StorageManagementClient
azure-mgmt-web>=6.0.0           # WebManagementClient
azure-mgmt-eventgrid>=10.2.0    # EventGridManagementClient
azure-mgmt-search==9.2.0b3      # SearchManagementClient
azure-mgmt-costmanagement>=4.0.0 # CostManagementClient

# Azure Services
azure-search-documents==11.5.2
azure-storage-blob==12.17.0
azure-cosmos==4.5.1
azure-communication-email==1.0.0

# AI/ML Services
openai==1.83.0

# HTTP & WebSocket Libraries
aiohttp==3.9.2
httpx==0.25.2
websockets>=11.0.3               # WebSocket client/server support

# Data Validation & Settings
pydantic>=2.0.0
pydantic-settings==2.2.1

# Testing & Development (for TestClient usage in app.py)
starlette>=0.27.0                # TestClient for FastAPI integration

# Data Processing (PriorityPlot functionality)
pandas>=2.0.0                    # Excel processing
openpyxl>=3.1.0                  # Excel file support

# Authentication
msal>=1.20.0                     # Microsoft Authentication Library

# Configuration
python-dotenv==1.0.0

# Flask Support
Flask>=3.0,<4.0                  # Flask framework (for compatibility)
flask-cors>=4.0.0                # CORS support in Flask

# Additional Libraries
websocket                        # WebSocket client library
requests                         # HTTP library

# Scheduling
aioschedule>=0.5.2

# Form handling
python-multipart>=0.0.5

# Web servers
gunicorn>=20.1.0
uvicorn[standard]>=0.18.0

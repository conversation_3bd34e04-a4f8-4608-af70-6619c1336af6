# Firefox Automation MCP Server

MCP (Model Context Protocol) server that enables Claude Code to interact with and automate Firefox browser instances for local development and UI debugging.

## Features

- **Browser Control**: Launch, navigate, and manage Firefox instances
- **DOM Interaction**: Click, type, and extract content from web pages
- **Visual Debugging**: Capture screenshots of pages or specific elements
- **Script Execution**: Run JavaScript in browser context
- **Console Monitoring**: Access browser console logs and errors
- **Session Management**: Handle multiple browser sessions concurrently

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd firefox-mcp-server

# Install dependencies
npm install

# Build the TypeScript code
npm run build
```

## Prerequisites

- Node.js 16+
- Firefox browser installed
- Playwright (automatically installs browser dependencies)

## Configuration

Add to your Claude Code MCP configuration:

```json
{
  "mcpServers": {
    "firefox-automation": {
      "command": "node",
      "args": ["/path/to/firefox-mcp-server/dist/index.js"],
      "env": {
        "LOG_LEVEL": "info",
        "NODE_ENV": "production"
      }
    }
  }
}
```

## Usage Examples

### Basic Navigation
```
# Create a browser session
create_session({ headless: false })

# Navigate to a URL
navigate({ sessionId: "...", url: "http://localhost:3000" })

# Take a screenshot
take_screenshot({ sessionId: "...", full_page: true, save_path: "./screenshot.png" })
```

### DOM Interaction
```
# Click a button
click_element({ sessionId: "...", selector: "#submit-button" })

# Type text
type_text({ sessionId: "...", selector: "input[name='email']", text: "<EMAIL>" })

# Extract text
get_element_text({ sessionId: "...", selector: "h1.page-title" })
```

### Advanced Usage
```
# Execute JavaScript
execute_script({ 
  sessionId: "...", 
  script: "return document.title + ' - ' + window.location.href" 
})

# Monitor console logs
get_console_logs({ sessionId: "...", level: "error" })

# Wait for element
wait_for_element({ sessionId: "...", selector: ".loading-complete", timeout: 10000 })
```

## Available Tools

1. **create_session** - Create a new browser session
2. **navigate** - Navigate to a URL
3. **click_element** - Click on DOM elements
4. **type_text** - Type text into input fields
5. **get_element_text** - Extract text content
6. **take_screenshot** - Capture screenshots
7. **execute_script** - Run JavaScript code
8. **get_console_logs** - Retrieve console logs
9. **wait_for_element** - Wait for elements to appear
10. **get_page_info** - Get current page information
11. **close_session** - Close browser session

## Development

```bash
# Run in development mode with auto-reload
npm run dev

# Run tests
npm test

# Build for production
npm run build
```

## Security Considerations

- Browser sessions are sandboxed
- File system access restricted to project directories
- Automatic cleanup of idle sessions (30 minutes)
- No remote URL restrictions for development flexibility

## Troubleshooting

### Browser Won't Launch
- Ensure Firefox is installed: `firefox --version`
- Check Playwright installation: `npx playwright install firefox`

### Session Timeout Issues
- Sessions auto-close after 30 minutes of inactivity
- Increase timeout in sessionManager.ts if needed

### Permission Errors
- Ensure write permissions for screenshot save paths
- Check Firefox can be launched by current user

## License

MIT
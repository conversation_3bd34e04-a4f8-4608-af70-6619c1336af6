export const tools = [
  {
    name: 'create_session',
    description: 'Create a new Firefox browser session',
    inputSchema: {
      type: 'object',
      properties: {
        headless: { type: 'boolean', default: false },
        viewport: {
          type: 'object',
          properties: {
            width: { type: 'number', default: 1280 },
            height: { type: 'number', default: 720 }
          }
        }
      }
    }
  },
  {
    name: 'navigate',
    description: 'Navigate to a URL in Firefox',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        url: { type: 'string' },
        timeout: { type: 'number', default: 30000 },
        waitUntil: {
          type: 'string',
          enum: ['load', 'domcontentloaded', 'networkidle'],
          default: 'load'
        }
      },
      required: ['sessionId', 'url']
    }
  },
  {
    name: 'click_element',
    description: 'Click on a DOM element',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        selector: { type: 'string' },
        strategy: {
          type: 'string',
          enum: ['css', 'xpath', 'text', 'role'],
          default: 'css'
        },
        timeout: { type: 'number', default: 5000 }
      },
      required: ['sessionId', 'selector']
    }
  },
  {
    name: 'type_text',
    description: 'Type text into an input element',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        selector: { type: 'string' },
        text: { type: 'string' },
        strategy: {
          type: 'string',
          enum: ['css', 'xpath', 'text', 'role'],
          default: 'css'
        },
        delay: { type: 'number', default: 0 }
      },
      required: ['sessionId', 'selector', 'text']
    }
  },
  {
    name: 'get_element_text',
    description: 'Extract text content from element',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        selector: { type: 'string' },
        strategy: {
          type: 'string',
          enum: ['css', 'xpath', 'text', 'role'],
          default: 'css'
        }
      },
      required: ['sessionId', 'selector']
    }
  },
  {
    name: 'take_screenshot',
    description: 'Capture screenshot of page or element',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        element_selector: { type: 'string' },
        full_page: { type: 'boolean', default: false },
        save_path: { type: 'string' }
      },
      required: ['sessionId']
    }
  },
  {
    name: 'execute_script',
    description: 'Execute JavaScript in browser context',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        script: { type: 'string' },
        args: { type: 'array', items: {} }
      },
      required: ['sessionId', 'script']
    }
  },
  {
    name: 'get_console_logs',
    description: 'Retrieve browser console logs',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        level: {
          type: 'string',
          enum: ['all', 'error', 'warn', 'info', 'debug'],
          default: 'all'
        }
      },
      required: ['sessionId']
    }
  },
  {
    name: 'wait_for_element',
    description: 'Wait for an element to appear',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' },
        selector: { type: 'string' },
        strategy: {
          type: 'string',
          enum: ['css', 'xpath', 'text', 'role'],
          default: 'css'
        },
        timeout: { type: 'number', default: 30000 },
        state: {
          type: 'string',
          enum: ['attached', 'detached', 'visible', 'hidden'],
          default: 'visible'
        }
      },
      required: ['sessionId', 'selector']
    }
  },
  {
    name: 'get_page_info',
    description: 'Get current page information',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' }
      },
      required: ['sessionId']
    }
  },
  {
    name: 'close_session',
    description: 'Close a browser session',
    inputSchema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string' }
      },
      required: ['sessionId']
    }
  }
];
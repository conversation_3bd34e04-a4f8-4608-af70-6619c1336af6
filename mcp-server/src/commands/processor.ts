import { BrowserSessionManager } from '../browser/sessionManager';
import { logger } from '../utils/logger';
import { Page } from 'playwright';
import * as path from 'path';
import * as fs from 'fs/promises';

export class CommandProcessor {
  constructor(private sessionManager: BrowserSessionManager) {}

  async execute(toolName: string, args: any): Promise<any> {
    logger.info(`Executing tool: ${toolName}`, { args });

    switch (toolName) {
      case 'create_session':
        return await this.createSession(args);
      case 'navigate':
        return await this.navigate(args);
      case 'click_element':
        return await this.clickElement(args);
      case 'type_text':
        return await this.typeText(args);
      case 'get_element_text':
        return await this.getElementText(args);
      case 'take_screenshot':
        return await this.takeScreenshot(args);
      case 'execute_script':
        return await this.executeScript(args);
      case 'get_console_logs':
        return await this.getConsoleLogs(args);
      case 'wait_for_element':
        return await this.waitForElement(args);
      case 'get_page_info':
        return await this.getPageInfo(args);
      case 'close_session':
        return await this.closeSession(args);
      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  private async createSession(args: any): Promise<{ sessionId: string }> {
    const sessionId = await this.sessionManager.createSession({
      headless: args.headless,
      viewport: args.viewport
    });
    return { sessionId };
  }

  private async navigate(args: any): Promise<{ success: boolean; url: string }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    await session.page.goto(args.url, {
      timeout: args.timeout || 30000,
      waitUntil: args.waitUntil || 'load'
    });

    return {
      success: true,
      url: session.page.url()
    };
  }

  private async clickElement(args: any): Promise<{ success: boolean }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    const selector = this.buildSelector(args.selector, args.strategy);
    await session.page.click(selector, { timeout: args.timeout || 5000 });

    return { success: true };
  }

  private async typeText(args: any): Promise<{ success: boolean }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    const selector = this.buildSelector(args.selector, args.strategy);
    await session.page.type(selector, args.text, { delay: args.delay || 0 });

    return { success: true };
  }

  private async getElementText(args: any): Promise<{ text: string }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    const selector = this.buildSelector(args.selector, args.strategy);
    const text = await session.page.textContent(selector);

    return { text: text || '' };
  }

  private async takeScreenshot(args: any): Promise<{ 
    success: boolean; 
    path?: string; 
    base64?: string 
  }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    const options: any = {
      fullPage: args.full_page || false
    };

    let screenshot: Buffer;
    
    if (args.element_selector) {
      const selector = this.buildSelector(args.element_selector, args.strategy);
      const element = await session.page.$(selector);
      if (!element) throw new Error('Element not found');
      screenshot = await element.screenshot(options);
    } else {
      screenshot = await session.page.screenshot(options);
    }

    if (args.save_path) {
      const fullPath = path.resolve(args.save_path);
      await fs.writeFile(fullPath, screenshot);
      return { success: true, path: fullPath };
    }

    return {
      success: true,
      base64: screenshot.toString('base64')
    };
  }

  private async executeScript(args: any): Promise<{ result: any }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    const result = await session.page.evaluate(args.script, ...(args.args || []));
    return { result };
  }

  private async getConsoleLogs(args: any): Promise<{ logs: any[] }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    const logs: any[] = [];
    
    session.page.on('console', (msg) => {
      if (args.level === 'all' || msg.type() === args.level) {
        logs.push({
          type: msg.type(),
          text: msg.text(),
          location: msg.location()
        });
      }
    });

    // Wait a bit to collect logs
    await new Promise(resolve => setTimeout(resolve, 1000));

    return { logs };
  }

  private async waitForElement(args: any): Promise<{ found: boolean }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    const selector = this.buildSelector(args.selector, args.strategy);
    
    try {
      await session.page.waitForSelector(selector, {
        timeout: args.timeout || 30000,
        state: args.state || 'visible'
      });
      return { found: true };
    } catch (error) {
      return { found: false };
    }
  }

  private async getPageInfo(args: any): Promise<{
    url: string;
    title: string;
    viewport: { width: number; height: number };
  }> {
    const session = await this.sessionManager.getSession(args.sessionId);
    if (!session) throw new Error('Session not found');

    const viewport = session.page.viewportSize();
    
    return {
      url: session.page.url(),
      title: await session.page.title(),
      viewport: viewport || { width: 0, height: 0 }
    };
  }

  private async closeSession(args: any): Promise<{ success: boolean }> {
    await this.sessionManager.closeSession(args.sessionId);
    return { success: true };
  }

  private buildSelector(selector: string, strategy: string = 'css'): string {
    switch (strategy) {
      case 'xpath':
        return `xpath=${selector}`;
      case 'text':
        return `text=${selector}`;
      case 'role':
        return `role=${selector}`;
      case 'css':
      default:
        return selector;
    }
  }
}
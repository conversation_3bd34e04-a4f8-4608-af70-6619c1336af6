import { Server } from '@modelcontextprotocol/server';
import { BrowserSessionManager } from './browser/sessionManager';
import { CommandProcessor } from './commands/processor';
import { logger } from './utils/logger';
import { tools } from './tools/definitions';

export class FirefoxMCPServer {
  private server: Server;
  private browserManager: BrowserSessionManager;
  private commandProcessor: CommandProcessor;

  constructor() {
    this.server = new Server({
      name: 'firefox-automation-mcp',
      version: '1.0.0',
      description: 'MCP server for Firefox browser automation'
    });

    this.browserManager = new BrowserSessionManager();
    this.commandProcessor = new CommandProcessor(this.browserManager);

    this.setupTools();
  }

  private setupTools(): void {
    tools.forEach(tool => {
      this.server.addTool(tool);
    });

    this.server.on('tool', async (request) => {
      try {
        const result = await this.commandProcessor.execute(
          request.name,
          request.arguments
        );
        return result;
      } catch (error) {
        logger.error(`Tool execution failed: ${error}`);
        throw error;
      }
    });
  }

  async start(): Promise<void> {
    try {
      await this.server.start();
      logger.info('Firefox MCP Server started successfully');
    } catch (error) {
      logger.error(`Failed to start server: ${error}`);
      throw error;
    }
  }

  async shutdown(): Promise<void> {
    await this.browserManager.cleanup();
    await this.server.stop();
  }
}

// Start the server
const server = new FirefoxMCPServer();
server.start().catch(error => {
  logger.error('Fatal error:', error);
  process.exit(1);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Shutting down...');
  await server.shutdown();
  process.exit(0);
});
import { chromium, firefox, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontex<PERSON>, Page } from 'playwright';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

export interface BrowserSession {
  id: string;
  browser: Browser;
  context: BrowserContext;
  page: Page;
  createdAt: Date;
  lastUsed: Date;
}

export class BrowserSessionManager {
  private sessions: Map<string, BrowserSession> = new Map();
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Periodic cleanup of idle sessions
    this.cleanupInterval = setInterval(() => {
      this.cleanupIdleSessions();
    }, 5 * 60 * 1000); // Check every 5 minutes
  }

  async createSession(options: {
    headless?: boolean;
    viewport?: { width: number; height: number };
  } = {}): Promise<string> {
    const sessionId = uuidv4();
    
    try {
      const browser = await firefox.launch({
        headless: options.headless ?? false,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const context = await browser.newContext({
        viewport: options.viewport || { width: 1280, height: 720 },
        acceptDownloads: false,
        ignoreHTTPSErrors: true,
        locale: 'en-US',
        timezoneId: 'America/New_York'
      });

      const page = await context.newPage();

      const session: BrowserSession = {
        id: sessionId,
        browser,
        context,
        page,
        createdAt: new Date(),
        lastUsed: new Date()
      };

      this.sessions.set(sessionId, session);
      logger.info(`Created browser session: ${sessionId}`);

      return sessionId;
    } catch (error) {
      logger.error(`Failed to create browser session: ${error}`);
      throw new Error(`Failed to create browser session: ${error}`);
    }
  }

  async getSession(sessionId: string): Promise<BrowserSession | null> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.lastUsed = new Date();
      return session;
    }
    return null;
  }

  async closeSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      try {
        await session.browser.close();
        this.sessions.delete(sessionId);
        logger.info(`Closed browser session: ${sessionId}`);
      } catch (error) {
        logger.error(`Error closing session ${sessionId}: ${error}`);
      }
    }
  }

  private async cleanupIdleSessions(): Promise<void> {
    const now = Date.now();
    const sessionsToClose: string[] = [];

    for (const [id, session] of this.sessions) {
      const idleTime = now - session.lastUsed.getTime();
      if (idleTime > this.SESSION_TIMEOUT) {
        sessionsToClose.push(id);
      }
    }

    for (const id of sessionsToClose) {
      await this.closeSession(id);
      logger.info(`Cleaned up idle session: ${id}`);
    }
  }

  async cleanup(): Promise<void> {
    clearInterval(this.cleanupInterval);
    
    // Close all sessions
    const closePromises: Promise<void>[] = [];
    for (const sessionId of this.sessions.keys()) {
      closePromises.push(this.closeSession(sessionId));
    }
    
    await Promise.all(closePromises);
    logger.info('All browser sessions closed');
  }

  getActiveSessions(): number {
    return this.sessions.size;
  }
}
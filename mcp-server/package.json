{"name": "firefox-automation-mcp", "version": "1.0.0", "description": "MCP server for Firefox browser automation", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"@modelcontextprotocol/server": "^1.0.0", "playwright": "^1.40.0", "winston": "^3.11.0", "joi": "^17.11.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "typescript": "^5.3.0", "tsx": "^4.6.0", "jest": "^29.7.0", "@types/jest": "^29.5.0"}}
# Local Development Workflow Examples

## Example 1: Debugging React Application Layout

```typescript
// Scenario: User reports that the navigation menu is misaligned on their local React app

// 1. Create a browser session
const { sessionId } = await create_session({ headless: false });

// 2. Navigate to local development server
await navigate({ 
  sessionId, 
  url: "http://localhost:3000",
  waitUntil: "networkidle" 
});

// 3. Take screenshot of current state
await take_screenshot({ 
  sessionId, 
  element_selector: "nav.main-navigation",
  save_path: "./nav-issue-before.png" 
});

// 4. Inspect the navigation structure
const navHTML = await execute_script({
  sessionId,
  script: `
    const nav = document.querySelector('nav.main-navigation');
    return {
      html: nav.outerHTML,
      computedStyles: window.getComputedStyle(nav),
      childCount: nav.children.length
    };
  `
});

// 5. Check for console errors
const { logs } = await get_console_logs({ sessionId, level: "error" });
```

## Example 2: Form Validation Testing

```typescript
// Scenario: Testing form validation on a contact form

// 1. Navigate to form page
await navigate({ sessionId, url: "http://localhost:3000/contact" });

// 2. Try submitting empty form
await click_element({ sessionId, selector: "button[type='submit']" });

// 3. Check for validation messages
const errorMessage = await get_element_text({ 
  sessionId, 
  selector: ".error-message" 
});

// 4. Fill in form with invalid email
await type_text({ 
  sessionId, 
  selector: "input[name='email']", 
  text: "invalid-email" 
});

// 5. Submit and capture validation state
await click_element({ sessionId, selector: "button[type='submit']" });
await take_screenshot({ 
  sessionId, 
  full_page: true, 
  save_path: "./form-validation-error.png" 
});

// 6. Fill with valid data
await type_text({ 
  sessionId, 
  selector: "input[name='email']", 
  text: "<EMAIL>" 
});
await type_text({ 
  sessionId, 
  selector: "textarea[name='message']", 
  text: "Test message content" 
});

// 7. Submit and verify success
await click_element({ sessionId, selector: "button[type='submit']" });
await wait_for_element({ 
  sessionId, 
  selector: ".success-message", 
  timeout: 5000 
});
```

## Example 3: Responsive Design Testing

```typescript
// Scenario: Testing mobile responsiveness

// 1. Create mobile viewport session
const { sessionId: mobileSession } = await create_session({ 
  viewport: { width: 375, height: 667 } // iPhone SE
});

// 2. Navigate to page
await navigate({ sessionId: mobileSession, url: "http://localhost:3000" });

// 3. Check if mobile menu is visible
const mobileMenuVisible = await execute_script({
  sessionId: mobileSession,
  script: `
    const menu = document.querySelector('.mobile-menu-toggle');
    return menu && window.getComputedStyle(menu).display !== 'none';
  `
});

// 4. Open mobile menu
if (mobileMenuVisible) {
  await click_element({ 
    sessionId: mobileSession, 
    selector: ".mobile-menu-toggle" 
  });
  
  // Wait for menu animation
  await wait_for_element({ 
    sessionId: mobileSession, 
    selector: ".mobile-menu.open" 
  });
  
  // Capture mobile menu state
  await take_screenshot({ 
    sessionId: mobileSession, 
    save_path: "./mobile-menu-open.png" 
  });
}

// 5. Test tablet viewport
const { sessionId: tabletSession } = await create_session({ 
  viewport: { width: 768, height: 1024 } // iPad
});

await navigate({ sessionId: tabletSession, url: "http://localhost:3000" });
await take_screenshot({ 
  sessionId: tabletSession, 
  full_page: true, 
  save_path: "./tablet-layout.png" 
});
```

## Example 4: API Integration Debugging

```typescript
// Scenario: Debugging API calls from frontend

// 1. Set up console log monitoring
await navigate({ sessionId, url: "http://localhost:3000/dashboard" });

// 2. Inject network monitoring
await execute_script({
  sessionId,
  script: `
    window.apiCalls = [];
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const start = Date.now();
      try {
        const response = await originalFetch(...args);
        window.apiCalls.push({
          url: args[0],
          method: args[1]?.method || 'GET',
          status: response.status,
          duration: Date.now() - start
        });
        return response;
      } catch (error) {
        window.apiCalls.push({
          url: args[0],
          error: error.message,
          duration: Date.now() - start
        });
        throw error;
      }
    };
  `
});

// 3. Trigger API call by interacting with UI
await click_element({ sessionId, selector: ".refresh-data-btn" });

// 4. Wait for API call to complete
await execute_script({
  sessionId,
  script: "return new Promise(r => setTimeout(r, 2000));"
});

// 5. Get API call results
const apiResults = await execute_script({
  sessionId,
  script: "return window.apiCalls;"
});

// 6. Check for failed API calls
const { logs } = await get_console_logs({ sessionId, level: "error" });
```

## Example 5: Visual Regression Testing

```typescript
// Scenario: Comparing UI changes after code modifications

// 1. Capture baseline screenshots
const pages = [
  { url: "/", name: "home" },
  { url: "/about", name: "about" },
  { url: "/products", name: "products" }
];

for (const page of pages) {
  await navigate({ sessionId, url: `http://localhost:3000${page.url}` });
  await wait_for_element({ sessionId, selector: "body", state: "visible" });
  
  // Let any animations complete
  await execute_script({
    sessionId,
    script: "return new Promise(r => setTimeout(r, 1000));"
  });
  
  await take_screenshot({ 
    sessionId, 
    full_page: true, 
    save_path: `./baseline/${page.name}.png` 
  });
}

// After making code changes, capture new screenshots
for (const page of pages) {
  await navigate({ sessionId, url: `http://localhost:3000${page.url}` });
  await wait_for_element({ sessionId, selector: "body", state: "visible" });
  
  await execute_script({
    sessionId,
    script: "return new Promise(r => setTimeout(r, 1000));"
  });
  
  await take_screenshot({ 
    sessionId, 
    full_page: true, 
    save_path: `./current/${page.name}.png` 
  });
}
```

## Best Practices

1. **Always close sessions** when done to free resources
2. **Use wait_for_element** before interacting with dynamic content
3. **Capture screenshots** at key points for visual debugging
4. **Monitor console logs** to catch JavaScript errors
5. **Test different viewports** for responsive design issues
6. **Use execute_script** for complex DOM queries or state checks
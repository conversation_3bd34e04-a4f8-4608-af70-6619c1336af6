DEPLOY-174 - Implement Polling Loop for Function App Readiness Check
Assignee: Third-Party Senior Azure Developer
Reporter: Azure Mentor Copilot
Priority: High

1. Background
Our resource deployment script, deploy_project_resources.py, exhibits a reliability issue when deploying new projects. After successfully deploying the project's Azure Function App from a container image, the script often fails to retrieve the function access keys, leading to long delays and the appearance of a failed deployment.

Problem: The script immediately attempts to retrieve function keys after the Function App resource is created.
Root Cause (Race Condition): The script uses a static time.sleep(30) to wait for the Function App to become operational. However, the actual startup time for the containerized function app can vary and often exceeds this 30-second window.
Symptoms: This race condition results in the az functionapp function keys list command failing with multiple "Request Timeout" errors, as evidenced in the deployment logs. While the script's retry logic eventually succeeds, the process is unacceptably slow and brittle.
2. Task Description & Acceptance Criteria
The objective is to replace the fixed-time wait with a robust polling loop that actively checks the Function App's status before attempting to retrieve its keys.

Definition of Done:

The time.sleep(30) call before the function key retrieval loop in deploy_project_resources.py is removed.
A polling loop is implemented in its place. This loop must:
Periodically call az functionapp show --name <function_app_name> --resource-group <rg_name> --query state -o tsv.
Continue polling until the returned state is Running.
Include a reasonable delay (e.g., 15 seconds) between polling attempts.
Have a total timeout (e.g., 10 minutes) to prevent an infinite loop in case the app never starts, at which point it should raise an exception.
The overall deployment process is now more resilient to variable Function App startup times.
The "Request Timeout" errors during key retrieval are eliminated from the logs.
3. Implementation Steps
The necessary changes are to be made within the main deployment orchestration script.

File to Modify: deploy_project_resources.py

Current Code to be Replaced:

Python

# In deploy_project_resources.py, within the deploy_project_resources function

                    # ... (inside the 'if deployed_function_app_name:' block)

                    # Add a delay to ensure the function app is fully deployed
                    logging.info(
                        "Waiting 30 seconds for function app to be fully deployed..."
                    )
                    time.sleep(30) # <- THIS LINE SHOULD BE REPLACED

                    for function_name in function_names:
                        # ... attempts to retrieve keys start here
Proposed New Logic:
Implement a polling function or an inline loop to replace the time.sleep(30) call.

Python

# In deploy_project_resources.py, within the deploy_project_resources function

                    # ... (inside the 'if deployed_function_app_name:' block)

                    # NEW: Actively poll for Function App readiness instead of a fixed sleep
                    logging.info(f"Waiting for Function App '{deployed_function_app_name}' to be in 'Running' state...")
                    total_wait_time = 0
                    max_wait_seconds = 600  # 10 minutes timeout
                    poll_interval_seconds = 15
                    is_running = False

                    while total_wait_time < max_wait_seconds:
                        try:
                            check_cmd = [
                                "az", "functionapp", "show",
                                "--name", deployed_function_app_name,
                                "--resource-group", resource_group,
                                "--query", "state", "-o", "tsv"
                            ]
                            result = subprocess.run(check_cmd, capture_output=True, text=True, check=True)
                            state = result.stdout.strip()
                            logging.info(f"Function App state is currently: '{state}'")
                            
                            if state == "Running":
                                logging.info("Function App is running. Proceeding to key retrieval.")
                                is_running = True
                                break
                        except subprocess.CalledProcessError as e:
                            logging.warning(f"Polling failed with an error, will retry: {e.stderr}")
                        except Exception as e:
                            logging.error(f"An unexpected error occurred while polling: {e}")

                        time.sleep(poll_interval_seconds)
                        total_wait_time += poll_interval_seconds

                    if not is_running:
                        error_message = f"Function App '{deployed_function_app_name}' did not start within the {max_wait_seconds // 60} minute timeout."
                        logging.error(error_message)
                        raise RuntimeError(error_message)

                    # The original key retrieval loop follows
                    for function_name in function_names:
                        # ...
4. Expected Outcome
This change will significantly improve the reliability of the project deployment process. Deployments will proceed as soon as the Function App is ready, making them faster on average while also resiliently handling cases where the startup takes longer than expected. This will eliminate a key point of failure and improve the overall stability of the system.
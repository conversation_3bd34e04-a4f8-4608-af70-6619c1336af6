{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "13981893579041863451"}}, "parameters": {"projectId": {"type": "string", "metadata": {"description": "Project ID (UUID)"}}, "projectName": {"type": "string", "metadata": {"description": "Project name"}}, "regionId": {"type": "string", "metadata": {"description": "Region ID (UUID)"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for all resources"}}}, "variables": {"resourceTags": {"project-id": "[parameters('projectId')]", "region-id": "[if(empty(parameters('regionId')), '00000000-0000-0000-0000-************', parameters('regionId'))]", "project-name": "[parameters('projectName')]"}, "sanitizedName": "[replace(replace(toLower(parameters('projectName')), ' ', '-'), '[^a-z0-9-]', '')]", "uniqueSuffix": "[substring(uniqueString(parameters('projectId')), 0, 4)]", "storageNameBase": "[replace(variables('sanitizedName'), '-', '')]", "storageNameTruncated": "[if(greater(length(variables('storageNameBase')), 16), substring(variables('storageNameBase'), 0, 16), variables('storageNameBase'))]", "storageAccountName": "[format('st{0}{1}', variables('storageNameTruncated'), variables('uniqueSuffix'))]", "functionAppName": "[format('func-{0}-{1}', variables('sanitizedName'), variables('uniqueSuffix'))]", "appServicePlanName": "[format('plan-{0}', variables('functionAppName'))]", "searchServiceName": "[format('search-{0}-{1}', variables('sanitizedName'), variables('uniqueSuffix'))]", "eventGridTopicName": "[format('eg-{0}-{1}', variables('sanitizedName'), variables('uniqueSuffix'))]", "uploadsContainerName": "[format('uploads-{0}-{1}', variables('sanitizedName'), variables('uniqueSuffix'))]", "inputContainerName": "[format('input-{0}-{1}', variables('sanitizedName'), variables('uniqueSuffix'))]", "outputContainerName": "[format('output-{0}-{1}', variables('sanitizedName'), variables('uniqueSuffix'))]", "searchIndexName": "[format('project-{0}-index', variables('sanitizedName'))]", "searchIndexerName": "[format('project-{0}-indexer', variables('sanitizedName'))]", "searchDatasourceName": "[format('project-{0}-ds', variables('sanitizedName'))]"}, "resources": [{"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "storage-deployment", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"storageAccountName": {"value": "[variables('storageAccountName')]"}, "location": {"value": "[parameters('location')]"}, "uploadsContainerName": {"value": "[variables('uploadsContainerName')]"}, "inputContainerName": {"value": "[variables('inputContainerName')]"}, "outputContainerName": {"value": "[variables('outputContainerName')]"}, "tags": {"value": "[variables('resourceTags')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "9196367539417876657"}}, "parameters": {"storageAccountName": {"type": "string", "metadata": {"description": "Storage account name"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for the storage account"}}, "uploadsContainerName": {"type": "string", "metadata": {"description": "Container name for uploads"}}, "inputContainerName": {"type": "string", "metadata": {"description": "Container name for input"}}, "outputContainerName": {"type": "string", "metadata": {"description": "Container name for output"}}, "tags": {"type": "object", "defaultValue": {}, "metadata": {"description": "Resource tags"}}}, "resources": [{"type": "Microsoft.Storage/storageAccounts", "apiVersion": "2022-09-01", "name": "[parameters('storageAccountName')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "sku": {"name": "Standard_LRS"}, "kind": "StorageV2", "properties": {"supportsHttpsTrafficOnly": true, "encryption": {"services": {"file": {"keyType": "Account", "enabled": true}, "blob": {"keyType": "Account", "enabled": true}}, "keySource": "Microsoft.Storage"}, "accessTier": "Hot"}}, {"type": "Microsoft.Storage/storageAccounts/blobServices", "apiVersion": "2022-09-01", "name": "[format('{0}/{1}', parameters('storageAccountName'), 'default')]", "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName'))]"]}, {"type": "Microsoft.Storage/storageAccounts/blobServices/containers", "apiVersion": "2022-09-01", "name": "[format('{0}/{1}/{2}', parameters('storageAccountName'), 'default', parameters('uploadsContainerName'))]", "properties": {"publicAccess": "None"}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts/blobServices', parameters('storageAccountName'), 'default')]"]}, {"type": "Microsoft.Storage/storageAccounts/blobServices/containers", "apiVersion": "2022-09-01", "name": "[format('{0}/{1}/{2}', parameters('storageAccountName'), 'default', parameters('inputContainerName'))]", "properties": {"publicAccess": "None"}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts/blobServices', parameters('storageAccountName'), 'default')]"]}, {"type": "Microsoft.Storage/storageAccounts/blobServices/containers", "apiVersion": "2022-09-01", "name": "[format('{0}/{1}/{2}', parameters('storageAccountName'), 'default', parameters('outputContainerName'))]", "properties": {"publicAccess": "None"}, "dependsOn": ["[resourceId('Microsoft.Storage/storageAccounts/blobServices', parameters('storageAccountName'), 'default')]"]}], "outputs": {"storageAccountId": {"type": "string", "value": "[resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName'))]"}, "storageAccountName": {"type": "string", "value": "[parameters('storageAccountName')]"}, "storageConnectionString": {"type": "string", "value": "[format('DefaultEndpointsProtocol=https;AccountName={0};EndpointSuffix={1};AccountKey={2}', parameters('storageAccountName'), environment().suffixes.storage, listKeys(resourceId('Microsoft.Storage/storageAccounts', parameters('storageAccountName')), '2022-09-01').keys[0].value)]"}, "uploadsContainerName": {"type": "string", "value": "[parameters('uploadsContainerName')]"}, "inputContainerName": {"type": "string", "value": "[parameters('inputContainerName')]"}, "outputContainerName": {"type": "string", "value": "[parameters('outputContainerName')]"}}}}}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "search-deployment", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"searchServiceName": {"value": "[variables('searchServiceName')]"}, "location": {"value": "[parameters('location')]"}, "searchIndexName": {"value": "[variables('searchIndexName')]"}, "searchIndexerName": {"value": "[variables('searchIndexerName')]"}, "searchDatasourceName": {"value": "[variables('searchDatasourceName')]"}, "storageConnectionString": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'storage-deployment'), '2022-09-01').outputs.storageConnectionString.value]"}, "inputContainerName": {"value": "[variables('inputContainerName')]"}, "tags": {"value": "[variables('resourceTags')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "11899027099051219433"}}, "parameters": {"searchServiceName": {"type": "string", "metadata": {"description": "Azure AI Search service name"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for the search service"}}, "searchIndexName": {"type": "string", "metadata": {"description": "Search index name"}}, "searchIndexerName": {"type": "string", "metadata": {"description": "Search indexer name"}}, "searchDatasourceName": {"type": "string", "metadata": {"description": "Search datasource name"}}, "storageConnectionString": {"type": "securestring", "metadata": {"description": "Storage connection string"}}, "inputContainerName": {"type": "string", "metadata": {"description": "Input container name"}}, "tags": {"type": "object", "defaultValue": {}, "metadata": {"description": "Resource tags"}}}, "resources": [{"type": "Microsoft.Search/searchServices", "apiVersion": "2022-09-01", "name": "[parameters('searchServiceName')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "sku": {"name": "standard"}, "properties": {"replicaCount": 1, "partitionCount": 1, "hostingMode": "default"}}, {"type": "Microsoft.Resources/deploymentScripts", "apiVersion": "2020-10-01", "name": "search-resources-deployment-script", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "kind": "AzureCLI", "properties": {"azCliVersion": "2.40.0", "timeout": "PT30M", "retentionInterval": "P1D", "environmentVariables": [{"name": "SEARCH_SERVICE_NAME", "value": "[parameters('searchServiceName')]"}, {"name": "SEARCH_API_KEY", "value": "[listAdminKeys(resourceId('Microsoft.Search/searchServices', parameters('searchServiceName')), '2022-09-01').primaryKey]"}, {"name": "SEARCH_INDEX_NAME", "value": "[parameters('searchIndexName')]"}, {"name": "SEARCH_INDEXER_NAME", "value": "[parameters('searchIndexerName')]"}, {"name": "SEARCH_DATASOURCE_NAME", "value": "[parameters('searchDatasourceName')]"}, {"name": "STORAGE_CONNECTION_STRING", "secureValue": "[parameters('storageConnectionString')]"}, {"name": "STORAGE_CONTAINER_NAME", "value": "[parameters('inputContainerName')]"}], "scriptContent": "      #!/bin/bash\n      set -e\n\n      # Create the datasource\n      echo \"Creating datasource: $SEARCH_DATASOURCE_NAME\"\n      az rest --method put \\\n        --uri \"https://$SEARCH_SERVICE_NAME.search.windows.net/datasources/$SEARCH_DATASOURCE_NAME?api-version=2023-11-01\" \\\n        --headers \"Content-Type=application/json\" \"api-key=$SEARCH_API_KEY\" \\\n        --body \"{\n          \\\"name\\\": \\\"$SEARCH_DATASOURCE_NAME\\\",\n          \\\"type\\\": \\\"azureblob\\\",\n          \\\"credentials\\\": {\n            \\\"connectionString\\\": \\\"$STORAGE_CONNECTION_STRING\\\"\n          },\n          \\\"container\\\": {\n            \\\"name\\\": \\\"$STORAGE_CONTAINER_NAME\\\"\n          }\n        }\"\n\n      # Create the index\n      echo \"Creating index: $SEARCH_INDEX_NAME\"\n      az rest --method put \\\n        --uri \"https://$SEARCH_SERVICE_NAME.search.windows.net/indexes/$SEARCH_INDEX_NAME?api-version=2023-11-01\" \\\n        --headers \"Content-Type=application/json\" \"api-key=$SEARCH_API_KEY\" \\\n        --body \"{\n          \\\"name\\\": \\\"$SEARCH_INDEX_NAME\\\",\n          \\\"fields\\\": [\n            {\n              \\\"name\\\": \\\"id\\\",\n              \\\"type\\\": \\\"Edm.String\\\",\n              \\\"key\\\": true,\n              \\\"searchable\\\": true,\n              \\\"filterable\\\": true,\n              \\\"sortable\\\": true,\n              \\\"facetable\\\": false\n            },\n            {\n              \\\"name\\\": \\\"content\\\",\n              \\\"type\\\": \\\"Edm.String\\\",\n              \\\"searchable\\\": true,\n              \\\"filterable\\\": false,\n              \\\"sortable\\\": false,\n              \\\"facetable\\\": false\n            },\n            {\n              \\\"name\\\": \\\"metadata_storage_name\\\",\n              \\\"type\\\": \\\"Edm.String\\\",\n              \\\"searchable\\\": true,\n              \\\"filterable\\\": true,\n              \\\"sortable\\\": true,\n              \\\"facetable\\\": true\n            },\n            {\n              \\\"name\\\": \\\"metadata_storage_path\\\",\n              \\\"type\\\": \\\"Edm.String\\\",\n              \\\"searchable\\\": false,\n              \\\"filterable\\\": true,\n              \\\"sortable\\\": true,\n              \\\"facetable\\\": false\n            },\n            {\n              \\\"name\\\": \\\"metadata_content_type\\\",\n              \\\"type\\\": \\\"Edm.String\\\",\n              \\\"searchable\\\": true,\n              \\\"filterable\\\": true,\n              \\\"sortable\\\": true,\n              \\\"facetable\\\": true\n            }\n          ]\n        }\"\n\n      # Create the indexer\n      echo \"Creating indexer: $SEARCH_INDEXER_NAME\"\n      az rest --method put \\\n        --uri \"https://$SEARCH_SERVICE_NAME.search.windows.net/indexers/$SEARCH_INDEXER_NAME?api-version=2023-11-01\" \\\n        --headers \"Content-Type=application/json\" \"api-key=$SEARCH_API_KEY\" \\\n        --body \"{\n          \\\"name\\\": \\\"$SEARCH_INDEXER_NAME\\\",\n          \\\"dataSourceName\\\": \\\"$SEARCH_DATASOURCE_NAME\\\",\n          \\\"targetIndexName\\\": \\\"$SEARCH_INDEX_NAME\\\",\n          \\\"parameters\\\": {\n            \\\"configuration\\\": {\n              \\\"parsingMode\\\": \\\"default\\\",\n              \\\"indexStorageMetadataOnlyForOversizedDocuments\\\": true\n            }\n          },\n          \\\"schedule\\\": {\n            \\\"interval\\\": \\\"PT5M\\\"\n          }\n        }\"\n\n      echo \"Search resources created successfully\"\n    "}, "dependsOn": ["[resourceId('Microsoft.Search/searchServices', parameters('searchServiceName'))]"]}], "outputs": {"searchServiceName": {"type": "string", "value": "[parameters('searchServiceName')]"}, "searchServiceId": {"type": "string", "value": "[resourceId('Microsoft.Search/searchServices', parameters('searchServiceName'))]"}, "searchApiKey": {"type": "string", "value": "[listAdminKeys(resourceId('Microsoft.Search/searchServices', parameters('searchServiceName')), '2022-09-01').primaryKey]"}, "searchIndexName": {"type": "string", "value": "[parameters('searchIndexName')]"}, "searchIndexerName": {"type": "string", "value": "[parameters('searchIndexerName')]"}, "searchDatasourceName": {"type": "string", "value": "[parameters('searchDatasourceName')]"}}}}, "dependsOn": ["[resourceId('Microsoft.Resources/deployments', 'storage-deployment')]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "function-app-deployment", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"functionAppName": {"value": "[variables('functionAppName')]"}, "appServicePlanName": {"value": "[variables('appServicePlanName')]"}, "location": {"value": "[parameters('location')]"}, "projectId": {"value": "[parameters('projectId')]"}, "projectName": {"value": "[parameters('projectName')]"}, "storageConnectionString": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'storage-deployment'), '2022-09-01').outputs.storageConnectionString.value]"}, "uploadsContainerName": {"value": "[variables('uploadsContainerName')]"}, "inputContainerName": {"value": "[variables('inputContainerName')]"}, "outputContainerName": {"value": "[variables('outputContainerName')]"}, "searchServiceName": {"value": "[variables('searchServiceName')]"}, "searchApiKey": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'search-deployment'), '2022-09-01').outputs.searchApiKey.value]"}, "searchIndexName": {"value": "[variables('searchIndexName')]"}, "searchIndexerName": {"value": "[variables('searchIndexerName')]"}, "searchDatasourceName": {"value": "[variables('searchDatasourceName')]"}, "acrName": {"value": "functionappaiscope"}, "containerImageName": {"value": "functionapp"}, "containerImageTag": {"value": "latest"}, "tags": {"value": "[variables('resourceTags')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "16547093489902069500"}}, "parameters": {"functionAppName": {"type": "string", "metadata": {"description": "Name of the Azure Function App"}}, "appServicePlanName": {"type": "string", "metadata": {"description": "App Service Plan name"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for all resources"}}, "projectId": {"type": "string", "metadata": {"description": "Project ID"}}, "projectName": {"type": "string", "metadata": {"description": "Project name"}}, "storageConnectionString": {"type": "securestring", "metadata": {"description": "Storage connection string"}}, "uploadsContainerName": {"type": "string", "metadata": {"description": "Project storage container for uploads"}}, "inputContainerName": {"type": "string", "metadata": {"description": "Project storage container for input"}}, "outputContainerName": {"type": "string", "metadata": {"description": "Project storage container for output"}}, "searchServiceName": {"type": "string", "metadata": {"description": "Project search service name"}}, "searchApiKey": {"type": "securestring", "metadata": {"description": "Project search API key"}}, "searchIndexName": {"type": "string", "metadata": {"description": "Project search index name"}}, "searchIndexerName": {"type": "string", "metadata": {"description": "Project search indexer name"}}, "searchDatasourceName": {"type": "string", "metadata": {"description": "Project search datasource name"}}, "acrName": {"type": "string", "defaultValue": "functionappaiscope", "metadata": {"description": "Container Registry name"}}, "containerImageName": {"type": "string", "defaultValue": "functionapp", "metadata": {"description": "Container image name"}}, "containerImageTag": {"type": "string", "defaultValue": "latest", "metadata": {"description": "Container image tag"}}, "openAiServiceName": {"type": "string", "defaultValue": "openai-service", "metadata": {"description": "OpenAI service name"}}, "openAiApiKey": {"type": "securestring", "defaultValue": "", "metadata": {"description": "OpenAI API key"}}, "openAiModelDeployment": {"type": "string", "defaultValue": "gpt-35-turbo", "metadata": {"description": "OpenAI model deployment name"}}, "tags": {"type": "object", "defaultValue": {}, "metadata": {"description": "Resource tags"}}}, "resources": [{"type": "Microsoft.Web/serverfarms", "apiVersion": "2022-03-01", "name": "[parameters('appServicePlanName')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "kind": "linux", "sku": {"name": "B1", "tier": "Basic"}, "properties": {"reserved": true}}, {"type": "Microsoft.Web/sites", "apiVersion": "2022-03-01", "name": "[parameters('functionAppName')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "kind": "functionapp,linux,container", "properties": {"serverFarmId": "[resourceId('Microsoft.Web/serverfarms', parameters('appServicePlanName'))]", "siteConfig": {"linuxFxVersion": "[format('DOCKER|{0}.azurecr.io/{1}:{2}', parameters('acrName'), parameters('containerImageName'), parameters('containerImageTag'))]", "alwaysOn": true, "appSettings": [{"name": "DOCKER_REGISTRY_SERVER_URL", "value": "[format('https://{0}.azurecr.io', parameters('acrName'))]"}, {"name": "DOCKER_REGISTRY_SERVER_USERNAME", "value": "[listCredentials(resourceId('Microsoft.ContainerRegistry/registries', parameters('acrName')), '2023-07-01').username]"}, {"name": "DOCKER_REGISTRY_SERVER_PASSWORD", "value": "[listCredentials(resourceId('Microsoft.ContainerRegistry/registries', parameters('acrName')), '2023-07-01').passwords[0].value]"}, {"name": "AzureWebJobsStorage", "value": "[parameters('storageConnectionString')]"}, {"name": "FUNCTIONS_EXTENSION_VERSION", "value": "~4"}, {"name": "FUNCTIONS_WORKER_RUNTIME", "value": "python"}, {"name": "__PROJECT_ID__", "value": "[parameters('projectId')]"}, {"name": "__PROJECT_NAME__", "value": "[parameters('projectName')]"}, {"name": "__PROJECT_UPLOADS_CONTAINER__", "value": "[parameters('uploadsContainerName')]"}, {"name": "__PROJECT_INPUT_CONTAINER__", "value": "[parameters('inputContainerName')]"}, {"name": "__PROJECT_OUTPUT_CONTAINER__", "value": "[parameters('outputContainerName')]"}, {"name": "__PROJECT_SEARCH_INDEX__", "value": "[parameters('searchIndexName')]"}, {"name": "__PROJECT_INDEXER_NAME__", "value": "[parameters('searchIndexerName')]"}, {"name": "__PROJECT_DATASOURCE_NAME__", "value": "[parameters('searchDatasourceName')]"}, {"name": "__PROJECT_FUNCTION_APP_NAME__", "value": "[parameters('functionAppName')]"}, {"name": "__SHARED_STORAGE_CONNECTION_STRING__", "value": "[parameters('storageConnectionString')]"}, {"name": "__SHARED_SEARCH_SERVICE__", "value": "[parameters('searchServiceName')]"}, {"name": "__SHARED_SEARCH_KEY__", "value": "[parameters('searchApiKey')]"}, {"name": "__SHARED_OPENAI_SERVICE__", "value": "[parameters('openAiServiceName')]"}, {"name": "__SHARED_OPENAI_KEY__", "value": "[parameters('openAiApiKey')]"}, {"name": "__SHARED_OPENAI_DEPLOYMENT__", "value": "[parameters('openAiModelDeployment')]"}]}, "httpsOnly": true}, "identity": {"type": "SystemAssigned"}, "dependsOn": ["[resourceId('Microsoft.Web/serverfarms', parameters('appServicePlanName'))]"]}], "outputs": {"functionAppName": {"type": "string", "value": "[parameters('functionAppName')]"}, "functionAppUrl": {"type": "string", "value": "[format('https://{0}', reference(resourceId('Microsoft.Web/sites', parameters('functionAppName')), '2022-03-01').defaultHostName)]"}, "functionAppResourceId": {"type": "string", "value": "[resourceId('Microsoft.Web/sites', parameters('functionAppName'))]"}}}}, "dependsOn": ["[resourceId('Microsoft.Resources/deployments', 'search-deployment')]", "[resourceId('Microsoft.Resources/deployments', 'storage-deployment')]"]}, {"type": "Microsoft.Resources/deployments", "apiVersion": "2022-09-01", "name": "event-grid-deployment", "properties": {"expressionEvaluationOptions": {"scope": "inner"}, "mode": "Incremental", "parameters": {"eventGridTopicName": {"value": "[variables('eventGridTopicName')]"}, "location": {"value": "[parameters('location')]"}, "functionAppResourceId": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'function-app-deployment'), '2022-09-01').outputs.functionAppResourceId.value]"}, "storageAccountId": {"value": "[reference(resourceId('Microsoft.Resources/deployments', 'storage-deployment'), '2022-09-01').outputs.storageAccountId.value]"}, "tags": {"value": "[variables('resourceTags')]"}}, "template": {"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "metadata": {"_generator": {"name": "bicep", "version": "0.34.44.8038", "templateHash": "9338687482945896526"}}, "parameters": {"eventGridTopicName": {"type": "string", "metadata": {"description": "Event Grid Topic name"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for Event Grid resources"}}, "functionAppResourceId": {"type": "string", "metadata": {"description": "Function App resource ID"}}, "storageAccountId": {"type": "string", "metadata": {"description": "Storage account resource ID"}}, "tags": {"type": "object", "defaultValue": {}, "metadata": {"description": "Resource tags"}}}, "resources": [{"type": "Microsoft.EventGrid/topics", "apiVersion": "2022-06-15", "name": "[parameters('eventGridTopicName')]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "properties": {"inputSchema": "EventGridSchema", "publicNetworkAccess": "Enabled"}}, {"type": "Microsoft.EventGrid/systemTopics", "apiVersion": "2022-06-15", "name": "[format('{0}-blob-events', parameters('eventGridTopicName'))]", "location": "[parameters('location')]", "tags": "[parameters('tags')]", "properties": {"source": "[parameters('storageAccountId')]", "topicType": "Microsoft.Storage.StorageAccounts"}}, {"type": "Microsoft.EventGrid/systemTopics/eventSubscriptions", "apiVersion": "2022-06-15", "name": "[format('{0}/{1}', format('{0}-blob-events', parameters('eventGridTopicName')), 'blob-to-function-subscription')]", "properties": {"destination": {"endpointType": "AzureFunction", "properties": {"resourceId": "[parameters('functionAppResourceId')]", "maxEventsPerBatch": 1, "preferredBatchSizeInKilobytes": 64}}, "filter": {"includedEventTypes": ["Microsoft.Storage.BlobCreated", "Microsoft.Storage.BlobDeleted"], "subjectBeginsWith": "/blobServices/default/containers/", "subjectEndsWith": ".pdf", "enableAdvancedFilteringOnArrays": true}, "eventDeliverySchema": "EventGridSchema", "retryPolicy": {"maxDeliveryAttempts": 30, "eventTimeToLiveInMinutes": 1440}}, "dependsOn": ["[resourceId('Microsoft.EventGrid/systemTopics', format('{0}-blob-events', parameters('eventGridTopicName')))]"]}], "outputs": {"eventGridTopicName": {"type": "string", "value": "[parameters('eventGridTopicName')]"}, "eventGridEndpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.EventGrid/topics', parameters('eventGridTopicName')), '2022-06-15').endpoint]"}, "eventGridKey": {"type": "string", "value": "[listKeys(resourceId('Microsoft.EventGrid/topics', parameters('eventGridTopicName')), '2022-06-15').key1]"}}}}, "dependsOn": ["[resourceId('Microsoft.Resources/deployments', 'function-app-deployment')]", "[resourceId('Microsoft.Resources/deployments', 'storage-deployment')]"]}], "outputs": {"deploymentParameters": {"type": "object", "value": {"projectId": "[parameters('projectId')]", "projectName": "[parameters('projectName')]", "regionId": "[parameters('regionId')]", "location": "[parameters('location')]"}}, "storageAccountName": {"type": "string", "value": "[variables('storageAccountName')]"}, "storageAccountId": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'storage-deployment'), '2022-09-01').outputs.storageAccountId.value]"}, "uploadsContainerName": {"type": "string", "value": "[variables('uploadsContainerName')]"}, "inputContainerName": {"type": "string", "value": "[variables('inputContainerName')]"}, "outputContainerName": {"type": "string", "value": "[variables('outputContainerName')]"}, "searchServiceName": {"type": "string", "value": "[variables('searchServiceName')]"}, "searchIndexName": {"type": "string", "value": "[variables('searchIndexName')]"}, "searchIndexerName": {"type": "string", "value": "[variables('searchIndexerName')]"}, "searchDatasourceName": {"type": "string", "value": "[variables('searchDatasourceName')]"}, "functionAppName": {"type": "string", "value": "[variables('functionAppName')]"}, "functionAppUrl": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'function-app-deployment'), '2022-09-01').outputs.functionAppUrl.value]"}, "eventGridTopicName": {"type": "string", "value": "[variables('eventGridTopicName')]"}, "eventGridEndpoint": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'event-grid-deployment'), '2022-09-01').outputs.eventGridEndpoint.value]"}, "eventGridKey": {"type": "string", "value": "[reference(resourceId('Microsoft.Resources/deployments', 'event-grid-deployment'), '2022-09-01').outputs.eventGridKey.value]"}}}
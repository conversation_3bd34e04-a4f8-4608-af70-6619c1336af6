tag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '158'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '496'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0200094b-0000-0d00-0000-683577130000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:root:Found user in database: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'type': 'user', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Set user role from database to: SUPER_ADMIN
INFO:root:Updated user with database info: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Returning authenticated user with role SUPER_ADMIN: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '158'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '496'
    'Date': 'Tue, 27 May 2025 08:26:11 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0200094b-0000-0d00-0000-683577130000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:root:Found user in database: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'type': 'user', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Set user role from database to: SUPER_ADMIN
INFO:root:Updated user with database info: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Returning authenticated user with role SUPER_ADMIN: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '158'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '496'
    'Date': 'Tue, 27 May 2025 08:26:11 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0200094b-0000-0d00-0000-683577130000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:root:Found user in database: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'type': 'user', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Set user role from database to: SUPER_ADMIN
INFO:root:Updated user with database info: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Returning authenticated user with role SUPER_ADMIN: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '158'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '496'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0200094b-0000-0d00-0000-683577130000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:root:Found user in database: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'type': 'user', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Set user role from database to: SUPER_ADMIN
INFO:root:Updated user with database info: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Returning authenticated user with role SUPER_ADMIN: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '158'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '1704'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/roleAssignments/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '169'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:root:DEBUG_ROUTE: Path /api/rbac/projects/2baae374-b419-4c28-bdc6-04879e40b452/teams is being routed to rbac_app.
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/rbac/projects/2baae374-b419-4c28-bdc6-04879e40b452/teams
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:50505/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8'}
INFO:root:✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...QO3sLZg2Kg (truncated, length: 2609)
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
INFO:root:✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...QO3sLZg2Kg (length: 2602)
INFO:root:✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'Bfkgng22VubcImsjSHcJ-DMPXQ6F9zB8O1rcFIlolbo', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'family_name', 'given_name', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'signin_state', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1748336994
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
No body was attached to the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '1719'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/roleAssignments/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '169'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '2544'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/roleAssignments/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '169'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '2567'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/roleAssignments/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '169'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '1704'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/roleAssignments/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '169'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '49'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '49'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '49'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '49'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '49'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
INFO:cosmos_db:Successfully connected to CosmosDB database db_conversation_history
INFO:root:RBAC client initialized successfully.
INFO:root:Using Entra ID authentication
INFO:root:Attempting to use delegated token from request
INFO:root:All request headers: {'host': 'localhost:50505', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJub25jZSI6IkJma2duZzIyVnViY0ltc2pTSGNKLURNUFhRNkY5ekI4TzFyY0ZJbG9sYm8iLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FPn4bFGhxSs1hHw9xVgEvuyB7qy0CjxNus7DcQBGl71jhYdDepVBFGW7ucJi3e8jw3onOfIND4oFUUW7D-Wnu17jn8f5VbgTZSPXyydrCuwsejgKftbu9GmkvMivryK2QFaTGcU50984wrqxDa2JGNqjWb2msWqzfMZLnCSLW950SqdtKrevmFuS99n-9l-0hR_xeRWSsHWMGHWxBZBa8CvqNFSxCFVtVBli9bqcUwzIJuz7p3zaKlZJP0tSOnIgkahgg9T_u3-GQddu1748gt51V2LprzO6zdKzeeDwkMPV35wQNDA4O3qvOUW8hGJsk5XrhVm4vZVLQO3sLZg2Kg', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:50505/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8', 'cookie': 'username-localhost-8888="2|1:0|10:**********|23:username-localhost-8888|200:eyJ1c2VybmFtZSI6ICI0YmM4NTQ1YTBiMDU0ODFiODE5YzhhZDg5MWUyODUwZSIsICJuYW1lIjogIkFub255bW91cyBBbWFsdGhlYSIsICJkaXNwbGF5X25hbWUiOiAiQW5vbnltb3VzIEFtYWx0aGVhIiwgImluaXRpYWxzIjogIkFBIiwgImNvbG9yIjogbnVsbH0=|c44c05e1af9c630cd4a9bb80a08878c9cbd4e7a68b76954e86f2406166b89552"; _xsrf=2|f695e03c|12e3479ae1563555a60c00ce6782a04a|**********; msal.cache.encryption=%7B%22id%22%3A%220196f340-d112-742b-bfda-1e7064f283ec%22%2C%22key%22%3A%22Cr43FIgHjSW8BdNl2DsWDWmiWxvOj_2wfyPojhspzus%22%7D; session=.eJyVU22P4jYY_CtVvu4ZHMdOHKRKDbvZNyAcb3ssX5BjO5BNcMBxIHC6_37OVSdd1VZq83E8fsaZZ-ar09RSO4OvTi62piqk2vKS5Ye6g1gjnIGTpq5MM-4CHAgKMAo8ECLoA49x7sKAu9Ijzicnr-0dZ2_MsR70-2W1y1XvkHNd1VVmKlXmSvZ4dehLGVAaBAxw32MAQyIAJcIHNHSxRyhjxPP6Z9SD3UxmnIEbYIoI8iH85Kg0-ysg2-NPwCcdwPLKPiNKZ9GiTzeR_Ua3hF2fpkUbNDIZIRnp6mn-urlNH8T02ERhFWepboYvm1bBZL2_4-VLcn_F4-g5XcYzfrnNMT1O0agkZiKqMvM2_eUdZKc23_M43hP_ZTyKZ6F3kVm5aS_xHPnJ4oxmhmGxuZnLKbyLcnzQKvWa1yReYjIRMdKPj95ck8KnyST2oGovKv_cJMFOr_13Q_W8He_u9hccGn_F1Adrd9fdw4jT8MKDof8WbdbL42lUFic0qXLPXL9I_SX63XomDywvrQVnpup9r85Z-Uchr7qpO_c7T8Xxlz3Vpu5dciWqS91T0vRdn0pMUwSom0GAGeMg5UEABKECBgJBIlHfTlHsIO2Yt07kt4UV6bBK8Q6EbhjANJMAcYhBIIkHUkkZgJ7HpGAhEdy19Crv0kWo9AULXBBKSQAW1CYBcpsuN-OZjzmTmFvyUctMai3Ftsvrr-q9xd9-Ue_toduLFlEkK4GDD_20Xt0_ulWxSt5P5YQ-bZ_1-X3uJffF7t2_DYf0-WNdRONiF0U9O6D-8TQIcSZIGALkYwZCwlOAJIMgSAVDCBHpZ0FHblJLVoJN9XRZ2oR-djfD9EwNN-3JLVeCLh7I-O28_-Dz1rjznb1jfgj8lypYcmNyS74W4u2ZHPzXlcRyPYPiHNdRZI_PXX-drjDfbPw5l3X9Z5OdgWrK8p939TMl_2bh_1iOVd0epT4wJZWtq9GN_PYdBYlU_g.aDV3FQ.XkwH2fIwnpXCPYLPIiwGulG-Pv4'}
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/rbac/projects/2baae374-b419-4c28-bdc6-04879e40b452/teams
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'connection': 'keep-alive', 'sec-ch-ua-platform': '"Windows"', 'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'sec-fetch-site': 'same-origin', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'referer': 'http://localhost:50505/', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'en-GB,en-US;q=0.9,en;q=0.8'}
INFO:root:✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...QO3sLZg2Kg (truncated, length: 2609)
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
INFO:root:✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...QO3sLZg2Kg (length: 2602)
INFO:root:✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'Bfkgng22VubcImsjSHcJ-DMPXQ6F9zB8O1rcFIlolbo', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'family_name', 'given_name', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'signin_state', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1748336994
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
INFO:root:🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
INFO:root:Calling Microsoft Graph API with delegated token
DEBUG:root:Token: eyJ0eXAiOi...QO3sLZg2Kg (truncated)
DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): graph.microsoft.com:443
DEBUG:urllib3.connectionpool:https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
INFO:root:Successfully retrieved user info from Graph API: Vansh Sial
INFO:root:Delegated token authentication returned user: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'REGULAR_USER'}
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/58e6da71-9ee5-4d86-80c6-31fcf64cae4c/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
No body was attached to the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '496'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0200094b-0000-0d00-0000-683577130000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:root:Found user in database: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'type': 'user', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Set user role from database to: SUPER_ADMIN
INFO:root:Updated user with database info: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:root:Returning authenticated user with role SUPER_ADMIN: {'id': '58e6da71-9ee5-4d86-80c6-31fcf64cae4c', 'name': 'Vansh Sial', 'email': 'vansh.sial_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com', 'role': 'SUPER_ADMIN', 'type': 'user', 'region': None, 'avatar': None, 'created_at': '2025-05-21T09:39:11.231849+00:00', 'updated_at': '2025-05-27T08:25:53.945798+00:00', '_rid': 'cVwbAKDuCuozAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuozAAAAAAAAAA==/', '_etag': '"0200094b-0000-0d00-0000-683577130000"', '_attachments': 'attachments/', '_ts': 1748334355}
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '158'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '2654'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
INFO:azure.core.pipeline.policies.http_logging_policy:Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/roleAssignments/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '169'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.16 (Linux-**********-microsoft-standard-WSL2-x86_64-with-glibc2.36)'
A body is sent with the request
INFO:azure.core.pipeline.policies.http_logging_policy:Response status: 200
Response headers:
    'Content-Length': '49'
    'Date': 'Tue, 27 May 2025 08:26:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
DEBUG:backend.web_sockets.index_blob_status:Active projects with WebSocket connections: set()
DEBUG:backend.web_sockets.index_blob_status:No active projects with connections found to update
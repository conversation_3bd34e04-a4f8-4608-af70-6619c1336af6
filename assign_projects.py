import os
import asyncio
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

async def assign_projects():
    load_dotenv()
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    print('Initializing CosmosDB client...')
    init_success = await cosmos_client.initialize()
    print(f'Initialization success: {init_success}')
    
    if init_success:
        print('Testing connection...')
        success, message = await cosmos_client.ensure()
        print(f'Connection test: {success}, Message: {message}')
        
        if success:
            # Get the user ID
            user_id = '7e43ed9b-4998-468e-bcd1-9b78f7c82977'
            
            # Get all projects
            all_projects = await cosmos_client.get_projects()
            print(f'Found {len(all_projects)} projects')
            
            # Get accessible projects for the user
            user_projects = await cosmos_client.get_accessible_projects(user_id)
            print(f'User currently has access to {len(user_projects)} projects')
            
            # Check if the user has the SUPER_ADMIN role
            user = await cosmos_client.get_user(user_id)
            if user and user.get('role') == 'SUPER_ADMIN':
                print('User has SUPER_ADMIN role. Verifying permissions...')
                
                # Check if there are any project assignments for the user
                project_assignments = []
                query = f"SELECT * FROM c WHERE c.type = 'project_user' AND c.userId = '{user_id}'"
                async for item in cosmos_client.projects_container.query_items(query=query, parameters=[]):
                    project_assignments.append(item)
                
                print(f'Found {len(project_assignments)} project assignments for the user')
                
                # If the user doesn't have any project assignments, create them
                if len(project_assignments) == 0:
                    print('Creating project assignments for the user...')
                    
                    for project in all_projects:
                        project_id = project.get('id')
                        assignment = {
                            'id': f"{project_id}_{user_id}",
                            'type': 'project_user',
                            'projectId': project_id,
                            'userId': user_id,
                            'role': 'PROJECT_OWNER'
                        }
                        
                        try:
                            await cosmos_client.projects_container.create_item(body=assignment)
                            print(f'Assigned project {project_id} to user {user_id}')
                        except Exception as e:
                            print(f'Error assigning project {project_id} to user {user_id}: {e}')
                
                # Verify the user now has access to all projects
                updated_user_projects = await cosmos_client.get_accessible_projects(user_id)
                print(f'User now has access to {len(updated_user_projects)} projects')
            else:
                print(f'User does not have SUPER_ADMIN role: {user}')
            
    await cosmos_client.close()

if __name__ == "__main__":
    asyncio.run(assign_projects())

# Claude Reference Guide for AI Scope

This document provides a comprehensive overview of the AI Scope repository, designed to be a reference for the Claude coding assistant in VS Code.

---

## Project Overview

AI Scope is a full-stack web application for managing AI projects. It provides a user interface for creating, managing, and interacting with AI projects, including features for cost management, user and team management, and resource deployment on Azure.

---

## Technology Stack

### Backend

* **Framework**: Quart (an async version of Flask)
* **Language**: Python
* **Database**: Azure Cosmos DB
* **Authentication**: Azure Entra ID (formerly Azure Active Directory)
* **Deployment**: Azure Bicep, Azure CLI, and Python scripts for orchestration

### Frontend

* **Framework**: React
* **Language**: TypeScript
* **UI Library**: Fluent UI
* **State Management**: React Context API (`AppProvider`, `UserProvider`)
* **Routing**: React Router

---

## Development Setup

### Prerequisites

* Python 3.8+
* Node.js 16+
* Azure CLI
* Access to an Azure subscription with Cosmos DB and Entra ID

### Installation

```bash
# Backend dependencies
cd backend && pip install -r requirements.txt

# Frontend dependencies  
cd frontend && npm install
```

---

## Development Workflow

### Common Tasks

* **Starting the application**: Run `./start.sh` from the root directory to launch both the frontend and backend services for local development
* **Backend development**: Located in `backend/`, uses the Quart framework
* **Frontend development**: Located in `frontend/`, built with React and TypeScript
* **Database operations**: Primarily handled through the ProjectDataService in `backend/services/project_service.py` for interacting with Azure Cosmos DB

### Key File Relationships

* API routes defined in the `backend/routes/` directory correspond to frontend API calls located in `frontend/src/api/`
* Backend data models are defined in `backend/models/`
* The core deployment logic is in `deploy_project_resources.py`, which triggers status updates broadcasted via WebSockets

---

## Code Patterns & Conventions

### Backend (Python/Quart)

* Route handlers generally follow RESTful conventions
* A service layer contains business logic, while routes handle the HTTP requests and responses
* Authentication decorators are used to protect endpoints
* Async/await patterns are used throughout the application for non-blocking I/O

### Frontend (React/TypeScript)

* The UI is built with functional components and hooks
* The React Context API (`AppProvider`, `UserProvider`) is used for global state management
* Custom hooks for encapsulating logic are located in `src/hooks/`
* API calls to the backend are centralized in the `src/api/` directory

---

## Common Issues & Solutions

### Local Development

* **Port conflicts**: The backend typically runs on port 50505, and the frontend on 5173. Ensure these ports are free
* **Authentication issues**: For local development, ensure your Azure CLI is logged in (`az login`) and that the necessary environment variables for Entra ID are set correctly
* **Database connection**: Verify that your Cosmos DB connection strings and keys are correctly configured in your local environment settings

### Deployment

* **Resource deployment failures**: Check the logs generated by the `deploy_project_resources.py` script for detailed error messages
* **Status not updating**: Verify the WebSocket connection is established correctly and that there are no errors in the browser's developer console

---

## Key Files for Development

### Backend Entry Points

* `backend/main.py`: The main application entry point
* `deploy_project_resources.py`: The script that orchestrates Azure resource deployment

### Frontend Entry Points

* `frontend/src/App.tsx`: The application root and router setup
* `frontend/src/pages/ProjectSelector.tsx`: The main dashboard page after login
* `frontend/src/state/AppProvider.tsx`: Manages global application state

### Configuration

* `infra/`: Contains the Bicep templates that define the Azure infrastructure
* `scripts/`: Holds various deployment and utility scripts

---

## Deployment Process

The application is deployed to Azure as an **Azure Web App**. The end-to-end process is automated and orchestrated by the `deploy_project_resources.py` script.

### Deployed Resources

The deployment script creates the following Azure resources for each project:

* **Azure Storage Account**:
   * Uploads Container: For user-uploaded documents
   * Input Container: For template files
   * Output Container: For processed results
* **Azure AI Search**:
   * Search Service, Index, Datasource, and Indexer for RAG capabilities
* **Azure Function App**:
   * Deployed from a container image in Azure Container Registry (ACR)
   * Includes functions for maturity assessment and executive summaries
* **Azure Event Grid**:
   * System Topic and Event Subscription to trigger the Function App when new blobs are created

### Deployment File Workflow

The deployment is a multi-step process involving several key files:

1. **Initiation**: A user creates a project in the UI, which calls the backend API. The API creates a project document in Cosmos DB with `status: "pending"` and triggers the main deployment script
2. **Orchestration** (`deploy_project_resources.py`): This Python script is the main orchestrator. It controls the entire deployment flow from start to finish
3. **Main Infrastructure** (`deploy_project_resources.sh`): The Python script calls this shell script to execute the main Bicep template (`project_resources_with_eventgrid_system.bicep`), which deploys the Storage and Search resources
4. **Function App** (`deploy_function_app_from_acr.sh`): After the main infrastructure is up, this shell script is called to deploy the Function App from ACR using its specific Bicep template (`function_app_acr.bicep`)
5. **Event Grid** (`modules/event_grid.bicep`): Finally, the Event Grid resources are configured to link the storage account to the new Function App

### Cosmos DB Status Updates

The frontend receives live updates on the deployment progress because the orchestration script updates the project's document in Cosmos DB at key milestones.

* **ProjectDataService**: The `deploy_project_resources.py` script uses this service to interact with Cosmos DB
* **`update_deployment_status()`**: This method is called multiple times to update the `deployment_status` field. For example, it's called at the beginning to set the status to `in_progress` and after each major phase
* **`update_project()`**: This method is used to save the names and details of the newly created Azure resources into the project document
* **Final Update**: Once the deployment is complete, `update_final_deployment_summary()` is called to set the final status to `completed` or `failed` and to store a summary of the deployment, including resource names and timing information

---

## Authentication & Authorization

* **Authentication**: The application uses Azure Entra ID for authentication. The frontend uses the `@azure/msal-react` library to handle the MSAL (Microsoft Authentication Library) flow
* **Authorization**: Role-Based Access Control (RBAC) is implemented on the backend. User roles and permissions are stored in Cosmos DB and are used to control access to different features and projects
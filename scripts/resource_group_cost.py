#!/usr/bin/env python
"""
Resource Group Cost Script

A simple script to get the monthly cost of all resources in a specific resource group.

Usage:
    python resource_group_cost.py --subscription-id <SUBSCRIPTION_ID> --resource-group <RESOURCE_GROUP>

Example:
    python resource_group_cost.py --subscription-id 4c1c14a3-de17-4cda-af60-01610fb493f9 --resource-group rg-internal-ai
"""

import argparse
import sys
import logging
import os
from typing import Optional

# Set environment variables to ensure HTTPS is used
os.environ["AZURE_AUTHORITY_HOST"] = "https://login.microsoftonline.com"
os.environ["AZURE_RESOURCE_MANAGER_URL"] = "https://management.azure.com"

from azure.identity import DefaultAzureCredential
from azure.mgmt.costmanagement import CostManagementClient
from azure.mgmt.costmanagement.models import (
    QueryDefinition,
    QueryDataset,
    QueryAggregation,
    QueryGrouping,
    QueryFilter,
    TimeframeType
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_resource_group_cost(subscription_id: str, resource_group: str):
    """
    Get the monthly cost of all resources in a specific resource group.

    Args:
        subscription_id: Azure Subscription ID
        resource_group: Resource Group name
    """
    # 1. Authenticate with Azure
    try:
        logger.info(f"Authenticating with Azure for subscription {subscription_id}...")
        credential = DefaultAzureCredential()
    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        logger.error("Make sure you're logged in with 'az login' or have proper environment variables set.")
        sys.exit(1)

    # 2. Initialize the Cost Management Client
    try:
        logger.info("Initializing Cost Management Client...")
        # Initialize without specifying base_url to use the default
        cost_client = CostManagementClient(credential, subscription_id)
    except Exception as e:
        logger.error(f"Failed to initialize Cost Management Client: {str(e)}")
        sys.exit(1)

    # 3. Build the query to get costs for the resource group
    logger.info(f"Building cost query for resource group: {resource_group}")

    # Create resource group filter - using the correct syntax
    resource_group_filter = QueryFilter(
        dimensions={
            "name": "ResourceGroup",
            "operator": "In",
            "values": [resource_group]
        }
    )

    # Create the query definition for month-to-date costs
    query = QueryDefinition(
        type="ActualCost",  # Use actual costs
        timeframe=TimeframeType.MONTH_TO_DATE,  # Month to date
        dataset=QueryDataset(
            aggregation={
                "totalCost": QueryAggregation(name="PreTaxCost", function="Sum")
            },
            grouping=[
                QueryGrouping(type="Dimension", name="ResourceType")  # Group by resource type
            ],
            filter=resource_group_filter
        )
    )

    # 4. Execute the query
    scope = f"/subscriptions/{subscription_id}"

    try:
        logger.info("Executing cost query...")
        response = cost_client.query.usage(
            scope=scope,
            parameters=query
        )
    except Exception as e:
        logger.error(f"Query execution failed: {str(e)}")

        # Provide more helpful error messages based on common issues
        if "Bearer token authentication is not permitted for non-TLS" in str(e):
            logger.error("\nThis is an HTTPS/TLS configuration issue. Try the following:")
            logger.error("1. Make sure you're logged in with 'az login'")
            logger.error("2. If you're behind a proxy, configure it properly")
            logger.error("3. Try running with these additional environment variables:")
            logger.error("   export AZURE_AUTH_TRACING=1")
            logger.error("   export AZURE_HTTP_USER_AGENT='MyScript/1.0.0'")
        elif "AuthenticationFailed" in str(e):
            logger.error("Authentication failed. Make sure you're properly logged in with 'az login'")
            logger.error("Or set the following environment variables:")
            logger.error("  export AZURE_TENANT_ID=your-tenant-id")
            logger.error("  export AZURE_CLIENT_ID=your-client-id")
            logger.error("  export AZURE_CLIENT_SECRET=your-client-secret")
        elif "does not have authorization" in str(e):
            logger.error("You don't have sufficient permissions to access cost data.")
            logger.error("Make sure your account has at least Cost Management Reader role.")

        sys.exit(1)

    # 5. Process and display the results
    print(f"\n=== Monthly Cost for Resource Group: {resource_group} ===\n")

    if not response.rows:
        print("No cost data found for this resource group.")
        return

    # Print header
    print(f"{'Resource Type':<50} {'Cost ($)':<15}")
    print("-" * 65)

    # Print each resource type and its cost
    total_cost = 0
    for row in response.rows:
        resource_type = row[0]  # ResourceType
        cost = row[1]  # PreTaxCost
        total_cost += cost

        # Format the resource type name to be more readable
        resource_type_display = resource_type.replace("Microsoft.", "")

        print(f"{resource_type_display:<50} ${cost:.2f}")

    # Print total
    print("-" * 65)
    print(f"{'TOTAL':<50} ${total_cost:.2f}")
    print("\n")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Get monthly cost for a resource group")

    parser.add_argument("--subscription-id", required=True, help="Azure Subscription ID")
    parser.add_argument("--resource-group", required=True, help="Resource Group name")

    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()

    get_resource_group_cost(
        subscription_id=args.subscription_id,
        resource_group=args.resource_group
    )


if __name__ == "__main__":
    main()

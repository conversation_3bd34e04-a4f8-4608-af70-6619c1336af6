# Docker Deployment Scripts

This directory contains scripts for building and deploying the AI Scope application using Docker containers on Azure App Service.

## Scripts Overview

### 1. `build_and_push.sh`
Main deployment script that:
- Builds the Docker image using `WebApp.Dockerfile`
- Pushes to Azure Container Registry (ACR)
- Optionally deploys to App Service (interactive prompt)
- Handles all configuration including port settings and startup command

**Usage:**
```bash
./build_and_push.sh
```

### 2. `deploy_webapp.sh`
Deployment-only script for when you already have a built image:
- Deploys a specific image tag to App Service
- Clears custom startup commands
- Sets correct port configuration
- Restarts the App Service

**Usage:**
```bash
./deploy_webapp.sh
```

### 3. `quick_fix.sh`
Recovery script for common deployment issues:
- Resets to latest container image
- Clears problematic startup commands
- Ensures correct port configuration
- Verifies managed identity settings
- Tests health endpoint after restart

**Usage:**
```bash
./quick_fix.sh
```

### 4. `force_rebuild.sh`
Forces a complete rebuild when caching issues occur:
- Creates a timestamp file to invalidate cache
- Builds and pushes fresh image
- Deploys with all correct settings

**Usage:**
```bash
./force_rebuild.sh
```

## Common Configuration

All scripts use these settings:
- **Resource Group**: `rg-internal-ai`
- **ACR Name**: `webappaiscope`
- **App Service**: `ai-scope-app3`
- **Image**: `aiscope/webapp`
- **Port**: `8000`

## Important Notes

1. **Startup Command**: Always use a space `" "` not empty string `""` to clear startup commands
2. **Port Configuration**: Must set `WEBSITES_PORT=8000` for App Service to probe correctly
3. **Dockerfile CMD**: Uses `--bind` instead of `-b` for gunicorn
4. **Authentication**: App Service uses managed identity to pull from ACR

## Troubleshooting

If deployment fails:
1. Run `quick_fix.sh` first
2. Check logs with: `az webapp log tail --resource-group rg-internal-ai --name ai-scope-app3`
3. Verify Python syntax in app.py (common cause of startup failures)
4. Ensure all try blocks have except/finally blocks
5. Check for proper indentation in Python files

## Environment Variables

The scripts expect a `.env` file in the project root. The `build_and_push.sh` script will convert this to JSON format for App Service configuration.
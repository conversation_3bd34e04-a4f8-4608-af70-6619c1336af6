#!/bin/bash
# Deploy the fixed Docker image to App Service

RESOURCE_GROUP="rg-internal-ai"
ACR_NAME="webappaiscope"
APP_NAME="ai-scope-app3"
IMAGE_NAME="aiscope/webapp"
IMAGE_TAG="v2-20250618-153011"

# Update App Service to use the new image
echo "Deploying image ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:${IMAGE_TAG} to ${APP_NAME}..."
az webapp config container set \
  --name ${APP_NAME} \
  --resource-group ${RESOURCE_GROUP} \
  --docker-custom-image-name ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:${IMAGE_TAG}

# Clear any custom startup command to use Dockerfile CMD
echo "Clearing custom startup command..."
az webapp config set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --startup-file " "

# Ensure port is set correctly
echo "Setting WEBSITES_PORT to 8000..."
az webapp config appsettings set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --settings WEBSITES_PORT=8000

# Restart the App Service
echo "Restarting App Service..."
az webapp restart \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME}

echo "Deployment complete! You can monitor the logs with:"
echo "az webapp log tail --resource-group ${RESOURCE_GROUP} --name ${APP_NAME}"
#!/bin/bash
# Force rebuild Docker image (ensures fresh build)
set -e

RESOURCE_GROUP="rg-internal-ai"
ACR_NAME="webappaiscope"
APP_NAME="ai-scope-app3"
IMAGE_NAME="aiscope/webapp"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
IMAGE_TAG="v3-${TIMESTAMP}"

echo "Force rebuilding Docker image..."
echo "Note: az acr build doesn't support --no-cache, but always builds fresh from source"

# Add timestamp to a file to force cache invalidation
echo "BUILD_TIME=${TIMESTAMP}" > .buildtime

az acr build \
  --registry ${ACR_NAME} \
  --image ${IMAGE_NAME}:${IMAGE_TAG} \
  --image ${IMAGE_NAME}:latest \
  --file WebApp.Dockerfile \
  . || {
    echo "Failed to build and push Docker image"
    rm -f .buildtime
    exit 1
}

# Clean up
rm -f .buildtime

echo "Docker image built successfully!"
echo "Deploying to App Service..."

az webapp config container set \
  --name ${APP_NAME} \
  --resource-group ${RESOURCE_GROUP} \
  --docker-custom-image-name ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:${IMAGE_TAG}

# Clear any custom startup command
echo "Clearing custom startup command..."
az webapp config set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --startup-file " "

# Ensure port is set correctly
echo "Setting WEBSITES_PORT..."
az webapp config appsettings set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --settings WEBSITES_PORT=8000

az webapp restart \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME}

echo "Deployment complete!"
echo "Monitor logs with: az webapp log tail --resource-group ${RESOURCE_GROUP} --name ${APP_NAME}"
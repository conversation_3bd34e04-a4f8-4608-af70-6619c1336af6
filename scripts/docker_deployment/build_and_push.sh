#!/bin/bash
# Build and push Docker image to Azure Container Registry
# This script builds the AI Scope application Docker image and pushes it to ACR

# Set error handling
set -e

# Variables
RESOURCE_GROUP="rg-internal-ai"
ACR_NAME="webappaiscope"
APP_NAME="ai-scope-app3"
IMAGE_NAME="aiscope/webapp"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
IMAGE_TAG="v2-${TIMESTAMP}"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "WebApp.Dockerfile" ]; then
    print_error "WebApp.Dockerfile not found. Please run this script from the repository root."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Environment variables will need to be set separately."
fi

print_status "Starting Docker image build and push process..."
print_status "ACR: ${ACR_NAME}"
print_status "Image: ${IMAGE_NAME}"
print_status "Tag: ${IMAGE_TAG}"

# Build and push the Docker image
print_status "Building Docker image and pushing to ACR..."
print_status "This may take several minutes..."

az acr build \
  --registry ${ACR_NAME} \
  --image ${IMAGE_NAME}:${IMAGE_TAG} \
  --image ${IMAGE_NAME}:latest \
  --file WebApp.Dockerfile \
  . || {
    print_error "Failed to build and push Docker image"
    exit 1
}

print_status "Docker image built and pushed successfully!"
print_status "Image tags:"
print_status "  - ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:${IMAGE_TAG}"
print_status "  - ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:latest"

# Ask if user wants to deploy
echo ""
read -p "Do you want to deploy this image to App Service now? (y/N) " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Deploying to App Service..."
    
    # Update App Service to use the new image
    az webapp config container set \
      --name ${APP_NAME} \
      --resource-group ${RESOURCE_GROUP} \
      --docker-custom-image-name ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:${IMAGE_TAG} || {
        print_error "Failed to update App Service container configuration"
        exit 1
    }
    
    # Clear any custom startup command to use Dockerfile CMD
    az webapp config set \
      --resource-group ${RESOURCE_GROUP} \
      --name ${APP_NAME} \
      --startup-file " " || {
        print_warning "Failed to clear startup command"
    }
    
    # Ensure port is set correctly
    az webapp config appsettings set \
      --resource-group ${RESOURCE_GROUP} \
      --name ${APP_NAME} \
      --settings WEBSITES_PORT=8000 || {
        print_warning "Failed to set WEBSITES_PORT"
    }
    
    # Restart the App Service
    print_status "Restarting App Service..."
    az webapp restart \
      --resource-group ${RESOURCE_GROUP} \
      --name ${APP_NAME} || {
        print_error "Failed to restart App Service"
        exit 1
    }
    
    print_status "Deployment complete!"
    print_status "You can monitor the logs with:"
    echo "  az webapp log tail --resource-group ${RESOURCE_GROUP} --name ${APP_NAME}"
else
    print_status "Skipping deployment."
    print_status "To deploy this image later, use:"
    echo "  az webapp config container set \\"
    echo "    --name ${APP_NAME} \\"
    echo "    --resource-group ${RESOURCE_GROUP} \\"
    echo "    --docker-custom-image-name ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:${IMAGE_TAG}"
fi

print_status "Build and push process completed successfully!"
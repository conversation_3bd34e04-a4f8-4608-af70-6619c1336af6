#!/bin/bash
# Quick fix script for common Docker deployment issues
# This script resets the App Service to a working state

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Variables
RESOURCE_GROUP="rg-internal-ai"
ACR_NAME="webappaiscope"
APP_NAME="ai-scope-app3"
IMAGE_NAME="aiscope/webapp"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

print_error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

print_status "Starting quick fix for AI Scope application..."

# Reset to latest image
print_status "Resetting to latest container image..."
az webapp config container set \
  --name ${APP_NAME} \
  --resource-group ${RESOURCE_GROUP} \
  --docker-custom-image-name ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:latest || {
    print_error "Failed to update container image"
    exit 1
}

# Clear startup command (use space to effectively clear)
print_status "Clearing custom startup command..."
az webapp config set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --startup-file " " || {
    print_warning "Failed to clear startup command"
}

# Ensure port setting
print_status "Setting WEBSITES_PORT to 8000..."
az webapp config appsettings set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --settings WEBSITES_PORT=8000 || {
    print_warning "Failed to set WEBSITES_PORT"
}

# Ensure managed identity is configured for ACR
print_status "Verifying managed identity configuration..."
az webapp config set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --generic-configurations '{"acrUseManagedIdentityCreds": true}' || {
    print_warning "Failed to set ACR managed identity configuration"
}

# Restart App Service
print_status "Restarting App Service..."
az webapp restart \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} || {
    print_error "Failed to restart App Service"
    exit 1
}

print_status "Quick fix complete!"
print_status "Waiting 30 seconds for app to start..."
sleep 30

# Check app status
print_status "Checking application status..."
STATE=$(az webapp show \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --query state -o tsv)

if [ "$STATE" == "Running" ]; then
    print_status "App Service is running!"
    
    # Test health endpoint
    print_status "Testing health endpoint..."
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" -I https://${APP_NAME}.azurewebsites.net/health)
    
    if [ "$HTTP_CODE" == "401" ]; then
        print_status "Health endpoint returned 401 (authentication required) - This is expected!"
        print_status "Application is running correctly."
    elif [ "$HTTP_CODE" == "200" ]; then
        print_status "Health endpoint returned 200 - Application is healthy!"
    else
        print_warning "Health endpoint returned unexpected status code: $HTTP_CODE"
    fi
else
    print_error "App Service is not in Running state. Current state: $STATE"
fi

print_status "You can monitor logs with:"
echo "  az webapp log tail --resource-group ${RESOURCE_GROUP} --name ${APP_NAME}"

print_status "To view recent logs:"
echo "  az webapp log download --resource-group ${RESOURCE_GROUP} --name ${APP_NAME} --log-file logs.zip"
echo "  unzip -p logs.zip LogFiles/*_default_docker.log | tail -100"
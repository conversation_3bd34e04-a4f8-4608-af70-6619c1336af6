#!/bin/bash
# <PERSON><PERSON>t to assign A<PERSON>r<PERSON>ull role to webap<PERSON>'s managed identity for ACR access

set -e

WEBAPP_NAME="ai-scope-app3"
RG="rg-internal-ai"
ACR_NAME="webappaiscope"

echo "Assigning AcrPull role to webapp ${WEBAPP_NAME} for ACR ${ACR_NAME}..."

# Enable managed identity for the webapp if not already enabled
echo "Enabling managed identity for webapp..."
az webapp identity assign \
  --name $WEBAPP_NAME \
  --resource-group $RG

# Get the Web App's principal object ID
echo "Getting webapp's principal ID..."
PRINCIPAL_ID=$(az webapp identity show \
  --name $WEBAPP_NAME \
  --resource-group $RG \
  --query principalId -o tsv)

if [ -z "$PRINCIPAL_ID" ]; then
  echo "ERROR: Could not get principal ID for webapp $WEBAPP_NAME"
  exit 1
fi

echo "Principal ID: $PRINCIPAL_ID"

# Get the ACR's resource ID
echo "Getting ACR resource ID..."
ACR_RESOURCE_ID=$(az acr show \
  --name $ACR_NAME \
  --resource-group $RG \
  --query id -o tsv)

if [ -z "$ACR_RESOURCE_ID" ]; then
  echo "ERROR: Could not get resource ID for ACR $ACR_NAME"
  exit 1
fi

echo "ACR Resource ID: $ACR_RESOURCE_ID"

# Assign the AcrPull role
echo "Assigning AcrPull role..."
az role assignment create \
  --assignee $PRINCIPAL_ID \
  --role AcrPull \
  --scope $ACR_RESOURCE_ID

echo "Role assignment completed successfully!"
echo "The webapp $WEBAPP_NAME can now pull images from ACR $ACR_NAME"

 
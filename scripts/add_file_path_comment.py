# ./scripts/add_file_path_comment.py
import os

def add_file_path_comment(directory):
    # Define comment syntax for each file extension
    comment_syntax = {
        '.py': lambda path: f'# {path}\n',
        '.js': lambda path: f'// {path}\n',
        '.ts': lambda path: f'// {path}\n',
        '.tsx': lambda path: f'// {path}\n',
        '.jsx': lambda path: f'// {path}\n',
        '.css': lambda path: f'/* {path} */\n',
        '.html': lambda path: f'<!-- {path} -->\n',
        # Add more extensions and their comment syntaxes here if needed
    }

    for dirpath, _, files in os.walk(directory):
        for file_name in files:
            file_ext = os.path.splitext(file_name)[1]
            if file_ext in comment_syntax:
                file_path = os.path.join(dirpath, file_name)
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        content = file.read()

                    # Check if the file already starts with the file path comment
                    comment = comment_syntax[file_ext](file_path)
                    if not content.startswith(comment.strip()):
                        new_content = comment + content
                        with open(file_path, 'w', encoding='utf-8') as file:
                            file.write(new_content)
                        print(f'Added comment to {file_path}')
                    else:
                        print(f'Comment already exists in {file_path}, skipping.')
                except Exception as e:
                    print(f'Error processing {file_path}: {e}')

# Replace 'your_repo_directory' with the path to your repository
if __name__ == "__main__":
    repo_directory = '.'  # Update this path accordingly
    if os.path.isdir(repo_directory):
        add_file_path_comment(repo_directory)
    else:
        print(f'The directory {repo_directory} does not exist.')

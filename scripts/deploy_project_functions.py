import argparse
import os
import requests
import json
import shutil
import subprocess
import tempfile
import sys
import logging
import time
import re
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


# --- Configuration ---
# Read shared secrets/config from .env file
def load_env_file(env_file_path):
    """Load environment variables from a .env file."""
    env_vars = {}
    try:
        with open(env_file_path, "r") as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue
                key, value = line.split("=", 1)
                env_vars[key] = value
        return env_vars
    except Exception as e:
        logging.error(f"Error loading .env file: {e}")
        return {}


# Load environment variables from .env file
env_file_path = os.path.join(os.getcwd(), ".env")
env_vars = load_env_file(env_file_path)

# Set up shared configuration variables
SHARED_CONFIG_ENV_VARS = {
    # Azure AI Search Configuration
    "__SHARED_AI_SEARCH_API_KEY__": env_vars.get("AZURE_SEARCH_KEY"),
    "__SHARED_AI_SEARCH_ENDPOINT__": (
        f"https://{env_vars.get('AZURE_SEARCH_SERVICE')}.search.windows.net"
        if env_vars.get("AZURE_SEARCH_SERVICE")
        else None
    ),
    "__SHARED_SEARCH_SERVICE_NAME__": env_vars.get("AZURE_SEARCH_SERVICE"),
    # Azure OpenAI Configuration
    "__SHARED_OPENAI_API_KEY__": env_vars.get("AZURE_OPENAI_KEY"),
    "__SHARED_OPENAI_ENDPOINT__": env_vars.get("AZURE_OPENAI_ENDPOINT"),
    "__SHARED_OPENAI_VERSION__": env_vars.get(
        "AZURE_OPENAI_VERSION", "2024-02-01"
    ),  # Default if not set
    "__SHARED_OPENAI_DEPLOYMENT_ID__": env_vars.get("AZURE_OPENAI_MODEL"),
    "__SHARED_OPENAI_DEPLOYMENT_ID_2__": env_vars.get(
        "AZURE_OPENAI_MODEL"
    ),  # Assuming same model for secondary, adjust if needed
    # Azure Storage Configuration
    "__SHARED_AZURE_WEBJOBS_STORAGE__": env_vars.get(
        "AZURE_WEBJOBS_STORAGE"
    ),  # Connection string for the Function App's storage
    "__SHARED_STORAGE_ACCOUNT_NAME__": env_vars.get("AZURE_STORAGE_ACCOUNT_NAME"),
    "__SHARED_STORAGE_ACCOUNT_KEY__": env_vars.get("AZURE_STORAGE_ACCOUNT_KEY"),
    # Azure Function App Configuration
    "__SHARED_FUNCTION_APP_NAME__": env_vars.get("AZURE_FUNCTION_APP_NAME"),
    # Add others if needed
}

# Fall back to environment variables if not found in .env file
for key, value in SHARED_CONFIG_ENV_VARS.items():
    if value is None:
        env_key = key.strip("_").replace("SHARED_", "")
        SHARED_CONFIG_ENV_VARS[key] = os.environ.get(env_key)

# Define which variables are mandatory
MANDATORY_ENV_VARS = [
    "__SHARED_AI_SEARCH_API_KEY__",
    "__SHARED_AI_SEARCH_ENDPOINT__",
    "__SHARED_OPENAI_API_KEY__",
    "__SHARED_OPENAI_ENDPOINT__",
    "__SHARED_OPENAI_DEPLOYMENT_ID__",
    "__SHARED_AZURE_WEBJOBS_STORAGE__",
]

# Check for missing mandatory shared environment variables
missing_vars = [
    key for key in MANDATORY_ENV_VARS if SHARED_CONFIG_ENV_VARS.get(key) is None
]
if missing_vars:
    logging.error(
        f"Missing required environment variables for placeholders: {missing_vars}"
    )
    logging.warning(
        "Will continue with deployment, but functions may not work correctly without these variables."
    )
    # Don't exit, just warn and continue

# --- Helper Functions ---


def fetch_project_config(base_url, project_id, auth_token=None):
    """Fetches project-specific configuration from the API."""
    config_url = f"{base_url.rstrip('/')}/api/projects/{project_id}/function-config"
    headers = {}
    if auth_token:
        # Assuming a simple Bearer token, adjust if using Msal etc.
        headers["Authorization"] = f"Bearer {auth_token}"
        headers["X-Forwarded-Authorization"] = (
            f"Bearer {auth_token}"  # Common for proxies
        )

    logging.info(f"Fetching config from: {config_url}")
    try:
        response = requests.get(config_url, headers=headers, timeout=30)
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        config_data = response.json()
        logging.info(f"Successfully fetched config for project {project_id}")
        return config_data
    except requests.exceptions.RequestException as e:
        logging.error(f"Failed to fetch project config: {e}")
        if hasattr(e, "response") and e.response is not None:
            logging.error(f"Response status: {e.response.status_code}")
            logging.error(f"Response body: {e.response.text}")
        return None


def replace_placeholders(file_path, replacements):
    """Replaces placeholders in a given file."""
    try:
        with open(file_path, "r") as f:
            content = f.read()

        original_content = content
        for placeholder, value in replacements.items():
            if value is None:
                logging.warning(
                    f"Value for placeholder '{placeholder}' is None. Skipping replacement."
                )
                continue
            content = content.replace(placeholder, str(value))

        if content == original_content:
            logging.warning(
                f"No placeholders were replaced in {file_path}. Check placeholders and values."
            )
        else:
            logging.info(f"Placeholders replaced in {file_path}")

        with open(file_path, "w") as f:
            f.write(content)
        return True
    except Exception as e:
        logging.error(f"Error replacing placeholders in {file_path}: {e}")
        return False


def copy_function_source(source_dir, dest_dir):
    """Copies the Azure Functions source code, respecting .funcignore."""
    source_path = Path(source_dir)
    dest_path = Path(dest_dir)
    ignore_patterns = []

    # Read .funcignore if it exists
    funcignore_path = source_path / ".funcignore"
    if funcignore_path.exists():
        logging.info(f"Reading ignore patterns from {funcignore_path}")
        with open(funcignore_path, "r") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#"):
                    # Basic handling: treat as glob patterns relative to source_dir
                    # More robust parsing might be needed for complex .funcignore rules
                    ignore_patterns.append(line)
    else:
        logging.warning(f".funcignore not found in {source_dir}. Copying all files.")

    # Add common ignores if not present
    common_ignores = [
        ".venv",
        ".git",
        ".vscode",
        "__pycache__",
        "*.pyc",
        "*.pyo",
        ".env",
        "local.settings.json",
        "*.template",
        "azurite",
        "*.log",
    ]
    for pattern in common_ignores:
        if pattern not in ignore_patterns:
            ignore_patterns.append(pattern)

    logging.info(f"Effective ignore patterns: {ignore_patterns}")

    try:
        shutil.copytree(
            source_path,
            dest_path,
            ignore=shutil.ignore_patterns(*ignore_patterns),
            dirs_exist_ok=True,
        )
        logging.info(f"Copied function source from {source_dir} to {dest_dir}")
        return True
    except Exception as e:
        logging.error(f"Error copying function source: {e}")
        return False


def validate_function_app_exists(
    function_app_name, resource_group="rg-internal-ai", max_retries=5, retry_delay=10
):
    """Validates that the Azure Function App exists.

    Args:
        function_app_name (str): The name of the Azure Function App.
        resource_group (str): The resource group containing the Function App.
        max_retries (int): Maximum number of retries to check if the Function App exists.
        retry_delay (int): Delay in seconds between retries.

    Returns:
        bool: True if the Function App exists, False otherwise.
    """
    for attempt in range(1, max_retries + 1):
        try:
            # Use Azure CLI to check if the function app exists
            logging.info(
                f"Checking if Azure Function App '{function_app_name}' exists in resource group '{resource_group}' (attempt {attempt}/{max_retries})"
            )
            result = subprocess.run(
                [
                    "az",
                    "functionapp",
                    "show",
                    "--name",
                    function_app_name,
                    "--resource-group",
                    resource_group,
                    "--query",
                    "name",
                    "--output",
                    "tsv",
                ],
                capture_output=True,
                text=True,
                check=False,  # Don't raise an exception if the command fails
            )

            if result.returncode == 0 and result.stdout.strip() == function_app_name:
                logging.info(f"Azure Function App '{function_app_name}' exists")
                return True
            else:
                logging.warning(
                    f"Azure Function App '{function_app_name}' does not exist or is not accessible (attempt {attempt}/{max_retries})"
                )
                logging.warning(f"Error: {result.stderr.strip()}")

                if attempt < max_retries:
                    logging.info(f"Waiting {retry_delay} seconds before retrying...")
                    time.sleep(retry_delay)
                    # Increase delay for next retry (exponential backoff)
                    retry_delay *= 1.5
                else:
                    logging.error(
                        f"Azure Function App '{function_app_name}' does not exist after {max_retries} attempts"
                    )
                    return False
        except Exception as e:
            logging.error(f"Error validating Function App existence: {e}")
            if attempt < max_retries:
                logging.info(f"Waiting {retry_delay} seconds before retrying...")
                time.sleep(retry_delay)
                # Increase delay for next retry (exponential backoff)
                retry_delay *= 1.5
            else:
                return False

    return False


def validate_function_directory(deploy_dir):
    """Validates that the deployment directory contains the required functions."""
    required_functions = [
        "HttpTriggerAppMaturityAssessment",
        "HttpTriggerAppExecutiveSummary",
        "EventGridTriggerBlobIndexer",
    ]

    missing_functions = []
    for func_name in required_functions:
        func_dir = Path(deploy_dir) / func_name
        if not func_dir.exists() or not func_dir.is_dir():
            missing_functions.append(func_name)

    if missing_functions:
        logging.error(f"Missing required function directories: {missing_functions}")
        return False

    logging.info(f"All required function directories found: {required_functions}")
    return True


def deploy_functions(
    deploy_dir,
    function_app_name,
    resource_group="rg-internal-ai",
    max_retries=3,
    retry_delay=30,
):
    """Deploys the functions using Azure Functions Core Tools.

    Args:
        deploy_dir (str): Directory containing the functions to deploy.
        function_app_name (str): Name of the Azure Function App to deploy to.
        resource_group (str): The resource group containing the Function App.
        max_retries (int): Maximum number of retries for deployment.
        retry_delay (int): Delay in seconds between retries.

    Returns:
        bool: True if deployment was successful, False otherwise.
    """
    # Ensure Azure CLI is logged in
    try:
        # Check if already logged in
        account_check = subprocess.run(
            ["az", "account", "show"], capture_output=True, text=True, check=False
        )
        if account_check.returncode != 0:
            logging.warning(
                "Not logged in to Azure CLI. Attempting to use default credentials..."
            )
            # Try to login with managed identity if available
            login_result = subprocess.run(
                ["az", "login", "--identity"],
                capture_output=True,
                text=True,
                check=False,
            )
            if login_result.returncode != 0:
                logging.error(
                    "Failed to login with managed identity. Azure CLI authentication required."
                )
                logging.error(f"Error: {login_result.stderr.strip()}")
                # Continue anyway, as the user might be logged in through other means
    except Exception as e:
        logging.error(f"Error checking Azure CLI login status: {e}")
        # Continue anyway, as the error might be unrelated to authentication

    # Validate the function app exists with retries
    if not validate_function_app_exists(
        function_app_name, resource_group, max_retries=5, retry_delay=20
    ):
        logging.error(
            f"Cannot deploy to non-existent Function App: {function_app_name} in resource group {resource_group}"
        )
        logging.info(f"Attempting to continue with deployment anyway...")
        # Don't return False here, try to deploy anyway

    # Validate the deployment directory contains all required functions
    if not validate_function_directory(deploy_dir):
        logging.error(f"Deployment directory is missing required functions")
        return False

    # Log the contents of the deployment directory
    logging.info(f"Contents of deployment directory {deploy_dir}:")
    try:
        for root, dirs, files in os.walk(deploy_dir):
            level = root.replace(deploy_dir, "").count(os.sep)
            indent = " " * 4 * level
            logging.info(f"{indent}{os.path.basename(root)}/")
            sub_indent = " " * 4 * (level + 1)
            for f in files:
                logging.info(f"{sub_indent}{f}")
    except Exception as e:
        logging.error(f"Error listing deployment directory: {e}")

    # Prepare the deployment command
    deploy_command = [
        "func",
        "azure",
        "functionapp",
        "publish",
        function_app_name,
        "--resource-group",
        resource_group,
        "--force",  # Overwrite existing deployment
        "--build-native-deps",  # Build native dependencies
        "--build",  # Force build
        "--python",  # Specify Python runtime
    ]

    # Alternative deployment command with remote build
    alt_deploy_command = [
        "func",
        "azure",
        "functionapp",
        "publish",
        function_app_name,
        "--resource-group",
        resource_group,
        "--force",  # Overwrite existing deployment
        "--build-remote",  # Use remote build on Azure
    ]

    # Try deployment with retries
    for attempt in range(1, max_retries + 1):
        logging.info(f"Deployment attempt {attempt}/{max_retries}")

        # For first attempt, use the standard command
        # For subsequent attempts, alternate between standard and alternative commands
        current_command = deploy_command if attempt % 2 == 1 else alt_deploy_command

        logging.info(f"Running deployment command: {' '.join(current_command)}")
        logging.info(f"Deployment directory: {deploy_dir}")

        try:
            # Run the command from the deployment directory with real-time output processing
            process = subprocess.Popen(
                current_command,
                cwd=deploy_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,  # Line buffered
            )

            # Initialize variables to track deployment progress
            stdout_lines = []
            stderr_lines = []
            deployment_stage = "initializing"
            deployment_progress = 0
            function_app_url = None
            deployed_functions = []

            # Process output in real-time
            while True:
                stdout_line = process.stdout.readline()
                stderr_line = process.stderr.readline()

                # Process stdout
                if stdout_line:
                    line = stdout_line.strip()
                    stdout_lines.append(line)
                    logging.info(f"DEPLOY: {line}")

                    # Parse output to detect deployment stages and progress
                    if "[STATUS]" in line:
                        status_msg = line.split("[STATUS]", 1)[1].strip()
                        logging.info(f"Status update: {status_msg}")

                    if "[PROGRESS]" in line:
                        progress_parts = (
                            line.split("[PROGRESS]", 1)[1].strip().split(":")
                        )
                        if len(progress_parts) == 2:
                            deployment_stage = progress_parts[0]
                            try:
                                deployment_progress = int(progress_parts[1])
                                logging.info(
                                    f"Progress update: {deployment_stage} - {deployment_progress}%"
                                )
                            except ValueError:
                                pass

                    if "[RESOURCE]" in line:
                        resource_parts = (
                            line.split("[RESOURCE]", 1)[1].strip().split("=")
                        )
                        if len(resource_parts) == 2:
                            resource_name = resource_parts[0]
                            resource_value = resource_parts[1]
                            logging.info(
                                f"Resource: {resource_name} = {resource_value}"
                            )

                            # Extract function app URL if present
                            if (
                                resource_name == "FUNCTION_APP_URL"
                                and "https://" in resource_value
                            ):
                                function_app_url = resource_value

                    # Look for function names in output
                    if "HttpTrigger" in line:
                        func_match = re.search(r"HttpTrigger([A-Za-z]+)", line)
                        if func_match and func_match.group(0) not in deployed_functions:
                            deployed_functions.append(func_match.group(0))

                    # Check for common warning patterns
                    if "error" in line.lower() or "failed" in line.lower():
                        if "[ERROR]" not in line:  # Don't double log errors
                            logging.warning(f"Potential issue detected: {line}")

                # Process stderr
                if stderr_line:
                    line = stderr_line.strip()
                    stderr_lines.append(line)
                    logging.error(f"DEPLOY ERROR: {line}")

                # Check if process has finished
                if (
                    stdout_line == ""
                    and stderr_line == ""
                    and process.poll() is not None
                ):
                    break

            # Get the return code
            return_code = process.poll()

            # Combine all output
            all_stdout = "\n".join(stdout_lines)
            all_stderr = "\n".join(stderr_lines)

            # Check if deployment was successful
            if return_code == 0:
                logging.info("Deployment successful.")
                if all_stderr:
                    logging.warning(f"stderr:\n{all_stderr}")

                # Check for common warning patterns in the output that might indicate issues
                if "error" in all_stdout.lower() or "failed" in all_stdout.lower():
                    logging.warning(
                        "Potential issues detected in deployment output. Please review the logs."
                    )

                # Return deployment information
                logging.info(
                    f"Deployment completed successfully with stage: {deployment_stage}, progress: {deployment_progress}%"
                )
                if function_app_url:
                    logging.info(f"Function App URL: {function_app_url}")
                if deployed_functions:
                    logging.info(f"Deployed functions: {', '.join(deployed_functions)}")

                return True
        except subprocess.CalledProcessError as e:
            # This should not happen with Popen approach, but keeping as a fallback
            logging.error(
                f"Deployment failed with exit code {e.returncode} on attempt {attempt}/{max_retries}."
            )
            logging.error(f"stdout:\n{e.stdout if hasattr(e, 'stdout') else 'N/A'}")
            logging.error(f"stderr:\n{e.stderr if hasattr(e, 'stderr') else 'N/A'}")

            if attempt < max_retries:
                logging.info(f"Waiting {retry_delay} seconds before retrying...")
                time.sleep(retry_delay)
                # Increase delay for next retry (exponential backoff)
                retry_delay *= 1.5
            else:
                logging.error(f"All {max_retries} deployment attempts failed.")
                return False

        except FileNotFoundError:
            logging.error(
                "Error: 'func' command not found. Make sure Azure Functions Core Tools are installed and in your PATH."
            )
            return False

        except Exception as e:
            logging.error(f"An unexpected error occurred: {e}")

            if attempt < max_retries:
                logging.info(f"Waiting {retry_delay} seconds before retrying...")
                time.sleep(retry_delay)
                # Increase delay for next retry (exponential backoff)
                retry_delay *= 1.5
            else:
                logging.error(f"All {max_retries} deployment attempts failed.")
                return False
        except Exception as e:
            logging.error(
                f"An unexpected error occurred during deployment attempt {attempt}/{max_retries}: {e}"
            )

            if attempt < max_retries:
                logging.info(f"Waiting {retry_delay} seconds before retrying...")
                time.sleep(retry_delay)
                # Increase delay for next retry (exponential backoff)
                retry_delay *= 1.5
            else:
                logging.error(f"All {max_retries} deployment attempts failed.")
                return False

    return False


# --- Main Execution ---


def main():
    parser = argparse.ArgumentParser(
        description="Deploy Azure Functions for a specific project."
    )
    parser.add_argument("project_id", help="The UUID of the project.")
    parser.add_argument(
        "function_app_name", help="The name of the target Azure Function App."
    )
    parser.add_argument(
        "--url",
        default="http://localhost:50505",
        help="Base URL of the web application API (default: http://localhost:50505).",
    )
    parser.add_argument("--token", help="Optional authentication token for the API.")
    parser.add_argument(
        "--source-dir",
        default="./azure-functions",
        help="Path to the source Azure Functions directory (default: ./azure-functions).",
    )
    parser.add_argument(
        "--resource-group",
        default="rg-internal-ai",
        help="Resource group containing the Function App (default: rg-internal-ai).",
    )

    args = parser.parse_args()

    # 1. Fetch Project Configuration
    project_config = fetch_project_config(args.url, args.project_id, args.token)
    if not project_config:
        sys.exit(1)  # Error logged in fetch_project_config

    # Prepare project-specific replacements
    project_replacements = {
        # Project identification
        "__PROJECT_ID__": project_config.get("project_id"),
        "__PROJECT_NAME__": project_config.get("name"),
        # Azure AI Search resources
        "__PROJECT_SEARCH_INDEX__": project_config.get("search_index_name"),
        "__PROJECT_INDEXER_NAME__": project_config.get("search_indexer_name"),
        "__PROJECT_DATASOURCE_NAME__": project_config.get("search_datasource_name"),
        # Storage containers
        "__PROJECT_UPLOADS_CONTAINER__": project_config.get(
            "storage_container_uploads"
        ),
        "__PROJECT_INPUT_CONTAINER__": project_config.get("storage_container_input"),
        "__PROJECT_OUTPUT_CONTAINER__": project_config.get("storage_container_output"),
        # Event Grid resources (removed)
        # Function App
        "__PROJECT_FUNCTION_APP_NAME__": project_config.get("function_app_name"),
        "__PROJECT_FUNCTION_APP_URL__": project_config.get("function_app_url"),
        # Add others if needed, e.g. "__PROJECT_INPUT_BLOB_NAME__": project_config.get("default_input_blob")
    }

    # Combine shared and project-specific replacements
    all_replacements = {**SHARED_CONFIG_ENV_VARS, **project_replacements}

    # Define which project-specific values are mandatory
    MANDATORY_PROJECT_VALUES = [
        "__PROJECT_ID__",
        "__PROJECT_SEARCH_INDEX__",
        "__PROJECT_INDEXER_NAME__",
        "__PROJECT_INPUT_CONTAINER__",
        "__PROJECT_OUTPUT_CONTAINER__",
    ]

    # Check for missing project-specific values
    missing_project_values = [
        key for key in MANDATORY_PROJECT_VALUES if project_replacements.get(key) is None
    ]
    if missing_project_values:
        logging.error(
            f"Missing required configuration values from project {args.project_id}: {missing_project_values}"
        )
        sys.exit(
            f"Error: Missing project configuration values: {', '.join(var.strip('_').replace('PROJECT_', '') for var in missing_project_values)}"
        )

    # 2. Prepare Deployment Directory
    with tempfile.TemporaryDirectory() as temp_dir:
        logging.info(f"Created temporary directory: {temp_dir}")
        deploy_dir = Path(temp_dir) / "deploy"

        # 3. Copy Source Code
        if not copy_function_source(args.source_dir, deploy_dir):
            sys.exit("Failed to copy function source code.")

        # 4. Replace Placeholders in Templates
        env_template_path = deploy_dir / ".env.template"
        local_settings_template_path = deploy_dir / "local.settings.template.json"
        env_output_path = deploy_dir / ".env"
        local_settings_output_path = deploy_dir / "local.settings.json"

        if env_template_path.exists():
            shutil.copyfile(env_template_path, env_output_path)
            if not replace_placeholders(env_output_path, all_replacements):
                sys.exit("Failed to replace placeholders in .env file.")
            logging.info(f"Created populated .env file at {env_output_path}")
        else:
            logging.warning(
                f"Template file not found: {env_template_path}. Skipping .env creation."
            )

        if local_settings_template_path.exists():
            shutil.copyfile(local_settings_template_path, local_settings_output_path)
            # Need to load/dump JSON for local.settings.json
            try:
                with open(local_settings_output_path, "r") as f:
                    settings_json = json.load(f)

                if "Values" in settings_json:
                    for key, value in settings_json["Values"].items():
                        if isinstance(value, str) and value in all_replacements:
                            replacement_value = all_replacements[value]
                            if replacement_value is not None:
                                settings_json["Values"][key] = replacement_value
                            else:
                                logging.warning(
                                    f"Value for placeholder '{value}' used in local.settings.json is None. Keeping placeholder."
                                )
                    logging.info("Placeholders replaced in local.settings.json content")
                else:
                    logging.warning(
                        "'Values' key not found in local.settings.template.json"
                    )

                with open(local_settings_output_path, "w") as f:
                    json.dump(settings_json, f, indent=2)
                logging.info(
                    f"Created populated local.settings.json file at {local_settings_output_path}"
                )

            except Exception as e:
                logging.error(f"Failed to process local.settings.json: {e}")
                sys.exit("Failed to process local.settings.json.")
        else:
            logging.warning(
                f"Template file not found: {local_settings_template_path}. Skipping local.settings.json creation."
            )

        # 5. Validate Configuration Files
        logging.info("Validating configuration files...")
        config_files = {
            ".env": env_output_path.exists(),
            "local.settings.json": local_settings_output_path.exists(),
            "host.json": (deploy_dir / "host.json").exists(),
            "requirements.txt": (deploy_dir / "requirements.txt").exists(),
        }

        missing_files = [file for file, exists in config_files.items() if not exists]
        if missing_files:
            logging.error(f"Missing required configuration files: {missing_files}")
            sys.exit(
                f"Error: Missing required configuration files: {', '.join(missing_files)}"
            )

        logging.info("All required configuration files are present.")

        # 6. Validate Function Directories
        if not validate_function_directory(deploy_dir):
            sys.exit("Error: Missing required function directories.")

        # 7. Deploy Functions
        logging.info(
            f"Deploying functions to {args.function_app_name} in resource group {args.resource_group}..."
        )
        if not deploy_functions(
            deploy_dir,
            args.function_app_name,
            args.resource_group,
            max_retries=5,
            retry_delay=30,
        ):
            sys.exit("Function deployment failed after multiple attempts.")

        logging.info("Script finished successfully.")
        logging.info(
            f"Functions deployed to: https://{args.function_app_name}.azurewebsites.net"
        )
        logging.info("Available endpoints:")
        logging.info(
            f"  - Maturity Assessment: https://{args.function_app_name}.azurewebsites.net/api/HttpTriggerAppMaturityAssessment"
        )
        logging.info(
            f"  - Executive Summary: https://{args.function_app_name}.azurewebsites.net/api/HttpTriggerAppExecutiveSummary"
        )
        logging.info(
            f"  - Event Grid Blob Indexer is configured to respond to blob events"
        )

    # Temporary directory is automatically cleaned up here


if __name__ == "__main__":
    main()

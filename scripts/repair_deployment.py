#!/usr/bin/env python3
"""
Utility to repair an incomplete project deployment.

This script fetches a project's state from Cosmos DB, checks for missing resource
details, and attempts to automatically retrieve them from Azure.
python -m scripts.repair_deployment <project_id_or_name>

  For example:
  - python -m scripts.repair_deployment BDA1
  - python -m scripts.repair_deployment ********
  - python -m scripts.repair_deployment 69ef9dc8-c21c-4aa3-8ea1-7a2b9f8c70cf

  Additional options:
  - python -m scripts.repair_deployment ******** --validate (validates project fields)
  - python -m scripts.repair_deployment BDA1 --no-force-complete (doesn't force deployment status
  to completed)
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional

# This allows the script to be run from the root directory and find the backend modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backend.services.project_service import ProjectDataService

# --- Configure Logging ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)

# --- Helper Functions (Adapted from deploy_project_resources.py) ---

async def _run_az_command(cmd: list[str]) -> Optional[str]:
    """Runs an Azure CLI command and returns the output."""
    try:
        process = await asyncio.to_thread(
            subprocess.run,
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return process.stdout.strip()
    except subprocess.CalledProcessError as e:
        logging.error(f"AZ CLI command failed: {' '.join(cmd)}\nError: {e.stderr.strip()}")
        return None

async def _find_resource_by_tag(resource_type: str, project_id: str, resource_group: str) -> Optional[str]:
    """Finds a resource in a resource group by its type and project-id tag."""
    # First, list all resources of the type in the resource group
    cmd = [
        "az", "resource", "list",
        "--resource-group", resource_group,
        "--resource-type", resource_type,
        "--query", f"[?tags.\"project-id\"=='{project_id}'].name | [0]",
        "-o", "tsv"
    ]
    return await _run_az_command(cmd)

async def _generate_sas_token(account_name: str, resource_group: str) -> Optional[str]:
    """Generates a new SAS token for a storage account."""
    logging.info(f"Generating SAS token for storage account: {account_name}")
    expiry_date = (datetime.now(timezone.utc) + timedelta(days=365)).strftime("%Y-%m-%dT%H:%M:%SZ")
    cmd = [
        "az", "storage", "account", "generate-sas",
        "--account-name", account_name,
        "--resource-group", resource_group,
        "--resource-types", "sco",
        "--services", "bfqt",
        "--permissions", "rwdlacuptf",
        "--expiry", expiry_date,
        "--https-only",
        "--output", "tsv"
    ]
    return await _run_az_command(cmd)

async def _get_search_key(service_name: str, resource_group: str) -> Optional[str]:
    """Retrieves the primary admin key for a search service."""
    logging.info(f"Retrieving key for search service: {service_name}")
    cmd = [
        "az", "search", "admin-key", "show",
        "--service-name", service_name,
        "--resource-group", resource_group,
        "--query", "primaryKey",
        "-o", "tsv"
    ]
    return await _run_az_command(cmd)

async def _get_function_key(app_name: str, function_name: str, resource_group: str) -> Optional[str]:
    """Retrieves the default key for a specific function within a function app."""
    logging.info(f"Retrieving key for function: {function_name} in app: {app_name}")
    cmd = [
        "az", "functionapp", "function", "keys", "list",
        "--name", app_name,
        "--resource-group", resource_group,
        "--function-name", function_name,
        "--query", "default",
        "-o", "tsv"
    ]
    return await _run_az_command(cmd)


# --- Main Repair Function ---

async def find_project_by_name_or_id(identifier: str) -> Optional[Dict[str, Any]]:
    """Find a project by either its ID or name."""
    service = ProjectDataService()
    
    # First try as ID
    project = await service.get_project(identifier)
    if project:
        logging.info(f"Found project by ID: {identifier}")
        return project
    
    # Then try as name
    query = "SELECT * FROM c WHERE c.name = @name"
    params = [{"name": "@name", "value": identifier}]
    
    try:
        items = [
            item
            async for item in service.container.query_items(
                query=query, 
                parameters=params
            )
        ]
        if items:
            project = items[0]
            logging.info(f"Found project by name: {identifier} (ID: {project['id']})")
            return project
    except Exception as e:
        logging.error(f"Error querying for project by name: {e}")
    
    return None


async def close_cosmos_client():
    """Close the cosmos client to avoid async warnings."""
    import backend.utils.cosmos as cosmos_module
    if hasattr(cosmos_module, '_cosmos_client') and cosmos_module._cosmos_client:
        await cosmos_module._cosmos_client.close()
        cosmos_module._cosmos_client = None

async def repair_project_deployment(project_identifier: str, resource_group: str = "rg-internal-ai", force_completed: bool = True) -> Dict[str, Any]:
    """Analyzes and repairs an incomplete project deployment.
    
    Args:
        project_identifier: Can be either project ID or project name
        resource_group: Azure resource group name
        force_completed: If True, forces the deployment status to 'completed' regardless of actual state
    """
    logging.info(f"--- Starting repair process for project: {project_identifier} ---")
    
    # Find project by ID or name
    project = await find_project_by_name_or_id(project_identifier)
    
    if not project:
        logging.error(f"Repair failed: Project '{project_identifier}' not found in Cosmos DB.")
        return {"status": "failed", "message": f"Project '{project_identifier}' not found (tried both ID and name)."}
    
    project_id = project["id"]
    logging.info(f"Working with project ID: {project_id}, Name: {project.get('name', 'N/A')}")

    manual_actions = []
    actions_taken = []
    
    # Initialize deployment_status if not present
    if "deployment_status" not in project:
        project["deployment_status"] = {
            "status": "pending",
            "message": "Deployment status being initialized",
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "details": {
                "storage": {
                    "storage_account": False,
                    "containers": {
                        "uploads": False,
                        "input": False,
                        "output": False
                    }
                },
                "storage_complete": False,
                "search": {
                    "search_service": False,
                    "index": False,
                    "indexer": False,
                    "datasource": False
                },
                "search_complete": False,
                "function": {
                    "function_app": False,
                    "event_grid_topic": False,
                    "event_grid_system_topic": False,
                    "event_grid": False,
                    "maturity_assessment": False,
                    "executive_summary": False
                },
                "function_complete": False,
                "overall_complete": False,
                "completion_percentage": 0,
                "resource_times": {}
            }
        }
        actions_taken.append("Initialized deployment_status structure")
    
    # Initialize environment if not present
    if "environment" not in project:
        project["environment"] = {}
        actions_taken.append("Initialized environment structure")

    # 1. Check for Core Resource Names
    for resource_key, resource_type in [
        ("storage_account_name", "Microsoft.Storage/storageAccounts"),
        ("search_service_name", "Microsoft.Search/searchServices"),
        ("function_app_name", "Microsoft.Web/sites"),
    ]:
        if not project.get(resource_key):
            logging.warning(f"Missing '{resource_key}'. Searching in Azure...")
            found_name = await _find_resource_by_tag(resource_type, project_id, resource_group)
            if found_name:
                project[resource_key] = found_name
                actions_taken.append(f"Found and updated missing '{resource_key}': {found_name}")
            else:
                manual_actions.append(
                    f"Could not find resource '{resource_type}' for project {project_id}. It may need to be (re)created manually."
                )

    # 2. Check for Credentials (SAS, Search Key)
    if project.get("storage_account_name"):
        if not project.get("storage_account_sas_token"):
            sas_token = await _generate_sas_token(project["storage_account_name"], resource_group)
            if sas_token:
                project["storage_account_sas_token"] = sas_token
                # Also update in environment
                project["environment"]["STORAGE_ACCOUNT_SAS_TOKEN"] = sas_token
                actions_taken.append("Generated and updated missing Storage Account SAS token.")
            else:
                manual_actions.append("Failed to generate Storage Account SAS token.")
        elif "STORAGE_ACCOUNT_SAS_TOKEN" not in project["environment"]:
            # Copy existing SAS token to environment
            project["environment"]["STORAGE_ACCOUNT_SAS_TOKEN"] = project["storage_account_sas_token"]
            actions_taken.append("Copied existing SAS token to environment.")

    if project.get("search_service_name"):
        if not project.get("search_key"):
            search_key = await _get_search_key(project["search_service_name"], resource_group)
            if search_key:
                project["search_key"] = search_key
                # Also update in environment
                project["environment"]["AZURE_SEARCH_API_KEY"] = search_key
                actions_taken.append("Retrieved and updated missing Search Service key.")
            else:
                manual_actions.append("Failed to retrieve Search Service key.")
        elif "AZURE_SEARCH_API_KEY" not in project["environment"]:
            # Copy existing search key to environment
            project["environment"]["AZURE_SEARCH_API_KEY"] = project["search_key"]
            actions_taken.append("Copied existing search key to environment.")

    # 3. Check for Function App and its Keys
    if project.get("function_app_name"):
        # Update function app URL if missing
        if not project.get("function_app_url"):
            project["function_app_url"] = f"https://{project['function_app_name']}.azurewebsites.net"
            project["environment"]["FUNCTION_APP_URL"] = project["function_app_url"]
            actions_taken.append("Set function app URL")
        
        functions_to_check = {
            "function_key_maturity": ("HttpTriggerAppMaturityAssessment", "FUNCTION_KEY_MATURITY"),
            "function_key_executive_summary": ("HttpTriggerAppExecutiveSummary", "FUNCTION_KEY_EXECUTIVE_SUMMARY"),
            "function_key_powerpoint": ("HttpTriggerPowerPointGenerator", "FUNCTION_KEY_POWERPOINT"),
        }
        for key_field, (func_name, env_key) in functions_to_check.items():
            if not project.get(key_field):
                retrieved_key = await _get_function_key(project["function_app_name"], func_name, resource_group)
                if retrieved_key:
                    project[key_field] = retrieved_key
                    # Update environment variables
                    project["environment"][env_key] = retrieved_key
                    actions_taken.append(f"Retrieved and updated missing function key: '{key_field}'")
                else:
                    manual_actions.append(
                        f"Failed to retrieve key for function '{func_name}'. Check the Function App's status."
                    )
            elif env_key not in project["environment"]:
                # Copy existing function key to environment
                project["environment"][env_key] = project[key_field]
                actions_taken.append(f"Copied existing {key_field} to environment.")
        
        # Update function URLs
        if project.get("function_app_url"):
            base_url = project["function_app_url"]
            
            # Maturity assessment URL
            if not project.get("azure_function_maturity_assessment_url"):
                project["azure_function_maturity_assessment_url"] = f"{base_url}/api/HttpTriggerAppMaturityAssessment"
                actions_taken.append("Set azure_function_maturity_assessment_url")
            if "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL" not in project["environment"]:
                project["environment"]["AZURE_FUNCTION_MATURITY_ASSESSMENT_URL"] = project["azure_function_maturity_assessment_url"]
                
            # Executive summary URL
            if not project.get("azure_function_executive_summary_url"):
                project["azure_function_executive_summary_url"] = f"{base_url}/api/HttpTriggerAppExecutiveSummary"
                actions_taken.append("Set azure_function_executive_summary_url")
            if "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL" not in project["environment"]:
                project["environment"]["AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL"] = project["azure_function_executive_summary_url"]
            
            # PowerPoint URL
            if not project.get("azure_function_powerpoint_url"):
                project["azure_function_powerpoint_url"] = f"{base_url}/api/HttpTriggerPowerPointGenerator"
                actions_taken.append("Set azure_function_powerpoint_url")
            if "AZURE_FUNCTION_POWERPOINT_URL" not in project["environment"]:
                project["environment"]["AZURE_FUNCTION_POWERPOINT_URL"] = project["azure_function_powerpoint_url"]
            
    elif not any(action.startswith("Could not find resource 'Microsoft.Web/sites'") for action in manual_actions):
        manual_actions.append(
            "Critical failure: The Function App for this project does not exist. It cannot be repaired automatically."
        )

    # 4. Update environment variables from project fields
    env_mappings = {
        "storage_account_name": "STORAGE_ACCOUNT_NAME",
        "storage_container_uploads": "STORAGE_CONTAINER_UPLOADS",
        "storage_container_input": "STORAGE_CONTAINER_INPUT",
        "storage_container_output": "STORAGE_CONTAINER_OUTPUT",
        "search_service_name": "SEARCH_SERVICE_NAME",
        "search_index_name": "SEARCH_INDEX",
        "uploads_container": "STORAGE_CONTAINER_UPLOADS",
        "input_container": "STORAGE_CONTAINER_INPUT",
        "output_container": "STORAGE_CONTAINER_OUTPUT"
    }
    
    for project_field, env_var in env_mappings.items():
        if project.get(project_field) and env_var not in project["environment"]:
            project["environment"][env_var] = project[project_field]
            actions_taken.append(f"Updated environment variable {env_var}")
    
    # 5. Check for Event Grid (simple check, as it depends on others)
    if project.get("storage_account_name") and project.get("function_app_name") and not project.get("event_grid_system_topic_name"):
        logging.warning("Missing 'event_grid_system_topic_name'. Searching in Azure...")
        # Try to find event grid topic by tag
        topic_name = await _find_resource_by_tag("Microsoft.EventGrid/systemTopics", project_id, resource_group)
        if topic_name:
            project["event_grid_system_topic_name"] = topic_name
            actions_taken.append(f"Found and updated missing event_grid_system_topic_name: {topic_name}")
        else:
            manual_actions.append(
                "Event Grid topic details are missing. This may require manual Bicep deployment of the event_grid.bicep module."
            )
    
    # 6. Update deployment status based on what we found
    if project.get("deployment_status"):
        status_details = project["deployment_status"]["details"]
        
        # Update storage status
        if project.get("storage_account_name"):
            # Check if storage structure exists properly
            if not isinstance(status_details.get("storage"), dict):
                status_details["storage"] = {
                    "storage_account": False,
                    "containers": {
                        "uploads": False,
                        "input": False,
                        "output": False
                    }
                }
            
            status_details["storage"]["storage_account"] = True
            
            # Check if containers structure exists
            if "containers" not in status_details["storage"] or not isinstance(status_details["storage"]["containers"], dict):
                status_details["storage"]["containers"] = {
                    "uploads": False,
                    "input": False,
                    "output": False
                }
            
            if project.get("storage_container_uploads"):
                status_details["storage"]["containers"]["uploads"] = True
            if project.get("storage_container_input"):
                status_details["storage"]["containers"]["input"] = True
            if project.get("storage_container_output"):
                status_details["storage"]["containers"]["output"] = True
        
        # Update search status
        if project.get("search_service_name"):
            # Check if search structure exists properly
            if not isinstance(status_details.get("search"), dict):
                status_details["search"] = {
                    "search_service": False,
                    "index": False,
                    "indexer": False,
                    "datasource": False
                }
            
            status_details["search"]["search_service"] = True
            if project.get("search_index_name"):
                status_details["search"]["index"] = True
            if project.get("search_datasource_name"):
                status_details["search"]["datasource"] = True
            if project.get("search_indexer_name"):
                status_details["search"]["indexer"] = True
        
        # Update function status
        if project.get("function_app_name"):
            # Check if function structure exists properly
            if not isinstance(status_details.get("function"), dict):
                status_details["function"] = {
                    "function_app": False,
                    "event_grid_topic": False,
                    "event_grid_system_topic": False,
                    "event_grid": False,
                    "maturity_assessment": False,
                    "executive_summary": False
                }
            
            status_details["function"]["function_app"] = True
            if project.get("event_grid_system_topic_name"):
                status_details["function"]["event_grid_system_topic"] = True
        
        # Calculate completion
        total_checks = 0
        completed_checks = 0
        
        for category in ["storage", "search", "function"]:
            if category == "storage":
                for key, value in status_details["storage"].items():
                    if key == "containers":
                        for container_key, container_value in value.items():
                            total_checks += 1
                            if container_value:
                                completed_checks += 1
                    elif key != "storage_complete":
                        total_checks += 1
                        if value:
                            completed_checks += 1
            else:
                for key, value in status_details[category].items():
                    if not key.endswith("_complete"):
                        total_checks += 1
                        if value:
                            completed_checks += 1
        
        completion_percentage = int((completed_checks / total_checks * 100) if total_checks > 0 else 0)
        status_details["completion_percentage"] = completion_percentage
        
        # Update overall status
        if force_completed or completion_percentage == 100:
            project["deployment_status"]["status"] = "completed"
            project["deployment_status"]["message"] = "Project deployment completed"
            status_details["overall_complete"] = True
            # If forcing completed, set all sub-statuses to complete
            if force_completed and completion_percentage != 100:
                status_details["completion_percentage"] = 100
                status_details["storage_complete"] = True
                status_details["search_complete"] = True
                status_details["function_complete"] = True
                actions_taken.append("Forced deployment status to completed")
        elif completion_percentage > 0:
            project["deployment_status"]["status"] = "in_progress"
            project["deployment_status"]["message"] = f"Deployment {completion_percentage}% complete"
        
        project["deployment_status"]["updated_at"] = datetime.now(timezone.utc).isoformat()
    
    # 7. Set default values for missing fields
    if "search_api_version" not in project:
        project["search_api_version"] = "2021-04-30-Preview"
        actions_taken.append("Set default search API version")
    
    if "deployment_total" not in project:
        project["deployment_total"] = 0
    
    # Add missing container reference fields
    if project.get("storage_container_uploads") and not project.get("uploads_container"):
        project["uploads_container"] = project["storage_container_uploads"]
        actions_taken.append("Added uploads_container field")
    
    if project.get("storage_container_input") and not project.get("input_container"):
        project["input_container"] = project["storage_container_input"]
        actions_taken.append("Added input_container field")
        
    if project.get("storage_container_output") and not project.get("output_container"):
        project["output_container"] = project["storage_container_output"]
        actions_taken.append("Added output_container field")
    
    # Add event grid subscription name if missing
    if project.get("event_grid_system_topic_name") and not project.get("event_grid_subscription_name"):
        project["event_grid_subscription_name"] = "blob-to-function-subscription"
        actions_taken.append("Added default event_grid_subscription_name")
    
    # Set timestamps if missing
    if "created_at" not in project:
        project["created_at"] = datetime.now(timezone.utc).isoformat()
    project["updated_at"] = datetime.now(timezone.utc).isoformat()

    # 8. Finalize and Update
    if not actions_taken:
        logging.info("No repair actions were needed. Project data appears complete.")
        return {"status": "success", "message": "No repair needed.", "manual_actions_required": manual_actions}

    logging.info("Attempting to save repaired project data to Cosmos DB...")
    try:
        update_service = ProjectDataService()
        update_success = await update_service.update_project(project_id, project)
        if not update_success:
            raise Exception("The update_project method returned False.")
    except Exception as e:
        logging.error(f"--- Repair process failed: Could not update Cosmos DB. Error: {e} ---")
        manual_actions.append(
            "Failed to save updated data to Cosmos DB. Please check DB connectivity and permissions."
        )
        return {
            "status": "failed",
            "message": "Repairs were made but could not be saved to Cosmos DB.",
            "manual_actions_required": manual_actions,
        }

    logging.info("--- Repair process finished successfully ---")
    status = "partial_success" if manual_actions else "success"
    message = f"Repair complete. Actions taken: {len(actions_taken)}. "
    if manual_actions:
        message += f"Manual actions required: {len(manual_actions)}."

    return {
        "status": status,
        "message": message,
        "manual_actions_required": manual_actions,
    }

async def validate_project_********():
    """Validate the specific project ******** has all required fields."""
    expected_structure = {
        "id": str,
        "type": "project",
        "name": str,
        "description": str,
        "region": str,
        "owner": str,
        "storage_account_name": str,
        "storage_container_uploads": str,
        "storage_container_input": str,
        "storage_container_output": str,
        "search_service_name": str,
        "search_index_name": str,
        "search_datasource_name": str,
        "search_indexer_name": str,
        "function_app_name": str,
        "function_names": (type(None), list),
        "cost_limit": (type(None), float, int),
        "environment": dict,
        "icon": str,
        "color": str,
        "created_at": str,
        "updated_at": str,
        "deployment_status": dict,
        "deployment_total": (int, float),
        "search_key": str,
        "function_app_url": str,
        "function_key_maturity": str,
        "azure_function_maturity_assessment_url": str,
        "function_key_executive_summary": str,
        "azure_function_executive_summary_url": str,
        "function_key_powerpoint": str,
        "event_grid_system_topic_name": str,
        "event_grid_subscription_name": str,
        "storage_account_sas_token": str,
        "uploads_container": str,
        "input_container": str,
        "output_container": str,
        "search_api_version": str
    }
    
    service = ProjectDataService()
    # Find project by name "********" using a query
    query = "SELECT * FROM c WHERE c.name = @project_name"
    params = [{"name": "@project_name", "value": "********"}]
    
    try:
        items = [
            item
            async for item in service.container.query_items(
                query=query, 
                parameters=params
            )
        ]
        project = items[0] if items else None
    except Exception as e:
        logging.error(f"Error querying for project with name '********': {e}")
        return {"status": "failed", "message": f"Error querying project: {str(e)}"}
    
    if not project:
        return {"status": "failed", "message": "Project with name '********' not found"}
    
    missing_fields = []
    for field, expected_type in expected_structure.items():
        if field not in project:
            missing_fields.append(field)
        elif isinstance(expected_type, type):
            # Single type check
            if not isinstance(project[field], expected_type):
                missing_fields.append(f"{field} (wrong type: expected {expected_type.__name__}, got {type(project[field]).__name__})")
        elif isinstance(expected_type, tuple):
            # Multiple type check
            if not isinstance(project[field], expected_type):
                type_names = [t.__name__ if hasattr(t, '__name__') else str(t) for t in expected_type]
                missing_fields.append(f"{field} (wrong type: expected one of {type_names}, got {type(project[field]).__name__})")
    
    # Also check environment variables
    expected_env_vars = [
        "STORAGE_ACCOUNT_SAS_TOKEN",
        "FUNCTION_KEY_MATURITY", 
        "FUNCTION_KEY_EXECUTIVE_SUMMARY",
        "FUNCTION_KEY_POWERPOINT",
        "AZURE_FUNCTION_MATURITY_ASSESSMENT_URL",
        "AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL",
        "AZURE_FUNCTION_POWERPOINT_URL",
        "STORAGE_ACCOUNT_NAME",
        "STORAGE_CONTAINER_UPLOADS",
        "STORAGE_CONTAINER_INPUT",
        "STORAGE_CONTAINER_OUTPUT",
        "SEARCH_SERVICE_NAME",
        "SEARCH_INDEX",
        "AZURE_SEARCH_API_KEY"
    ]
    
    missing_env_vars = []
    if "environment" in project:
        for env_var in expected_env_vars:
            if env_var not in project["environment"]:
                missing_env_vars.append(f"environment.{env_var}")
    else:
        missing_env_vars = [f"environment.{var}" for var in expected_env_vars]
    
    all_missing = missing_fields + missing_env_vars
    
    if all_missing:
        return {
            "status": "validation_failed",
            "message": f"Project ******** is missing fields: {', '.join(all_missing)}",
            "missing_fields": missing_fields,
            "missing_env_vars": missing_env_vars
        }
    
    return {
        "status": "validation_passed",
        "message": "Project ******** has all required fields",
        "project_summary": {
            "id": project["id"],
            "name": project["name"],
            "deployment_status": project["deployment_status"]["status"],
            "completion_percentage": project["deployment_status"]["details"]["completion_percentage"]
        }
    }

async def main():
    """Main async function to handle script execution and cleanup."""
    if len(sys.argv) < 2:
        print("Usage: python -m scripts.repair_deployment <project_id_or_name> [--validate] [--no-force-complete]")
        print("\nExamples:")
        print("  python -m scripts.repair_deployment 69ef9dc8-c21c-4aa3-8ea1-7a2b9f8c70cf")
        print("  python -m scripts.repair_deployment 06101027")
        print("  python -m scripts.repair_deployment ******** --validate")
        print("  python -m scripts.repair_deployment 06101027 --no-force-complete")
        print("\nOptions:")
        print("  --validate           Validate project fields (only for project ********)")
        print("  --no-force-complete  Don't force deployment status to 'completed'")
        sys.exit(1)

    from dotenv import load_dotenv
    load_dotenv()
    
    try:
        # Parse arguments
        project_identifier = sys.argv[1]
        validate_mode = "--validate" in sys.argv
        force_completed = "--no-force-complete" not in sys.argv
        
        if validate_mode:
            if project_identifier == "********":
                result = await validate_project_********()
            else:
                print(f"Validation is currently only implemented for project '********'")
                sys.exit(1)
        else:
            result = await repair_project_deployment(project_identifier, force_completed=force_completed)
        
        print(json.dumps(result, indent=2))
    finally:
        # Clean up the cosmos client to avoid async warnings
        await close_cosmos_client()


if __name__ == "__main__":
    asyncio.run(main())

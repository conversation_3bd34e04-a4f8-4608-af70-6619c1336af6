# scripts/add_file_path.py
# ./scripts/add_file_path.py
import os

def add_file_path_comment(directory):
    # Define comment syntax for each file extension
    comment_syntax = {
        '.py': lambda path: f'# {path}\n',
        '.js': lambda path: f'// {path}\n',
        '.ts': lambda path: f'// {path}\n',
        '.tsx': lambda path: f'// {path}\n',
        '.jsx': lambda path: f'// {path}\n',
        '.css': lambda path: f'/* {path} */\n',
        '.scss': lambda path: f'/* {path} */\n',    # Added SCSS support
        '.less': lambda path: f'/* {path} */\n',    # Added LESS support
        '.html': lambda path: f'<!-- {path} -->\n',
        '.sh': lambda path: f'# {path}\n',
        '.bash': lambda path: f'# {path}\n',        # Added .bash support
        '.bat': lambda path: f'@REM {path}\n',      # Changed to '@REM' for batch files
        '.cmd': lambda path: f'@REM {path}\n',      # Added .cmd support
        '.md': lambda path: f'<!-- {path} -->\n',
        #'.json': lambda path: f'/* {path} */\n',     # Use with caution
        #'.yaml': lambda path: f'# {path}\n',
        #'.yml': lambda path: f'# {path}\n',
        # Add more extensions and their comment syntaxes here if needed
    }

    # Define directories and files to exclude
    exclude_dirs = {'node_modules', 'dist','.venv', 'build', '.git'}
    exclude_files = {'package-lock.json', 'yarn.lock'}

    for dirpath, dirnames, files in os.walk(directory):
        # Normalize the directory path for consistent comparison
        normalized_dirpath = os.path.normpath(dirpath)
        # Split the path into its components
        path_parts = set(normalized_dirpath.split(os.sep))

        # Check if any part of the current directory is in the exclusion list
        if exclude_dirs.intersection(path_parts):
            # Skip processing this directory by modifying dirnames in-place
            # This prevents os.walk from traversing into excluded subdirectories
            dirnames[:] = []
            print(f"Skipping directory and its subdirectories: {dirpath}")
            continue

        for file_name in files:
            # Skip excluded files
            if file_name in exclude_files:
                print(f"Skipping excluded file: {os.path.join(dirpath, file_name)}")
                continue

            file_ext = os.path.splitext(file_name)[1].lower()
            if file_ext in comment_syntax:
                file_path = os.path.join(dirpath, file_name)
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        content = file.read()

                    # Generate the comment with a relative path
                    relative_path = os.path.relpath(file_path, directory)
                    comment = comment_syntax[file_ext](relative_path)

                    # Normalize line endings and strip whitespace for comparison
                    comment_stripped = comment.strip().replace('\n', '').replace('\r', '')
                    content_start_stripped = content.strip().replace('\n', '').replace('\r', '')[:len(comment_stripped)]

                    # Check if the file already starts with the file path comment
                    if content_start_stripped != comment_stripped:
                        new_content = comment + content
                        with open(file_path, 'w', encoding='utf-8') as file:
                            file.write(new_content)
                        print(f'Added comment to {file_path}')
                    else:
                        print(f'Comment already exists in {file_path}, skipping.')
                except Exception as e:
                    print(f'Error processing {file_path}: {e}')

    print("Commenting process completed.")

# Replace 'your_repo_directory' with the path to your repository
if __name__ == "__main__":
    repo_directory = '.'  # Update this path accordingly
    if os.path.isdir(repo_directory):
        add_file_path_comment(repo_directory)
    else:
        print(f'The directory "{repo_directory}" does not exist.')

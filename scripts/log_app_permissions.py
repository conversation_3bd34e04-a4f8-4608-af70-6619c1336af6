#!/usr/bin/env python3
"""
Log Azure AD application permissions via Microsoft Graph.

The script acquires an **application token** (client-credential flow) with
`azure.identity.ClientSecretCredential` and reads the application object via
Microsoft Graph to confirm the token’s permissions.

Usage
-----
    python app_permissions_test.py <AZURE_CLIENT_ID> <AZURE_TENANT_ID> <AZURE_CLIENT_SECRET>

If you omit the CLI arguments, the script falls back to the environment
variables:

    AZURE_CLIENT_ID, AZURE_TENANT_ID, AZURE_CLIENT_SECRET (or AZURE_APP_SECRET)

Logs go both to the console and to **logs/app_permissions_test.log** via the
centralised logger in `backend.utils.logging_config`.
"""

from __future__ import annotations

import json
import os
import sys
from typing import Optional

import requests
from azure.identity import ClientSecretCredential

from backend.utils.logging_config import configure_logger

# --------------------------------------------------------------------------- #
# Logging
# --------------------------------------------------------------------------- #

logger = configure_logger(
    logger_name="app_permissions_test",
    log_to_console=True,
    log_to_file=True,
    log_file_name="app_permissions_test.log",
)

# --------------------------------------------------------------------------- #
# Constants
# --------------------------------------------------------------------------- #

GRAPH_SCOPE = "https://graph.microsoft.com/.default"
GRAPH_ENDPOINT = "https://graph.microsoft.com/v1.0"

# --------------------------------------------------------------------------- #
# Helpers
# --------------------------------------------------------------------------- #


def acquire_token(client_id: str, tenant_id: str, client_secret: str) -> Optional[str]:
    """Acquire an application token for Microsoft Graph."""
    try:
        credential = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret,
        )
        token = credential.get_token(GRAPH_SCOPE)
        logger.info("Successfully acquired Microsoft Graph token")
        return token.token
    except Exception as exc:  # pragma: no cover
        logger.exception("Failed to acquire token: %s", exc)
        return None


def call_graph(token: str, app_id: str) -> bool:
    """
    Fetch the application object from Microsoft Graph.

    We hit `/applications/{app-id}` because it requires **Application.Read.All**,
    giving a quick smoke-test that the app really holds that permission.
    """
    url = f"{GRAPH_ENDPOINT}/applications/{app_id}"
    headers = {"Authorization": f"Bearer {token}"}

    try:
        resp = requests.get(url, headers=headers, timeout=10)
        logger.info("Graph request %s -> %s", url, resp.status_code)

        if resp.ok:
            logger.debug("Graph response:\n%s", json.dumps(resp.json(), indent=2))
            return True

        # Log the error body (may be JSON or plain text)
        try:
            logger.error("Graph error: %s", json.dumps(resp.json(), indent=2))
        except ValueError:
            logger.error("Graph error: %s", resp.text)
        return False

    except Exception as exc:  # pragma: no cover
        logger.exception("Error calling Microsoft Graph: %s", exc)
        return False


# --------------------------------------------------------------------------- #
# Entry-point
# --------------------------------------------------------------------------- #


def main() -> int:  # noqa: D401  (imperative mood not needed here)
    # Prefer CLI args (handy when testing locally) but fall back to env-vars
    client_id = sys.argv[1] if len(sys.argv) > 1 else os.getenv("AZURE_CLIENT_ID")
    tenant_id = sys.argv[2] if len(sys.argv) > 2 else os.getenv("AZURE_TENANT_ID")
    client_secret = (
        sys.argv[3]
        if len(sys.argv) > 3
        else os.getenv("AZURE_CLIENT_SECRET") or os.getenv("AZURE_APP_SECRET")
    )

    if not all([client_id, tenant_id, client_secret]):
        logger.error(
            "Missing credentials. Provide AZURE_CLIENT_ID, AZURE_TENANT_ID and "
            "AZURE_CLIENT_SECRET (or AZURE_APP_SECRET)."
        )
        return 1

    token = acquire_token(client_id, tenant_id, client_secret)
    if not token:
        return 1

    if call_graph(token, client_id):
        logger.info("App permissions test completed successfully ✅")
        return 0

    logger.error("App permissions test failed ❌")
    return 1


if __name__ == "__main__":
    raise SystemExit(main())
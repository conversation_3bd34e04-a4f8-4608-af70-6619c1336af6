#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to get the default function keys and update Cosmos DB with them.

Usage:
    python scripts/test_single_function_keys.py --function-app-name FUNCTION_APP_NAME [--dry-run]
"""

import os
import sys
import subprocess
import argparse
import logging
import asyncio
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.cosmos import get_cosmos_client
from azure.cosmos import exceptions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration constants
RESOURCE_GROUP = "rg-internal-ai"

# Environment variable names for function keys
FUNCTION_KEY_ENV_VARS = [
    "FUNCTION_KEY_MATURITY",
    "FUNCTION_KEY_EXECUTIVE_SUMMARY",
    "FUNCTION_KEY_POWERPOINT"
]

# Function names mapping
FUNCTION_NAMES = {
    "FUNCTION_KEY_MATURITY": "HttpTriggerAppMaturityAssessment",
    "FUNCTION_KEY_EXECUTIVE_SUMMARY": "HttpTriggerAppExecutiveSummary", 
    "FUNCTION_KEY_POWERPOINT": "HttpTriggerPowerPointGenerator"
}


async def run_az_command(cmd: List[str], dry_run: bool = False) -> Optional[str]:
    """Run an Azure CLI command and return the output."""
    if dry_run:
        logger.info(f"DRY RUN: Would execute: {' '.join(cmd)}")
        return "dry-run-output"
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {' '.join(cmd)}")
        logger.error(f"Error: {e.stderr}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error running command: {e}")
        return None


async def get_default_function_key(function_app_name: str, function_name: str) -> Optional[str]:
    """Get the default function key for a specific function."""
    logger.info(f"Getting default function key for {function_name} in {function_app_name}")

    cmd = [
        "az", "functionapp", "function", "keys", "list",
        "--name", function_app_name,
        "--resource-group", RESOURCE_GROUP,
        "--function-name", function_name,
        "--query", "default",
        "--output", "tsv"
    ]

    result = await run_az_command(cmd)
    if result and result.strip():
        key_value = result.strip()
        logger.info(f"✅ Retrieved default key for {function_name}: {key_value[:10]}...")
        return key_value
    else:
        logger.error(f"❌ Failed to get default key for {function_name}")
        return None


class CosmosManager:
    """Class to manage Cosmos DB operations."""

    def __init__(self):
        """Initialize the Cosmos DB client."""
        self.cosmos_client = get_cosmos_client()
        if not self.cosmos_client:
            raise RuntimeError("Failed to initialize Cosmos DB client")

        self.database_name = os.environ.get("AZURE_COSMOSDB_DATABASE")
        self.container_name = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects")

        if not self.database_name:
            raise RuntimeError("AZURE_COSMOSDB_DATABASE environment variable not set")

        self.database = self.cosmos_client.get_database_client(self.database_name)
        self.container = self.database.get_container_client(self.container_name)

    async def find_project_by_function_app(self, function_app_name: str) -> Optional[Dict[str, Any]]:
        """Find project by function app name."""
        try:
            query = "SELECT * FROM c WHERE c.function_app_name = @function_app_name"
            parameters = [{"name": "@function_app_name", "value": function_app_name}]

            async for item in self.container.query_items(
                query=query,
                parameters=parameters,
                partition_key=None
            ):
                logger.info(f"Found project: {item.get('name', 'Unknown')} (ID: {item.get('id')})")
                return item

            logger.warning(f"No project found with function app name: {function_app_name}")
            return None

        except Exception as e:
            logger.error(f"Error searching for project: {e}")
            return None

    async def update_project_environment(self, project: Dict[str, Any], function_keys: Dict[str, str], dry_run: bool = False) -> bool:
        """Update project environment with function keys."""
        project_id = project.get('id')
        project_name = project.get('name', 'Unknown')

        logger.info(f"Updating environment for project {project_name} ({project_id})")

        if dry_run:
            logger.info(f"DRY RUN: Would update environment for project {project_id}")
            for key, value in function_keys.items():
                logger.info(f"DRY RUN: Would set {key} = {value[:10]}...")
            return True

        try:
            # Get current environment or create new one
            environment = project.get('environment', {})

            # Update with function keys
            for key, value in function_keys.items():
                environment[key] = value
                logger.info(f"Updated {key} in environment")

            # Update the project document
            project['environment'] = environment

            # Replace the document in Cosmos DB
            await self.container.replace_item(
                item=project['id'],
                body=project
            )

            logger.info(f"Successfully updated environment for project {project_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating environment for project {project_id}: {e}")
            return False

    async def close(self):
        """Close the Cosmos DB client."""
        if self.cosmos_client:
            await self.cosmos_client.close()


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Get default function keys and update Cosmos DB")
    parser.add_argument(
        "--function-app-name",
        required=True,
        help="Name of the function app to process"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )

    args = parser.parse_args()

    cosmos_manager = None
    try:
        logger.info(f"Getting default function keys for: {args.function_app_name}")

        # Initialize Cosmos manager
        cosmos_manager = CosmosManager()

        # Find the project
        project = await cosmos_manager.find_project_by_function_app(args.function_app_name)
        if not project:
            logger.error(f"Could not find project for function app: {args.function_app_name}")
            exit(1)

        # Get default function keys for each function
        function_keys = {}
        success_count = 0
        total_count = len(FUNCTION_NAMES)

        for env_key, function_name in FUNCTION_NAMES.items():
            default_key = await get_default_function_key(args.function_app_name, function_name)
            if default_key:
                function_keys[env_key] = default_key
                success_count += 1
            else:
                logger.error(f"Failed to get default key for {function_name}")

        logger.info(f"Retrieved {success_count}/{total_count} default function keys")

        if success_count > 0:
            # Update Cosmos DB environment with the retrieved keys
            if await cosmos_manager.update_project_environment(project, function_keys, args.dry_run):
                logger.info("Successfully updated project environment in Cosmos DB")
            else:
                logger.error("Failed to update project environment in Cosmos DB")

        if success_count == total_count:
            logger.info("All function keys processed successfully")

            # Print summary
            print(f"\n{'='*80}")
            print("RETRIEVED DEFAULT FUNCTION KEYS")
            print(f"{'='*80}")
            for env_key, key_value in function_keys.items():
                print(f"{env_key}: {key_value}")
            print(f"{'='*80}")

        else:
            logger.error("Some function keys failed to process")
            exit(1)

    except Exception as e:
        logger.error(f"Error in main: {e}")
        exit(1)
    finally:
        # Clean up Cosmos client
        if cosmos_manager:
            await cosmos_manager.close()


if __name__ == "__main__":
    asyncio.run(main())

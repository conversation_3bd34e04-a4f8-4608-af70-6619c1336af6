#!/usr/bin/env python3
"""
Enhanced repair utility that can both retrieve missing credentials AND recreate missing resources.

This script:
1. Fetches a project's state from Cosmos DB
2. Checks for missing resource details
3. Attempts to retrieve them from Azure
4. If resources don't exist, recreates them using deployment functions
"""

import asyncio
import json
import logging
import os
import subprocess
import sys
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Tuple

# Add parent directory to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backend.services.project_service import ProjectDataService
from azure.identity import DefaultAzureCredential
from azure.mgmt.resource import ResourceManagementClient
from azure.mgmt.storage import StorageManagementClient
from azure.mgmt.search import SearchManagementClient
from azure.mgmt.web import WebSiteManagementClient

# Import deployment functions from deploy_project_resources.py
from deploy_project_resources import (
    deploy_main_bicep,
    deploy_storage_containers,
    deploy_search_index,
    deploy_function_app_from_acr,
    deploy_event_grid,
    update_project_with_resource_data,
    update_deployment_status
)

# Configure Logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)

# --- Helper Functions ---

async def _run_az_command(cmd: list[str]) -> Optional[str]:
    """Runs an Azure CLI command and returns the output."""
    try:
        process = await asyncio.to_thread(
            subprocess.run,
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return process.stdout.strip()
    except subprocess.CalledProcessError as e:
        logging.error(f"AZ CLI command failed: {' '.join(cmd)}\nError: {e.stderr.strip()}")
        return None

async def _find_resource_by_tag(resource_type: str, project_id: str, resource_group: str) -> Optional[str]:
    """Finds a resource in a resource group by its type and project-id tag."""
    cmd = [
        "az", "resource", "list",
        "--resource-group", resource_group,
        "--resource-type", resource_type,
        "--query", f"[?tags.\"project-id\"=='{project_id}'].name | [0]",
        "-o", "tsv"
    ]
    return await _run_az_command(cmd)

async def _generate_sas_token(account_name: str, resource_group: str) -> Optional[str]:
    """Generates a new SAS token for a storage account."""
    logging.info(f"Generating SAS token for storage account: {account_name}")
    expiry_date = (datetime.now(timezone.utc) + timedelta(days=365)).strftime("%Y-%m-%dT%H:%M:%SZ")
    cmd = [
        "az", "storage", "account", "generate-sas",
        "--account-name", account_name,
        "--resource-group", resource_group,
        "--resource-types", "sco",
        "--services", "bfqt",
        "--permissions", "rwdlacuptf",
        "--expiry", expiry_date,
        "--https-only",
        "--output", "tsv"
    ]
    return await _run_az_command(cmd)

async def _get_search_key(service_name: str, resource_group: str) -> Optional[str]:
    """Retrieves the primary admin key for a search service."""
    logging.info(f"Retrieving key for search service: {service_name}")
    cmd = [
        "az", "search", "admin-key", "show",
        "--service-name", service_name,
        "--resource-group", resource_group,
        "--query", "primaryKey",
        "-o", "tsv"
    ]
    return await _run_az_command(cmd)

async def _get_function_key(app_name: str, function_name: str, resource_group: str) -> Optional[str]:
    """Retrieves the default key for a specific function within a function app."""
    logging.info(f"Retrieving key for function: {function_name} in app: {app_name}")
    cmd = [
        "az", "functionapp", "function", "keys", "list",
        "--name", app_name,
        "--resource-group", resource_group,
        "--function-name", function_name,
        "--query", "default",
        "-o", "tsv"
    ]
    return await _run_az_command(cmd)

# --- Resource Recreation Functions ---

async def recreate_storage_resources(project: Dict[str, Any], resource_group: str, location: str) -> Dict[str, Any]:
    """Recreate storage account and containers if missing."""
    results = {"success": False, "resources": {}, "errors": []}
    
    try:
        # Deploy main infrastructure if storage account is missing
        if not project.get("storage_account_name"):
            logging.info("Storage account missing. Deploying main infrastructure...")
            
            # Use deploy_main_bicep from deploy_project_resources.py
            deployment_result = await deploy_main_bicep(
                project_id=project["id"],
                project_name=project["name"],
                resource_group=resource_group,
                location=location
            )
            
            if deployment_result["success"]:
                results["resources"].update(deployment_result["outputs"])
                project.update(deployment_result["outputs"])
            else:
                results["errors"].append(f"Failed to deploy main infrastructure: {deployment_result.get('error', 'Unknown error')}")
                return results
        
        # Deploy containers if storage account exists but containers are missing
        if project.get("storage_account_name") and not all([
            project.get("storage_container_uploads"),
            project.get("storage_container_input"),
            project.get("storage_container_output")
        ]):
            logging.info("Creating missing storage containers...")
            containers_result = await deploy_storage_containers(
                project["storage_account_name"],
                project["name"],
                resource_group
            )
            
            if containers_result["success"]:
                results["resources"].update(containers_result["containers"])
                project.update(containers_result["containers"])
            else:
                results["errors"].append("Failed to create storage containers")
        
        # Generate SAS token if missing
        if project.get("storage_account_name") and not project.get("storage_account_sas_token"):
            sas_token = await _generate_sas_token(project["storage_account_name"], resource_group)
            if sas_token:
                results["resources"]["storage_account_sas_token"] = sas_token
                project["storage_account_sas_token"] = sas_token
            else:
                results["errors"].append("Failed to generate SAS token")
        
        results["success"] = len(results["errors"]) == 0
        
    except Exception as e:
        logging.error(f"Error recreating storage resources: {e}")
        results["errors"].append(str(e))
    
    return results

async def recreate_search_resources(project: Dict[str, Any], resource_group: str, location: str) -> Dict[str, Any]:
    """Recreate search service and index if missing."""
    results = {"success": False, "resources": {}, "errors": []}
    
    try:
        # If search service doesn't exist, deploy main infrastructure
        if not project.get("search_service_name"):
            logging.info("Search service missing. Deploying main infrastructure...")
            
            deployment_result = await deploy_main_bicep(
                project_id=project["id"],
                project_name=project["name"],
                resource_group=resource_group,
                location=location
            )
            
            if deployment_result["success"]:
                results["resources"].update(deployment_result["outputs"])
                project.update(deployment_result["outputs"])
            else:
                results["errors"].append(f"Failed to deploy search service: {deployment_result.get('error', 'Unknown error')}")
                return results
        
        # Deploy search index if service exists but index is missing
        if project.get("search_service_name") and project.get("storage_account_name"):
            logging.info("Deploying search index configuration...")
            
            # Get search key if missing
            if not project.get("search_key"):
                search_key = await _get_search_key(project["search_service_name"], resource_group)
                if search_key:
                    project["search_key"] = search_key
                else:
                    results["errors"].append("Failed to retrieve search key")
                    return results
            
            # Deploy search index
            index_result = await deploy_search_index(
                search_service_name=project["search_service_name"],
                search_key=project["search_key"],
                storage_account_name=project["storage_account_name"],
                storage_sas_token=project.get("storage_account_sas_token", ""),
                container_name=project.get("storage_container_input", ""),
                project_name=project["name"]
            )
            
            if index_result["success"]:
                results["resources"]["search_index_deployed"] = True
            else:
                results["errors"].append("Failed to deploy search index")
        
        results["success"] = len(results["errors"]) == 0
        
    except Exception as e:
        logging.error(f"Error recreating search resources: {e}")
        results["errors"].append(str(e))
    
    return results

async def recreate_function_resources(project: Dict[str, Any], resource_group: str, location: str) -> Dict[str, Any]:
    """Recreate function app and event grid if missing."""
    results = {"success": False, "resources": {}, "errors": []}
    
    try:
        # Deploy function app if missing
        if not project.get("function_app_name"):
            logging.info("Function app missing. Deploying from ACR...")
            
            # Ensure we have required resources
            if not project.get("storage_account_name") or not project.get("search_service_name"):
                results["errors"].append("Cannot deploy function app without storage and search services")
                return results
            
            # Deploy function app from ACR
            function_result = await deploy_function_app_from_acr(
                project_id=project["id"],
                project_name=project["name"],
                resource_group=resource_group,
                location=location,
                storage_account_name=project["storage_account_name"],
                search_service_name=project["search_service_name"],
                search_key=project.get("search_key", ""),
                openai_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT", ""),
                openai_key=os.environ.get("AZURE_OPENAI_KEY", "")
            )
            
            if function_result["success"]:
                results["resources"]["function_app_name"] = function_result["function_app_name"]
                project["function_app_name"] = function_result["function_app_name"]
            else:
                results["errors"].append(f"Failed to deploy function app: {function_result.get('error', 'Unknown error')}")
                return results
        
        # Get function keys if missing
        if project.get("function_app_name"):
            functions_to_check = {
                "function_key_maturity": "HttpTriggerAppMaturityAssessment",
                "function_key_executive_summary": "HttpTriggerAppExecutiveSummary",
                "function_key_powerpoint": "HttpTriggerPowerPointGenerator",
            }
            
            for key_field, func_name in functions_to_check.items():
                if not project.get(key_field):
                    retrieved_key = await _get_function_key(project["function_app_name"], func_name, resource_group)
                    if retrieved_key:
                        results["resources"][key_field] = retrieved_key
                        project[key_field] = retrieved_key
                    else:
                        results["errors"].append(f"Failed to retrieve key for function '{func_name}'")
        
        # Deploy Event Grid if missing
        if project.get("storage_account_name") and project.get("function_app_name") and not project.get("event_grid_system_topic_name"):
            logging.info("Deploying Event Grid system topic...")
            
            event_grid_result = await deploy_event_grid(
                project_id=project["id"],
                project_name=project["name"],
                resource_group=resource_group,
                location=location,
                storage_account_name=project["storage_account_name"],
                function_app_name=project["function_app_name"]
            )
            
            if event_grid_result["success"]:
                results["resources"]["event_grid_system_topic_name"] = event_grid_result["system_topic_name"]
                project["event_grid_system_topic_name"] = event_grid_result["system_topic_name"]
            else:
                results["errors"].append(f"Failed to deploy Event Grid: {event_grid_result.get('error', 'Unknown error')}")
        
        results["success"] = len(results["errors"]) == 0
        
    except Exception as e:
        logging.error(f"Error recreating function resources: {e}")
        results["errors"].append(str(e))
    
    return results

# --- Main Enhanced Repair Function ---

async def repair_project_deployment_enhanced(
    project_id: str, 
    resource_group: str = "rg-internal-ai",
    location: str = "westeurope",
    recreate_missing: bool = True
) -> Dict[str, Any]:
    """
    Enhanced repair that can both retrieve credentials and recreate missing resources.
    
    Args:
        project_id: The project ID to repair
        resource_group: Azure resource group
        location: Azure region
        recreate_missing: If True, recreate missing resources. If False, only retrieve credentials.
    """
    logging.info(f"--- Starting enhanced repair process for project: {project_id} ---")
    logging.info(f"Mode: {'Recreate missing resources' if recreate_missing else 'Retrieve credentials only'}")
    
    service = ProjectDataService()
    project = await service.get_project(project_id)

    if not project:
        logging.error(f"Repair failed: Project {project_id} not found in Cosmos DB.")
        return {"status": "failed", "message": "Project not found."}

    manual_actions = []
    actions_taken = []
    resources_created = []

    # Phase 1: Try to find existing resources
    logging.info("Phase 1: Searching for existing resources...")
    
    for resource_key, resource_type in [
        ("storage_account_name", "Microsoft.Storage/storageAccounts"),
        ("search_service_name", "Microsoft.Search/searchServices"),
        ("function_app_name", "Microsoft.Web/sites"),
    ]:
        if not project.get(resource_key):
            logging.warning(f"Missing '{resource_key}'. Searching in Azure...")
            found_name = await _find_resource_by_tag(resource_type, project_id, resource_group)
            if found_name:
                project[resource_key] = found_name
                actions_taken.append(f"Found existing '{resource_key}': {found_name}")
            else:
                logging.info(f"Resource '{resource_type}' not found in Azure")

    # Phase 2: Retrieve credentials for existing resources
    logging.info("Phase 2: Retrieving credentials for existing resources...")
    
    if project.get("storage_account_name") and not project.get("storage_account_sas_token"):
        sas_token = await _generate_sas_token(project["storage_account_name"], resource_group)
        if sas_token:
            project["storage_account_sas_token"] = sas_token
            actions_taken.append("Generated and updated missing Storage Account SAS token.")

    if project.get("search_service_name") and not project.get("search_key"):
        search_key = await _get_search_key(project["search_service_name"], resource_group)
        if search_key:
            project["search_key"] = search_key
            actions_taken.append("Retrieved and updated missing Search Service key.")

    # Phase 3: Recreate missing resources if enabled
    if recreate_missing:
        logging.info("Phase 3: Recreating missing resources...")
        
        # Recreate storage resources
        if not project.get("storage_account_name") or not all([
            project.get("storage_container_uploads"),
            project.get("storage_container_input"),
            project.get("storage_container_output")
        ]):
            storage_result = await recreate_storage_resources(project, resource_group, location)
            if storage_result["success"]:
                resources_created.extend(f"Storage: {k}" for k in storage_result["resources"].keys())
                actions_taken.append(f"Recreated storage resources: {list(storage_result['resources'].keys())}")
            else:
                manual_actions.extend(storage_result["errors"])
        
        # Recreate search resources
        if not project.get("search_service_name"):
            search_result = await recreate_search_resources(project, resource_group, location)
            if search_result["success"]:
                resources_created.extend(f"Search: {k}" for k in search_result["resources"].keys())
                actions_taken.append(f"Recreated search resources: {list(search_result['resources'].keys())}")
            else:
                manual_actions.extend(search_result["errors"])
        
        # Recreate function resources
        if not project.get("function_app_name") or not project.get("event_grid_system_topic_name"):
            function_result = await recreate_function_resources(project, resource_group, location)
            if function_result["success"]:
                resources_created.extend(f"Function: {k}" for k in function_result["resources"].keys())
                actions_taken.append(f"Recreated function resources: {list(function_result['resources'].keys())}")
            else:
                manual_actions.extend(function_result["errors"])
    else:
        # Just identify what would need to be recreated
        if not project.get("storage_account_name"):
            manual_actions.append("Storage account needs to be created")
        if not project.get("search_service_name"):
            manual_actions.append("Search service needs to be created")
        if not project.get("function_app_name"):
            manual_actions.append("Function app needs to be created")
        if not project.get("event_grid_system_topic_name"):
            manual_actions.append("Event Grid system topic needs to be created")

    # Phase 4: Update project in Cosmos DB
    if actions_taken or resources_created:
        logging.info("Phase 4: Saving updated project data to Cosmos DB...")
        try:
            update_service = ProjectDataService()
            update_success = await update_service.update_project(project_id, project)
            if not update_success:
                raise Exception("The update_project method returned False.")
            
            # Update deployment status
            completion_percentage = calculate_completion_percentage(project)
            status = "completed" if completion_percentage == 100 else "partial"
            
            await update_deployment_status(project_id, {
                "status": status,
                "message": f"Enhanced repair completed. {len(resources_created)} resources created.",
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "details": {
                    "completion_percentage": completion_percentage,
                    "resources_created": resources_created,
                    "actions_taken": actions_taken
                }
            })
            
        except Exception as e:
            logging.error(f"--- Repair process failed: Could not update Cosmos DB. Error: {e} ---")
            manual_actions.append("Failed to save updated data to Cosmos DB. Please check DB connectivity and permissions.")
            return {
                "status": "failed",
                "message": "Repairs were made but could not be saved to Cosmos DB.",
                "manual_actions_required": manual_actions,
                "resources_created": resources_created
            }

    # Finalize
    logging.info("--- Enhanced repair process finished ---")
    
    if not actions_taken and not resources_created:
        return {
            "status": "success",
            "message": "No repair needed. All resources and credentials are present.",
            "manual_actions_required": manual_actions
        }
    
    status = "partial_success" if manual_actions else "success"
    message = f"Repair complete. Actions: {len(actions_taken)}, Resources created: {len(resources_created)}"
    if manual_actions:
        message += f", Manual actions required: {len(manual_actions)}"

    return {
        "status": status,
        "message": message,
        "actions_taken": actions_taken,
        "resources_created": resources_created,
        "manual_actions_required": manual_actions,
    }

def calculate_completion_percentage(project: Dict[str, Any]) -> int:
    """Calculate deployment completion percentage based on resources present."""
    checks = [
        project.get("storage_account_name") is not None,
        project.get("storage_container_uploads") is not None,
        project.get("storage_container_input") is not None,
        project.get("storage_container_output") is not None,
        project.get("search_service_name") is not None,
        project.get("search_key") is not None,
        project.get("function_app_name") is not None,
        project.get("function_key_maturity") is not None,
        project.get("event_grid_system_topic_name") is not None,
    ]
    return int((sum(checks) / len(checks)) * 100)

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python -m scripts.repair_deployment_enhanced <project_id> [--recreate|--retrieve-only]")
        sys.exit(1)

    project_to_repair = sys.argv[1]
    recreate_mode = "--recreate" in sys.argv or len(sys.argv) == 2  # Default to recreate

    from dotenv import load_dotenv
    load_dotenv()

    result = asyncio.run(repair_project_deployment_enhanced(
        project_to_repair,
        recreate_missing=recreate_mode
    ))
    print(json.dumps(result, indent=2))
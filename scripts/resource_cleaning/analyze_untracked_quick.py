#!/usr/bin/env python3
"""
Quick analysis of untracked function apps by querying them in batch
"""

import json
import subprocess
import os
from datetime import datetime
from collections import defaultdict

def main():
    # Load the comparison results
    script_dir = os.path.dirname(os.path.abspath(__file__))
    results_file = os.path.join(script_dir, 'resource_comparison_results.json')
    
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    untracked_functions = data.get('untracked_resources', {}).get('functionApps', [])
    
    print(f"\nAnalyzing {len(untracked_functions)} untracked function apps...")
    print("=" * 100)
    
    # Get all function apps in one query
    print("Fetching function app states...")
    
    # Get all function apps in the resource group
    result = subprocess.run(
        [
            "az", "functionapp", "list",
            "--resource-group", "rg-internal-ai",
            "--query", "[].{name:name, state:state, enabled:enabled, kind:kind, location:location}",
            "-o", "json"
        ],
        capture_output=True,
        text=True,
        check=True
    )
    
    all_func_states = json.loads(result.stdout) if result.stdout else []
    
    # Create lookup dict
    func_state_lookup = {f['name']: f for f in all_func_states}
    
    # Categorize by state
    categorized = defaultdict(list)
    
    for func in untracked_functions:
        func_name = func['name']
        state_info = func_state_lookup.get(func_name, {
            'state': 'NotFound',
            'enabled': 'Unknown',
            'kind': 'Unknown'
        })
        
        category = state_info.get('state', 'Unknown')
        categorized[category].append({
            'name': func_name,
            'location': func['location'],
            'state': state_info.get('state', 'Unknown'),
            'enabled': state_info.get('enabled', 'Unknown'),
            'kind': state_info.get('kind', 'Unknown')
        })
    
    # Generate report
    print("\n" + "=" * 100)
    print("UNTRACKED FUNCTION APPS ANALYSIS REPORT")
    print("=" * 100)
    print(f"Generated at: {datetime.utcnow().isoformat()}Z")
    print(f"Total untracked function apps: {len(untracked_functions)}")
    print()
    
    # Summary by state
    print("SUMMARY BY STATE:")
    print("-" * 50)
    for state, funcs in sorted(categorized.items()):
        print(f"{state}: {len(funcs)} function apps")
    print()
    
    # Show first 5 of each category
    for state, funcs in sorted(categorized.items()):
        print(f"\n{state.upper()} FUNCTION APPS ({len(funcs)} total, showing first 5):")
        print("-" * 100)
        
        if state == "Stopped":
            print("⚠️  These function apps are stopped and likely candidates for deletion:")
        elif state == "Running":
            print("✓ These function apps are running but not tracked in Cosmos DB:")
        
        for func in sorted(funcs, key=lambda x: x['name'])[:5]:
            print(f"  - {func['name']:<50} | Location: {func['location']:<15} | Enabled: {func['enabled']}")
        
        if len(funcs) > 5:
            print(f"  ... and {len(funcs) - 5} more")
    
    # Save detailed report
    report_data = {
        'generated_at': datetime.utcnow().isoformat() + 'Z',
        'total_untracked': len(untracked_functions),
        'categorized': dict(categorized),
        'summary': {state: len(funcs) for state, funcs in categorized.items()}
    }
    
    output_file = os.path.join(script_dir, 'untracked_functions_analysis.json')
    with open(output_file, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\nDetailed analysis saved to: {output_file}")
    
    # Recommendations
    print("\n" + "=" * 100)
    print("RECOMMENDATIONS:")
    print("-" * 50)
    
    stopped_count = len(categorized.get('Stopped', []))
    running_count = len(categorized.get('Running', []))
    
    if stopped_count > 0:
        print(f"1. Found {stopped_count} STOPPED function apps that are likely safe to delete")
        print("   Example: az functionapp delete --name <function-name> --resource-group rg-internal-ai")
    
    if running_count > 0:
        print(f"\n2. Found {running_count} RUNNING function apps not tracked in Cosmos DB")
        print("   These may be:")
        print("   - Test/development functions")
        print("   - Functions created outside the normal deployment process")
        print("   - Orphaned functions from failed deployments")
    
    print("\n" + "=" * 100)

if __name__ == "__main__":
    main()
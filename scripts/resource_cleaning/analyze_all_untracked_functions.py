#!/usr/bin/env python3
"""
Script to analyze all untracked function apps and categorize them by their usage patterns
"""

import json
import subprocess
import logging
from datetime import datetime
from collections import defaultdict

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_function_app_state(function_app_name: str, resource_group: str) -> dict:
    """Get basic state information for a function app"""
    try:
        result = subprocess.run(
            [
                "az", "functionapp", "show",
                "--name", function_app_name,
                "--resource-group", resource_group,
                "--query", "{state:state, enabled:enabled, kind:kind, hostName:defaultHostName}",
                "-o", "json"
            ],
            capture_output=True,
            text=True,
            check=True
        )
        
        if result.stdout:
            return json.loads(result.stdout)
    except subprocess.CalledProcessError:
        return {"state": "Error", "enabled": "Unknown", "kind": "Unknown", "hostName": "Unknown"}

def main():
    # Load the comparison results
    import os
    script_dir = os.path.dirname(os.path.abspath(__file__))
    results_file = os.path.join(script_dir, 'resource_comparison_results.json')
    
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    untracked_functions = data.get('untracked_resources', {}).get('functionApps', [])
    
    print(f"\nAnalyzing {len(untracked_functions)} untracked function apps...")
    print("=" * 100)
    
    # Categorize by state
    categorized = defaultdict(list)
    
    for i, func in enumerate(untracked_functions):
        func_name = func['name']
        func_location = func['location']
        
        # Get function app state
        state_info = get_function_app_state(func_name, "rg-internal-ai")
        
        category = state_info['state']
        categorized[category].append({
            'name': func_name,
            'location': func_location,
            'state': state_info['state'],
            'enabled': state_info['enabled'],
            'kind': state_info['kind'],
            'hostName': state_info['hostName']
        })
        
        # Progress indicator
        if (i + 1) % 10 == 0:
            print(f"Processed {i + 1}/{len(untracked_functions)} function apps...")
    
    # Generate report
    print("\n" + "=" * 100)
    print("UNTRACKED FUNCTION APPS ANALYSIS REPORT")
    print("=" * 100)
    print(f"Generated at: {datetime.utcnow().isoformat()}Z")
    print(f"Total untracked function apps: {len(untracked_functions)}")
    print()
    
    # Summary by state
    print("SUMMARY BY STATE:")
    print("-" * 50)
    for state, funcs in sorted(categorized.items()):
        print(f"{state}: {len(funcs)} function apps")
    print()
    
    # Detailed breakdown
    for state, funcs in sorted(categorized.items()):
        print(f"\n{state.upper()} FUNCTION APPS ({len(funcs)}):")
        print("-" * 100)
        
        if state == "Stopped":
            print("⚠️  These function apps are stopped and likely candidates for deletion:")
        elif state == "Running":
            print("✓ These function apps are running but not tracked in Cosmos DB - needs investigation:")
        
        for func in sorted(funcs, key=lambda x: x['name']):
            print(f"  - {func['name']:<50} | Location: {func['location']:<15} | Enabled: {func['enabled']}")
    
    # Save detailed report
    report_data = {
        'generated_at': datetime.utcnow().isoformat() + 'Z',
        'total_untracked': len(untracked_functions),
        'categorized': dict(categorized),
        'summary': {state: len(funcs) for state, funcs in categorized.items()}
    }
    
    output_file = os.path.join(script_dir, 'untracked_functions_analysis.json')
    with open(output_file, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    print(f"\nDetailed analysis saved to: untracked_functions_analysis.json")
    
    # Recommendations
    print("\n" + "=" * 100)
    print("RECOMMENDATIONS:")
    print("-" * 50)
    
    stopped_count = len(categorized.get('Stopped', []))
    running_count = len(categorized.get('Running', []))
    
    if stopped_count > 0:
        print(f"1. Found {stopped_count} STOPPED function apps that are likely safe to delete")
        print("   Run: az functionapp delete --name <function-name> --resource-group rg-internal-ai")
    
    if running_count > 0:
        print(f"\n2. Found {running_count} RUNNING function apps not tracked in Cosmos DB")
        print("   These may be:")
        print("   - Test/development functions")
        print("   - Functions created outside the normal deployment process")
        print("   - Orphaned functions from failed deployments")
        print("   Recommend investigating each one individually")
    
    print("\n" + "=" * 100)

if __name__ == "__main__":
    main()
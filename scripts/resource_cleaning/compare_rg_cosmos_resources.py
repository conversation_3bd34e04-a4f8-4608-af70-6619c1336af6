#!/usr/bin/env python3
"""
Script to compare Azure resources in a resource group with those tracked in Cosmos DB.
Lists:
1. All resources in the resource group
2. All resources detailed in the Cosmos DB project container
3. Resources that are in the resource group but not in Cosmos DB
"""

import os
import json
import subprocess
import logging
import re
from typing import Dict, List, Set
from azure.cosmos import CosmosClient
from collections import defaultdict
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
RESOURCE_GROUP = "rg-internal-ai"
COSMOS_ACCOUNT_NAME = "internal-ai-conversation-history-db"
COSMOS_DB_NAME = "db_conversation_history"  # Fixed: correct database name
COSMOS_CONTAINER_NAME = "projects"

def get_cosmos_connection_string():
    """Get Cosmos DB connection string from Azure CLI"""
    logger.info(f"Getting connection string for Cosmos account: {COSMOS_ACCOUNT_NAME}")
    try:
        result = subprocess.run(
            [
                "az", "cosmosdb", "keys", "list",
                "--name", COSMOS_ACCOUNT_NAME,
                "--resource-group", RESOURCE_GROUP,
                "--type", "connection-strings",
                "--query", "connectionStrings[0].connectionString",
                "-o", "tsv"
            ],
            capture_output=True,
            text=True,
            check=True
        )
        connection_string = result.stdout.strip()
        if connection_string:
            logger.info("Successfully obtained connection string")
        return connection_string
    except subprocess.CalledProcessError as e:
        logger.error(f"Error getting Cosmos DB connection string: {e}")
        return None

def get_resources_from_resource_group() -> Dict[str, List[Dict]]:
    """Get all resources from the Azure resource group"""
    logger.info(f"Fetching resources from resource group: {RESOURCE_GROUP}")
    resources_by_type = defaultdict(list)
    
    try:
        # Get Function Apps separately
        logger.info("Fetching Function Apps...")
        result = subprocess.run(
            [
                "az", "functionapp", "list",
                "--resource-group", RESOURCE_GROUP,
                "--query", "[].{name:name, type:type, id:id, location:location, kind:kind}",
                "-o", "json"
            ],
            capture_output=True,
            text=True,
            check=True
        )
        function_apps = json.loads(result.stdout)
        logger.info(f"Found {len(function_apps)} Function Apps")
        for app in function_apps:
            app['type'] = 'Microsoft.Web/sites'  # Ensure consistent type
            resources_by_type['functionApps'].append(app)
        
        # Get Web Apps separately
        logger.info("Fetching Web Apps...")
        result = subprocess.run(
            [
                "az", "webapp", "list",
                "--resource-group", RESOURCE_GROUP,
                "--query", "[].{name:name, type:type, id:id, location:location, kind:kind}",
                "-o", "json"
            ],
            capture_output=True,
            text=True,
            check=True
        )
        web_apps = json.loads(result.stdout)
        # Filter out function apps from web apps (they share the same resource type)
        function_app_names = {app['name'] for app in function_apps}
        web_apps = [app for app in web_apps if app['name'] not in function_app_names]
        logger.info(f"Found {len(web_apps)} Web Apps (excluding Function Apps)")
        for app in web_apps:
            resources_by_type['webApps'].append(app)
        
        # Get all other resources
        logger.info("Fetching other resources...")
        result = subprocess.run(
            [
                "az", "resource", "list",
                "--resource-group", RESOURCE_GROUP,
                "--query", "[?type!='Microsoft.Web/sites'].{name:name, type:type, id:id, location:location}",
                "-o", "json"
            ],
            capture_output=True,
            text=True,
            check=True
        )
        other_resources = json.loads(result.stdout)
        logger.info(f"Found {len(other_resources)} other resources")
        
        # Group other resources by type
        for resource in other_resources:
            resource_type = resource['type'].split('/')[-1]
            resources_by_type[resource_type].append(resource)
        
        # Log summary
        total_resources = sum(len(resources) for resources in resources_by_type.values())
        logger.info(f"Total resources found: {total_resources}")
        logger.info(f"Grouped resources into {len(resources_by_type)} different types")
        
        return dict(resources_by_type)
    except subprocess.CalledProcessError as e:
        logger.error(f"Error listing resources: {e}")
        return {}

def get_resources_from_cosmos_db() -> Dict[str, Set[str]]:
    """Get all resources tracked in Cosmos DB project container"""
    connection_string = get_cosmos_connection_string()
    if not connection_string:
        return {}
    
    try:
        logger.info(f"Connecting to Cosmos DB account: {COSMOS_ACCOUNT_NAME}")
        logger.info(f"Using database: {COSMOS_DB_NAME}, container: {COSMOS_CONTAINER_NAME}")
        
        client = CosmosClient.from_connection_string(connection_string)
        database = client.get_database_client(COSMOS_DB_NAME)
        container = database.get_container_client(COSMOS_CONTAINER_NAME)
        
        # Query all project documents
        query = "SELECT * FROM c WHERE c.type = 'project'"
        logger.info("Querying projects from Cosmos DB...")
        items = list(container.query_items(query=query, enable_cross_partition_query=True))
        
        logger.info(f"Successfully retrieved {len(items)} projects from Cosmos DB")
        
        # Extract resource names from projects
        cosmos_resources = defaultdict(set)
        resources_per_project = {}
        all_function_apps_found = set()  # Track all function apps found in any field
        
        for project in items:
            project_name = project.get('name', 'Unknown')
            project_id = project.get('id', 'Unknown')
            resources_in_project = 0
            
            # Storage resources
            if 'storage_account_name' in project and project['storage_account_name']:
                cosmos_resources['storageAccounts'].add(project['storage_account_name'])
                resources_in_project += 1
            
            # Search resources
            if 'search_service_name' in project and project['search_service_name']:
                cosmos_resources['searchServices'].add(project['search_service_name'])
                resources_in_project += 1
            
            # Function App resources - check multiple possible fields
            function_app_fields = [
                'function_app_name',
                'functionAppName', 
                'function_app',
                'functionApp',
                'func_app_name',
                'func_name'
            ]
            
            for field in function_app_fields:
                if field in project and project[field]:
                    func_name = project[field]
                    cosmos_resources['sites'].add(func_name)
                    all_function_apps_found.add(func_name)
                    resources_in_project += 1
                    logger.debug(f"Found function app '{func_name}' in field '{field}' for project '{project_name}'")
            
            # Also scan the entire document for any field containing function app references
            # This catches cases where function apps might be nested in other structures
            func_pattern = r'func-[a-zA-Z0-9\-]+'
            project_str = json.dumps(project)
            import re
            found_funcs = re.findall(func_pattern, project_str)
            for func in found_funcs:
                all_function_apps_found.add(func)
                cosmos_resources['sites'].add(func)
            
            # Event Grid resources
            if 'event_grid_topic_name' in project and project['event_grid_topic_name']:
                cosmos_resources['topics'].add(project['event_grid_topic_name'])
                resources_in_project += 1
            
            if 'event_grid_system_topic_name' in project and project['event_grid_system_topic_name']:
                cosmos_resources['systemTopics'].add(project['event_grid_system_topic_name'])
                resources_in_project += 1
            
            resources_per_project[project_name] = resources_in_project
            logger.debug(f"Project '{project_name}' (ID: {project_id}) has {resources_in_project} tracked resources")
        
        # Log summary
        logger.info(f"Total function apps found across all fields and patterns: {len(all_function_apps_found)}")
        total_unique_resources = sum(len(resources) for resources in cosmos_resources.values())
        logger.info(f"Total unique resources tracked across all projects: {total_unique_resources}")
        logger.info(f"Resource breakdown by type:")
        for resource_type, resources in cosmos_resources.items():
            logger.info(f"  - {resource_type}: {len(resources)} resources")
        
        return dict(cosmos_resources)
    
    except Exception as e:
        logger.error(f"Error querying Cosmos DB: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {}

def compare_resources():
    """Compare resources and generate report"""
    print("=" * 80)
    print("Azure Resource Group vs Cosmos DB Comparison Report")
    print(f"Resource Group: {RESOURCE_GROUP}")
    print(f"Cosmos DB: {COSMOS_DB_NAME}")
    print("=" * 80)
    
    # Get resources from both sources
    rg_resources = get_resources_from_resource_group()
    cosmos_resources = get_resources_from_cosmos_db()
    
    # Prepare JSON output structure
    output_data = {
        "resource_group": RESOURCE_GROUP,
        "cosmos_account": COSMOS_ACCOUNT_NAME,
        "cosmos_database": COSMOS_DB_NAME,
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "resource_group_resources": {},
        "cosmos_db_resources": {},
        "untracked_resources": {},
        "summary": {}
    }
    
    # 1. List all resources in the resource group
    print("\n1. ALL RESOURCES IN RESOURCE GROUP:")
    print("-" * 40)
    total_rg_resources = 0
    for resource_type, resources in sorted(rg_resources.items()):
        print(f"\n{resource_type} ({len(resources)}):")
        output_data["resource_group_resources"][resource_type] = []
        for resource in resources:
            print(f"  - {resource['name']} (Location: {resource['location']})")
            output_data["resource_group_resources"][resource_type].append({
                "name": resource['name'],
                "location": resource['location'],
                "id": resource['id']
            })
        total_rg_resources += len(resources)
    print(f"\nTotal resources in resource group: {total_rg_resources}")
    
    # 2. List all resources tracked in Cosmos DB
    print("\n\n2. ALL RESOURCES TRACKED IN COSMOS DB:")
    print("-" * 40)
    total_cosmos_resources = 0
    for resource_type, resource_names in sorted(cosmos_resources.items()):
        print(f"\n{resource_type} ({len(resource_names)}):")
        output_data["cosmos_db_resources"][resource_type] = list(sorted(resource_names))
        for name in sorted(resource_names):
            print(f"  - {name}")
        total_cosmos_resources += len(resource_names)
    print(f"\nTotal unique resources tracked in Cosmos DB: {total_cosmos_resources}")
    
    # 3. List resources in RG but not in Cosmos DB
    print("\n\n3. RESOURCES IN RESOURCE GROUP BUT NOT IN COSMOS DB:")
    print("-" * 40)
    untracked_resources = defaultdict(list)
    
    # Map of Azure resource types to Cosmos DB tracking
    resource_type_mapping = {
        'storageAccounts': 'storageAccounts',
        'searchServices': 'searchServices',
        'functionApps': 'sites',  # Function Apps are tracked as 'sites' in Cosmos DB
        'webApps': 'sites',  # Web Apps are also 'sites' but we track them separately
        'topics': 'topics',  # Event Grid Topics
        'systemTopics': 'systemTopics'  # Event Grid System Topics
    }
    
    for azure_type, cosmos_type in resource_type_mapping.items():
        if azure_type in rg_resources:
            cosmos_tracked = cosmos_resources.get(cosmos_type, set())
            for resource in rg_resources[azure_type]:
                if resource['name'] not in cosmos_tracked:
                    untracked_resources[azure_type].append(resource)
    
    # Also check for resource types that might not be tracked at all
    for resource_type, resources in rg_resources.items():
        if resource_type not in resource_type_mapping:
            untracked_resources[resource_type].extend(resources)
    
    # Format untracked resources for JSON output
    for resource_type, resources in untracked_resources.items():
        output_data["untracked_resources"][resource_type] = []
        for resource in resources:
            output_data["untracked_resources"][resource_type].append({
                "name": resource['name'],
                "location": resource['location'],
                "id": resource['id']
            })
    
    if untracked_resources:
        for resource_type, resources in sorted(untracked_resources.items()):
            print(f"\n{resource_type} ({len(resources)}):")
            for resource in resources:
                print(f"  - {resource['name']} (Location: {resource['location']})")
    else:
        print("\nAll tracked resource types in the resource group are accounted for in Cosmos DB.")
    
    # Summary
    total_untracked = sum(len(resources) for resources in untracked_resources.values())
    
    # Create resource type breakdown
    resource_type_breakdown = {}
    for resource_type, resources in rg_resources.items():
        resource_type_breakdown[resource_type] = len(resources)
    
    output_data["summary"] = {
        "total_resources_in_resource_group": total_rg_resources,
        "total_resource_types_in_resource_group": len(rg_resources),
        "resource_type_breakdown": resource_type_breakdown,
        "total_unique_resources_in_cosmos_db": total_cosmos_resources,
        "untracked_resource_types": len(untracked_resources),
        "total_untracked_resources": total_untracked
    }
    
    print("\n" + "=" * 80)
    print("SUMMARY:")
    print(f"- Total resources in resource group: {total_rg_resources}")
    print(f"- Total resource types in resource group: {len(rg_resources)}")
    print("\nResource Type Breakdown:")
    for resource_type, count in sorted(resource_type_breakdown.items(), key=lambda x: x[1], reverse=True):
        print(f"  - {resource_type}: {count}")
    print(f"\n- Total unique resources tracked in Cosmos DB: {total_cosmos_resources}")
    print(f"- Untracked resource types: {len(untracked_resources)}")
    print(f"- Total untracked resources: {total_untracked}")
    print("=" * 80)
    
    # Write JSON output to file
    output_file = os.path.join(os.path.dirname(__file__), "resource_comparison_results.json")
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\nResults written to: {output_file}")
    
    return output_data

if __name__ == "__main__":
    try:
        compare_resources()
    except Exception as e:
        print(f"Error running comparison: {e}")
        import traceback
        traceback.print_exc()
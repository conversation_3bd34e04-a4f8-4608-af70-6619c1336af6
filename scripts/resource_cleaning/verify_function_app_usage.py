#!/usr/bin/env python3
"""
Script to verify if a specific function app is truly unused by checking:
1. Cosmos DB for any references
2. Application Insights for recent activity
3. Function app configuration and settings
"""

import os
import json
import subprocess
import logging
from typing import Dict, List, Optional
from azure.cosmos import CosmosClient
from datetime import datetime, timedelta

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
RESOURCE_GROUP = "rg-internal-ai"
COSMOS_ACCOUNT_NAME = "internal-ai-conversation-history-db"
COSMOS_DB_NAME = "db_conversation_history"
COSMOS_CONTAINER_NAME = "projects"

def get_cosmos_connection_string():
    """Get Cosmos DB connection string from Azure CLI"""
    try:
        result = subprocess.run(
            [
                "az", "cosmosdb", "keys", "list",
                "--name", COSMOS_ACCOUNT_NAME,
                "--resource-group", RESOURCE_GROUP,
                "--type", "connection-strings",
                "--query", "connectionStrings[0].connectionString",
                "-o", "tsv"
            ],
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logger.error(f"Error getting Cosmos DB connection string: {e}")
        return None

def search_function_app_in_cosmos(function_app_name: str) -> List[Dict]:
    """Search for any reference to the function app in Cosmos DB"""
    connection_string = get_cosmos_connection_string()
    if not connection_string:
        return []
    
    try:
        client = CosmosClient.from_connection_string(connection_string)
        database = client.get_database_client(COSMOS_DB_NAME)
        container = database.get_container_client(COSMOS_CONTAINER_NAME)
        
        # Search in all documents for any reference to the function app
        query = f"SELECT * FROM c WHERE CONTAINS(c, '{function_app_name}')"
        logger.info(f"Searching Cosmos DB for references to '{function_app_name}'...")
        
        items = list(container.query_items(query=query, enable_cross_partition_query=True))
        
        if items:
            logger.info(f"Found {len(items)} documents containing '{function_app_name}'")
            for item in items:
                logger.info(f"  - Document ID: {item.get('id', 'Unknown')}, Type: {item.get('type', 'Unknown')}")
        else:
            logger.info(f"No references found for '{function_app_name}' in Cosmos DB")
        
        return items
        
    except Exception as e:
        logger.error(f"Error searching Cosmos DB: {e}")
        return []

def get_function_app_details(function_app_name: str) -> Optional[Dict]:
    """Get details about the function app"""
    try:
        # Get function app details
        result = subprocess.run(
            [
                "az", "functionapp", "show",
                "--name", function_app_name,
                "--resource-group", RESOURCE_GROUP,
                "-o", "json"
            ],
            capture_output=True,
            text=True,
            check=True
        )
        
        if result.stdout:
            app_details = json.loads(result.stdout)
            logger.info(f"\nFunction App Details for '{function_app_name}':")
            logger.info(f"  - State: {app_details.get('state', 'Unknown')}")
            logger.info(f"  - Enabled: {app_details.get('enabled', 'Unknown')}")
            logger.info(f"  - Kind: {app_details.get('kind', 'Unknown')}")
            logger.info(f"  - Runtime: {app_details.get('siteConfig', {}).get('functionAppScaleLimit', 'Unknown')}")
            logger.info(f"  - Created: {app_details.get('createdTime', 'Unknown')}")
            logger.info(f"  - Modified: {app_details.get('lastModifiedTime', 'Unknown')}")
            
            return app_details
    except subprocess.CalledProcessError:
        logger.error(f"Function app '{function_app_name}' not found or error accessing it")
        return None

def check_function_invocations(function_app_name: str) -> bool:
    """Check if the function app has recent invocations"""
    try:
        # Get Application Insights component
        result = subprocess.run(
            [
                "az", "monitor", "app-insights", "component", "show",
                "--app", function_app_name,
                "--resource-group", RESOURCE_GROUP,
                "-o", "json"
            ],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0 and result.stdout:
            app_insights = json.loads(result.stdout)
            logger.info(f"\nApplication Insights found for '{function_app_name}'")
            
            # Query for recent function invocations (last 30 days)
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=30)
            
            query = "requests | where timestamp > ago(30d) | summarize count()"
            
            result = subprocess.run(
                [
                    "az", "monitor", "app-insights", "query",
                    "--app", app_insights['name'],
                    "--resource-group", RESOURCE_GROUP,
                    "--analytics-query", query,
                    "-o", "json"
                ],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and result.stdout:
                query_result = json.loads(result.stdout)
                if query_result.get('tables') and len(query_result['tables']) > 0:
                    count = query_result['tables'][0]['rows'][0][0] if query_result['tables'][0]['rows'] else 0
                    logger.info(f"  - Invocations in last 30 days: {count}")
                    return count > 0
        else:
            logger.info(f"No Application Insights found for '{function_app_name}'")
            
    except Exception as e:
        logger.error(f"Error checking function invocations: {e}")
    
    return False

def check_function_app_usage(function_app_name: str):
    """Main function to check if a function app is in use"""
    print(f"\n{'='*80}")
    print(f"Checking usage for Function App: {function_app_name}")
    print(f"{'='*80}\n")
    
    # 1. Check Cosmos DB references
    cosmos_refs = search_function_app_in_cosmos(function_app_name)
    
    # 2. Get function app details
    app_details = get_function_app_details(function_app_name)
    
    # 3. Check for recent invocations
    has_invocations = False
    if app_details:
        has_invocations = check_function_invocations(function_app_name)
    
    # Summary
    print(f"\n{'='*80}")
    print("USAGE SUMMARY:")
    print(f"- Cosmos DB References: {'Yes' if cosmos_refs else 'No'}")
    print(f"- Function App State: {app_details.get('state', 'Unknown') if app_details else 'Not Found'}")
    print(f"- Recent Activity (30 days): {'Yes' if has_invocations else 'No'}")
    
    if not cosmos_refs and not has_invocations and app_details and app_details.get('state') == 'Stopped':
        print(f"\n⚠️  This function app appears to be UNUSED and can likely be deleted")
    else:
        print(f"\n✓ This function app appears to be IN USE or requires further investigation")
    
    print(f"{'='*80}\n")
    
    return {
        'function_app_name': function_app_name,
        'cosmos_references': len(cosmos_refs),
        'state': app_details.get('state') if app_details else 'Not Found',
        'recent_activity': has_invocations,
        'safe_to_delete': not cosmos_refs and not has_invocations and app_details and app_details.get('state') == 'Stopped'
    }

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        function_app_name = sys.argv[1]
        check_function_app_usage(function_app_name)
    else:
        print("Usage: python verify_function_app_usage.py <function_app_name>")
        print("Example: python verify_function_app_usage.py func-projecta-7uar")
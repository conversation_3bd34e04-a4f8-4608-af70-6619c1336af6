#!/usr/bin/env python3
"""
Generate deletion commands for function apps with UUID pattern
"""

import json
import re
import os

def main():
    # Load the analysis results
    script_dir = os.path.dirname(os.path.abspath(__file__))
    analysis_file = os.path.join(script_dir, 'untracked_functions_analysis.json')
    
    with open(analysis_file, 'r') as f:
        data = json.load(f)
    
    # Pattern to match function apps with UUID format
    # Example: func-7a712b78-2840-4ac4-b61d-52fe5544df6c-5297
    uuid_pattern = re.compile(r'^func-[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}-[a-f0-9]{4}$')
    
    # Get all running functions
    running_functions = data.get('categorized', {}).get('Running', [])
    
    # Filter functions matching the UUID pattern
    uuid_functions = []
    for func in running_functions:
        if uuid_pattern.match(func['name']):
            uuid_functions.append(func['name'])
    
    # Sort for consistent output
    uuid_functions.sort()
    
    print(f"Found {len(uuid_functions)} function apps with UUID pattern out of {len(running_functions)} total untracked functions")
    print("=" * 100)
    
    # Generate deletion script
    deletion_script = os.path.join(script_dir, 'delete_uuid_functions.sh')
    
    with open(deletion_script, 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# Script to delete function apps with UUID pattern and their App Service Plans\n")
        f.write("# Generated on: $(date)\n\n")
        
        f.write("RESOURCE_GROUP=\"rg-internal-ai\"\n")
        f.write("FUNCTIONS_TO_DELETE=(\n")
        
        # Write function names
        for func in uuid_functions:
            f.write(f'    "{func}"\n')
        
        f.write(")\n\n")
        
        f.write("echo \"This script will delete ${#FUNCTIONS_TO_DELETE[@]} function apps\"\n")
        f.write("echo \"========================================\"\n")
        f.write("echo\n\n")
        
        f.write("# Confirmation prompt\n")
        f.write("read -p \"Are you sure you want to delete these function apps? (yes/no): \" confirm\n")
        f.write("if [ \"$confirm\" != \"yes\" ]; then\n")
        f.write("    echo \"Deletion cancelled.\"\n")
        f.write("    exit 0\n")
        f.write("fi\n\n")
        
        f.write("# Delete function apps\n")
        f.write("echo \"Starting deletion process...\"\n")
        f.write("echo\n\n")
        
        f.write("for func in \"${FUNCTIONS_TO_DELETE[@]}\"; do\n")
        f.write("    echo \"Deleting function app: $func\"\n")
        f.write("    az functionapp delete --name \"$func\" --resource-group \"$RESOURCE_GROUP\" --keep-empty-plan\n")
        f.write("    if [ $? -eq 0 ]; then\n")
        f.write("        echo \"  ✓ Successfully deleted $func\"\n")
        f.write("    else\n")
        f.write("        echo \"  ✗ Failed to delete $func\"\n")
        f.write("    fi\n")
        f.write("    echo\n")
        f.write("done\n\n")
        
        f.write("echo \"Function app deletion complete.\"\n")
        f.write("echo\n")
        f.write("echo \"Note: App Service Plans were kept. To delete unused App Service Plans, run:\"\n")
        f.write("echo \"az appservice plan list --resource-group $RESOURCE_GROUP --query \\\"[?numberOfSites==0]\\\" --output table\"\n")
    
    # Make script executable
    os.chmod(deletion_script, 0o755)
    
    # Also create a list file
    list_file = os.path.join(script_dir, 'uuid_functions_list.txt')
    with open(list_file, 'w') as f:
        for func in uuid_functions:
            f.write(f"{func}\n")
    
    # Create individual deletion commands file
    commands_file = os.path.join(script_dir, 'deletion_commands.txt')
    with open(commands_file, 'w') as f:
        f.write("# Individual deletion commands for function apps with UUID pattern\n")
        f.write("# Copy and paste these commands as needed\n\n")
        
        for func in uuid_functions:
            f.write(f"az functionapp delete --name \"{func}\" --resource-group \"rg-internal-ai\"\n")
    
    print(f"\nGenerated files:")
    print(f"1. Deletion script: {deletion_script}")
    print(f"2. Function list: {list_file}")
    print(f"3. Individual commands: {commands_file}")
    
    print(f"\n\nTo delete all {len(uuid_functions)} function apps, run:")
    print(f"   bash {deletion_script}")
    
    print(f"\n\nTo delete unused App Service Plans after function deletion:")
    print("   az appservice plan list --resource-group rg-internal-ai --query \"[?numberOfSites==0]\" --output table")
    print("   az appservice plan delete --name <plan-name> --resource-group rg-internal-ai")

if __name__ == "__main__":
    main()
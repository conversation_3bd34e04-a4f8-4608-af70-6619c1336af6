#!/usr/bin/env python
"""
Azure Cost Management API Example Script

This script demonstrates how to use the Azure Cost Management API to retrieve cost data
for Azure resources filtered by Project and Region tags.

Requirements:
- azure-identity
- azure-mgmt-costmanagement

Usage:
    python cost_management_example.py --subscription-id <SUBSCRIPTION_ID> [options]

Options:
    --subscription-id TEXT     Azure Subscription ID [required]
    --project TEXT             Project tag value to filter by (e.g., ProjectA) [default: None]
    --region TEXT              Region tag value to filter by (e.g., WestEurope) [default: None]
    --timeframe TEXT           Timeframe for cost data [default: MonthToDate]
"""

import argparse
import sys
import logging
from typing import Dict, List, Optional

from azure.identity import DefaultAzureCredential
from azure.mgmt.costmanagement import CostManagementClient
from azure.mgmt.costmanagement.models import (
    QueryDefinition,
    QueryDataset,
    QueryAggregation,
    QueryGrouping,
    QueryFilter,
    QueryComparisonExpression,
    TimeframeType
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_cost_data(
    subscription_id: str,
    project_tag: Optional[str] = None,
    region_tag: Optional[str] = None,
    timeframe: str = "MonthToDate"
):
    """
    Get cost data from Azure Cost Management API.

    Args:
        subscription_id: Azure Subscription ID
        project_tag: Project tag value to filter by
        region_tag: Region tag value to filter by
        timeframe: Timeframe for cost data
    """
    # 1. Authenticate
    try:
        logger.info("Authenticating with Azure...")
        credential = DefaultAzureCredential()
        logger.info("Authentication successful")
    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        sys.exit(1)

    # 2. Initialize the Cost Management Client
    try:
        logger.info("Initializing Cost Management Client...")
        cost_client = CostManagementClient(credential, subscription_id)
        logger.info("Cost Management Client initialized")
    except Exception as e:
        logger.error(f"Failed to initialize Cost Management Client: {str(e)}")
        sys.exit(1)

    # 3. Build the Query
    logger.info("Building cost management query...")
    
    # Convert timeframe string to TimeframeType enum
    timeframe_map = {
        "monthtodate": TimeframeType.MONTH_TO_DATE,
        "billingmonthtodate": TimeframeType.BILLING_MONTH_TO_DATE,
        "thelastmonth": TimeframeType.THE_LAST_MONTH,
        "thelastbillingmonth": TimeframeType.THE_LAST_BILLING_MONTH,
        "weektodate": TimeframeType.WEEK_TO_DATE,
    }
    timeframe_value = timeframe_map.get(timeframe.lower(), TimeframeType.MONTH_TO_DATE)
    
    # Build filters
    filters = []
    
    if project_tag:
        project_filter = QueryFilter(
            tags=QueryComparisonExpression(
                name="Project",
                operator="In",
                values=[project_tag]
            )
        )
        filters.append(project_filter)
    
    if region_tag:
        region_filter = QueryFilter(
            tags=QueryComparisonExpression(
                name="Region",
                operator="In",
                values=[region_tag]
            )
        )
        filters.append(region_filter)
    
    # Create the query definition
    query = QueryDefinition(
        type="ActualCost",
        timeframe=timeframe_value,
        dataset=QueryDataset(
            aggregation={
                "totalCost": QueryAggregation(name="PreTaxCost", function="Sum")
            },
            grouping=[
                QueryGrouping(type="Dimension", name="ServiceName")
            ],
            filter=QueryFilter(and_=filters) if filters else None
        )
    )

    # 4. Execute the Query
    scope = f"/subscriptions/{subscription_id}"
    
    try:
        logger.info("Executing cost management query...")
        response = cost_client.query.usage(
            scope=scope,
            parameters=query
        )
        logger.info("Query executed successfully")
    except Exception as e:
        logger.error(f"Query execution failed: {str(e)}")
        sys.exit(1)

    # 5. Process the Results
    print("\nAzure Cost Management Results:")
    print("==============================")
    
    if not response.rows:
        print("No cost data found for the specified filters.")
        return
    
    print(f"{'Service Name':<40} {'Cost ($)':<15}")
    print("-" * 55)
    
    total_cost = 0
    for row in response.rows:
        service_name = row[0]  # ServiceName
        service_cost = row[1]  # PreTaxCost
        total_cost += service_cost
        print(f"{service_name:<40} ${service_cost:.2f}")
    
    print("-" * 55)
    print(f"{'Total':<40} ${total_cost:.2f}")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Azure Cost Management API Example")
    
    parser.add_argument("--subscription-id", required=True, help="Azure Subscription ID")
    parser.add_argument("--project", help="Project tag value to filter by (e.g., ProjectA)")
    parser.add_argument("--region", help="Region tag value to filter by (e.g., WestEurope)")
    parser.add_argument("--timeframe", default="MonthToDate", 
                        help="Timeframe for cost data: MonthToDate, BillingMonthToDate, TheLastMonth, TheLastBillingMonth, WeekToDate")
    
    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()
    
    get_cost_data(
        subscription_id=args.subscription_id,
        project_tag=args.project,
        region_tag=args.region,
        timeframe=args.timeframe
    )


if __name__ == "__main__":
    main()

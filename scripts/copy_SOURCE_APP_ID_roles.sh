#!/bin/bash

# --- Configuration ---
# The application you want to get the roles FROM
SOURCE_APP_ID="bb1ebfc1-47d8-4273-9206-3acc107c1e35"

# The application you want to assign the roles TO
# This is app-internal-ai-scope
TARGET_APP_ID="b8e60630-b988-48cb-843a-779eaff15d94"

# --- Main Script ---
echo "--- Step 1: Fetching App Details ---"
# Get the display names of the source and target apps
SOURCE_APP_NAME=$(az ad sp show --id "$SOURCE_APP_ID" --query "displayName" -o tsv)
TARGET_APP_NAME=$(az ad sp show --id "$TARGET_APP_ID" --query "displayName" -o tsv)
echo "Source App: $SOURCE_APP_NAME ($SOURCE_APP_ID)"
echo "Target App: $TARGET_APP_NAME ($TARGET_APP_ID)"
echo

echo "--- Step 2: Fetching and Replicating Roles ---"
# Get the role assignments for the source app
ROLE_ASSIGNMENTS=$(az role assignment list --assignee "$SOURCE_APP_ID" -o json)

if [ -z "$ROLE_ASSIGNMENTS" ] || [ "$ROLE_ASSIGNMENTS" == "[]" ]; then
  echo "No role assignments found for the source app."
  exit 0
fi

echo "Found role assignments. Replicating for target app..."
echo "------------------------------------------------------------------"

# Initialize a variable to store the names of the assigned roles
ASSIGNED_ROLES_LIST=""

# Loop through each role assignment and apply it to the target app
echo "$ROLE_ASSIGNMENTS" | jq -c '.[]' | while read -r assignment; do
  ROLE=$(echo "$assignment" | jq -r '.roleDefinitionName')
  SCOPE=$(echo "$assignment" | jq -r '.scope')

  echo "Assigning role: '$ROLE'"
  echo "Scope: '$SCOPE'"

  # Assign the role to the target service principal
  az role assignment create \
    --assignee "$TARGET_APP_ID" \
    --role "$ROLE" \
    --scope "$SCOPE"

  if [ $? -eq 0 ]; then
    echo "SUCCESS: Role assigned."
    # Add the successfully assigned role to our list for the final summary
    ASSIGNED_ROLES_LIST+="- $ROLE\n"
  else
    echo "ERROR: Failed to assign role. You may lack permissions or the role/scope may be invalid for this context."
  fi
  echo "------------------------------------------------------------------"
done

# --- Final Summary ---
echo
echo "✅ Role Replication Summary"
echo "================================="
echo "Source App      : $SOURCE_APP_NAME ($SOURCE_APP_ID)"
echo "Target App      : $TARGET_APP_NAME ($TARGET_APP_ID)"
echo
echo "Roles Assigned to Target:"
if [ -z "$ASSIGNED_ROLES_LIST" ]; then
  echo "No new roles were assigned."
else
  # Print the formatted list of roles
  printf "$ASSIGNED_ROLES_LIST"
fi
echo "================================="
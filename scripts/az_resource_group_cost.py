#!/usr/bin/env python
"""
Azure Resource Group Cost Script using Azure CLI

A simple script to get the monthly cost of all resources in a specific resource group
using the Azure CLI instead of the SDK.

Usage:
    python az_resource_group_cost.py --subscription-id <SUBSCRIPTION_ID> --resource-group <RESOURCE_GROUP>

Example:
    python az_resource_group_cost.py --subscription-id 4c1c14a3-de17-4cda-af60-01610fb493f9 --resource-group rg-internal-ai
"""

import argparse
import json
import logging
import subprocess
import sys
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_resource_group_cost(subscription_id, resource_group):
    """
    Get the monthly cost of all resources in a specific resource group using Azure CLI.
    
    Args:
        subscription_id: Azure Subscription ID
        resource_group: Resource Group name
    """
    logger.info(f"Getting cost data for resource group: {resource_group}")
    
    # Get the current date and first day of the month
    today = datetime.now()
    first_day = today.replace(day=1).strftime("%Y-%m-%d")
    today_str = today.strftime("%Y-%m-%d")
    
    # Build the Azure CLI command
    cmd = [
        "az", "consumption", "usage", "list",
        "--subscription", subscription_id,
        "--start-date", first_day,
        "--end-date", today_str,
        "--query", f"[?contains(instanceId, '{resource_group}')].{{resourceType:meterDetails.meterName, cost:pretaxCost, date:date}}",
        "--output", "json"
    ]
    
    logger.info("Executing Azure CLI command...")
    
    try:
        # Run the Azure CLI command
        process = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        
        # Parse the JSON output
        usage_data = json.loads(process.stdout)
        
        if not usage_data:
            print(f"\nNo cost data found for resource group: {resource_group}")
            return
        
        # Group costs by resource type
        resource_costs = {}
        for item in usage_data:
            resource_type = item.get('resourceType', 'Unknown')
            cost = float(item.get('cost', 0))
            
            if resource_type in resource_costs:
                resource_costs[resource_type] += cost
            else:
                resource_costs[resource_type] = cost
        
        # Display the results
        print(f"\n=== Monthly Cost for Resource Group: {resource_group} ===")
        print(f"Period: {first_day} to {today_str}\n")
        
        print(f"{'Resource Type':<50} {'Cost ($)':<15}")
        print("-" * 65)
        
        total_cost = 0
        for resource_type, cost in sorted(resource_costs.items(), key=lambda x: x[1], reverse=True):
            total_cost += cost
            print(f"{resource_type:<50} ${cost:.2f}")
        
        print("-" * 65)
        print(f"{'TOTAL':<50} ${total_cost:.2f}")
        print("\n")
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Azure CLI command failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        
        if "Please run 'az login' to setup account" in e.stderr:
            logger.error("You need to log in with Azure CLI first. Run 'az login'")
        elif "does not have authorization" in e.stderr:
            logger.error("You don't have sufficient permissions to access cost data.")
            logger.error("Make sure your account has at least Cost Management Reader role.")
        
        sys.exit(1)
    except json.JSONDecodeError:
        logger.error("Failed to parse Azure CLI output as JSON")
        logger.error(f"Output: {process.stdout}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {str(e)}")
        sys.exit(1)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Get monthly cost for a resource group using Azure CLI")
    
    parser.add_argument("--subscription-id", required=True, help="Azure Subscription ID")
    parser.add_argument("--resource-group", required=True, help="Resource Group name")
    
    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()
    
    get_resource_group_cost(
        subscription_id=args.subscription_id,
        resource_group=args.resource_group
    )


if __name__ == "__main__":
    main()

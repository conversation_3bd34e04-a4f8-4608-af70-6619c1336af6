#!/usr/bin/env python
"""
Azure Cost Management API Demo Script

This script demonstrates how to use the Azure Cost Management API to retrieve cost data
for Azure resources. It can filter costs by resource group, tags (like Project and Region),
and group results by different dimensions (like ServiceName).

Requirements:
- azure-identity
- azure-mgmt-costmanagement

Usage:
    python azure_cost_management.py --subscription-id <SUBSCRIPTION_ID> [options]

Options:
    --subscription-id TEXT     Azure Subscription ID [required]
    --resource-group TEXT      Filter by resource group name
    --timeframe TEXT           Timeframe for cost data: MonthToDate, BillingMonthToDate,
                              TheLastMonth, TheLastBillingMonth, WeekToDate, Custom [default: MonthToDate]
    --start-date TEXT          Start date for custom timeframe (format: YYYY-MM-DD)
    --end-date TEXT            End date for custom timeframe (format: YYYY-MM-DD)
    --group-by TEXT            Group results by dimension (ServiceName, ResourceGroup, ResourceType, etc.)
                              Multiple values can be comma-separated [default: ServiceName]
    --tag-name TEXT            Filter by tag name (e.g., Project, Region)
    --tag-value TEXT           Filter by tag value (e.g., ProjectA, WestEurope)
    --output TEXT              Output format: table, json [default: table]
    --debug                    Enable debug logging
"""

import argparse
import json
import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union

from azure.identity import DefaultAzureCredential
from azure.mgmt.costmanagement import CostManagementClient
from azure.mgmt.costmanagement.models import (
    QueryDefinition,
    QueryTimePeriod,
    QueryDataset,
    QueryAggregation,
    QueryGrouping,
    QueryFilter,
    QueryComparisonExpression,
    TimeframeType
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AzureCostManager:
    """Class to manage Azure Cost Management API interactions."""

    def __init__(self, subscription_id: str, debug: bool = False):
        """
        Initialize the Azure Cost Manager.

        Args:
            subscription_id: Azure Subscription ID
            debug: Enable debug logging
        """
        if debug:
            logger.setLevel(logging.DEBUG)
            # Set azure-identity logging to DEBUG
            azure_logger = logging.getLogger('azure')
            azure_logger.setLevel(logging.DEBUG)

        self.subscription_id = subscription_id
        self.scope = f"/subscriptions/{subscription_id}"

        logger.info("Authenticating with Azure...")
        try:
            self.credential = DefaultAzureCredential()
            # Ensure we're using HTTPS for the base URL
            self.cost_client = CostManagementClient(
                self.credential,
                self.subscription_id,
                base_url="https://management.azure.com"
            )
            logger.info("Authentication successful")
        except Exception as e:
            if "Bearer token authentication is not permitted for non-TLS" in str(e):
                logger.error("Authentication failed: The Azure SDK is trying to use a non-HTTPS endpoint.")
                logger.error("Please ensure you're using HTTPS for all Azure endpoints.")
                logger.error("If you're behind a proxy, make sure it's properly configured.")
            else:
                logger.error(f"Authentication failed: {str(e)}")
            raise

    def build_query(
        self,
        timeframe: str = "MonthToDate",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        group_by: List[str] = None,
        tag_filters: Dict[str, List[str]] = None,
        resource_group: Optional[str] = None
    ) -> QueryDefinition:
        """
        Build a cost management query definition.

        Args:
            timeframe: Timeframe for cost data
            start_date: Start date for custom timeframe (format: YYYY-MM-DD)
            end_date: End date for custom timeframe (format: YYYY-MM-DD)
            group_by: List of dimensions to group by
            tag_filters: Dictionary of tag names and values to filter by
            resource_group: Resource group name to filter by

        Returns:
            QueryDefinition object
        """
        if group_by is None:
            group_by = ["ServiceName"]

        # Convert timeframe string to TimeframeType enum
        if timeframe.lower() == "custom":
            if not start_date or not end_date:
                raise ValueError("Start date and end date are required for custom timeframe")

            timeframe_value = TimeframeType.CUSTOM
            time_period = QueryTimePeriod(
                from_property=datetime.strptime(start_date, "%Y-%m-%d").strftime("%Y-%m-%dT%H:%M:%SZ"),
                to=datetime.strptime(end_date, "%Y-%m-%d").strftime("%Y-%m-%dT%H:%M:%SZ")
            )
        else:
            timeframe_map = {
                "monthtodate": TimeframeType.MONTH_TO_DATE,
                "billingmonthtodate": TimeframeType.BILLING_MONTH_TO_DATE,
                "thelastmonth": TimeframeType.THE_LAST_MONTH,
                "thelastbillingmonth": TimeframeType.THE_LAST_BILLING_MONTH,
                "weektodate": TimeframeType.WEEK_TO_DATE,
            }
            timeframe_value = timeframe_map.get(timeframe.lower(), TimeframeType.MONTH_TO_DATE)
            time_period = None

        # Build grouping
        groupings = [QueryGrouping(type="Dimension", name=dim) for dim in group_by]

        # Build filters
        filters = []

        # Add resource group filter if specified
        if resource_group:
            resource_group_filter = QueryFilter(
                dimension=QueryComparisonExpression(
                    name="ResourceGroup",
                    operator="In",
                    values=[resource_group]
                )
            )
            filters.append(resource_group_filter)

        # Add tag filters if specified
        if tag_filters:
            for tag_name, tag_values in tag_filters.items():
                tag_filter = QueryFilter(
                    tags=QueryComparisonExpression(
                        name=tag_name,
                        operator="In",
                        values=tag_values
                    )
                )
                filters.append(tag_filter)

        # Create the query definition
        query = QueryDefinition(
            type="ActualCost",
            timeframe=timeframe_value,
            time_period=time_period,
            dataset=QueryDataset(
                aggregation={
                    "totalCost": QueryAggregation(name="PreTaxCost", function="Sum")
                },
                grouping=groupings,
                filter=QueryFilter(and_=filters) if filters else None
            )
        )

        logger.debug(f"Query definition: {query.as_dict()}")
        return query

    def execute_query(self, query: QueryDefinition) -> Dict:
        """
        Execute a cost management query.

        Args:
            query: QueryDefinition object

        Returns:
            Dictionary containing the query results
        """
        logger.info("Executing cost management query...")
        try:
            response = self.cost_client.query.usage(
                scope=self.scope,
                parameters=query
            )
            logger.info("Query executed successfully")
            return response
        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            raise

    def format_results(self, response, group_by: List[str], output_format: str = "table") -> str:
        """
        Format the query results.

        Args:
            response: Query response
            group_by: List of dimensions used for grouping
            output_format: Output format (table or json)

        Returns:
            Formatted results as a string
        """
        if output_format.lower() == "json":
            # Convert to a more readable JSON structure
            result = {
                "columns": group_by + ["Cost"],
                "rows": []
            }

            for row in response.rows:
                formatted_row = {}
                for i, column in enumerate(group_by):
                    formatted_row[column] = row[i]
                formatted_row["Cost"] = row[-1]
                result["rows"].append(formatted_row)

            return json.dumps(result, indent=2)
        else:
            # Format as a table
            result = []

            # Add header
            header = " | ".join(group_by + ["Cost ($)"])
            result.append(header)
            result.append("-" * len(header))

            # Add rows
            for row in response.rows:
                values = [str(row[i]) for i in range(len(group_by))]
                values.append(f"{row[-1]:.2f}")
                result.append(" | ".join(values))

            return "\n".join(result)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Azure Cost Management API Demo")

    parser.add_argument("--subscription-id", required=True, help="Azure Subscription ID")
    parser.add_argument("--resource-group", help="Filter by resource group name")
    parser.add_argument("--timeframe", default="MonthToDate",
                        help="Timeframe for cost data: MonthToDate, BillingMonthToDate, TheLastMonth, TheLastBillingMonth, WeekToDate, Custom")
    parser.add_argument("--start-date", help="Start date for custom timeframe (format: YYYY-MM-DD)")
    parser.add_argument("--end-date", help="End date for custom timeframe (format: YYYY-MM-DD)")
    parser.add_argument("--group-by", default="ServiceName",
                        help="Group results by dimension (ServiceName, ResourceGroup, ResourceType, etc.). Multiple values can be comma-separated")
    parser.add_argument("--tag-name", help="Filter by tag name (e.g., Project, Region)")
    parser.add_argument("--tag-value", help="Filter by tag value (e.g., ProjectA, WestEurope)")
    parser.add_argument("--output", default="table", help="Output format: table, json")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()

    try:
        # Initialize the cost manager
        cost_manager = AzureCostManager(args.subscription_id, args.debug)

        # Parse group_by
        group_by = [dim.strip() for dim in args.group_by.split(",")]

        # Parse tag filters
        tag_filters = None
        if args.tag_name and args.tag_value:
            tag_filters = {args.tag_name: [args.tag_value]}

        # Build the query
        query = cost_manager.build_query(
            timeframe=args.timeframe,
            start_date=args.start_date,
            end_date=args.end_date,
            group_by=group_by,
            tag_filters=tag_filters,
            resource_group=args.resource_group
        )

        # Execute the query
        response = cost_manager.execute_query(query)

        # Format and print the results
        formatted_results = cost_manager.format_results(response, group_by, args.output)
        print(formatted_results)

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()

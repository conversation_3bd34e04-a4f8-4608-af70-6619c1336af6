#!/usr/bin/env python3
"""
Simplified repair script that uses deploy_project_resources.py functions to recreate missing resources.

This approach:
1. Checks what resources are missing
2. Selectively runs only the needed deployment steps
3. Updates the project with the results
"""

import asyncio
import json
import logging
import os
import sys
import subprocess
from datetime import datetime, timezone
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backend.services.project_service import ProjectDataService

# Configure Logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Import specific deployment steps from deploy_project_resources
try:
    # Try to import the modular deployment functions
    from deploy_project_resources import (
        run_deployment_command,
        mask_sensitive_output,
        get_main_bicep_outputs,
        get_search_service_key,
        get_storage_sas_token,
        create_storage_containers,
        create_search_index_and_indexer,
        get_function_urls_and_keys,
        # These might not exist as separate functions, so we'll handle that
    )
    MODULAR_IMPORTS = True
except ImportError:
    MODULAR_IMPORTS = False
    logging.warning("Could not import modular functions from deploy_project_resources.py")


async def check_resource_exists(resource_type: str, resource_name: str, resource_group: str) -> bool:
    """Check if a resource exists in Azure."""
    cmd = [
        "az", "resource", "show",
        "--resource-type", resource_type,
        "--name", resource_name,
        "--resource-group", resource_group,
        "--query", "id",
        "-o", "tsv"
    ]
    
    try:
        process = await asyncio.to_thread(
            subprocess.run,
            cmd,
            capture_output=True,
            text=True,
            check=False
        )
        return process.returncode == 0 and process.stdout.strip()
    except Exception:
        return False


async def deploy_main_infrastructure(project: Dict[str, Any], resource_group: str, location: str) -> Dict[str, Any]:
    """Deploy main infrastructure using Bicep."""
    logging.info("Deploying main infrastructure (storage, search)...")
    
    bicep_file = "project_resources_with_eventgrid_system.bicep"
    deployment_name = f"main-infra-{project['id'][:8]}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    cmd = [
        "az", "deployment", "group", "create",
        "--resource-group", resource_group,
        "--template-file", bicep_file,
        "--parameters",
        f"projectId={project['id']}",
        f"projectName={project['name']}",
        f"location={location}",
        "--name", deployment_name,
        "--output", "json"
    ]
    
    try:
        process = await asyncio.to_thread(
            subprocess.run,
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        
        result = json.loads(process.stdout)
        outputs = result.get("properties", {}).get("outputs", {})
        
        return {
            "success": True,
            "outputs": {
                "storage_account_name": outputs.get("storageAccountName", {}).get("value"),
                "search_service_name": outputs.get("searchServiceName", {}).get("value"),
            }
        }
    except Exception as e:
        logging.error(f"Failed to deploy main infrastructure: {e}")
        return {"success": False, "error": str(e)}


async def deploy_function_app(project: Dict[str, Any], resource_group: str, location: str) -> Dict[str, Any]:
    """Deploy function app from ACR."""
    logging.info("Deploying function app from ACR...")
    
    # Use the ACR deployment script
    script_path = "scripts/ACR_deployment/deploy_function_app_from_acr.sh"
    
    if not os.path.exists(script_path):
        return {"success": False, "error": f"ACR deployment script not found at {script_path}"}
    
    # Prepare parameters
    acr_name = os.environ.get("ACR_NAME", "functionappaiscope")
    image_name = os.environ.get("FUNCTIONS_CONTAINER_IMAGE_NAME", "functionapp")
    image_tag = os.environ.get("FUNCTIONS_CONTAINER_IMAGE_TAG", "latest")
    
    env = os.environ.copy()
    env.update({
        "PROJECT_ID": project["id"],
        "PROJECT_NAME": project["name"],
        "RESOURCE_GROUP": resource_group,
        "LOCATION": location,
        "STORAGE_ACCOUNT_NAME": project.get("storage_account_name", ""),
        "SEARCH_SERVICE_NAME": project.get("search_service_name", ""),
        "SEARCH_KEY": project.get("search_key", ""),
        "AZURE_OPENAI_ENDPOINT": os.environ.get("AZURE_OPENAI_ENDPOINT", ""),
        "AZURE_OPENAI_KEY": os.environ.get("AZURE_OPENAI_KEY", ""),
        "ACR_NAME": acr_name,
        "IMAGE_NAME": image_name,
        "IMAGE_TAG": image_tag,
    })
    
    try:
        process = await asyncio.to_thread(
            subprocess.run,
            ["bash", script_path],
            env=env,
            capture_output=True,
            text=True,
            check=True
        )
        
        # Extract function app name from output
        function_app_name = None
        for line in process.stdout.splitlines():
            if "Function App Name:" in line:
                function_app_name = line.split(":")[-1].strip()
                break
        
        return {
            "success": True,
            "function_app_name": function_app_name
        }
    except Exception as e:
        logging.error(f"Failed to deploy function app: {e}")
        return {"success": False, "error": str(e)}


async def deploy_event_grid_topic(project: Dict[str, Any], resource_group: str, location: str) -> Dict[str, Any]:
    """Deploy Event Grid system topic using Bicep."""
    logging.info("Deploying Event Grid system topic...")
    
    bicep_file = "modules/event_grid.bicep"
    deployment_name = f"event-grid-{project['id'][:8]}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # Generate topic name
    import hashlib
    sanitized_name = project['name'].lower().replace(" ", "-")
    sanitized_name = ''.join(c for c in sanitized_name if c.isalnum() or c == '-')
    unique_suffix = hashlib.md5(project['id'].encode()).hexdigest()[:4]
    topic_name = f"evgt-{sanitized_name}-{unique_suffix}"
    
    cmd = [
        "az", "deployment", "group", "create",
        "--resource-group", resource_group,
        "--template-file", bicep_file,
        "--parameters",
        f"storageAccountName={project['storage_account_name']}",
        f"functionAppName={project['function_app_name']}",
        f"projectName={project['name']}",
        f"projectId={project['id']}",
        f"location={location}",
        f"eventGridSystemTopicName={topic_name}",
        "--name", deployment_name,
        "--output", "json"
    ]
    
    try:
        process = await asyncio.to_thread(
            subprocess.run,
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        
        result = json.loads(process.stdout)
        
        return {
            "success": True,
            "system_topic_name": topic_name
        }
    except Exception as e:
        logging.error(f"Failed to deploy Event Grid: {e}")
        return {"success": False, "error": str(e)}


async def repair_project_with_recreation(
    project_id: str,
    resource_group: str = "rg-internal-ai",
    location: str = "westeurope"
) -> Dict[str, Any]:
    """
    Repair project by recreating missing resources.
    """
    logging.info(f"=== Starting repair with recreation for project: {project_id} ===")
    
    # Get project from Cosmos DB
    service = ProjectDataService()
    project = await service.get_project(project_id)
    
    if not project:
        return {"status": "failed", "message": "Project not found in Cosmos DB"}
    
    logging.info(f"Project: {project['name']} (ID: {project_id})")
    logging.info(f"Current resources: Storage={project.get('storage_account_name')}, "
                 f"Search={project.get('search_service_name')}, "
                 f"Function={project.get('function_app_name')}")
    
    actions_taken = []
    errors = []
    
    try:
        # Step 1: Deploy main infrastructure if missing
        if not project.get("storage_account_name") or not project.get("search_service_name"):
            logging.info("\n=== Step 1: Deploying missing main infrastructure ===")
            
            infra_result = await deploy_main_infrastructure(project, resource_group, location)
            
            if infra_result["success"]:
                if infra_result["outputs"].get("storage_account_name"):
                    project["storage_account_name"] = infra_result["outputs"]["storage_account_name"]
                    actions_taken.append(f"Created storage account: {project['storage_account_name']}")
                
                if infra_result["outputs"].get("search_service_name"):
                    project["search_service_name"] = infra_result["outputs"]["search_service_name"]
                    actions_taken.append(f"Created search service: {project['search_service_name']}")
                
                # Save progress
                await service.update_project(project_id, project)
            else:
                errors.append(f"Failed to deploy main infrastructure: {infra_result.get('error')}")
                return {"status": "failed", "errors": errors}
        
        # Step 2: Get credentials for existing resources
        if project.get("storage_account_name") and not project.get("storage_account_sas_token"):
            logging.info("\n=== Step 2: Generating storage SAS token ===")
            cmd = [
                "az", "storage", "account", "generate-sas",
                "--account-name", project["storage_account_name"],
                "--resource-group", resource_group,
                "--resource-types", "sco",
                "--services", "bfqt",
                "--permissions", "rwdlacuptf",
                "--expiry", (datetime.now(timezone.utc).replace(year=datetime.now().year + 1)).strftime("%Y-%m-%dT%H:%M:%SZ"),
                "--https-only",
                "-o", "tsv"
            ]
            
            process = await asyncio.to_thread(subprocess.run, cmd, capture_output=True, text=True)
            if process.returncode == 0:
                project["storage_account_sas_token"] = process.stdout.strip()
                actions_taken.append("Generated storage SAS token")
                await service.update_project(project_id, project)
        
        if project.get("search_service_name") and not project.get("search_key"):
            logging.info("\n=== Getting search service key ===")
            cmd = [
                "az", "search", "admin-key", "show",
                "--service-name", project["search_service_name"],
                "--resource-group", resource_group,
                "--query", "primaryKey",
                "-o", "tsv"
            ]
            
            process = await asyncio.to_thread(subprocess.run, cmd, capture_output=True, text=True)
            if process.returncode == 0:
                project["search_key"] = process.stdout.strip()
                actions_taken.append("Retrieved search service key")
                await service.update_project(project_id, project)
        
        # Step 3: Create storage containers if missing
        if project.get("storage_account_name") and not all([
            project.get("storage_container_uploads"),
            project.get("storage_container_input"),
            project.get("storage_container_output")
        ]):
            logging.info("\n=== Step 3: Creating storage containers ===")
            containers = [
                ("uploads", f"uploads-{project['name'].lower()}-33rd"),
                ("input", f"input-{project['name'].lower()}-33rd"),
                ("output", f"output-{project['name'].lower()}-33rd")
            ]
            
            for container_type, container_name in containers:
                if not project.get(f"storage_container_{container_type}"):
                    cmd = [
                        "az", "storage", "container", "create",
                        "--name", container_name,
                        "--account-name", project["storage_account_name"],
                        "--resource-group", resource_group
                    ]
                    
                    process = await asyncio.to_thread(subprocess.run, cmd, capture_output=True, text=True)
                    if process.returncode == 0:
                        project[f"storage_container_{container_type}"] = container_name
                        actions_taken.append(f"Created container: {container_name}")
            
            await service.update_project(project_id, project)
        
        # Step 4: Deploy function app if missing
        if not project.get("function_app_name"):
            logging.info("\n=== Step 4: Deploying function app ===")
            
            function_result = await deploy_function_app(project, resource_group, location)
            
            if function_result["success"] and function_result.get("function_app_name"):
                project["function_app_name"] = function_result["function_app_name"]
                actions_taken.append(f"Deployed function app: {project['function_app_name']}")
                await service.update_project(project_id, project)
            else:
                errors.append(f"Failed to deploy function app: {function_result.get('error')}")
        
        # Step 5: Get function keys if missing
        if project.get("function_app_name"):
            functions = {
                "function_key_maturity": "HttpTriggerAppMaturityAssessment",
                "function_key_executive_summary": "HttpTriggerAppExecutiveSummary",
                "function_key_powerpoint": "HttpTriggerPowerPointGenerator"
            }
            
            for key_field, func_name in functions.items():
                if not project.get(key_field):
                    logging.info(f"Getting key for function: {func_name}")
                    cmd = [
                        "az", "functionapp", "function", "keys", "list",
                        "--name", project["function_app_name"],
                        "--resource-group", resource_group,
                        "--function-name", func_name,
                        "--query", "default",
                        "-o", "tsv"
                    ]
                    
                    process = await asyncio.to_thread(subprocess.run, cmd, capture_output=True, text=True)
                    if process.returncode == 0 and process.stdout.strip():
                        project[key_field] = process.stdout.strip()
                        actions_taken.append(f"Retrieved key for {func_name}")
            
            await service.update_project(project_id, project)
        
        # Step 6: Deploy Event Grid if missing
        if project.get("storage_account_name") and project.get("function_app_name") and not project.get("event_grid_system_topic_name"):
            logging.info("\n=== Step 6: Deploying Event Grid ===")
            
            event_grid_result = await deploy_event_grid_topic(project, resource_group, location)
            
            if event_grid_result["success"]:
                project["event_grid_system_topic_name"] = event_grid_result["system_topic_name"]
                actions_taken.append(f"Deployed Event Grid topic: {project['event_grid_system_topic_name']}")
                await service.update_project(project_id, project)
            else:
                errors.append(f"Failed to deploy Event Grid: {event_grid_result.get('error')}")
        
        # Update deployment status
        completion = calculate_completion(project)
        await service.update_deployment_status(
            project_id,
            {
                "status": "completed" if completion == 100 and not errors else "partial",
                "message": f"Repair completed. {len(actions_taken)} actions taken.",
                "updated_at": datetime.now(timezone.utc).isoformat(),
                "details": {
                    "completion_percentage": completion,
                    "repair_actions": actions_taken,
                    "repair_errors": errors
                }
            }
        )
        
    except Exception as e:
        logging.error(f"Error during repair: {e}")
        errors.append(str(e))
    
    # Return results
    if errors:
        return {
            "status": "partial_success" if actions_taken else "failed",
            "message": f"Repair {'partially ' if actions_taken else ''}completed with {len(errors)} errors",
            "actions_taken": actions_taken,
            "errors": errors
        }
    else:
        return {
            "status": "success",
            "message": f"Repair completed successfully. {len(actions_taken)} actions taken.",
            "actions_taken": actions_taken
        }


def calculate_completion(project: Dict[str, Any]) -> int:
    """Calculate deployment completion percentage."""
    checks = [
        project.get("storage_account_name") is not None,
        project.get("storage_container_uploads") is not None,
        project.get("storage_container_input") is not None,
        project.get("storage_container_output") is not None,
        project.get("storage_account_sas_token") is not None,
        project.get("search_service_name") is not None,
        project.get("search_key") is not None,
        project.get("function_app_name") is not None,
        any(project.get(f"function_key_{k}") for k in ["maturity", "executive_summary", "powerpoint"]),
        project.get("event_grid_system_topic_name") is not None,
    ]
    return int((sum(checks) / len(checks)) * 100)


if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python -m scripts.repair_with_recreation <project_id>")
        sys.exit(1)
    
    from dotenv import load_dotenv
    load_dotenv()
    
    project_id = sys.argv[1]
    result = asyncio.run(repair_project_with_recreation(project_id))
    print(json.dumps(result, indent=2))
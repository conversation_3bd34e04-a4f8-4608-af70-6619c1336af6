#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to list all function app names for each project from Cosmos DB.

This script connects to Cosmos DB and retrieves all projects, then lists
their associated function app names.

Usage:
    python scripts/list_function_apps.py
"""

import os
import sys
import asyncio
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.cosmos import get_cosmos_client
from azure.cosmos import exceptions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FunctionAppLister:
    """Class to handle listing function apps from Cosmos DB projects."""
    
    def __init__(self):
        """Initialize the Cosmos DB client."""
        self.cosmos_client = get_cosmos_client()
        if not self.cosmos_client:
            raise RuntimeError("Failed to initialize Cosmos DB client")
        
        self.database_name = os.environ.get("AZURE_COSMOSDB_DATABASE")
        self.container_name = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects")
        
        if not self.database_name:
            raise RuntimeError("AZURE_COSMOSDB_DATABASE environment variable not set")
        
        self.database = self.cosmos_client.get_database_client(self.database_name)
        self.container = self.database.get_container_client(self.container_name)
    
    async def get_all_projects(self) -> List[Dict[str, Any]]:
        """
        Retrieve all projects from Cosmos DB.

        Returns:
            List of project documents
        """
        try:
            query = "SELECT * FROM c WHERE c.type = 'project' OR NOT IS_DEFINED(c.type)"
            projects = []

            # Use the correct parameter name for cross-partition queries
            async for item in self.container.query_items(
                query=query,
                partition_key=None  # This enables cross-partition query
            ):
                projects.append(item)

            logger.info(f"Retrieved {len(projects)} projects from Cosmos DB")
            return projects

        except exceptions.CosmosHttpResponseError as e:
            logger.error(f"Error querying Cosmos DB: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving projects: {e}")
            return []
    
    async def list_function_apps(self) -> Dict[str, Dict[str, Any]]:
        """
        List all function apps with their project information.
        
        Returns:
            Dictionary mapping project_id to project info including function_app_name
        """
        projects = await self.get_all_projects()
        function_apps = {}
        
        for project in projects:
            project_id = project.get('id')
            project_name = project.get('name', 'Unknown')
            function_app_name = project.get('function_app_name')
            storage_account_name = project.get('storage_account_name')
            search_index_name = project.get('search_index_name')
            
            if project_id:
                function_apps[project_id] = {
                    'project_name': project_name,
                    'function_app_name': function_app_name,
                    'storage_account_name': storage_account_name,
                    'search_index_name': search_index_name,
                    'has_function_app': bool(function_app_name)
                }
        
        return function_apps
    
    def print_function_apps(self, function_apps: Dict[str, Dict[str, Any]]):
        """
        Print the function apps in a formatted way.
        
        Args:
            function_apps: Dictionary of function app information
        """
        print("\n" + "="*80)
        print("FUNCTION APPS BY PROJECT")
        print("="*80)
        
        projects_with_apps = {k: v for k, v in function_apps.items() if v['has_function_app']}
        projects_without_apps = {k: v for k, v in function_apps.items() if not v['has_function_app']}
        
        if projects_with_apps:
            print(f"\nProjects with Function Apps ({len(projects_with_apps)}):")
            print("-" * 50)
            for project_id, info in projects_with_apps.items():
                print(f"Project ID: {project_id}")
                print(f"  Name: {info['project_name']}")
                print(f"  Function App: {info['function_app_name']}")
                print(f"  Storage Account: {info['storage_account_name'] or 'Not set'}")
                print(f"  Search Index: {info['search_index_name'] or 'Not set'}")
                print()
        
        if projects_without_apps:
            print(f"\nProjects without Function Apps ({len(projects_without_apps)}):")
            print("-" * 50)
            for project_id, info in projects_without_apps.items():
                print(f"Project ID: {project_id}")
                print(f"  Name: {info['project_name']}")
                print(f"  Storage Account: {info['storage_account_name'] or 'Not set'}")
                print(f"  Search Index: {info['search_index_name'] or 'Not set'}")
                print()
        
        print(f"Total Projects: {len(function_apps)}")
        print(f"Projects with Function Apps: {len(projects_with_apps)}")
        print(f"Projects without Function Apps: {len(projects_without_apps)}")
        print("="*80)


async def main():
    """Main function to list function apps."""
    lister = None
    try:
        lister = FunctionAppLister()
        function_apps = await lister.list_function_apps()
        lister.print_function_apps(function_apps)

        # Also return the data for potential use by other scripts
        return function_apps

    except Exception as e:
        logger.error(f"Error in main: {e}")
        sys.exit(1)
    finally:
        # Clean up Cosmos client
        if lister and lister.cosmos_client:
            await lister.cosmos_client.close()


if __name__ == "__main__":
    asyncio.run(main())

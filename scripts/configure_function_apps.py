#!/usr/bin/env python3
"""
Script to configure function apps with required settings.

This script performs the following operations on function apps:
1. Add project-id tag
2. Get storage account name and search index for the project
3. Enable Access-Control-Allow-Credentials and add allowed origins
4. Add/update environment variables
5. Update function keys

Usage:
    python scripts/configure_function_apps.py [--project-id PROJECT_ID] [--dry-run]
"""

import os
import sys
import asyncio
import logging
import subprocess
import json
import argparse
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.cosmos import get_cosmos_client
from azure.cosmos import exceptions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration constants
RESOURCE_GROUP = "rg-internal-ai"
ALLOWED_ORIGINS = "https://ai-scope-app3.azurewebsites.net"

# Environment variables to set
REQUIRED_ENV_VARS = {
    "AZURE_OPENAI_DEPLOYMENT_ID": "gpt-4o-ai-scope",
    "AZURE_OPENAI_DEPLOYMENT_ID_2": "gpt-4o-mini",
    "AZURE_OPENAI_DEPLOYMENT_ID_3": "o3-mini",
    "AZURE_OPENAI_VERSION": "2024-05-01-preview",
    "AZURE_OPENAI_DEPLOYMENT_ID_3_VERSION": "2025-01-31",
    "AZURE_OPENAI_ENDPOINT": "https://ai-scope-openai.openai.azure.com/"
}

# Function names for key retrieval
FUNCTION_NAMES = {
    "FUNCTION_KEY_MATURITY": "HttpTriggerAppMaturityAssessment",
    "FUNCTION_KEY_EXECUTIVE_SUMMARY": "HttpTriggerAppExecutiveSummary",
    "FUNCTION_KEY_POWERPOINT": "HttpTriggerPowerPointGenerator"
}


class FunctionAppConfigurator:
    """Class to handle function app configuration operations."""
    
    def __init__(self, dry_run: bool = False):
        """Initialize the configurator."""
        self.dry_run = dry_run
        self.cosmos_client = get_cosmos_client()
        if not self.cosmos_client:
            raise RuntimeError("Failed to initialize Cosmos DB client")
        
        self.database_name = os.environ.get("AZURE_COSMOSDB_DATABASE")
        self.container_name = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects")
        
        if not self.database_name:
            raise RuntimeError("AZURE_COSMOSDB_DATABASE environment variable not set")
        
        self.database = self.cosmos_client.get_database_client(self.database_name)
        self.container = self.database.get_container_client(self.container_name)
    
    async def run_az_command(self, cmd: List[str]) -> Optional[str]:
        """
        Run an Azure CLI command and return the output.
        
        Args:
            cmd: List of command arguments
            
        Returns:
            Command output or None if failed
        """
        if self.dry_run:
            logger.info(f"DRY RUN: Would execute: {' '.join(cmd)}")
            return "dry-run-output"
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {' '.join(cmd)}")
            logger.error(f"Error: {e.stderr}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error running command: {e}")
            return None
    
    async def get_project_by_id(self, project_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a project by ID from Cosmos DB.
        
        Args:
            project_id: The project ID to search for
            
        Returns:
            Project document or None if not found
        """
        try:
            query = "SELECT * FROM c WHERE c.id = @project_id"
            parameters = [{"name": "@project_id", "value": project_id}]
            
            async for item in self.container.query_items(
                query=query,
                parameters=parameters,
                partition_key=None  # This enables cross-partition query
            ):
                return item
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving project {project_id}: {e}")
            return None
    
    async def add_project_tag(self, function_app_name: str, project_id: str) -> bool:
        """
        Add project-id tag to function app.
        
        Args:
            function_app_name: Name of the function app
            project_id: Project ID to tag with
            
        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Adding project-id tag to {function_app_name}")
        
        cmd = [
            "az", "functionapp", "update",
            "--name", function_app_name,
            "--resource-group", RESOURCE_GROUP,
            "--set", f"tags.project-id={project_id}"
        ]
        
        result = await self.run_az_command(cmd)
        if result is not None:
            logger.info(f"Successfully added project-id tag to {function_app_name}")
            return True
        else:
            logger.error(f"Failed to add project-id tag to {function_app_name}")
            return False
    
    async def get_storage_connection_string(self, storage_account_name: str) -> Optional[str]:
        """
        Get storage account connection string.
        
        Args:
            storage_account_name: Name of the storage account
            
        Returns:
            Connection string or None if failed
        """
        logger.info(f"Getting connection string for storage account {storage_account_name}")
        
        cmd = [
            "az", "storage", "account", "show-connection-string",
            "--resource-group", RESOURCE_GROUP,
            "--name", storage_account_name,
            "--query", "connectionString",
            "--output", "tsv"
        ]
        
        return await self.run_az_command(cmd)

    async def configure_cors(self, function_app_name: str) -> bool:
        """
        Configure CORS settings for function app.

        Args:
            function_app_name: Name of the function app

        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Configuring CORS for {function_app_name}")

        # Enable credentials
        cmd_credentials = [
            "az", "functionapp", "cors", "credentials",
            "--name", function_app_name,
            "--resource-group", RESOURCE_GROUP,
            "--enable", "true"
        ]

        # Add allowed origins
        cmd_origins = [
            "az", "functionapp", "cors", "add",
            "--name", function_app_name,
            "--resource-group", RESOURCE_GROUP,
            "--allowed-origins", ALLOWED_ORIGINS
        ]

        credentials_result = await self.run_az_command(cmd_credentials)
        origins_result = await self.run_az_command(cmd_origins)

        if credentials_result is not None and origins_result is not None:
            logger.info(f"Successfully configured CORS for {function_app_name}")
            return True
        else:
            logger.error(f"Failed to configure CORS for {function_app_name}")
            return False

    async def update_app_settings(self, function_app_name: str, project: Dict[str, Any]) -> bool:
        """
        Update function app settings with required environment variables.

        Args:
            function_app_name: Name of the function app
            project: Project data from Cosmos DB

        Returns:
            True if successful, False otherwise
        """
        logger.info(f"Updating app settings for {function_app_name}")

        # Get storage connection string
        storage_account_name = project.get('storage_account_name')
        if not storage_account_name:
            logger.error(f"No storage account name found for project {project.get('id')}")
            return False

        connection_string = await self.get_storage_connection_string(storage_account_name)
        if not connection_string:
            logger.error(f"Failed to get connection string for {storage_account_name}")
            return False

        # Prepare settings
        settings = REQUIRED_ENV_VARS.copy()
        settings["AZURE_AI_SEARCH_INDEX"] = project.get('search_index_name', '')
        settings["AzureWebJobsStorage"] = connection_string

        # Convert settings to Azure CLI format
        settings_args = []
        for key, value in settings.items():
            settings_args.extend(["--settings", f"{key}={value}"])

        cmd = [
            "az", "functionapp", "config", "appsettings", "set",
            "--name", function_app_name,
            "--resource-group", RESOURCE_GROUP
        ] + settings_args

        result = await self.run_az_command(cmd)
        if result is not None:
            logger.info(f"Successfully updated app settings for {function_app_name}")
            return True
        else:
            logger.error(f"Failed to update app settings for {function_app_name}")
            return False

    async def get_function_key(self, function_app_name: str, function_name: str) -> Optional[str]:
        """
        Get function key for a specific function.

        Args:
            function_app_name: Name of the function app
            function_name: Name of the function

        Returns:
            Function key or None if failed
        """
        logger.info(f"Getting function key for {function_name} in {function_app_name}")

        cmd = [
            "az", "functionapp", "function", "keys", "list",
            "--name", function_app_name,
            "--resource-group", RESOURCE_GROUP,
            "--function-name", function_name,
            "--query", "default",
            "--output", "tsv"
        ]

        return await self.run_az_command(cmd)

    async def update_function_keys(self, function_app_name: str) -> Dict[str, str]:
        """
        Update function keys for all required functions.

        Args:
            function_app_name: Name of the function app

        Returns:
            Dictionary of function keys
        """
        logger.info(f"Updating function keys for {function_app_name}")

        function_keys = {}

        for env_var_name, function_name in FUNCTION_NAMES.items():
            key = await self.get_function_key(function_app_name, function_name)
            if key:
                function_keys[env_var_name] = key
                logger.info(f"Retrieved key for {function_name}")
            else:
                logger.warning(f"Failed to retrieve key for {function_name}")

        # Update app settings with function keys
        if function_keys:
            settings_args = []
            for key, value in function_keys.items():
                settings_args.extend(["--settings", f"{key}={value}"])

            cmd = [
                "az", "functionapp", "config", "appsettings", "set",
                "--name", function_app_name,
                "--resource-group", RESOURCE_GROUP
            ] + settings_args

            result = await self.run_az_command(cmd)
            if result is not None:
                logger.info(f"Successfully updated function keys for {function_app_name}")
            else:
                logger.error(f"Failed to update function keys for {function_app_name}")

        return function_keys

    async def function_app_exists(self, function_app_name: str) -> bool:
        """
        Check if a function app exists in Azure.

        Args:
            function_app_name: Name of the function app to check

        Returns:
            True if function app exists, False otherwise
        """
        logger.info(f"Checking if function app {function_app_name} exists")

        cmd = [
            "az", "functionapp", "show",
            "--name", function_app_name,
            "--resource-group", RESOURCE_GROUP,
            "--query", "name",
            "--output", "tsv"
        ]

        result = await self.run_az_command(cmd)
        exists = result is not None and result.strip() == function_app_name

        if exists:
            logger.info(f"Function app {function_app_name} exists")
        else:
            logger.warning(f"Function app {function_app_name} does not exist in Azure")

        return exists

    async def configure_function_app(self, project_id: str, project: Dict[str, Any]) -> bool:
        """
        Configure a single function app with all required settings.

        Args:
            project_id: The project ID
            project: Project data from Cosmos DB

        Returns:
            True if successful, False otherwise
        """
        function_app_name = project.get('function_app_name')
        if not function_app_name:
            logger.warning(f"No function app name found for project {project_id}")
            return False

        # Check if function app exists before trying to configure it
        if not await self.function_app_exists(function_app_name):
            logger.error(f"Function app {function_app_name} does not exist - skipping configuration for project {project_id}")
            return False

        logger.info(f"Configuring function app {function_app_name} for project {project_id}")

        success = True

        # Step 1: Add project-id tag
        if not await self.add_project_tag(function_app_name, project_id):
            success = False

        # Step 2: Configure CORS
        if not await self.configure_cors(function_app_name):
            success = False

        # Step 3: Update app settings
        if not await self.update_app_settings(function_app_name, project):
            success = False

        # Step 4: Update function keys
        function_keys = await self.update_function_keys(function_app_name)
        if not function_keys:
            logger.warning(f"No function keys retrieved for {function_app_name}")

        if success:
            logger.info(f"Successfully configured function app {function_app_name}")
        else:
            logger.error(f"Some configuration steps failed for {function_app_name}")

        return success

    async def get_all_projects(self) -> List[Dict[str, Any]]:
        """
        Retrieve all projects from Cosmos DB.

        Returns:
            List of project documents
        """
        try:
            query = "SELECT * FROM c WHERE c.type = 'project' OR NOT IS_DEFINED(c.type)"
            projects = []

            async for item in self.container.query_items(
                query=query,
                partition_key=None  # This enables cross-partition query
            ):
                projects.append(item)

            logger.info(f"Retrieved {len(projects)} projects from Cosmos DB")
            return projects

        except exceptions.CosmosHttpResponseError as e:
            logger.error(f"Error querying Cosmos DB: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving projects: {e}")
            return []

    async def configure_all_function_apps(self) -> Dict[str, Any]:
        """
        Configure all function apps for all projects.

        Returns:
            Dictionary mapping project_id to success status
            Values can be: True (success), False (failed), None (no function app), "not_found" (function app doesn't exist)
        """
        projects = await self.get_all_projects()
        results = {}

        for project in projects:
            project_id = project.get('id')
            if project_id and project.get('function_app_name'):
                # Check if function app exists first
                if not await self.function_app_exists(project.get('function_app_name')):
                    results[project_id] = "not_found"
                else:
                    results[project_id] = await self.configure_function_app(project_id, project)
            elif project_id:
                logger.info(f"Skipping project {project_id} - no function app")
                results[project_id] = None

        return results

    async def configure_single_function_app(self, project_id: str) -> bool:
        """
        Configure function app for a single project.

        Args:
            project_id: The project ID to configure

        Returns:
            True if successful, False otherwise
        """
        project = await self.get_project_by_id(project_id)
        if not project:
            logger.error(f"Project {project_id} not found")
            return False

        return await self.configure_function_app(project_id, project)


async def main():
    """Main function to configure function apps."""
    parser = argparse.ArgumentParser(description="Configure Azure Function Apps")
    parser.add_argument(
        "--project-id",
        help="Configure function app for specific project ID only"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )

    args = parser.parse_args()

    configurator = None
    try:
        configurator = FunctionAppConfigurator(dry_run=args.dry_run)

        if args.project_id:
            # Configure single project
            success = await configurator.configure_single_function_app(args.project_id)
            if success:
                logger.info(f"Successfully configured function app for project {args.project_id}")
            else:
                logger.error(f"Failed to configure function app for project {args.project_id}")
                sys.exit(1)
        else:
            # Configure all projects
            results = await configurator.configure_all_function_apps()

            # Print summary
            successful = sum(1 for v in results.values() if v is True)
            failed = sum(1 for v in results.values() if v is False)
            skipped = sum(1 for v in results.values() if v is None)
            not_found = sum(1 for v in results.values() if v == "not_found")

            print(f"\nConfiguration Summary:")
            print(f"  Successful: {successful}")
            print(f"  Failed: {failed}")
            print(f"  Function app not found: {not_found}")
            print(f"  Skipped (no function app): {skipped}")
            print(f"  Total projects: {len(results)}")

            # Show details of function apps not found
            if not_found > 0:
                print(f"\nFunction apps not found in Azure:")
                projects = await configurator.get_all_projects()
                for project_id, status in results.items():
                    if status == "not_found":
                        project = next((p for p in projects if p.get('id') == project_id), None)
                        if project:
                            print(f"  - Project: {project.get('name', 'Unknown')} (ID: {project_id})")
                            print(f"    Function App: {project.get('function_app_name', 'Unknown')}")

            if failed > 0:
                sys.exit(1)

    except Exception as e:
        logger.error(f"Error in main: {e}")
        sys.exit(1)
    finally:
        # Clean up Cosmos client
        if configurator and configurator.cosmos_client:
            await configurator.cosmos_client.close()


if __name__ == "__main__":
    asyncio.run(main())

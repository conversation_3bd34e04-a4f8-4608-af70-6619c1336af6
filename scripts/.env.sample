# resource switch 
FLAG_EMBEDDING_MODEL = "AOAI" # "AOAI" or "COHERE"
FLAG_COHERE = "ENGLISH" # "MULTILINGUAL" or "ENGLISH" options for Cohere embedding models
FLAG_AOAI = "V3" # "V2" or "V3" options for AOAI embedding models

# update vector dimension based on model chosen
VECTOR_DIMENSION = 1536 # change it to desired, e.g., 1536 for AOAI ada 002, 1024 for COHERE

# AOAI resource
AZURE_OPENAI_API_VERSION = '2023-05-15'
AZURE_OPENAI_ENDPOINT = ""
AZURE_OPENAI_API_KEY = ""

# Cohere multilingual resource
COHERE_MULTILINGUAL_ENDPOINT = ""
COHERE_MULTILINGUAL_API_KEY = ""

# Cohere English resource
COHERE_ENGLISH_ENDPOINT = ""
COHERE_ENGLISH_API_KEY = ""


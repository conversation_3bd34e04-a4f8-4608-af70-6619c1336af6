#!/bin/bash
# Simple script to get Azure resource group costs using Azure CLI

# Check if required arguments are provided
if [ "$#" -lt 2 ]; then
    echo "Usage: $0 <subscription-id> <resource-group>"
    echo "Example: $0 4c1c14a3-de17-4cda-af60-01610fb493f9 rg-internal-ai"
    exit 1
fi

SUBSCRIPTION_ID=$1
RESOURCE_GROUP=$2

# Get the current date and first day of the month
FIRST_DAY=$(date -d "$(date +%Y-%m-01)" +%Y-%m-%d)
TODAY=$(date +%Y-%m-%d)

echo "Getting cost data for resource group: $RESOURCE_GROUP"
echo "Period: $FIRST_DAY to $TODAY"
echo ""

# Run the Azure CLI command to get cost data
COST_DATA=$(az consumption usage list \
  --subscription "$SUBSCRIPTION_ID" \
  --start-date "$FIRST_DAY" \
  --end-date "$TODAY" \
  --query "[?contains(instanceId, '$RESOURCE_GROUP')].{resourceType:meterDetails.meterName, cost:pretaxCost, date:date}" \
  --output json)

# Check if the command was successful
if [ $? -ne 0 ]; then
    echo "Error: Failed to get cost data. Make sure you're logged in with 'az login'"
    exit 1
fi

# Check if we got any data
if [ "$COST_DATA" == "[]" ]; then
    echo "No cost data found for resource group: $RESOURCE_GROUP"
    exit 0
fi

# Process the data to group by resource type
echo "=== Monthly Cost for Resource Group: $RESOURCE_GROUP ==="
echo ""
echo "Resource Type                                        Cost ($)"
echo "-----------------------------------------------------------------"

# Use jq to process the JSON if available
if command -v jq &> /dev/null; then
    # Group by resource type and sum costs
    echo "$COST_DATA" | jq -r 'group_by(.resourceType) | map({resourceType: .[0].resourceType, cost: (map(.cost | tonumber) | add)}) | sort_by(-.cost) | .[] | "\(.resourceType)|\(.cost)"' | while IFS="|" read -r resource_type cost; do
        printf "%-50s $%.2f\n" "$resource_type" "$cost"
        TOTAL=$(echo "$TOTAL + $cost" | bc)
    done
    
    # Calculate total
    TOTAL=$(echo "$COST_DATA" | jq -r '[.[].cost | tonumber] | add')
    
    echo "-----------------------------------------------------------------"
    printf "%-50s $%.2f\n" "TOTAL" "$TOTAL"
else
    # Fallback if jq is not available
    echo "For detailed breakdown, please install 'jq' package"
    TOTAL=$(echo "$COST_DATA" | grep -o '"cost":[0-9.]*' | cut -d':' -f2 | paste -sd+ | bc)
    echo "-----------------------------------------------------------------"
    printf "%-50s $%.2f\n" "TOTAL" "$TOTAL"
fi

#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix environment variables by setting them one by one.
Automatically retrieves search index from Cosmos DB project data.

Usage:
    python scripts/fix_env_vars.py --function-app-name FUNCTION_APP_NAME [--dry-run]
"""

import os
import sys
import subprocess
import argparse
import logging
import time
import asyncio
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.cosmos import get_cosmos_client
from azure.cosmos import exceptions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration constants
RESOURCE_GROUP = "rg-internal-ai"

# Environment variables to set
REQUIRED_ENV_VARS = {
    "AZURE_OPENAI_DEPLOYMENT_ID": "gpt-4o-ai-scope",
    "AZURE_OPENAI_DEPLOYMENT_ID_2": "gpt-4o-mini",
    "AZURE_OPENAI_DEPLOYMENT_ID_3": "o3-mini",
    "AZURE_OPENAI_VERSION": "2024-05-01-preview",
    "AZURE_OPENAI_DEPLOYMENT_ID_3_VERSION": "2025-01-31",
    "AZURE_OPENAI_ENDPOINT": "https://ai-scope-openai.openai.azure.com/"
}


class ProjectDataRetriever:
    """Class to retrieve project data from Cosmos DB."""

    def __init__(self):
        """Initialize the Cosmos DB client."""
        self.cosmos_client = get_cosmos_client()
        if not self.cosmos_client:
            raise RuntimeError("Failed to initialize Cosmos DB client")

        self.database_name = os.environ.get("AZURE_COSMOSDB_DATABASE")
        self.container_name = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects")

        if not self.database_name:
            raise RuntimeError("AZURE_COSMOSDB_DATABASE environment variable not set")

        self.database = self.cosmos_client.get_database_client(self.database_name)
        self.container = self.database.get_container_client(self.container_name)

    async def find_project_by_function_app(self, function_app_name: str) -> Optional[Dict[str, Any]]:
        """
        Find a project by its function app name.

        Args:
            function_app_name: Name of the function app to search for

        Returns:
            Project document or None if not found
        """
        try:
            query = "SELECT * FROM c WHERE c.function_app_name = @function_app_name"
            parameters = [{"name": "@function_app_name", "value": function_app_name}]

            async for item in self.container.query_items(
                query=query,
                parameters=parameters,
                partition_key=None  # This enables cross-partition query
            ):
                logger.info(f"Found project: {item.get('name', 'Unknown')} (ID: {item.get('id')})")
                return item

            logger.warning(f"No project found with function app name: {function_app_name}")
            return None

        except Exception as e:
            logger.error(f"Error searching for project with function app {function_app_name}: {e}")
            return None

    async def close(self):
        """Close the Cosmos DB client."""
        if self.cosmos_client:
            await self.cosmos_client.close()


def run_az_command(cmd, dry_run=False):
    """Run an Azure CLI command and return the output."""
    if dry_run:
        logger.info(f"DRY RUN: Would execute: {' '.join(cmd)}")
        return "dry-run-output"
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {' '.join(cmd)}")
        logger.error(f"Error: {e.stderr}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error running command: {e}")
        return None


def set_single_env_var(function_app_name, key, value, dry_run=False):
    """Set a single environment variable."""
    logger.info(f"Setting {key}={value} for {function_app_name}")
    
    cmd = [
        "az", "functionapp", "config", "appsettings", "set",
        "--name", function_app_name,
        "--resource-group", RESOURCE_GROUP,
        "--settings", f"{key}={value}"
    ]
    
    result = run_az_command(cmd, dry_run)
    if result is not None:
        logger.info(f"Successfully set {key}")
        return True
    else:
        logger.error(f"Failed to set {key}")
        return False


def verify_single_env_var(function_app_name, key, expected_value):
    """Verify a single environment variable."""
    cmd = [
        "az", "functionapp", "config", "appsettings", "list",
        "--name", function_app_name,
        "--resource-group", RESOURCE_GROUP,
        "--query", f"[?name=='{key}'].value",
        "--output", "tsv"
    ]
    
    result = run_az_command(cmd)
    if result:
        actual_value = result.strip()
        if actual_value == expected_value:
            logger.info(f"✅ {key}: {actual_value}")
            return True
        else:
            logger.error(f"❌ {key}: Expected '{expected_value}', got '{actual_value}'")
            return False
    else:
        logger.error(f"❌ {key}: Failed to retrieve value")
        return False


def fix_env_vars(function_app_name, search_index=None, dry_run=False):
    """Fix environment variables by setting them one by one."""
    logger.info(f"Fixing environment variables for {function_app_name}")

    # Create a copy of required env vars
    env_vars_to_set = REQUIRED_ENV_VARS.copy()

    # Add AZURE_AI_SEARCH_INDEX if provided
    if search_index:
        env_vars_to_set["AZURE_AI_SEARCH_INDEX"] = search_index
        logger.info(f"Adding AZURE_AI_SEARCH_INDEX: {search_index}")
    else:
        logger.warning("No search index provided - AZURE_AI_SEARCH_INDEX will not be set")

    success_count = 0
    total_count = len(env_vars_to_set)

    for key, value in env_vars_to_set.items():
        if set_single_env_var(function_app_name, key, value, dry_run):
            success_count += 1
            if not dry_run:
                # Wait a bit between commands to avoid rate limiting
                time.sleep(2)
        else:
            logger.error(f"Failed to set {key}")

    logger.info(f"Set {success_count}/{total_count} environment variables")

    if not dry_run and success_count > 0:
        # Wait a bit for changes to propagate
        logger.info("Waiting for changes to propagate...")
        time.sleep(10)

        # Verify all variables
        logger.info("Verifying environment variables...")
        verified_count = 0
        for key, expected_value in env_vars_to_set.items():
            if verify_single_env_var(function_app_name, key, expected_value):
                verified_count += 1

        logger.info(f"Verified {verified_count}/{total_count} environment variables")
        return verified_count == total_count

    return success_count == total_count


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Fix environment variables for Azure Function App")
    parser.add_argument(
        "--function-app-name",
        required=True,
        help="Name of the function app to fix"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )

    args = parser.parse_args()

    retriever = None
    try:
        # Initialize project data retriever
        retriever = ProjectDataRetriever()

        # Find the project data for this function app
        logger.info(f"Looking up project data for function app: {args.function_app_name}")
        project_data = await retriever.find_project_by_function_app(args.function_app_name)

        if not project_data:
            logger.error(f"Could not find project data for function app: {args.function_app_name}")
            logger.info("Please check that the function app name is correct and exists in Cosmos DB")
            exit(1)

        # Get the search index from project data
        search_index = project_data.get('search_index_name')
        if not search_index:
            logger.warning("No search_index_name found in project data")
            logger.info("AZURE_AI_SEARCH_INDEX will not be set")
        else:
            logger.info(f"Found search index: {search_index}")

        # Fix the environment variables
        success = fix_env_vars(args.function_app_name, search_index, args.dry_run)

        if success:
            logger.info(f"Successfully fixed environment variables for {args.function_app_name}")
        else:
            logger.error(f"Some environment variables could not be fixed for {args.function_app_name}")
            exit(1)

    except Exception as e:
        logger.error(f"Error in main: {e}")
        exit(1)
    finally:
        # Clean up Cosmos client
        if retriever:
            await retriever.close()


if __name__ == "__main__":
    asyncio.run(main())

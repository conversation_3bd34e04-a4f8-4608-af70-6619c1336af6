#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to get default function keys for all function apps and update Cosmos DB.

This script:
1. Gets the default function keys for all 3 functions (maturity, executive summary, powerpoint)
2. Updates the Cosmos DB project documents with these default keys

Usage:
    python scripts/set_fixed_function_keys.py [--dry-run]
"""

import os
import sys
import subprocess
import argparse
import logging
import asyncio
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.cosmos import get_cosmos_client
from azure.cosmos import exceptions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration constants
RESOURCE_GROUP = "rg-internal-ai"

# Environment variable names for function keys
FUNCTION_KEY_ENV_VARS = [
    "FUNCTION_KEY_MATURITY",
    "FUNCTION_KEY_EXECUTIVE_SUMMARY",
    "FUNCTION_KEY_POWERPOINT"
]

# Function names mapping
FUNCTION_NAMES = {
    "FUNCTION_KEY_MATURITY": "HttpTriggerAppMaturityAssessment",
    "FUNCTION_KEY_EXECUTIVE_SUMMARY": "HttpTriggerAppExecutiveSummary", 
    "FUNCTION_KEY_POWERPOINT": "HttpTriggerPowerPointGenerator"
}


class FunctionKeyManager:
    """Class to manage function keys and Cosmos DB updates."""
    
    def __init__(self, dry_run: bool = False):
        """Initialize the manager."""
        self.dry_run = dry_run
        self.cosmos_client = get_cosmos_client()
        if not self.cosmos_client:
            raise RuntimeError("Failed to initialize Cosmos DB client")
        
        self.database_name = os.environ.get("AZURE_COSMOSDB_DATABASE")
        self.container_name = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects")
        
        if not self.database_name:
            raise RuntimeError("AZURE_COSMOSDB_DATABASE environment variable not set")
        
        self.database = self.cosmos_client.get_database_client(self.database_name)
        self.container = self.database.get_container_client(self.container_name)
    
    async def run_az_command(self, cmd: List[str]) -> Optional[str]:
        """Run an Azure CLI command and return the output."""
        if self.dry_run:
            logger.info(f"DRY RUN: Would execute: {' '.join(cmd)}")
            return "dry-run-output"
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {' '.join(cmd)}")
            logger.error(f"Error: {e.stderr}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error running command: {e}")
            return None
    
    async def get_default_function_key(self, function_app_name: str, function_name: str) -> Optional[str]:
        """Get the default function key for a specific function."""
        logger.info(f"Getting default function key for {function_name} in {function_app_name}")

        cmd = [
            "az", "functionapp", "function", "keys", "list",
            "--name", function_app_name,
            "--resource-group", RESOURCE_GROUP,
            "--function-name", function_name,
            "--query", "default",
            "--output", "tsv"
        ]

        result = await self.run_az_command(cmd)
        if result and result.strip():
            key_value = result.strip()
            logger.info(f"✅ Retrieved default key for {function_name}: {key_value[:10]}...")
            return key_value
        else:
            logger.error(f"❌ Failed to get default key for {function_name}")
            return None
    
    async def get_all_projects(self) -> List[Dict[str, Any]]:
        """Retrieve all projects from Cosmos DB."""
        try:
            query = "SELECT * FROM c WHERE c.type = 'project' OR NOT IS_DEFINED(c.type)"
            projects = []
            
            async for item in self.container.query_items(
                query=query,
                partition_key=None
            ):
                projects.append(item)
            
            logger.info(f"Retrieved {len(projects)} projects from Cosmos DB")
            return projects
            
        except exceptions.CosmosHttpResponseError as e:
            logger.error(f"Error querying Cosmos DB: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving projects: {e}")
            return []
    
    async def update_project_environment(self, project: Dict[str, Any], function_keys: Dict[str, str]) -> bool:
        """Update project environment with function keys."""
        project_id = project.get('id')
        project_name = project.get('name', 'Unknown')

        logger.info(f"Updating environment for project {project_name} ({project_id})")

        if self.dry_run:
            logger.info(f"DRY RUN: Would update environment for project {project_id}")
            for key, value in function_keys.items():
                logger.info(f"DRY RUN: Would set {key} = {value[:10]}...")
            return True

        try:
            # Get current environment or create new one
            environment = project.get('environment', {})

            # Update with function keys
            for key, value in function_keys.items():
                environment[key] = value
                logger.info(f"Updated {key} in environment")

            # Update the project document
            project['environment'] = environment

            # Replace the document in Cosmos DB
            await self.container.replace_item(
                item=project['id'],
                body=project
            )

            logger.info(f"Successfully updated environment for project {project_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating environment for project {project_id}: {e}")
            return False
    
    async def process_function_app(self, project: Dict[str, Any]) -> bool:
        """Process a single function app - get default keys and update Cosmos DB."""
        project_id = project.get('id')
        project_name = project.get('name', 'Unknown')
        function_app_name = project.get('function_app_name')

        if not function_app_name:
            logger.info(f"Skipping project {project_name} - no function app")
            return True

        logger.info(f"Processing function app {function_app_name} for project {project_name}")

        # Get default function keys for each function
        function_keys = {}
        success_count = 0
        total_count = len(FUNCTION_NAMES)

        for env_key, function_name in FUNCTION_NAMES.items():
            default_key = await self.get_default_function_key(function_app_name, function_name)
            if default_key:
                function_keys[env_key] = default_key
                success_count += 1
            else:
                logger.error(f"Failed to get default key for {function_name}")

        logger.info(f"Retrieved {success_count}/{total_count} default function keys for {project_name}")

        # Update Cosmos DB environment with the retrieved keys
        if success_count > 0:
            if await self.update_project_environment(project, function_keys):
                logger.info(f"Successfully updated environment for {project_name}")
                return success_count == total_count
            else:
                logger.error(f"Failed to update environment for {project_name}")
                return False
        else:
            logger.error(f"No function keys retrieved for {project_name}")
            return False
    
    async def process_all_function_apps(self) -> Dict[str, Any]:
        """Process all function apps."""
        projects = await self.get_all_projects()
        results = {
            "successful": 0,
            "failed": 0,
            "skipped": 0,
            "total": len(projects)
        }
        
        for project in projects:
            project_id = project.get('id')
            function_app_name = project.get('function_app_name')
            
            if not function_app_name:
                results["skipped"] += 1
                continue
            
            success = await self.process_function_app(project)
            if success:
                results["successful"] += 1
            else:
                results["failed"] += 1
        
        return results
    
    async def close(self):
        """Close the Cosmos DB client."""
        if self.cosmos_client:
            await self.cosmos_client.close()


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Get default function keys and update Cosmos DB")
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )
    
    args = parser.parse_args()
    
    manager = None
    try:
        manager = FunctionKeyManager(dry_run=args.dry_run)
        
        logger.info("Getting default function keys for all function apps...")
        logger.info(f"Function key environment variables: {FUNCTION_KEY_ENV_VARS}")
        
        results = await manager.process_all_function_apps()
        
        # Print summary
        print(f"\n{'='*80}")
        print("SUMMARY")
        print(f"{'='*80}")
        print(f"Total projects: {results['total']}")
        print(f"Successful: {results['successful']}")
        print(f"Failed: {results['failed']}")
        print(f"Skipped (no function app): {results['skipped']}")
        
        if results['failed'] > 0:
            logger.error("Some function apps failed to process")
            exit(1)
        else:
            logger.info("All function apps processed successfully")
        
    except Exception as e:
        logger.error(f"Error in main: {e}")
        exit(1)
    finally:
        if manager:
            await manager.close()


if __name__ == "__main__":
    asyncio.run(main())

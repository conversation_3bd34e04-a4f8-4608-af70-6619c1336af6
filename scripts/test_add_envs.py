#!/usr/bin/env python3
"""
Test script to add the required environment variables to function apps.

Usage:
    python scripts/test_add_envs.py --function-app-name FUNCTION_APP_NAME [--dry-run]
    python scripts/test_add_envs.py --all [--dry-run]
"""

import os
import sys
import subprocess
import argparse
import logging
import asyncio
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.utils.cosmos import get_cosmos_client
from azure.cosmos import exceptions

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration constants
RESOURCE_GROUP = "rg-internal-ai"

# Environment variables to set
REQUIRED_ENV_VARS = {
    "AZURE_OPENAI_DEPLOYMENT_ID": "gpt-4o-ai-scope",
    "AZURE_OPENAI_DEPLOYMENT_ID_2": "gpt-4o-mini",
    "AZURE_OPENAI_DEPLOYMENT_ID_3": "o3-mini",
    "AZURE_OPENAI_VERSION": "2024-05-01-preview",
    "AZURE_OPENAI_DEPLOYMENT_ID_3_VERSION": "2025-01-31",
    "AZURE_OPENAI_ENDPOINT": "https://ai-scope-openai.openai.azure.com/",
    # AZURE_AI_SEARCH_INDEX will be set dynamically per project
}


class FunctionAppEnvManager:
    """Class to handle environment variable management for function apps."""

    def __init__(self, dry_run: bool = False):
        """Initialize the manager."""
        self.dry_run = dry_run
        self.cosmos_client = get_cosmos_client()
        if not self.cosmos_client:
            raise RuntimeError("Failed to initialize Cosmos DB client")

        self.database_name = os.environ.get("AZURE_COSMOSDB_DATABASE")
        self.container_name = os.environ.get("AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects")

        if not self.database_name:
            raise RuntimeError("AZURE_COSMOSDB_DATABASE environment variable not set")

        self.database = self.cosmos_client.get_database_client(self.database_name)
        self.container = self.database.get_container_client(self.container_name)

    async def get_all_projects(self) -> List[Dict[str, Any]]:
        """
        Retrieve all projects from Cosmos DB.

        Returns:
            List of project documents
        """
        try:
            query = "SELECT * FROM c WHERE c.type = 'project' OR NOT IS_DEFINED(c.type)"
            projects = []

            async for item in self.container.query_items(
                query=query,
                partition_key=None  # This enables cross-partition query
            ):
                projects.append(item)

            logger.info(f"Retrieved {len(projects)} projects from Cosmos DB")
            return projects

        except exceptions.CosmosHttpResponseError as e:
            logger.error(f"Error querying Cosmos DB: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error retrieving projects: {e}")
            return []

def run_az_command(cmd, dry_run=False):
    """
    Run an Azure CLI command and return the output.
    
    Args:
        cmd: List of command arguments
        dry_run: If True, just print what would be executed
        
    Returns:
        Command output or None if failed
    """
    if dry_run:
        logger.info(f"DRY RUN: Would execute: {' '.join(cmd)}")
        return "dry-run-output"
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {' '.join(cmd)}")
        logger.error(f"Error: {e.stderr}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error running command: {e}")
        return None


def check_current_settings(function_app_name):
    """Check current environment variables."""
    logger.info(f"Checking current environment variables for {function_app_name}")
    
    cmd = [
        "az", "functionapp", "config", "appsettings", "list",
        "--name", function_app_name,
        "--resource-group", RESOURCE_GROUP,
        "--query", "[].{name:name, value:value}",
        "--output", "table"
    ]
    
    result = run_az_command(cmd)
    if result:
        print("\nCurrent environment variables:")
        print(result)
    else:
        logger.error("Failed to retrieve current settings")


def add_required_envs(function_app_name, project_data=None, dry_run=False):
    """Add the required environment variables."""
    logger.info(f"Adding required environment variables to {function_app_name}")

    # Start with the base required environment variables
    settings = REQUIRED_ENV_VARS.copy()

    # Add project-specific environment variables if project data is provided
    if project_data:
        search_index = project_data.get('search_index_name')
        if search_index:
            settings["AZURE_AI_SEARCH_INDEX"] = search_index
            logger.info(f"Adding project-specific search index: {search_index}")

    # Convert settings to Azure CLI format
    settings_args = []
    for key, value in settings.items():
        settings_args.extend(["--settings", f"{key}={value}"])

    cmd = [
        "az", "functionapp", "config", "appsettings", "set",
        "--name", function_app_name,
        "--resource-group", RESOURCE_GROUP
    ] + settings_args

    result = run_az_command(cmd, dry_run)
    if result is not None:
        logger.info(f"Successfully updated environment variables for {function_app_name}")
        return True
    else:
        logger.error(f"Failed to update environment variables for {function_app_name}")
        return False


def verify_settings(function_app_name):
    """Verify the required environment variables were set."""
    logger.info(f"Verifying required environment variables for {function_app_name}")
    
    # Check each required environment variable
    for env_var in REQUIRED_ENV_VARS.keys():
        cmd = [
            "az", "functionapp", "config", "appsettings", "list",
            "--name", function_app_name,
            "--resource-group", RESOURCE_GROUP,
            "--query", f"[?name=='{env_var}'].{{name:name, value:value}}",
            "--output", "table"
        ]
        
        result = run_az_command(cmd)
        if result:
            print(f"\n{env_var}:")
            print(result)
        else:
            logger.error(f"Failed to check {env_var}")


def function_app_exists(function_app_name, dry_run=False):
    """Check if a function app exists in Azure."""
    logger.info(f"Checking if function app {function_app_name} exists")

    cmd = [
        "az", "functionapp", "show",
        "--name", function_app_name,
        "--resource-group", RESOURCE_GROUP,
        "--query", "name",
        "--output", "tsv"
    ]

    result = run_az_command(cmd, dry_run)
    exists = result is not None and result.strip() == function_app_name

    if exists:
        logger.info(f"Function app {function_app_name} exists")
    else:
        logger.warning(f"Function app {function_app_name} does not exist in Azure")

    return exists


async def process_all_function_apps(dry_run=False, check_only=False):
    """Process all function apps from Cosmos DB projects."""
    manager = None
    try:
        manager = FunctionAppEnvManager(dry_run)
        projects = await manager.get_all_projects()

        results = {
            "successful": 0,
            "failed": 0,
            "not_found": 0,
            "skipped": 0
        }

        for project in projects:
            project_id = project.get('id')
            project_name = project.get('name', 'Unknown')
            function_app_name = project.get('function_app_name')

            if not function_app_name:
                logger.info(f"Skipping project {project_name} ({project_id}) - no function app")
                results["skipped"] += 1
                continue

            print(f"\n{'='*80}")
            print(f"Processing: {project_name} ({project_id})")
            print(f"Function App: {function_app_name}")
            print(f"{'='*80}")

            # Check if function app exists
            if not function_app_exists(function_app_name, dry_run):
                logger.error(f"Function app {function_app_name} does not exist - skipping")
                results["not_found"] += 1
                continue

            # Check current settings
            check_current_settings(function_app_name)

            if not check_only:
                # Add required environment variables
                success = add_required_envs(function_app_name, project, dry_run)

                if success:
                    results["successful"] += 1
                    if not dry_run:
                        # Verify the settings were applied
                        print(f"\n{'-'*50}")
                        print("VERIFICATION - Required environment variables:")
                        print(f"{'-'*50}")
                        verify_settings(function_app_name)
                else:
                    results["failed"] += 1

        # Print summary
        print(f"\n{'='*80}")
        print("SUMMARY")
        print(f"{'='*80}")
        print(f"Successful: {results['successful']}")
        print(f"Failed: {results['failed']}")
        print(f"Function app not found: {results['not_found']}")
        print(f"Skipped (no function app): {results['skipped']}")
        print(f"Total projects: {len(projects)}")

        return results

    except Exception as e:
        logger.error(f"Error processing all function apps: {e}")
        return None
    finally:
        # Clean up Cosmos client
        if manager and manager.cosmos_client:
            await manager.cosmos_client.close()


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Add required environment variables to Azure Function App(s)")
    parser.add_argument(
        "--function-app-name",
        help="Name of the specific function app to configure"
    )
    parser.add_argument(
        "--all",
        action="store_true",
        help="Process all function apps from Cosmos DB projects"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without making changes"
    )
    parser.add_argument(
        "--check-only",
        action="store_true",
        help="Only check current settings, don't make changes"
    )

    args = parser.parse_args()

    if not args.function_app_name and not args.all:
        parser.error("Either --function-app-name or --all must be specified")

    try:
        if args.all:
            # Process all function apps from Cosmos DB
            await process_all_function_apps(args.dry_run, args.check_only)
        else:
            # Process single function app
            # Check current settings
            check_current_settings(args.function_app_name)

            if not args.check_only:
                # Add required environment variables
                success = add_required_envs(args.function_app_name, None, args.dry_run)

                if success and not args.dry_run:
                    # Verify the settings were applied
                    print("\n" + "="*50)
                    print("VERIFICATION - Required environment variables:")
                    print("="*50)
                    verify_settings(args.function_app_name)

    except Exception as e:
        logger.error(f"Error in main: {e}")


if __name__ == "__main__":
    asyncio.run(main())

#!/bin/bash
# Simple wrapper script to run the resource_group_cost.py script with proper environment variables

# Set environment variables to ensure HTTPS is used
export AZURE_AUTHORITY_HOST=https://login.microsoftonline.com
export AZURE_RESOURCE_MANAGER_URL=https://management.azure.com
export AZURE_AUTH_TRACING=1
export AZURE_HTTP_USER_AGENT='AzureCostScript/1.0.0'

# Check if required arguments are provided
if [ "$#" -lt 2 ]; then
    echo "Usage: $0 <subscription-id> <resource-group>"
    echo "Example: $0 4c1c14a3-de17-4cda-af60-01610fb493f9 rg-internal-ai"
    exit 1
fi

SUBSCRIPTION_ID=$1
RESOURCE_GROUP=$2

echo "Running cost analysis for resource group: $RESOURCE_GROUP"
echo "Using subscription ID: $SUBSCRIPTION_ID"
echo ""

# Run the Python script with the provided arguments
python resource_group_cost.py --subscription-id "$SUBSCRIPTION_ID" --resource-group "$RESOURCE_GROUP"

{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"_comment": {"value": "This is a template file with placeholders. It's used as a fallback if no project-specific parameters file is provided. In normal operation, a project-specific parameters file is dynamically generated by deploy_project_resources.py."}, "projectId": {"value": "project-id-placeholder"}, "projectName": {"value": "project-name-placeholder"}, "acrName": {"value": "functionappaiscope"}, "containerImageName": {"value": "functionapp"}, "containerImageTag": {"value": "latest"}, "searchServiceName": {"value": "search-service-name-placeholder"}, "searchIndexName": {"value": "project-index-placeholder"}, "searchIndexerName": {"value": "project-indexer-placeholder"}, "searchDatasourceName": {"value": "project-datasource-placeholder"}, "uploadsContainer": {"value": "uploads-container-placeholder"}, "inputContainer": {"value": "input-container-placeholder"}, "outputContainer": {"value": "output-container-placeholder"}, "openAiServiceName": {"value": "your-openai-service"}, "openAiModelDeployment": {"value": "gpt-35-turbo"}}}
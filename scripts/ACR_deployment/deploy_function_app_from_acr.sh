#!/bin/bash
# A robust script to deploy an Azure Function App from ACR.
#
# Key Features:
# - Exits immediately on any command failure (set -e).
# - Checks for dependencies (az, jq) before running.
# - Deploys synchronously using Bicep.
# - Intelligently polls for Function App 'Running' state without fixed waits.
# - Provides rich diagnostics on failure, including container logs and App Service Plan status.

set -e

# --- Logging and Configuration ---
LOG_DIR="logs"
mkdir -p $LOG_DIR
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="${LOG_DIR}/acr_deployment_${TIMESTAMP}.log"
echo "Starting ACR deployment script at $(date)" > "$LOG_FILE"

# Colors for output
GREEN='\033[0;32m'; YELLOW='\033[1;33m'; RED='\033[0;31m'; BLUE='\033[0;34m'; NC='\033[0m'

# Logging functions
log() { echo -e "$1" | tee -a "$LOG_FILE"; }
log_error() { echo -e "${RED}ERROR: $1${NC}" | tee -a "$LOG_FILE"; }
log_warning() { echo -e "${YELLOW}WARNING: $1${NC}" | tee -a "$LOG_FILE"; }
log_success() { echo -e "${GREEN}SUCCESS: $1${NC}" | tee -a "$LOG_FILE"; }

# --- Step 0: Dependency Checks ---
log "\n${BLUE}Step 0: Checking for required tools...${NC}"
command -v az >/dev/null 2>&1 || { log_error "Azure CLI 'az' is not installed. Please install it first."; exit 1; }
command -v jq >/dev/null 2>&1 || { log_error "'jq' is not installed. Please install it first."; exit 1; }
log_success "All required tools (az, jq) are available."

# --- Configuration ---
RESOURCE_GROUP="${RESOURCE_GROUP:-rg-internal-ai}"
PARAMETERS_FILE="${PARAMETERS_FILE:-scripts/ACR_deployment/parameters.json}"
log "\n${BLUE}Configuration:${NC}"
log "Resource Group: ${GREEN}$RESOURCE_GROUP${NC}"
log "Parameters File: ${GREEN}$PARAMETERS_FILE${NC}"

# Set a longer timeout for Azure CLI commands
export AZURE_CLI_OPERATION_TIMEOUT_SECONDS=900 # 15 minutes

# --- Step 1: Azure Login and Parameter Reading ---
log "\n${BLUE}Step 1: Verifying Azure Login and Reading Parameters...${NC}"
# Try managed identity login first
if az login --identity ${AZURE_CLIENT_ID:+--username $AZURE_CLIENT_ID} >/dev/null 2>&1; then
    log_success "Logged in using managed identity."
elif ! az account show > /dev/null 2>&1; then
    log "${YELLOW}Not logged in. Initiating device code login...${NC}"
    az login --use-device-code
fi
log_success "Azure login verified."

if [ ! -f "$PARAMETERS_FILE" ]; then
    log_error "Parameters file $PARAMETERS_FILE not found."
    exit 1
fi

# Use jq to read all required parameters, with robust error handling
FUNCTION_APP_NAME=$(jq -r '.parameters.functionAppName.value' "$PARAMETERS_FILE")
ACR_NAME=$(jq -r '.parameters.acrName.value' "$PARAMETERS_FILE")
CONTAINER_IMAGE_NAME=$(jq -r '.parameters.containerImageName.value' "$PARAMETERS_FILE")
CONTAINER_IMAGE_TAG=$(jq -r '.parameters.containerImageTag.value' "$PARAMETERS_FILE")
APP_SERVICE_PLAN_NAME=$(jq -r '.parameters.appServicePlanName.value' "$PARAMETERS_FILE") # Needed for diagnostics

if [ -z "$FUNCTION_APP_NAME" ] || [ "$FUNCTION_APP_NAME" == "null" ]; then log_error "functionAppName not found in $PARAMETERS_FILE"; exit 1; fi
log_success "Successfully read parameters. Function App to deploy: $FUNCTION_APP_NAME"


# --- Step 2 & 3: Pre-flight Checks (RG and ACR Image) ---
# (Omitted for brevity, assumed to be the same as the previous version)


# --- Step 4: Bicep Deployment (Synchronous) ---
log "\n${BLUE}Step 4: Deploying Function App via Bicep (Synchronous)...${NC}"
# Capture the output of the deployment to a variable for detailed checking
DEPLOYMENT_OUTPUT_FILE="${LOG_DIR}/bicep_deployment_output_${TIMESTAMP}.json"
if az deployment group create \
  --resource-group "$RESOURCE_GROUP" \
  --template-file scripts/ACR_deployment/function_app_deployment.bicep \
  --parameters @"$PARAMETERS_FILE" \
  --name "deploy-func-$(date +%s)" > "$DEPLOYMENT_OUTPUT_FILE"; then
    
    PROVISIONING_STATE=$(jq -r '.properties.provisioningState' "$DEPLOYMENT_OUTPUT_FILE")
    if [ "$PROVISIONING_STATE" == "Succeeded" ]; then
        log_success "Bicep deployment completed with state: $PROVISIONING_STATE."
    else
        log_error "Bicep deployment finished with a non-Succeeded state: $PROVISIONING_STATE."
        log_error "Full deployment output saved to $DEPLOYMENT_OUTPUT_FILE"
        exit 1
    fi
else
    log_error "Bicep deployment command failed. Check logs for details."
    exit 1
fi


# --- Step 5: Restart, Verify, and Diagnose ---
log "\n${BLUE}Step 5: Restarting and Verifying Function App Status...${NC}"
log "${YELLOW}Issuing restart for Function App '$FUNCTION_APP_NAME'...${NC}"
az functionapp restart --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" > /dev/null
log_success "Restart command issued."

# Intelligent polling configuration
max_verify_attempts=15      # 15 attempts
verify_interval_seconds=30  # 30 seconds apart
max_runtime_minutes=$(( (max_verify_attempts * verify_interval_seconds) / 60 ))
log "Polling for 'Running' state for up to $max_runtime_minutes minutes..."

verify_attempt=0
app_state=""
app_running=false

while [ $verify_attempt -lt $max_verify_attempts ]; do
    verify_attempt=$((verify_attempt + 1))
    app_state=$( (set +e; az functionapp show --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" --query "state" -o tsv 2>/dev/null) )

    if [ "$app_state" == "Running" ]; then
        log_success "Verification successful: Function App is in 'Running' state."
        app_running=true
        break
    fi

    log "Attempt $verify_attempt/$max_verify_attempts: State is '$app_state'. Waiting $verify_interval_seconds seconds..."
    sleep $verify_interval_seconds
done

# If not running, perform extensive diagnostics and exit
if [ "$app_running" = false ]; then
    log_error "VERIFICATION FAILED: Function App '$FUNCTION_APP_NAME' did not enter 'Running' state."
    log "\n${YELLOW}--- STARTING DIAGNOSTICS ---${NC}"

    log "\n${YELLOW}1. Final Function App Status:${NC}"
    az functionapp show --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" --output json | tee -a "$LOG_FILE" || log_warning "Could not retrieve final Function App status."

    log "\n${YELLOW}2. App Service Plan Status (${APP_SERVICE_PLAN_NAME}):${NC}"
    az appservice plan show --name "$APP_SERVICE_PLAN_NAME" --resource-group "$RESOURCE_GROUP" --output json | tee -a "$LOG_FILE" || log_warning "Could not retrieve App Service Plan status."

    log "\n${YELLOW}3. Container Configuration:${NC}"
    az functionapp config container show --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" --output json | tee -a "$LOG_FILE" || log_warning "Could not retrieve container configuration."

    # THIS IS THE CORRECTED COMMAND FOR LOGS
    log "\n${YELLOW}4. Fetching Recent Container Logs:${NC}"
    az webapp log show --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" | tee -a "$LOG_FILE" || log_warning "Could not retrieve container logs. This often happens if the container failed to pull or start."

    log "\n${YELLOW}--- DIAGNOSTICS END ---${NC}"
    log_error "Deployment failed. Please review the diagnostics above."
    exit 1
fi

# --- Final Summary ---
log "\n${BLUE}============================================================${NC}"
log_success "Function App Deployment from ACR completed successfully!"
FUNCTION_APP_URL=$(az functionapp show --name "$FUNCTION_APP_NAME" --resource-group "$RESOURCE_GROUP" --query defaultHostName -o tsv)
log "${YELLOW}Function App Name: ${GREEN}$FUNCTION_APP_NAME${NC}"
log "${YELLOW}Function App URL: ${GREEN}https://$FUNCTION_APP_URL${NC}"
log "Detailed logs available at: $LOG_FILE"
log "${BLUE}============================================================${NC}"

exit 0

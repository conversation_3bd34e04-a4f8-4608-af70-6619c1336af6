{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"_comment": {"value": "This is a test template file with placeholders. It's used for testing purposes only. In normal operation, a project-specific parameters file is dynamically generated by deploy_project_resources.py."}, "projectId": {"value": "test-project-id"}, "projectName": {"value": "test-project"}, "acrName": {"value": "functionappaiscope"}, "containerImageName": {"value": "functionapp"}, "containerImageTag": {"value": "latest"}, "searchServiceName": {"value": "search-test-service"}, "searchIndexName": {"value": "test-project-index"}, "searchIndexerName": {"value": "test-project-indexer"}, "searchDatasourceName": {"value": "test-project-ds"}, "uploadsContainer": {"value": "uploads-test-container"}, "inputContainer": {"value": "input-test-container"}, "outputContainer": {"value": "output-test-container"}, "openAiServiceName": {"value": "test-openai-service"}, "openAiModelDeployment": {"value": "gpt-35-turbo"}}}
{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "1.0.0.0", "parameters": {"functionAppName": {"value": "func-050813-test-28a5"}, "location": {"value": "westeurope"}, "appServicePlanName": {"value": "plan-func-050813-test-28a5"}, "projectId": {"value": "050813-test"}, "projectName": {"value": "050813"}, "uploadsContainer": {"value": ""}, "inputContainer": {"value": ""}, "outputContainer": {"value": ""}, "searchIndexName": {"value": ""}, "searchIndexerName": {"value": ""}, "searchDatasourceName": {"value": ""}, "searchServiceName": {"value": "search-050813-mxjm"}, "searchApiKey": {"value": "7G9PMSuHSqt6ukl5XPF9m6f9QPm5N6cpwuPzWJWH64AzSeBvuL1q"}, "openAiServiceName": {"value": "openai-service"}, "openAiApiKey": {"value": "placeholder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "openAiModelDeployment": {"value": "gpt-35-turbo"}, "storageConnectionString": {"value": "DefaultEndpointsProtocol=https;EndpointSuffix=core.windows.net;AccountName=stfunc050813test28a5;AccountKey=****************************************************************************************;BlobEndpoint=https://stfunc050813test28a5.blob.core.windows.net/;FileEndpoint=https://stfunc050813test28a5.file.core.windows.net/;QueueEndpoint=https://stfunc050813test28a5.queue.core.windows.net/;TableEndpoint=https://stfunc050813test28a5.table.core.windows.net/"}, "acrName": {"value": "functionappaiscope"}, "containerImageName": {"value": "functionapp"}, "containerImageTag": {"value": "latest"}}}
#!/usr/bin/env python3
"""
Fix missing SAS tokens in project environment fields.
This script ensures that all projects have their SAS tokens properly stored in the environment field.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timezone

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
from backend.services.project_service import ProjectDataService
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def fix_project_sas_tokens():
    """Fix missing SAS tokens in project environment fields."""
    
    # Initialize services
    project_service = ProjectDataService()
    rbac_client = CosmosRbacClient()
    
    try:
        # Get all projects
        logger.info("Fetching all projects...")
        projects = await rbac_client.list_all_projects()
        
        if not projects:
            logger.info("No projects found.")
            return
        
        logger.info(f"Found {len(projects)} projects")
        
        fixed_count = 0
        error_count = 0
        
        for project in projects:
            project_id = project.get("id")
            project_name = project.get("name", "Unknown")
            
            try:
                # Check if project has SAS token at root level
                root_sas_token = project.get("storage_account_sas_token")
                env_sas_token = project.get("environment", {}).get("STORAGE_ACCOUNT_SAS_TOKEN")
                
                if root_sas_token and not env_sas_token:
                    logger.info(f"Fixing project {project_name} ({project_id}): Adding SAS token to environment")
                    
                    # Ensure environment field exists
                    if "environment" not in project:
                        project["environment"] = {}
                    
                    # Add SAS token to environment
                    project["environment"]["STORAGE_ACCOUNT_SAS_TOKEN"] = root_sas_token
                    
                    # Also ensure storage account name is in environment
                    if project.get("storage_account_name") and "STORAGE_ACCOUNT_NAME" not in project["environment"]:
                        project["environment"]["STORAGE_ACCOUNT_NAME"] = project["storage_account_name"]
                    
                    # Ensure container names are in environment
                    if project.get("storage_container_uploads") and "STORAGE_CONTAINER_UPLOADS" not in project["environment"]:
                        project["environment"]["STORAGE_CONTAINER_UPLOADS"] = project["storage_container_uploads"]
                    
                    if project.get("storage_container_input") and "STORAGE_CONTAINER_INPUT" not in project["environment"]:
                        project["environment"]["STORAGE_CONTAINER_INPUT"] = project["storage_container_input"]
                    
                    if project.get("storage_container_output") and "STORAGE_CONTAINER_OUTPUT" not in project["environment"]:
                        project["environment"]["STORAGE_CONTAINER_OUTPUT"] = project["storage_container_output"]
                    
                    # Update the project
                    success = await project_service.update_project(project_id, project)
                    
                    if success:
                        logger.info(f"Successfully updated project {project_name}")
                        fixed_count += 1
                    else:
                        logger.error(f"Failed to update project {project_name}")
                        error_count += 1
                        
                elif not root_sas_token and not env_sas_token:
                    logger.warning(f"Project {project_name} ({project_id}) has no SAS token at all - needs deployment repair")
                    
                elif env_sas_token:
                    logger.info(f"Project {project_name} ({project_id}) already has SAS token in environment - skipping")
                    
            except Exception as e:
                logger.error(f"Error processing project {project_name} ({project_id}): {e}")
                error_count += 1
        
        logger.info(f"\nSummary:")
        logger.info(f"- Total projects: {len(projects)}")
        logger.info(f"- Fixed: {fixed_count}")
        logger.info(f"- Errors: {error_count}")
        logger.info(f"- Already correct: {len(projects) - fixed_count - error_count}")
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        raise


async def main():
    """Main entry point."""
    logger.info("Starting SAS token fix process...")
    await fix_project_sas_tokens()
    logger.info("Process completed.")


if __name__ == "__main__":
    asyncio.run(main())
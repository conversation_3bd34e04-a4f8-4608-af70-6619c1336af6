# scripts/auth_update.py
import argparse
import subprocess
import json

def get_azure_cli_token():
    """Get an access token using Azure CLI."""
    try:
        result = subprocess.run(
            ['az', 'account', 'get-access-token', '--resource', 'https://graph.microsoft.com'],
            capture_output=True,
            text=True,
            check=True
        )
        return json.loads(result.stdout)['accessToken']
    except subprocess.CalledProcessError as e:
        print(f"Error getting token: {e.stderr}")
        raise
    except json.JSONDecodeError as e:
        print(f"Error parsing token response: {e}")
        raise

def update_redirect_uris(app_id, uri):
    """Update the redirect URIs for an Azure AD application."""
    import urllib3
    
    # Get the access token
    token = get_azure_cli_token()
    
    # Define local development ports and their callback paths
    local_dev_uris = [
        "http://localhost:50505/.auth/login/aad/callback",
        "http://localhost:50505/.auth/login/aad/callback",
        "http://localhost:50507/.auth/login/aad/callback",
        "http://localhost:50508/.auth/login/aad/callback",
        "http://localhost:50509/.auth/login/aad/callback",
        "http://localhost:50505",  # Root URL for local development
        "http://localhost:50505",
        "http://localhost:50507",
        "http://localhost:50508",
        "http://localhost:50509"
    ]
    
    # Combine local development URIs with production URI
    redirect_uris = local_dev_uris + [f"{uri}"]
    
    # Make the PATCH request to update the redirect URIs
    urllib3.request(
        "PATCH",
        f"https://graph.microsoft.com/v1.0/applications/{app_id}",
        headers={
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        },
        json={
            "web": {
                "redirectUris": redirect_uris
            }
        },
    )
    print(f"Successfully updated redirect URIs for application {app_id}")
    print("Added the following redirect URIs:")
    for uri in redirect_uris:
        print(f"  - {uri}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Add a redirect URI to a registered application",
        epilog="Example: auth_update.py --appid 123 --uri https://abc.azureservices.net",
    )
    parser.add_argument(
        "--appid",
        required=True,
        help="Required. ID of the application to update.",
    )
    parser.add_argument(
        "--uri",
        required=True,
        help="Required. URI of the deployed application.",
    )
    args = parser.parse_args()

    print(
        f"Updating application registration {args.appid} with redirect URI for {args.uri}"
    )
    update_redirect_uris(args.appid, args.uri)

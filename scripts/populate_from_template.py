import argparse
import os
from azure.identity import DefaultAzureCredential
from azure.storage.blob import BlobServiceClient
from azure.cosmos import CosmosClient


def main():
    parser = argparse.ArgumentParser(
        description="Populate a project's input container from a template."
    )
    parser.add_argument(
        "--project-name",
        required=True,
        help="The name of the project to populate.",
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help=(
            "Required to confirm the operation, which may overwrite existing data."
        ),
    )

    args = parser.parse_args()

    if not args.force:
        print(
            "\U0001F6D1 ERROR: The --force flag is required to run this script. This will overwrite data in the destination container."
        )
        return

    print(f"Starting population for project: {args.project_name}")

    # Cosmos DB configuration
    cosmos_account = os.environ.get("AZURE_COSMOSDB_ACCOUNT")
    cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
    cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE")
    projects_container_name = os.environ.get(
        "AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects"
    )

    if not cosmos_account or not cosmos_key or not cosmos_database:
        print("\u274C ERROR: Cosmos DB environment variables are not fully set.")
        return

    cosmos_endpoint = (
        cosmos_account
        if cosmos_account.startswith("https://")
        else f"https://{cosmos_account}.documents.azure.com:443/"
    )

    try:
        cosmos_client = CosmosClient(cosmos_endpoint, credential=cosmos_key)
        database = cosmos_client.get_database_client(cosmos_database)
        container = database.get_container_client(projects_container_name)

        query = "SELECT * FROM c WHERE c.name = @name"
        parameters = [{"name": "@name", "value": args.project_name}]
        items = list(
            container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True,
            )
        )
    except Exception as e:
        print(f"\u274C ERROR querying Cosmos DB: {e}")
        return

    if not items:
        print(f"\u274C ERROR: Project '{args.project_name}' not found in Cosmos DB.")
        return

    project_doc = items[0]
    destination_storage_account = project_doc.get("storage_account_name")
    destination_container_name = project_doc.get("storage_container_input")

    if not destination_storage_account or not destination_container_name:
        print("\u274C ERROR: Project document missing storage information.")
        return

    print(
        f"Found project. Destination storage: {destination_storage_account}, container: {destination_container_name}"
    )

    source_account_name = os.environ.get("AZURE_STORAGE_ACCOUNT_NAME")
    if not source_account_name:
        print("\u274C ERROR: AZURE_STORAGE_ACCOUNT_NAME environment variable not set.")
        return

    source_url = f"https://{source_account_name}.blob.core.windows.net"
    dest_url = f"https://{destination_storage_account}.blob.core.windows.net"
    source_container_name = "input-template"

    credential = DefaultAzureCredential()

    try:
        source_client = BlobServiceClient(account_url=source_url, credential=credential)
        dest_client = BlobServiceClient(account_url=dest_url, credential=credential)

        source_container = source_client.get_container_client(source_container_name)
        dest_container = dest_client.get_container_client(destination_container_name)

        blobs = source_container.list_blobs()
        copied_count = 0
        for blob in blobs:
            print(f"  -> Copying '{blob.name}'...")
            stream = source_container.download_blob(blob.name)
            dest_container.upload_blob(name=blob.name, data=stream.readall(), overwrite=True)
            copied_count += 1

        print(
            f"\n\u2705 Success! Copied {copied_count} files to container '{destination_container_name}'."
        )
    except Exception as e:
        print(f"\u274C An error occurred during the blob copy operation: {e}")


if __name__ == "__main__":
    main()

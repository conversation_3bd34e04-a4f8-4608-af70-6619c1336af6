#!/usr/bin/env python
"""
Azure Resource Group Cost Example

This script demonstrates how to use the Azure Cost Management API to retrieve cost data
for a specific resource group.

Requirements:
- azure-identity
- azure-mgmt-costmanagement

Usage:
    python resource_group_cost_example.py --subscription-id <SUBSCRIPTION_ID> --resource-group <RESOURCE_GROUP>
"""

import argparse
import sys
import logging
from typing import Optional

from azure.identity import DefaultAzureCredential
from azure.mgmt.costmanagement import CostManagementClient
from azure.mgmt.costmanagement.models import (
    QueryDefinition,
    QueryDataset,
    QueryAggregation,
    QueryGrouping,
    QueryFilter,
    QueryComparisonExpression,
    TimeframeType
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_resource_group_cost(
    subscription_id: str,
    resource_group: str,
    timeframe: str = "MonthToDate"
):
    """
    Get cost data for a specific resource group.

    Args:
        subscription_id: Azure Subscription ID
        resource_group: Resource group name
        timeframe: Timeframe for cost data
    """
    # 1. Authenticate
    try:
        logger.info("Authenticating with Azure...")
        credential = DefaultAzureCredential()
        logger.info("Authentication successful")
    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        sys.exit(1)

    # 2. Initialize the Cost Management Client
    try:
        logger.info("Initializing Cost Management Client...")
        # Ensure we're using HTTPS for the base URL
        cost_client = CostManagementClient(
            credential,
            subscription_id,
            base_url="https://management.azure.com"
        )
        logger.info("Cost Management Client initialized")
    except Exception as e:
        if "Bearer token authentication is not permitted for non-TLS" in str(e):
            logger.error("Authentication failed: The Azure SDK is trying to use a non-HTTPS endpoint.")
            logger.error("Please ensure you're using HTTPS for all Azure endpoints.")
            logger.error("If you're behind a proxy, make sure it's properly configured.")
        else:
            logger.error(f"Failed to initialize Cost Management Client: {str(e)}")
        sys.exit(1)

    # 3. Build the Query
    logger.info(f"Building cost management query for resource group: {resource_group}...")

    # Convert timeframe string to TimeframeType enum
    timeframe_map = {
        "monthtodate": TimeframeType.MONTH_TO_DATE,
        "billingmonthtodate": TimeframeType.BILLING_MONTH_TO_DATE,
        "thelastmonth": TimeframeType.THE_LAST_MONTH,
        "thelastbillingmonth": TimeframeType.THE_LAST_BILLING_MONTH,
        "weektodate": TimeframeType.WEEK_TO_DATE,
    }
    timeframe_value = timeframe_map.get(timeframe.lower(), TimeframeType.MONTH_TO_DATE)

    # Create resource group filter
    resource_group_filter = QueryFilter(
        dimension=QueryComparisonExpression(
            name="ResourceGroup",
            operator="In",
            values=[resource_group]
        )
    )

    # Create the query definition
    query = QueryDefinition(
        type="ActualCost",
        timeframe=timeframe_value,
        dataset=QueryDataset(
            aggregation={
                "totalCost": QueryAggregation(name="PreTaxCost", function="Sum")
            },
            grouping=[
                QueryGrouping(type="Dimension", name="ServiceName")
            ],
            filter=resource_group_filter
        )
    )

    # 4. Execute the Query
    scope = f"/subscriptions/{subscription_id}"

    try:
        logger.info("Executing cost management query...")
        response = cost_client.query.usage(
            scope=scope,
            parameters=query
        )
        logger.info("Query executed successfully")
    except Exception as e:
        logger.error(f"Query execution failed: {str(e)}")
        sys.exit(1)

    # 5. Process the Results
    print(f"\nAzure Cost Management Results for Resource Group: {resource_group}")
    print("=" * 70)

    if not response.rows:
        print("No cost data found for the specified resource group.")
        return

    print(f"{'Service Name':<40} {'Cost ($)':<15}")
    print("-" * 55)

    total_cost = 0
    for row in response.rows:
        service_name = row[0]  # ServiceName
        service_cost = row[1]  # PreTaxCost
        total_cost += service_cost
        print(f"{service_name:<40} ${service_cost:.2f}")

    print("-" * 55)
    print(f"{'Total':<40} ${total_cost:.2f}")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Azure Resource Group Cost Example")

    parser.add_argument("--subscription-id", required=True, help="Azure Subscription ID")
    parser.add_argument("--resource-group", required=True, help="Resource group name")
    parser.add_argument("--timeframe", default="MonthToDate",
                        help="Timeframe for cost data: MonthToDate, BillingMonthToDate, TheLastMonth, TheLastBillingMonth, WeekToDate")

    return parser.parse_args()


def main():
    """Main function."""
    args = parse_args()

    get_resource_group_cost(
        subscription_id=args.subscription_id,
        resource_group=args.resource_group,
        timeframe=args.timeframe
    )


if __name__ == "__main__":
    main()

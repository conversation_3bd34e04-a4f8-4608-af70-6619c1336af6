#!/usr/bin/env python3
"""
<PERSON>ript to identify duplicate users based on normalized emails.
This script will list all duplicates without removing them.
"""

import asyncio
import os
from dotenv import load_dotenv
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.rbac.user_context import normalize_email
from backend.models.rbac import UserRole

async def identify_duplicate_users():
    """Identify duplicate users based on normalized emails"""
    load_dotenv()
    
    print("Identifying duplicate users...")
    
    # Initialize Cosmos client
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    try:
        # Initialize the client
        init_success = await cosmos_client.initialize()
        if not init_success:
            print("Failed to initialize Cosmos client")
            return
        
        # Get all users
        print("\nFetching all users...")
        all_users = await cosmos_client.get_users()
        print(f"Found {len(all_users)} total users")
        
        # Group users by normalized email
        user_groups = {}
        for user in all_users:
            email = user.get("email")
            if email:
                # Use stored normalized_email if available, otherwise normalize
                normalized = user.get("normalized_email") or normalize_email(email)
                if normalized not in user_groups:
                    user_groups[normalized] = []
                user_groups[normalized].append(user)
        
        # Find and display duplicates
        duplicates_found = False
        total_duplicates = 0
        
        print("\n=== DUPLICATE USERS REPORT ===")
        
        for normalized_email, users in user_groups.items():
            if len(users) > 1:
                duplicates_found = True
                total_duplicates += len(users) - 1  # Count extra users
                
                print(f"\n[{normalized_email}] - {len(users)} users found:")
                
                # Sort by creation date (oldest first)
                users.sort(key=lambda u: u.get("created_at", "1900-01-01"))
                
                for i, user in enumerate(users):
                    status = "KEEP" if i == 0 else "DUPLICATE"
                    print(f"  {i+1}. [{status}] {user.get('name')}")
                    print(f"     ID: {user.get('id')}")
                    print(f"     Email: {user.get('email')}")
                    print(f"     Role: {user.get('role')}")
                    print(f"     Region: {user.get('region', 'N/A')}")
                    print(f"     Created: {user.get('created_at', 'Unknown')}")
                    
                    # Check for project assignments
                    project_users = await cosmos_client.get_project_users("")
                    user_projects = [p for p in project_users if p.get("userId") == user.get("id")]
                    if user_projects:
                        print(f"     Projects: {len(user_projects)} assignments")
                    
                    # Check for team memberships
                    team_members = await cosmos_client.get_team_members("")
                    user_teams = [t for t in team_members if t.get("userId") == user.get("id")]
                    if user_teams:
                        print(f"     Teams: {len(user_teams)} memberships")
        
        if not duplicates_found:
            print("\nNo duplicate users found!")
        else:
            print(f"\n=== SUMMARY ===")
            print(f"Total duplicate users to remove: {total_duplicates}")
            print(f"Unique emails with duplicates: {sum(1 for g in user_groups.values() if len(g) > 1)}")
            
            # Show which specific users are duplicates
            print("\n=== RECOMMENDED ACTIONS ===")
            for normalized_email, users in user_groups.items():
                if len(users) > 1:
                    # Find Vansh Sial and Eduard Yanchevsky specifically
                    if "vansh.sial" in normalized_email:
                        print(f"\nVansh Sial duplicates ({normalized_email}):")
                        for user in users:
                            print(f"  - {user.get('name')} (ID: {user.get('id')}, Email: {user.get('email')})")
                    elif "eduard.yanchevsky" in normalized_email:
                        print(f"\nEduard Yanchevsky duplicates ({normalized_email}):")
                        for user in users:
                            print(f"  - {user.get('name')} (ID: {user.get('id')}, Email: {user.get('email')})")
                    elif "mauricio.yan" in normalized_email:
                        print(f"\nMauricio Yan duplicates ({normalized_email}):")
                        for user in users:
                            print(f"  - {user.get('name')} (ID: {user.get('id')}, Email: {user.get('email')})")
        
    finally:
        await cosmos_client.close()

def main():
    """Run the duplicate identification"""
    print("=== User Duplicate Identification ===\n")
    asyncio.run(identify_duplicate_users())
    print("\n=== Report Complete ===")

if __name__ == "__main__":
    main()
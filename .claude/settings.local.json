{"permissions": {"allow": ["Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(git pull:*)", "Bash(git merge:*)", "Bash(git rebase:*)", "<PERSON><PERSON>(python:*)", "Bash(npm run build:*)", "Bash(grep:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(chmod:*)", "Bash(rm:*)", "Bash(pip install:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(awk:*)", "Bash(git restore:*)", "Bash(git rm:*)", "<PERSON><PERSON>(true)", "Bash(git reset:*)", "Bash(git commit:*)", "Bash(git stash:*)", "Bash(find:*)", "<PERSON><PERSON>(cat:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(az storage blob download:*)", "Bash(./start.sh:*)", "Bash(./scripts/copy_SOURCE_APP_ID_roles.sh:*)", "<PERSON><PERSON>(az acr show:*)", "<PERSON><PERSON>(echo:*)", "Bash(rg:*)", "Bash(rg:*)", "Bash(./.venv/bin/pip:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run dev:*)", "Bash(bash:*)", "Bash(az webapp log tail:*)", "Bash(timeout 60 az webapp log tail --resource-group rg-internal-ai --name ai-scope-app3)", "<PERSON><PERSON>(az webapp config show:*)", "Bash(az webapp config set:*)", "Bash(az webapp restart:*)", "<PERSON><PERSON>(az webapp show:*)", "Bash(az webapp log download:*)", "Ba<PERSON>(unzip:*)", "Bash(az acr repository show-tags:*)", "Bash(az webapp config container set:*)", "Bash(npm ls:*)", "Bash(npm install:*)", "mcp__ide__getDiagnostics", "Bash(for f in *.css)", "Bash(do echo \"=== $f ===\")", "Bash(done)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "<PERSON><PERSON>(az account show:*)", "Bash(ss:*)", "WebFetch(domain:docs.anthropic.com)"], "deny": []}}
#!/bin/bash
# Don't exit on error - we'll handle errors ourselves
# Initial login using the managed identity
az login --identity
# set -e

# Trap any errors and log them but continue execution
trap 'log_error "Command failed with exit code $?: $BASH_COMMAND"; LAST_ERROR_CODE=$?' ERR

# Initialize error tracking
LAST_ERROR_CODE=0

# Initialize deployment success tracking
DEPLOYMENT_SUCCESS=false

# Create logs directory if it doesn't exist
LOGS_DIR="logs"
mkdir -p $LOGS_DIR

# Generate a unique log file name based on timestamp and project ID
TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
LOG_FILE="${LOGS_DIR}/deployment_${TIMESTAMP}.log"

# Function to log with timestamp to both console and log file
log() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo "$message"
    echo "$message" >> "$LOG_FILE"
}

# Function to log with timestamp and measure elapsed time
start_timer() {
    TIMER_START=$(date +%s)
    log "STARTING: $1"
}

end_timer() {
    TIMER_END=$(date +%s)
    ELAPSED=$((TIMER_END - TIMER_START))
    log "COMPLETED: $1 (took ${ELAPSED}s)"
}

# Function to log errors with more visibility
log_error() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1"
    echo -e "\e[31m$message\e[0m"  # Red color in terminal
    echo "$message" >> "$LOG_FILE"
}

# Function to log warnings with more visibility
log_warning() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1"
    echo -e "\e[33m$message\e[0m"  # Yellow color in terminal
    echo "$message" >> "$LOG_FILE"
}

# Function to log success messages with more visibility
log_success() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1"
    echo -e "\e[32m$message\e[0m"  # Green color in terminal
    echo "$message" >> "$LOG_FILE"
}

# Function to output JSON for easier parsing by the Python script
output_json() {
    local json_data="$1"
    echo "$json_data"
    echo "JSON_OUTPUT_START"
    echo "$json_data"
    echo "JSON_OUTPUT_END"
}

log "Starting deployment script - Logging to $LOG_FILE"

# Update the log file name with project ID once we have it
update_log_file() {
    if [ -n "$PROJECT_ID" ]; then
        NEW_LOG_FILE="${LOGS_DIR}/deployment_${TIMESTAMP}_${PROJECT_ID}.log"
        mv "$LOG_FILE" "$NEW_LOG_FILE"
        LOG_FILE="$NEW_LOG_FILE"
        log "Updated log file to $LOG_FILE"
    fi
}

# Default values that can be overridden by environment variables
RESOURCE_GROUP="${RESOURCE_GROUP:-rg-internal-ai}"
LOCATION="${LOCATION:-westeurope}"
REGION_ID="${REGION_ID:-}"
FUNCTION_APP_RESOURCE_ID="${FUNCTION_APP_RESOURCE_ID:-}"  # Will be populated if provided
STORAGE_ACCOUNT_ID="${STORAGE_ACCOUNT_ID:-}"  # Will be populated if provided for event grid system topic deployment
TEMPLATE_FILE="${TEMPLATE_FILE:-project_resources_with_eventgrid_system.bicep}"  # Default template file with Event Grid System Topic
EVENT_GRID_SYSTEM_TOPIC_NAME="${EVENT_GRID_SYSTEM_TOPIC_NAME:-}"  # Will be populated if provided for event grid system topic deployment
SUBSCRIPTION_ID="${SUBSCRIPTION_ID:-}"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --region-id)
            REGION_ID="$2"
            shift 2
            ;;
        --resource-group)
            RESOURCE_GROUP="$2"
            shift 2
            ;;
        --subscription-id)
            SUBSCRIPTION_ID="$2"
            shift 2
            ;;
        --location)
            LOCATION="$2"
            shift 2
            ;;
        --function-app-id)
            FUNCTION_APP_RESOURCE_ID="$2"
            shift 2
            ;;
        --storage-account-id)
            STORAGE_ACCOUNT_ID="$2"
            shift 2
            ;;
        --event-grid-system-topic-name)
            EVENT_GRID_SYSTEM_TOPIC_NAME="$2"
            shift 2
            ;;
        --template-file)
            TEMPLATE_FILE="$2"
            shift 2
            ;;
        *)
            # Assume the first two positional arguments are project_id and project_name
            if [ -z "$PROJECT_ID" ]; then
                PROJECT_ID="$1"
            elif [ -z "$PROJECT_NAME" ]; then
                PROJECT_NAME="$1"
            else
                echo "Unknown parameter: $1"
                echo "Usage: $0 <project_id> <project_name> [--region-id <region_id>] [--resource-group <resource_group>] [--subscription-id <subscription_id>] [--location <location>] [--function-app-id <function_app_id>] [--storage-account-id <storage_account_id>] [--template-file <template_file>]"
                exit 1
            fi
            shift
            ;;
    esac
done

# Check if required parameters are provided
if [ -z "$PROJECT_ID" ] || [ -z "$PROJECT_NAME" ]; then
    log_error "Missing required parameters: project_id and project_name"
    log "Usage: $0 <project_id> <project_name> [--region-id <region_id>] [--resource-group <resource_group>] [--subscription-id <subscription_id>] [--location <location>] [--function-app-id <function_app_id>] [--storage-account-id <storage_account_id>] [--template-file <template_file>]"
    exit 1
fi

# Update log file name with project ID
update_log_file

# Log the template file being used
log "Using template file: $TEMPLATE_FILE"

log "Deploying Azure resources for project: $PROJECT_NAME (ID: $PROJECT_ID)"

# Ensure Azure CLI is logged in
start_timer "Azure CLI login check"
log "Checking Azure CLI login status..."
# Attempt non-interactive login using managed identity. If AZURE_CLIENT_ID is
# set, a user-assigned identity will be used. Otherwise the system-assigned
# identity is assumed.
az login --identity ${AZURE_CLIENT_ID:+--username $AZURE_CLIENT_ID} >/dev/null 2>&1

if ! az account show > /dev/null 2>&1; then
    log_error "Not logged in to Azure CLI. Ensure managed identity access is configured."

    # Output JSON for error handling in Python script
    output_json "{\"error\": \"authentication_error\", \"message\": \"Not logged in to Azure CLI. Ensure managed identity access is configured.\"}"

    exit 1
fi
log_success "Azure CLI login verified"
end_timer "Azure CLI login check"

# Get or use the provided subscription ID
start_timer "Subscription setup"
if [ -z "$SUBSCRIPTION_ID" ]; then
    SUBSCRIPTION_ID=$(az account show --query id -o tsv)
    log "Using current Azure subscription: $SUBSCRIPTION_ID"
else
    log "Using provided Azure subscription: $SUBSCRIPTION_ID"
    # Set the subscription context
    log "Setting subscription context..."
    az account set --subscription "$SUBSCRIPTION_ID"

    # Verify the subscription was set correctly
    CURRENT_SUB=$(az account show --query id -o tsv)
    if [ "$CURRENT_SUB" != "$SUBSCRIPTION_ID" ]; then
        log_error "Failed to set subscription context to $SUBSCRIPTION_ID. Current subscription is $CURRENT_SUB."
        output_json "{\"error\": \"subscription_error\", \"message\": \"Failed to set subscription context to $SUBSCRIPTION_ID\"}"
        exit 1
    fi
    log_success "Subscription context set to $SUBSCRIPTION_ID"
fi
end_timer "Subscription setup"

# Verify access to the resource group
start_timer "Resource group verification"
log "Verifying access to resource group: $RESOURCE_GROUP"
if ! az group show --name $RESOURCE_GROUP > /dev/null 2>&1; then
    log_error "Resource group $RESOURCE_GROUP not found or you don't have access to it."

    # Try to list available resource groups for debugging
    log "Available resource groups in subscription $SUBSCRIPTION_ID:"
    az group list --query "[].name" -o tsv | tee -a "$LOG_FILE"

    output_json "{\"error\": \"resource_group_error\", \"message\": \"Resource group $RESOURCE_GROUP not found or you don't have access to it.\"}"
    exit 1
fi
log_success "Resource group $RESOURCE_GROUP verified"
end_timer "Resource group verification"

# ACR configuration
ACR_NAME="functionappaiscope"
CONTAINER_IMAGE_NAME="functionapp"
CONTAINER_IMAGE_TAG="latest"

# Verify access to the ACR with detailed error handling
start_timer "ACR verification"
log "Verifying access to ACR: $ACR_NAME"
if ! az acr show --name $ACR_NAME > /dev/null 2>&1; then
    log_error "ACR $ACR_NAME not found or you don't have access to it."

    # Check if the ACR exists at all
    if ! az acr list --query "[?name=='$ACR_NAME']" -o tsv | grep -q "$ACR_NAME"; then
        log_error "ACR $ACR_NAME does not exist in the current subscription."
        log "Available ACRs in subscription:"
        az acr list --query "[].name" -o tsv | tee -a "$LOG_FILE"
    else
        log_error "You don't have access to ACR $ACR_NAME."
        log "Please check your permissions."
    fi

    # Output JSON for error handling in Python script
    output_json "{\"error\": \"acr_access_error\", \"message\": \"ACR $ACR_NAME not found or you don't have access to it.\"}"
    exit 1
fi
log_success "ACR $ACR_NAME verified"
end_timer "ACR verification"

# Verify the container image exists with detailed error handling
start_timer "Container image verification"
log "Verifying container image: $CONTAINER_IMAGE_NAME:$CONTAINER_IMAGE_TAG"
if ! az acr repository show --name $ACR_NAME --image $CONTAINER_IMAGE_NAME:$CONTAINER_IMAGE_TAG > /dev/null 2>&1; then
    log_error "Image $CONTAINER_IMAGE_NAME:$CONTAINER_IMAGE_TAG not found in ACR $ACR_NAME."

    # Check if the repository exists
    if ! az acr repository list --name $ACR_NAME --query "[?contains(@, '$CONTAINER_IMAGE_NAME')]" -o tsv | grep -q "$CONTAINER_IMAGE_NAME"; then
        log_error "Repository $CONTAINER_IMAGE_NAME does not exist in ACR $ACR_NAME."
        log "Available repositories in ACR $ACR_NAME:"
        az acr repository list --name $ACR_NAME -o table | tee -a "$LOG_FILE"

        # Output JSON for error handling in Python script
        output_json "{\"error\": \"container_repository_error\", \"message\": \"Repository $CONTAINER_IMAGE_NAME does not exist in ACR $ACR_NAME.\"}"
    else
        # Repository exists, but tag might be missing
        log_warning "Repository $CONTAINER_IMAGE_NAME exists, but tag $CONTAINER_IMAGE_TAG not found."
        log "Available tags for repository $CONTAINER_IMAGE_NAME:"
        az acr repository show-tags --name $ACR_NAME --repository $CONTAINER_IMAGE_NAME --output table | tee -a "$LOG_FILE"

        # Suggest using the latest available tag
        LATEST_TAG=$(az acr repository show-tags --name $ACR_NAME --repository $CONTAINER_IMAGE_NAME --orderby time_desc --query "[0]" -o tsv)
        if [ -n "$LATEST_TAG" ]; then
            log_warning "Consider using the latest available tag: $LATEST_TAG"
        fi

        # Output JSON for error handling in Python script
        output_json "{\"error\": \"container_tag_error\", \"message\": \"Tag $CONTAINER_IMAGE_TAG not found for repository $CONTAINER_IMAGE_NAME in ACR $ACR_NAME.\", \"suggestion\": \"$LATEST_TAG\"}"
    fi

    exit 1
fi
log_success "Container image $CONTAINER_IMAGE_NAME:$CONTAINER_IMAGE_TAG verified"
end_timer "Container image verification"

log_success "ACR and container image verified successfully."

# Deploy the Bicep template
log "Preparing to deploy Bicep template with the following parameters:"
log "Project ID: $PROJECT_ID"
log "Project Name: $PROJECT_NAME"
log "Region ID: $REGION_ID"
log "Location: $LOCATION"
log "Resource Group: $RESOURCE_GROUP"

# Convert ALLOWED_ORIGINS environment variable into JSON array for Bicep
ALLOWED_ORIGINS_VALUE="${ALLOWED_ORIGINS:-}"
if [ -n "$ALLOWED_ORIGINS_VALUE" ]; then
    IFS=',' read -ra ORIGIN_ARRAY <<< "$ALLOWED_ORIGINS_VALUE"
    CORS_ALLOWED_ORIGINS_JSON="["
    for origin in "${ORIGIN_ARRAY[@]}"; do
        trimmed=$(echo "$origin" | xargs)
        CORS_ALLOWED_ORIGINS_JSON="$CORS_ALLOWED_ORIGINS_JSON\"$trimmed\", "
    done
    CORS_ALLOWED_ORIGINS_JSON="${CORS_ALLOWED_ORIGINS_JSON%, }]"
else
    CORS_ALLOWED_ORIGINS_JSON="[]"
fi
log "CORS allowed origins: $CORS_ALLOWED_ORIGINS_JSON"

# Make sure region ID is in the correct format
if [ -z "$REGION_ID" ]; then
    log "Warning: Region ID is empty. Using default region ID."
    REGION_ID="00000000-0000-0000-0000-000000000000"
elif [[ ! "$REGION_ID" =~ ^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$ ]]; then
    # If region ID is not a UUID, check if it's a region name
    if [[ "$REGION_ID" =~ ^[a-zA-Z]+[a-zA-Z0-9]*$ ]]; then
        log "Region ID appears to be a region name, not a UUID. Converting to a deterministic UUID."
        # Generate a deterministic UUID from the region name
        REGION_UUID=$(echo -n "$REGION_ID" | md5sum | sed 's/^\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\)\(..\).*$/\1\2\3\4-\5\6-\7\8-\9\10-\11\12\13\14\15\16/')
        log "Converted region name '$REGION_ID' to UUID: $REGION_UUID"
        REGION_ID="$REGION_UUID"
    else
        log "Warning: Region ID is not in UUID format. Using default region ID."
        REGION_ID="00000000-0000-0000-0000-000000000000"
    fi
fi

log "Final region ID being used: $REGION_ID"

# Create a temporary parameters file to ensure proper parameter passing
start_timer "Creating parameters file"
PARAMS_FILE=$(mktemp)
# Generate event grid system topic name if not provided and we're deploying event_grid.bicep
if [[ "$TEMPLATE_FILE" == *"event_grid.bicep" ]] && [ -z "$EVENT_GRID_SYSTEM_TOPIC_NAME" ]; then
    # Generate sanitized name for Azure resources
    SANITIZED_NAME=$(echo "$PROJECT_NAME" | tr '[:upper:]' '[:lower:]' | tr -d ' ' | tr -cd 'a-z0-9-')
    UNIQUE_SUFFIX=$(echo "$PROJECT_ID" | md5sum | cut -c1-4)
    EVENT_GRID_SYSTEM_TOPIC_NAME="evgt-${SANITIZED_NAME}-${UNIQUE_SUFFIX}"
    log "Generated Event Grid System Topic name: $EVENT_GRID_SYSTEM_TOPIC_NAME"
fi

# Create parameters file based on template file
if [[ "$TEMPLATE_FILE" == *"event_grid.bicep" ]]; then
    # Parameters for event_grid.bicep
    cat > $PARAMS_FILE << EOF
{
    "\$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "1.0.0.0",
    "parameters": {
        "eventGridSystemTopicName": {
            "value": "$EVENT_GRID_SYSTEM_TOPIC_NAME"
        },
        "location": {
            "value": "$LOCATION"
        },
        "functionAppResourceId": {
            "value": "$FUNCTION_APP_RESOURCE_ID"
        },
        "storageAccountId": {
            "value": "$STORAGE_ACCOUNT_ID"
        },
        "tags": {
            "value": {
                "project-id": "$PROJECT_ID",
                "region-id": "$REGION_ID",
                "project-name": "$PROJECT_NAME"
            }
        }
    }
}
EOF
else
    # Parameters for other templates
    cat > $PARAMS_FILE << EOF
{
    "\$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#",
    "contentVersion": "1.0.0.0",
    "parameters": {
        "projectId": {
            "value": "$PROJECT_ID"
        },
        "projectName": {
            "value": "$PROJECT_NAME"
        },
        "regionId": {
            "value": "$REGION_ID"
        },
        "location": {
            "value": "$LOCATION"
        },
        "functionAppResourceId": {
            "value": "$FUNCTION_APP_RESOURCE_ID"
        },
        "storageAccountId": {
            "value": "$STORAGE_ACCOUNT_ID"
        },
        "corsAllowedOrigins": {
            "value": $CORS_ALLOWED_ORIGINS_JSON
        }
    }
}
EOF
fi
end_timer "Creating parameters file"

log "Using parameters file: $PARAMS_FILE"
cat $PARAMS_FILE

# Function to monitor deployment progress
monitor_deployment() {
    local deployment_name="project_resources"
    local start_time=$(date +%s)
    local last_status=""
    local last_resource=""
    local check_interval=10  # seconds between checks

    log "Starting deployment monitoring..."

    while true; do
        # Get current deployment status
        local status=$(az deployment group show \
            --resource-group $RESOURCE_GROUP \
            --name $deployment_name \
            --query "properties.provisioningState" \
            --output tsv 2>/dev/null)

        # If we can't get status yet, wait and try again
        if [ -z "$status" ]; then
            sleep 5
            continue
        fi

        # If status changed, log it
        if [ "$status" != "$last_status" ]; then
            log "Deployment status: $status"
            last_status="$status"

            # If deployment is complete, break the loop
            if [ "$status" == "Succeeded" ] || [ "$status" == "Failed" ] || [ "$status" == "Canceled" ]; then
                break
            fi
        fi

        # Try to get the current resource being deployed
        local current_resources=$(az deployment group show \
            --resource-group $RESOURCE_GROUP \
            --name $deployment_name \
            --query "properties.outputResources[].id" \
            --output tsv 2>/dev/null)

        # Count resources and report
        if [ ! -z "$current_resources" ]; then
            local resource_count=$(echo "$current_resources" | wc -l)
            local newest_resource=$(echo "$current_resources" | tail -1)

            # Only log if we have a new resource
            if [ "$newest_resource" != "$last_resource" ] && [ ! -z "$newest_resource" ]; then
                local resource_name=$(echo "$newest_resource" | awk -F'/' '{print $NF}')
                local resource_type=$(echo "$newest_resource" | awk -F'/' '{print $(NF-1)}')
                log "Deploying resource ($resource_count): $resource_type/$resource_name"
                last_resource="$newest_resource"
            fi
        fi

        # Calculate elapsed time and log progress every minute
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        if [ $((elapsed % 60)) -eq 0 ] && [ $((elapsed)) -gt 0 ]; then
            log "Deployment in progress... (${elapsed}s elapsed)"
        fi

        sleep $check_interval
    done

    # Final status check
    local final_status=$(az deployment group show \
        --resource-group $RESOURCE_GROUP \
        --name $deployment_name \
        --query "properties.provisioningState" \
        --output tsv 2>/dev/null)

    if [ "$final_status" == "Succeeded" ]; then
        log "Deployment completed successfully after $(($(date +%s) - start_time)) seconds"
        return 0
    else
        log "Deployment ended with status: $final_status after $(($(date +%s) - start_time)) seconds"
        return 1
    fi
}

# Start the Bicep deployment with detailed logging
log "Starting Bicep deployment - this may take several minutes..."
start_timer "Bicep deployment"

# Add more detailed logging for the deployment process
log "Deploying storage account, search service, and other resources..."

# Create a deployment log file
DEPLOYMENT_LOG_FILE="${LOGS_DIR}/bicep_deployment_${TIMESTAMP}_${PROJECT_ID}.log"
log "Bicep deployment logs will be saved to: $DEPLOYMENT_LOG_FILE"

# Run the deployment directly (not in background) to capture output
log "Starting Azure deployment..."

# Set environment variables to disable SSL verification for Azure CLI
# This helps with SSL connection issues in containerized environments
export AZURE_CLI_DISABLE_CONNECTION_VERIFICATION=1
export AZURE_CLI_EXPERIMENTAL_CONNECTION_VERIFY_OFF=1

# Single deployment attempt with improved waiting mechanism
SUCCESS=false
TIMEOUT_SECONDS=1800  # 30 minutes timeout for the deployment

# Generate a unique deployment name including partial project ID
PROJECT_ID_SHORT=$(echo "$PROJECT_ID" | cut -c1-8)
DEPLOYMENT_NAME="project-resources-$(date +%Y%m%d%H%M%S)-${PROJECT_ID_SHORT}"
log "Starting deployment with name: $DEPLOYMENT_NAME"
log "Using template file: $TEMPLATE_FILE"

# Function to wait for deployment completion
wait_for_deployment() {
    local deployment_name=$1
    local timeout=$2
    local start_time=$(date +%s)
    local end_time=$((start_time + timeout))
    local current_time=$start_time
    local status=""
    local check_interval=30  # Check every 30 seconds

    log "Waiting for deployment $deployment_name to complete (timeout: ${timeout}s)..."

    while [ $current_time -lt $end_time ]; do
        # Get current deployment status
        status=$(az deployment group show \
            --resource-group $RESOURCE_GROUP \
            --name $deployment_name \
            --query "properties.provisioningState" \
            --output tsv 2>/dev/null)

        if [ -z "$status" ]; then
            log "Deployment not found yet, waiting..."
        else
            log "Current deployment status: $status"

            # Check if deployment is complete
            if [ "$status" == "Succeeded" ]; then
                log_success "Deployment completed successfully after $(($(date +%s) - start_time)) seconds"
                return 0
            elif [ "$status" == "Failed" ] || [ "$status" == "Canceled" ]; then
                log_error "Deployment ended with status: $status after $(($(date +%s) - start_time)) seconds"
                return 1
            fi
        fi

        # Sleep before checking again
        sleep $check_interval
        current_time=$(date +%s)

        # Log progress every 5 minutes
        elapsed=$((current_time - start_time))
        if [ $((elapsed % 300)) -lt $check_interval ]; then
            log "Deployment in progress... (${elapsed}s elapsed, timeout at ${timeout}s)"
        fi
    done

    log_error "Deployment timed out after ${timeout}s"
    return 2  # Timeout
}

# Start the deployment
log "Executing Bicep deployment..."
(
    set -o pipefail
    az deployment group create \
      --resource-group $RESOURCE_GROUP \
      --name $DEPLOYMENT_NAME \
      --template-file $TEMPLATE_FILE \
      --parameters @$PARAMS_FILE \
      --verbose 2>&1 | tee -a "$DEPLOYMENT_LOG_FILE" &

    # Store the background process PID
    DEPLOYMENT_PID=$!

    # Wait for the deployment to start
    sleep 5

    # Wait for deployment to complete
    wait_for_deployment "$DEPLOYMENT_NAME" $TIMEOUT_SECONDS
    WAIT_RESULT=$?

    # Check if the background process is still running
    if kill -0 $DEPLOYMENT_PID 2>/dev/null; then
        log "Deployment process is still running, waiting for it to complete..."
        wait $DEPLOYMENT_PID
    fi

    # Return the wait result
    exit $WAIT_RESULT
)

DEPLOYMENT_STATUS=$?

if [ $DEPLOYMENT_STATUS -eq 0 ]; then
    log_success "Bicep deployment completed successfully"
    SUCCESS=true
else
    log_error "Bicep deployment failed with status code $DEPLOYMENT_STATUS"
    SUCCESS=false
fi

# Get detailed error information if deployment failed
if [ $DEPLOYMENT_STATUS -ne 0 ]; then

    log "Getting detailed error information..."

    # Try to get the last deployment details
    ERROR_DETAILS=$(az deployment group show \
      --resource-group $RESOURCE_GROUP \
      --name $DEPLOYMENT_NAME \
      --query "properties.error" \
      -o json 2>/dev/null)

    if [ ! -z "$ERROR_DETAILS" ] && [ "$ERROR_DETAILS" != "null" ]; then
        log_error "Error details:"
        log "$ERROR_DETAILS" | tee -a "$LOG_FILE"

        # Extract error code and message for JSON output
        ERROR_CODE=$(echo "$ERROR_DETAILS" | jq -r '.code // "unknown"')
        ERROR_MESSAGE=$(echo "$ERROR_DETAILS" | jq -r '.message // "Unknown error"')

        # Output JSON for error handling in Python script
        output_json "{\"error\": \"bicep_deployment_error\", \"code\": \"$ERROR_CODE\", \"message\": \"$ERROR_MESSAGE\"}"
    else
        log_warning "Could not retrieve detailed error information."

        # Output JSON for error handling in Python script
        output_json "{\"error\": \"bicep_deployment_error\", \"code\": \"unknown\", \"message\": \"Deployment failed with status code $DEPLOYMENT_STATUS\"}"
    fi

    exit $DEPLOYMENT_STATUS
fi

end_timer "Bicep deployment"

# Clean up the temporary file
rm -f $PARAMS_FILE

log "Retrieving deployment outputs..."
start_timer "Output retrieval"

# Get the outputs from the deployment
log "Retrieving Function App information..."
FUNCTION_APP_NAME=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.functionAppName.value" \
  --output tsv)

FUNCTION_APP_URL=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.functionAppUrl.value" \
  --output tsv)

log "Retrieving Storage Account information..."
STORAGE_ACCOUNT_NAME=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.storageAccountName.value" \
  --output tsv)

UPLOADS_CONTAINER=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.uploadsContainerName.value" \
  --output tsv)

INPUT_CONTAINER=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.inputContainerName.value" \
  --output tsv)

OUTPUT_CONTAINER=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.outputContainerName.value" \
  --output tsv)

log "Retrieving Search Service information..."
SEARCH_SERVICE_NAME=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.searchServiceName.value" \
  --output tsv)

SEARCH_INDEX_NAME=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.searchIndexName.value" \
  --output tsv)

SEARCH_INDEXER_NAME=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.searchIndexerName.value" \
  --output tsv)

SEARCH_DATASOURCE_NAME=$(az deployment group show \
  --resource-group $RESOURCE_GROUP \
  --name $DEPLOYMENT_NAME \
  --query "properties.outputs.searchDatasourceName.value" \
  --output tsv)

log "Event Grid System Topic information will be populated separately..."
EVENT_GRID_SYSTEM_TOPIC_NAME=""
EVENT_GRID_SYSTEM_TOPIC_ID=""
EVENT_GRID_SUBSCRIPTION_NAME=""

# If we're deploying only the event grid system topic, get the outputs
if [[ "$TEMPLATE_FILE" == *"event_grid.bicep" ]]; then
  log "Retrieving Event Grid System Topic information from event grid deployment..."
  EVENT_GRID_SYSTEM_TOPIC_NAME=$(az deployment group show \
    --resource-group $RESOURCE_GROUP \
    --name $DEPLOYMENT_NAME \
    --query "properties.outputs.eventGridSystemTopicName.value" \
    --output tsv)

  EVENT_GRID_SYSTEM_TOPIC_ID=$(az deployment group show \
    --resource-group $RESOURCE_GROUP \
    --name $DEPLOYMENT_NAME \
    --query "properties.outputs.eventGridSystemTopicId.value" \
    --output tsv)

  EVENT_GRID_SUBSCRIPTION_NAME=$(az deployment group show \
    --resource-group $RESOURCE_GROUP \
    --name $DEPLOYMENT_NAME \
    --query "properties.outputs.eventSubscriptionName.value" \
    --output tsv)

  # Verify the event grid system topic exists
  if [ -n "$EVENT_GRID_SYSTEM_TOPIC_NAME" ]; then
    log "Verifying Event Grid System Topic exists..."
    if az eventgrid system-topic show --name "$EVENT_GRID_SYSTEM_TOPIC_NAME" --resource-group "$RESOURCE_GROUP" --query "name" -o tsv > /dev/null 2>&1; then
      log_success "Event Grid System Topic $EVENT_GRID_SYSTEM_TOPIC_NAME exists"
    else
      log_warning "Event Grid System Topic $EVENT_GRID_SYSTEM_TOPIC_NAME not found in Azure"
    fi
  else
    log_warning "Event Grid System Topic name not found in deployment outputs"
  fi

  # Verify the event subscription exists
  if [ -n "$EVENT_GRID_SYSTEM_TOPIC_NAME" ] && [ -n "$EVENT_GRID_SUBSCRIPTION_NAME" ]; then
    log "Verifying Event Subscription exists..."
    if az eventgrid system-topic event-subscription show --name "$EVENT_GRID_SUBSCRIPTION_NAME" --system-topic-name "$EVENT_GRID_SYSTEM_TOPIC_NAME" --resource-group "$RESOURCE_GROUP" --query "name" -o tsv > /dev/null 2>&1; then
      log_success "Event Subscription $EVENT_GRID_SUBSCRIPTION_NAME exists"

      # Get the destination details to verify it's pointing to the correct function
      DESTINATION_DETAILS=$(az eventgrid system-topic event-subscription show \
        --name "$EVENT_GRID_SUBSCRIPTION_NAME" \
        --system-topic-name "$EVENT_GRID_SYSTEM_TOPIC_NAME" \
        --resource-group "$RESOURCE_GROUP" \
        --query "destination.properties.resourceId" \
        -o tsv)

      log "Event Subscription destination: $DESTINATION_DETAILS"

      # Check if the destination contains EventGridTriggerBlobIndexer
      if [[ "$DESTINATION_DETAILS" == *"EventGridTriggerBlobIndexer"* ]]; then
        log_success "Event Subscription is correctly configured to trigger EventGridTriggerBlobIndexer function"
      else
        log_warning "Event Subscription may not be correctly configured. Expected EventGridTriggerBlobIndexer in destination."
      fi
    else
      log_warning "Event Subscription $EVENT_GRID_SUBSCRIPTION_NAME not found in Azure"
    fi
  else
    log_warning "Event Grid System Topic name or Event Subscription name not found in deployment outputs"
  fi
fi

end_timer "Output retrieval"

# Print the deployment information
log "Generating deployment summary..."
log "Deployment Summary:"
log "==================="
log "Project ID: $PROJECT_ID"
log "Project Name: $PROJECT_NAME"
log "Region ID: $REGION_ID"
log ""
log "Storage Account: $STORAGE_ACCOUNT_NAME"
log "Uploads Container: $UPLOADS_CONTAINER"
log "Input Container: $INPUT_CONTAINER"
log "Output Container: $OUTPUT_CONTAINER"
log ""
log "Search Service: $SEARCH_SERVICE_NAME"
log "Search Index: $SEARCH_INDEX_NAME"
log "Search Indexer: $SEARCH_INDEXER_NAME"
log "Search Datasource: $SEARCH_DATASOURCE_NAME"
log ""
log "Function App: $FUNCTION_APP_NAME"
log "Function App URL: $FUNCTION_APP_URL"
log ""
log "Event Grid System Topic: $EVENT_GRID_SYSTEM_TOPIC_NAME"
log "Event Grid Subscription: $EVENT_GRID_SUBSCRIPTION_NAME"
log ""
log_success "All resources have been deployed successfully!"

# Create a JSON output for the Python script to parse
DEPLOYMENT_JSON="{
  \"project_id\": \"$PROJECT_ID\",
  \"project_name\": \"$PROJECT_NAME\",
  \"region_id\": \"$REGION_ID\",
  \"resources\": {
    \"storage_account_name\": \"$STORAGE_ACCOUNT_NAME\",
    \"uploads_container\": \"$UPLOADS_CONTAINER\",
    \"input_container\": \"$INPUT_CONTAINER\",
    \"output_container\": \"$OUTPUT_CONTAINER\",
    \"search_service_name\": \"$SEARCH_SERVICE_NAME\",
    \"search_index_name\": \"$SEARCH_INDEX_NAME\",
    \"search_indexer_name\": \"$SEARCH_INDEXER_NAME\",
    \"search_datasource_name\": \"$SEARCH_DATASOURCE_NAME\",
    \"function_app_name\": \"$FUNCTION_APP_NAME\",
    \"function_app_url\": \"$FUNCTION_APP_URL\",
    \"event_grid_system_topic_name\": \"$EVENT_GRID_SYSTEM_TOPIC_NAME\",
    \"event_grid_subscription_name\": \"$EVENT_GRID_SUBSCRIPTION_NAME\"
  },
  \"status\": \"success\",
  \"deployment_time\": \"$(($(date +%s) - TIMER_START))s\",
  \"timestamp\": \"$(date -u +"%Y-%m-%dT%H:%M:%SZ")\"
}"

# Output the JSON for the Python script to parse
output_json "$DEPLOYMENT_JSON"

# Add a final summary of timing information
log ""
log "Deployment Timing Summary:"
log "=========================="
log "Total deployment time: $(($(date +%s) - TIMER_START))s"

# Get resource creation times from Azure
log ""
log "Resource Creation Times:"
log "======================="

# Function to get resource creation time
get_resource_time() {
    local resource_type=$1
    local resource_name=$2
    local creation_time=$(az resource show --resource-type "$resource_type" --name "$resource_name" --resource-group "$RESOURCE_GROUP" --query "createdTime" -o tsv 2>/dev/null)
    if [ ! -z "$creation_time" ]; then
        log "$resource_name: $creation_time"
        return 0
    else
        log_warning "$resource_name: Time not available"
        return 1
    fi
}

# Get creation times for key resources
log "Storage Account:"
get_resource_time "Microsoft.Storage/storageAccounts" "$STORAGE_ACCOUNT_NAME"

log "Search Service:"
get_resource_time "Microsoft.Search/searchServices" "$SEARCH_SERVICE_NAME"

log "Function App:"
get_resource_time "Microsoft.Web/sites" "$FUNCTION_APP_NAME"

log "Event Grid System Topic:"
get_resource_time "Microsoft.EventGrid/systemTopics" "$EVENT_GRID_SYSTEM_TOPIC_NAME"

# Save all logs to a final deployment summary file
SUMMARY_FILE="${LOGS_DIR}/deployment_summary_${TIMESTAMP}_${PROJECT_ID}.json"
echo "$DEPLOYMENT_JSON" > "$SUMMARY_FILE"
log "Deployment summary saved to: $SUMMARY_FILE"

log ""
log_success "Project deployment completed successfully!"

# Check if we had any errors during execution
if [ $LAST_ERROR_CODE -ne 0 ]; then
    log_warning "Errors occurred during script execution (code: $LAST_ERROR_CODE)"
    log_warning "Checking if resources were actually deployed despite errors..."

    # Check if storage account exists
    if az storage account show --name $STORAGE_ACCOUNT_NAME --resource-group $RESOURCE_GROUP --query name -o tsv > /dev/null 2>&1; then
        log_success "Storage account $STORAGE_ACCOUNT_NAME exists despite errors"
    else
        log_error "Storage account $STORAGE_ACCOUNT_NAME does not exist - deployment failed"
    fi

    # Check if search service exists
    if az search service show --name $SEARCH_SERVICE_NAME --resource-group $RESOURCE_GROUP --query name -o tsv > /dev/null 2>&1; then
        log_success "Search service $SEARCH_SERVICE_NAME exists despite errors"
    else
        log_error "Search service $SEARCH_SERVICE_NAME does not exist - deployment failed"
    fi

    # Log the last few commands executed
    log "Last 20 commands executed:"
    HISTFILE=~/.bash_history
    history | tail -20 2>/dev/null | tee -a "$LOG_FILE" || log_warning "Could not get command history"

    # Log system status
    log "System status:"
    free -h 2>/dev/null | tee -a "$LOG_FILE" || log_warning "Could not get memory status"
    df -h 2>/dev/null | tee -a "$LOG_FILE" || log_warning "Could not get disk status"

    # Check if any Azure CLI errors were logged
    log "Checking for Azure CLI errors in logs:"
    grep -i "error" "$LOG_FILE" 2>/dev/null | tail -10 | tee -a "$LOG_FILE" || log_warning "No errors found in log file"

    # If resources exist, consider the deployment successful despite errors
    if az storage account show --name $STORAGE_ACCOUNT_NAME --resource-group $RESOURCE_GROUP --query name -o tsv > /dev/null 2>&1 && \
       az search service show --name $SEARCH_SERVICE_NAME --resource-group $RESOURCE_GROUP --query name -o tsv > /dev/null 2>&1; then
        log_success "Critical resources exist - considering deployment successful despite errors"
        DEPLOYMENT_SUCCESS=true
    else
        log_error "Critical resources missing - deployment failed"
        DEPLOYMENT_SUCCESS=false
    fi
else
    log_success "Script executed without errors"
    DEPLOYMENT_SUCCESS=true
fi

# Get the current exit status
CURRENT_EXIT_STATUS=$?
log "Current exit status: $CURRENT_EXIT_STATUS"

# Always exit with success code if deployment was successful
# The Function App deployment will be handled separately by the Python script
if [ "$DEPLOYMENT_SUCCESS" = true ]; then
    log_success "Deployment successful - exiting with code 0"
    exit 0
else
    log_error "Deployment failed - exiting with code 1"
    exit 1
fi

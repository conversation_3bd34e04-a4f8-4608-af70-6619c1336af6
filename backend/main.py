"""
Main FastAPI application file that integrates all routes and middleware.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from backend.auth.auth_routes import router as auth_router
from backend.rbac.rbac_routes import router as rbac_router
from backend.rbac.user_context import router as user_context_router
from backend.cost_management import router as cost_router
from backend.support.support_routes import router as support_router
import logging

# Create the FastAPI app
app = FastAPI(title="AI Scope Backend API")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router)
app.include_router(rbac_router)
app.include_router(user_context_router)
app.include_router(cost_router)
app.include_router(support_router)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup_event():
    """Startup event handler"""
    logger.info("Starting up the application...")

@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler"""
    logger.info("Shutting down the application...")

import os
import json
import pandas as pd
import sys
import uuid
from datetime import datetime

# Ensure directories exist
def ensure_directory(dir_path):
    """Create a directory if it doesn't exist"""
    os.makedirs(dir_path, exist_ok=True)
    return dir_path

# Ensure public directory exists
def ensure_public_directory():
    """Create the public directory if it doesn't exist"""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    public_dir = os.path.join(base_dir, 'public')
    return ensure_directory(public_dir)

# Ensure uploads directory exists
def ensure_uploads_directory():
    """Create the uploads directory if it doesn't exist"""
    base_dir = os.path.dirname(os.path.abspath(__file__))
    uploads_dir = os.path.join(base_dir, 'uploads')
    return ensure_directory(uploads_dir)

def get_near_term_data(excel_path):
    """
    Read the Near_Term sheet from the specified Excel file into a pandas DataFrame.
    Skips the first row and uses the second row as the header.

    Parameters:
    -----------
    excel_path : str
        Path to the Excel file

    Returns:
    --------
    pandas.DataFrame
        DataFrame containing the Near_Term sheet data with proper column names
    """
    try:
        print(f"Attempting to read Excel file: {excel_path}")

        # Check if the file exists
        if not os.path.exists(excel_path):
            print(f"File does not exist: {excel_path}")
            return None

        # Get the file size
        file_size = os.path.getsize(excel_path)
        print(f"File size: {file_size} bytes")

        # List all sheets in the Excel file
        xls = pd.ExcelFile(excel_path)
        sheets = xls.sheet_names
        print(f"Sheets in the Excel file: {sheets}")

        # Try to find a sheet that contains 'Near_Term' (case insensitive)
        near_term_sheet = None
        for sheet in sheets:
            if 'near_term' in sheet.lower():
                near_term_sheet = sheet
                break

        if near_term_sheet:
            print(f"Found Near_Term sheet: {near_term_sheet}")
            # Read the Near_Term sheet specifically, skipping the first row and using the second row as header
            df = pd.read_excel(excel_path, sheet_name=near_term_sheet, header=1)
            print(f"Successfully read {len(df)} rows from {near_term_sheet} sheet")
            return df
        else:
            # If no Near_Term sheet is found, try to read the first sheet
            print(f"No Near_Term sheet found, trying to read the first sheet: {sheets[0]}")
            df = pd.read_excel(excel_path, sheet_name=0, header=1)
            print(f"Successfully read {len(df)} rows from the first sheet")
            return df
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        import traceback
        traceback.print_exc()
        return None

def excel_to_json(df):
    """
    Convert DataFrame to JSON format for the web application.

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame containing the data

    Returns:
    --------
    dict
        Dictionary containing the formatted data for the web application
    """
    if df is None or df.empty:
        print("No data available from DataFrame")
        return None

    print("\nColumn names in the DataFrame:")
    print(df.columns.tolist())

    # Define possible column name variations
    dimension_cols = ['Dimension', 'dimension', 'Category', 'category']
    criteria_cols = ['Criteria', 'criteria', 'Subcategory', 'subcategory']
    initiative_cols = ['3 months', '3 Months', 'Initiative', 'initiative', 'Action', 'action']
    reasoning_cols = ['reasoning ', 'reasoning', 'Reasoning', 'Description', 'description']
    conf_score_cols = ['conf score', 'Conf Score', 'Confidence', 'confidence']
    business_value_cols = ['Business Value', 'business value', 'Value', 'value']
    feasibility_cols = ['Feasability', 'feasibility', 'Feasibility', 'feasability']
    effort_cols = ['Effort', 'effort']

    # Find the actual column names in the DataFrame
    dimension_col = next((col for col in dimension_cols if col in df.columns), None)
    criteria_col = next((col for col in criteria_cols if col in df.columns), None)
    initiative_col = next((col for col in initiative_cols if col in df.columns), None)
    reasoning_col = next((col for col in reasoning_cols if col in df.columns), None)
    conf_score_col = next((col for col in conf_score_cols if col in df.columns), None)
    business_value_col = next((col for col in business_value_cols if col in df.columns), None)
    feasibility_col = next((col for col in feasibility_cols if col in df.columns), None)
    effort_col = next((col for col in effort_cols if col in df.columns), None)

    print("\nMatched columns:")
    print(f"Dimension: {dimension_col}")
    print(f"Criteria: {criteria_col}")
    print(f"Initiative: {initiative_col}")
    print(f"Reasoning: {reasoning_col}")
    print(f"Conf Score: {conf_score_col}")
    print(f"Business Value: {business_value_col}")
    print(f"Feasibility: {feasibility_col}")
    print(f"Effort: {effort_col}")

    # Format the data for the web application
    initiatives = []
    for _, row in df.iterrows():
        # Skip completely empty rows
        if row.isna().all():
            continue

        # Create an initiative object with the data from this row
        initiative = {
            'dimension': str(row.get(dimension_col, '')) if dimension_col else '',
            'criteria': str(row.get(criteria_col, '')) if criteria_col else '',
            'initiative': str(row.get(initiative_col, '')) if initiative_col else '',
            'reasoning': str(row.get(reasoning_col, '')) if reasoning_col else '',
            'confScore': float(row.get(conf_score_col, 0)) if conf_score_col and not pd.isna(row.get(conf_score_col)) else 0,
            'businessValue': float(row.get(business_value_col, 0)) if business_value_col and not pd.isna(row.get(business_value_col)) else 0,
            'feasibility': float(row.get(feasibility_col, 0)) if feasibility_col and not pd.isna(row.get(feasibility_col)) else 0,
            'effort': float(row.get(effort_col, 0)) if effort_col and not pd.isna(row.get(effort_col)) else 0
        }

        # Skip rows that don't have the essential data
        if not initiative['initiative']:
            continue

        initiatives.append(initiative)

    return {'initiatives': initiatives}

def process_uploaded_file(file_path, file_id=None):
    """Process an uploaded Excel file and save the results"""
    try:
        # Use the provided file ID or generate a new one
        if not file_id:
            file_id = str(uuid.uuid4())
        print(f"Processing file with ID: {file_id}")
        timestamp = datetime.now().isoformat()

        # Get the Near_Term data
        near_term_df = get_near_term_data(file_path)

        if near_term_df is not None:
            # Convert to JSON
            json_data = excel_to_json(near_term_df)

            if json_data:
                # Create file info
                file_info = {
                    "id": file_id,
                    "name": os.path.basename(file_path),
                    "timestamp": timestamp,
                    "path": f"/api/priorityplot/files/{file_id}"
                }

                # Save the JSON data
                public_dir = ensure_public_directory()
                uploads_dir = ensure_uploads_directory()

                # Save to uploads directory with the file ID
                json_path = os.path.join(uploads_dir, f"{file_id}.json")
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump({
                        "file_info": file_info,
                        "data": json_data
                    }, f, indent=2)

                # Also save to public directory for easy access
                public_json_path = os.path.join(public_dir, 'near_term_data.json')
                with open(public_json_path, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, indent=2)

                print(f"\nJSON data saved to: {json_path} and {public_json_path}")
                return file_info, json_data

        return None, None
    except Exception as e:
        print(f"Error processing file: {e}")
        return None, None

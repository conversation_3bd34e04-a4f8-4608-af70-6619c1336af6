import os
import json
import logging
from quart import Blueprint, request, jsonify, send_from_directory, current_app
from werkzeug.utils import secure_filename
from backend.priorityplot import data_utils

# Create a Blueprint for PriorityPlot routes
priorityplot_bp = Blueprint("priorityplot", __name__, url_prefix="/api/priorityplot")

# Allowed file extensions
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@priorityplot_bp.route('/process-excel', methods=['POST'])
async def process_excel():
    """Process an uploaded Excel file and return the processed data"""
    # Check if a file was uploaded
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = await request.files['file']

    # Check if the file is empty
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    # Check if the file is allowed
    if not allowed_file(file.filename):
        return jsonify({'error': 'File type not allowed'}), 400

    # Get the file ID from the request
    form = await request.form
    file_id = form.get('fileId')
    if not file_id:
        return jsonify({'error': 'No file ID provided'}), 400

    try:
        # Ensure the uploads directory exists
        uploads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
        os.makedirs(uploads_dir, exist_ok=True)

        # Save the file
        filename = secure_filename(file.filename)
        file_path = os.path.join(uploads_dir, filename)
        await file.save(file_path)

        # Process the file with the provided file ID
        file_info, data = data_utils.process_uploaded_file(file_path, file_id)

        if file_info and data:
            # Return both the file info and the data
            return jsonify({
                'fileInfo': file_info,
                'data': data
            }), 200
        else:
            return jsonify({'error': 'Failed to process file'}), 500

    except Exception as e:
        logging.error(f"Error processing file: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@priorityplot_bp.route('/files', methods=['GET'])
async def get_files():
    """Get a list of all processed files"""
    try:
        files = []
        uploads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')

        # List all JSON files in the uploads directory
        for filename in os.listdir(uploads_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(uploads_dir, filename)
                with open(file_path, 'r') as f:
                    file_data = json.load(f)
                    if 'file_info' in file_data:
                        files.append(file_data['file_info'])

        return jsonify(files), 200

    except Exception as e:
        logging.error(f"Error getting files: {e}")
        return jsonify({'error': str(e)}), 500

@priorityplot_bp.route('/files/<file_id>', methods=['GET', 'DELETE'])
async def file_operations(file_id):
    """Get or delete a specific file"""
    try:
        uploads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
        file_path = os.path.join(uploads_dir, f"{file_id}.json")

        if request.method == 'GET':
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    file_data = json.load(f)
                    return jsonify(file_data['data']), 200
            else:
                return jsonify({'error': 'File not found'}), 404

        elif request.method == 'DELETE':
            if os.path.exists(file_path):
                os.remove(file_path)
                return jsonify({'message': 'File deleted successfully'}), 200
            else:
                return jsonify({'error': 'File not found'}), 404

    except Exception as e:
        logging.error(f"Error in file operations: {e}")
        return jsonify({'error': str(e)}), 500

@priorityplot_bp.route('/static/<path:path>')
async def serve_static(path):
    """Serve static files from the public directory"""
    public_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'public')
    return await send_from_directory(public_dir, path)

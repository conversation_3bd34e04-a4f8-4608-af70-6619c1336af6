# backend/deployments/routes.py
"""
API routes for managing Azure resource deployments.
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from quart import Blueprint, jsonify, request, current_app, make_response

from backend.auth.auth_utils import get_authenticated_user_details
from backend.deployments.service import DeploymentService
from backend.deployments.models import DeploymentStatus, DeploymentRequest, DeploymentResponse

# Create Blueprint
deployment_bp = Blueprint("deployments", __name__)

# Initialize logger
logger = logging.getLogger(__name__)

# Initialize deployment service
deployment_service = None


@deployment_bp.before_app_request
async def init_deployment_service():
    """Initialize the deployment service."""
    global deployment_service
    if not deployment_service and hasattr(current_app, 'cosmos_conversation_client'):
        deployment_service = DeploymentService(current_app.cosmos_conversation_client)
        logger.info("Deployment service initialized")


@deployment_bp.route("/api/system/deployments", methods=["POST"])
async def create_deployment():
    """
    Create a new deployment.

    This endpoint initiates the deployment of Azure resources for a project.
    """
    # Check authentication
    authenticated_user = get_authenticated_user_details(request.headers)
    user_id = authenticated_user.get("user_principal_id")

    if not user_id:
        return jsonify({"error": {"code": "Unauthorized", "message": "Authentication required"}}), 401

    # Check authorization (only admins can create deployments)
    user_role = authenticated_user.get("role", "")
    if user_role not in ["SUPER_ADMIN", "REGIONAL_ADMIN"]:
        return jsonify({
            "error": {
                "code": "Forbidden",
                "message": "Only administrators can create deployments"
            }
        }), 403

    # Validate request
    if not request.is_json:
        return jsonify({
            "error": {
                "code": "InvalidRequest",
                "message": "Request must be JSON"
            }
        }), 415

    try:
        request_data = await request.get_json()

        # Validate required fields
        required_fields = ["project_id", "project_name", "region_id"]
        missing_fields = [field for field in required_fields if field not in request_data]

        if missing_fields:
            return jsonify({
                "error": {
                    "code": "ValidationError",
                    "message": f"Missing required fields: {', '.join(missing_fields)}"
                }
            }), 400

        # Create deployment request object
        deployment_request = DeploymentRequest(
            project_id=request_data["project_id"],
            project_name=request_data["project_name"],
            region_id=request_data["region_id"],
            resource_group=request_data.get("resource_group", "rg-internal-ai"),
            location=request_data.get("location", "westeurope"),
            deployment_type=request_data.get("deployment_type", "full"),
            resources=request_data.get("resources", {}),
            template_overrides=request_data.get("template_overrides", {}),
            callback_url=request_data.get("callback_url", ""),
            priority=request_data.get("priority", 3),
            timeout_minutes=request_data.get("timeout_minutes", 60),
            tags=request_data.get("tags", {})
        )

        # Create deployment
        if not deployment_service:
            return jsonify({
                "error": {
                    "code": "ServiceUnavailable",
                    "message": "Deployment service is not initialized"
                }
            }), 503

        deployment = await deployment_service.create_deployment(
            deployment_request=deployment_request,
            user_id=user_id
        )

        # Return response
        return jsonify(deployment.to_dict()), 202

    except Exception as e:
        logger.exception(f"Error creating deployment: {str(e)}")
        return jsonify({
            "error": {
                "code": "InternalServerError",
                "message": f"Error creating deployment: {str(e)}",
                "request_id": str(uuid.uuid4()),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }), 500


@deployment_bp.route("/api/system/deployments", methods=["GET"])
async def list_deployments():
    """
    List deployments.

    This endpoint returns a list of deployments with filtering options.
    """
    # Check authentication
    authenticated_user = get_authenticated_user_details(request.headers)
    user_id = authenticated_user.get("user_principal_id")

    if not user_id:
        return jsonify({"error": {"code": "Unauthorized", "message": "Authentication required"}}), 401

    # Get query parameters
    project_id = request.args.get("project_id")
    status = request.args.get("status")
    from_date = request.args.get("from_date")
    to_date = request.args.get("to_date")
    limit = int(request.args.get("limit", 20))
    offset = int(request.args.get("offset", 0))

    try:
        if not deployment_service:
            return jsonify({
                "error": {
                    "code": "ServiceUnavailable",
                    "message": "Deployment service is not initialized"
                }
            }), 503

        # Get deployments
        deployments, total = await deployment_service.list_deployments(
            user_id=user_id,
            project_id=project_id,
            status=status,
            from_date=from_date,
            to_date=to_date,
            limit=limit,
            offset=offset
        )

        # Return response
        return jsonify({
            "items": [d.to_dict() for d in deployments],
            "total": total,
            "limit": limit,
            "offset": offset
        }), 200

    except Exception as e:
        logger.exception(f"Error listing deployments: {str(e)}")
        return jsonify({
            "error": {
                "code": "InternalServerError",
                "message": f"Error listing deployments: {str(e)}",
                "request_id": str(uuid.uuid4()),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }), 500


@deployment_bp.route("/api/system/deployments/<deployment_id>", methods=["GET"])
async def get_deployment(deployment_id):
    """
    Get deployment details.

    This endpoint returns detailed information about a specific deployment.
    """
    # Check authentication
    authenticated_user = get_authenticated_user_details(request.headers)
    user_id = authenticated_user.get("user_principal_id")

    if not user_id:
        return jsonify({"error": {"code": "Unauthorized", "message": "Authentication required"}}), 401

    try:
        if not deployment_service:
            return jsonify({
                "error": {
                    "code": "ServiceUnavailable",
                    "message": "Deployment service is not initialized"
                }
            }), 503

        # Get deployment
        deployment = await deployment_service.get_deployment(
            deployment_id=deployment_id,
            user_id=user_id
        )

        if not deployment:
            return jsonify({
                "error": {
                    "code": "NotFound",
                    "message": f"Deployment {deployment_id} not found"
                }
            }), 404

        # Return response
        return jsonify(deployment.to_dict()), 200

    except Exception as e:
        logger.exception(f"Error getting deployment: {str(e)}")
        return jsonify({
            "error": {
                "code": "InternalServerError",
                "message": f"Error getting deployment: {str(e)}",
                "request_id": str(uuid.uuid4()),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }), 500

# backend/deployments/models.py
"""
Data models for the deployment API.
"""

import uuid
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Any, List, Optional


class DeploymentStatus(str, Enum):
    """Deployment status enum."""
    QUEUED = "queued"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ResourceStatus(str, Enum):
    """Resource status enum."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class DeploymentRequest:
    """Deployment request model."""
    
    def __init__(
        self,
        project_id: str,
        project_name: str,
        region_id: str,
        resource_group: str = "rg-internal-ai",
        location: str = "westeurope",
        deployment_type: str = "full",
        resources: Dict[str, bool] = None,
        template_overrides: Dict[str, Any] = None,
        callback_url: str = "",
        priority: int = 3,
        timeout_minutes: int = 60,
        tags: Dict[str, str] = None
    ):
        self.project_id = project_id
        self.project_name = project_name
        self.region_id = region_id
        self.resource_group = resource_group
        self.location = location
        self.deployment_type = deployment_type
        self.resources = resources or {}
        self.template_overrides = template_overrides or {}
        self.callback_url = callback_url
        self.priority = priority
        self.timeout_minutes = timeout_minutes
        self.tags = tags or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "project_id": self.project_id,
            "project_name": self.project_name,
            "region_id": self.region_id,
            "resource_group": self.resource_group,
            "location": self.location,
            "deployment_type": self.deployment_type,
            "resources": self.resources,
            "template_overrides": self.template_overrides,
            "callback_url": self.callback_url,
            "priority": self.priority,
            "timeout_minutes": self.timeout_minutes,
            "tags": self.tags
        }


class ResourceDetail:
    """Resource detail model."""
    
    def __init__(
        self,
        status: ResourceStatus = ResourceStatus.PENDING,
        started_at: Optional[str] = None,
        completed_at: Optional[str] = None,
        duration_seconds: Optional[int] = None,
        resource_id: Optional[str] = None,
        resource_name: Optional[str] = None,
        details: Dict[str, Any] = None,
        error: Dict[str, Any] = None
    ):
        self.status = status
        self.started_at = started_at
        self.completed_at = completed_at
        self.duration_seconds = duration_seconds
        self.resource_id = resource_id
        self.resource_name = resource_name
        self.details = details or {}
        self.error = error
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = {
            "status": self.status,
            "started_at": self.started_at,
            "completed_at": self.completed_at,
            "duration_seconds": self.duration_seconds,
            "resource_id": self.resource_id,
            "resource_name": self.resource_name,
            "details": self.details
        }
        
        if self.error:
            result["error"] = self.error
            
        return result


class LogEntry:
    """Log entry model."""
    
    def __init__(
        self,
        timestamp: str,
        level: str,
        message: str,
        resource: Optional[str] = None
    ):
        self.timestamp = timestamp
        self.level = level
        self.message = message
        self.resource = resource
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "timestamp": self.timestamp,
            "level": self.level,
            "message": self.message,
            "resource": self.resource
        }


class DeploymentResponse:
    """Deployment response model."""
    
    def __init__(
        self,
        deployment_id: str,
        project_id: str,
        project_name: str,
        status: DeploymentStatus = DeploymentStatus.QUEUED,
        progress_percentage: float = 0.0,
        created_at: Optional[str] = None,
        started_at: Optional[str] = None,
        updated_at: Optional[str] = None,
        completed_at: Optional[str] = None,
        duration_seconds: Optional[int] = None,
        resources: Dict[str, ResourceDetail] = None,
        logs: List[LogEntry] = None,
        queue_position: Optional[int] = None,
        estimated_completion_time: Optional[str] = None
    ):
        self.deployment_id = deployment_id
        self.project_id = project_id
        self.project_name = project_name
        self.status = status
        self.progress_percentage = progress_percentage
        self.created_at = created_at or datetime.now(timezone.utc).isoformat()
        self.started_at = started_at
        self.updated_at = updated_at or self.created_at
        self.completed_at = completed_at
        self.duration_seconds = duration_seconds
        self.resources = resources or {
            "storage": ResourceDetail(),
            "search": ResourceDetail(),
            "function_app": ResourceDetail(),
            "event_grid": ResourceDetail()
        }
        self.logs = logs or []
        self.queue_position = queue_position
        self.estimated_completion_time = estimated_completion_time
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        result = {
            "deployment_id": self.deployment_id,
            "project_id": self.project_id,
            "project_name": self.project_name,
            "status": self.status,
            "progress_percentage": self.progress_percentage,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "resources": {k: v.to_dict() for k, v in self.resources.items()},
            "logs": [log.to_dict() for log in self.logs],
            "_links": {
                "self": f"/api/system/deployments/{self.deployment_id}",
                "project": f"/api/projects/{self.project_id}"
            }
        }
        
        if self.started_at:
            result["started_at"] = self.started_at
            
        if self.completed_at:
            result["completed_at"] = self.completed_at
            
        if self.duration_seconds is not None:
            result["duration_seconds"] = self.duration_seconds
            
        if self.queue_position is not None:
            result["queue_position"] = self.queue_position
            
        if self.estimated_completion_time:
            result["estimated_completion_time"] = self.estimated_completion_time
            
        # Add action links based on status
        if self.status == DeploymentStatus.QUEUED or self.status == DeploymentStatus.IN_PROGRESS:
            result["_links"]["cancel"] = f"/api/system/deployments/{self.deployment_id}"
            
        if self.status == DeploymentStatus.FAILED:
            result["_links"]["retry"] = f"/api/system/deployments/{self.deployment_id}"
            
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeploymentResponse':
        """Create from dictionary."""
        resources = {}
        for k, v in data.get("resources", {}).items():
            resources[k] = ResourceDetail(
                status=v.get("status", ResourceStatus.PENDING),
                started_at=v.get("started_at"),
                completed_at=v.get("completed_at"),
                duration_seconds=v.get("duration_seconds"),
                resource_id=v.get("resource_id"),
                resource_name=v.get("resource_name"),
                details=v.get("details", {}),
                error=v.get("error")
            )
            
        logs = []
        for log_data in data.get("logs", []):
            logs.append(LogEntry(
                timestamp=log_data.get("timestamp"),
                level=log_data.get("level"),
                message=log_data.get("message"),
                resource=log_data.get("resource")
            ))
            
        return cls(
            deployment_id=data.get("deployment_id"),
            project_id=data.get("project_id"),
            project_name=data.get("project_name"),
            status=data.get("status", DeploymentStatus.QUEUED),
            progress_percentage=data.get("progress_percentage", 0.0),
            created_at=data.get("created_at"),
            started_at=data.get("started_at"),
            updated_at=data.get("updated_at"),
            completed_at=data.get("completed_at"),
            duration_seconds=data.get("duration_seconds"),
            resources=resources,
            logs=logs,
            queue_position=data.get("queue_position"),
            estimated_completion_time=data.get("estimated_completion_time")
        )

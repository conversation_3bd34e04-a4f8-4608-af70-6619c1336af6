import logging
from typing import Optional

from backend.services.project_service import ProjectDataService
from backend.deployments.models import DeploymentStatus, ResourceStatus

logger = logging.getLogger(__name__)

project_service = ProjectDataService()

async def sync_deployment_with_project(
    deployment_id: str,
    project_id: str,
    deployment_service,
    api_url: str = "http://localhost:50505",
) -> bool:
    """Synchronize deployment data with the project deployment status."""
    try:
        deployment = await deployment_service.get_deployment(
            deployment_id=deployment_id, user_id="system"
        )
        if not deployment:
            logger.error("Deployment %s not found", deployment_id)
            return False

        status = deployment.status.value
        message = f"Deployment {status}"
        details = {"completion_percentage": deployment.progress_percentage}

        resources = deployment.resources
        if resources:
            details["storage_complete"] = (
                resources.get("storage").status == ResourceStatus.COMPLETED
            )
            details["search_complete"] = (
                resources.get("search").status == ResourceStatus.COMPLETED
            )
            details["function_complete"] = (
                resources.get("function_app").status == ResourceStatus.COMPLETED
            )
            details["overall_complete"] = (
                deployment.status == DeploymentStatus.COMPLETED
            )

        error: Optional[str] = None
        if deployment.logs:
            for log in deployment.logs:
                if log.level == "error":
                    error = log.message
                    break

        return await project_service.update_deployment_status(
            project_id, status, message, details, error
        )
    except Exception as exc:  # pragma: no cover - network
        logger.error(
            "Error syncing deployment %s with project %s: %s", deployment_id, project_id, exc
        )
        return False

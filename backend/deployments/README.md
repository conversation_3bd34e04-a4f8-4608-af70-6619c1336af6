# Deployment API

This module provides a dedicated backend API for Azure resource deployment.

## Overview

The Deployment API allows you to:

1. Create new deployments for projects
2. Monitor deployment status
3. List all deployments
4. Get detailed information about a specific deployment

## API Endpoints

### Create a New Deployment

```
POST /api/system/deployments
```

**Request Body:**

```json
{
  "project_id": "string",                  // Required: ID of the project to deploy resources for
  "project_name": "string",                // Required: Name of the project
  "region_id": "string",                   // Required: Azure region ID for deployment
  "resource_group": "string",              // Optional: Azure resource group (default: "rg-internal-ai")
  "location": "string",                    // Optional: Azure region (default: "westeurope")
  "deployment_type": "string",             // Optional: "full" or "partial" (default: "full")
  "resources": {                           // Optional: Specific resources to deploy (for partial deployments)
    "storage": true,
    "search": true,
    "function_app": true,
    "event_grid": true
  },
  "template_overrides": {                  // Optional: Override default template parameters
    "storageSkuName": "Standard_LRS",
    "searchSkuName": "basic"
  },
  "priority": "integer",                   // Optional: Deployment priority (1-5, default: 3)
  "timeout_minutes": "integer"             // Optional: Deployment timeout in minutes (default: 60)
}
```

**Response:**

```json
{
  "deployment_id": "string",               // Unique ID for the deployment
  "project_id": "string",                  // Project ID
  "status": "queued",                      // Initial status
  "created_at": "string",                  // ISO 8601 timestamp
  "estimated_completion_time": "string",   // Estimated completion time
  "queue_position": "integer",             // Position in the deployment queue
  "resources": {                           // Resources to be deployed
    "storage": {
      "status": "pending"
    },
    "search": {
      "status": "pending"
    },
    "function_app": {
      "status": "pending"
    },
    "event_grid": {
      "status": "pending"
    }
  },
  "_links": {                              // HATEOAS links
    "self": "/api/system/deployments/deployment-123",
    "status": "/api/system/deployments/deployment-123",
    "cancel": "/api/system/deployments/deployment-123"
  }
}
```

### Get Deployment Details

```
GET /api/system/deployments/{deployment_id}
```

**Response:**

```json
{
  "deployment_id": "string",               // Unique ID for the deployment
  "project_id": "string",                  // Project ID
  "project_name": "string",                // Project name
  "status": "string",                      // Overall status: "queued", "in_progress", "completed", "failed", "cancelled"
  "progress_percentage": "number",         // Overall completion percentage (0-100)
  "created_at": "string",                  // ISO 8601 timestamp
  "started_at": "string",                  // When deployment started
  "updated_at": "string",                  // Last status update
  "completed_at": "string",                // When deployment completed (if applicable)
  "duration_seconds": "number",            // Total duration in seconds
  "resources": {                           // Detailed resource status
    "storage": {
      "status": "string",                  // "pending", "in_progress", "completed", "failed"
      "started_at": "string",              // When this resource deployment started
      "completed_at": "string",            // When this resource deployment completed
      "duration_seconds": "number",        // Duration for this resource
      "resource_id": "string",             // Azure resource ID (if created)
      "resource_name": "string",           // Azure resource name
      "details": {                         // Resource-specific details
        "storage_account_name": "string",
        "containers": ["uploads", "input", "output"]
      }
    },
    "search": { /* Similar structure */ },
    "function_app": { /* Similar structure */ },
    "event_grid": { /* Similar structure */ }
  },
  "logs": [                                // Deployment log entries (most recent first)
    {
      "timestamp": "string",               // ISO 8601 timestamp
      "level": "string",                   // "info", "warning", "error"
      "message": "string",                 // Log message
      "resource": "string"                 // Related resource (if applicable)
    }
  ],
  "_links": {                              // HATEOAS links
    "self": "/api/system/deployments/deployment-123",
    "project": "/api/projects/project-456",
    "cancel": "/api/system/deployments/deployment-123",
    "retry": "/api/system/deployments/deployment-123"
  }
}
```

### List Deployments

```
GET /api/system/deployments
```

**Query Parameters:**

- `project_id` - Filter by project ID
- `status` - Filter by status
- `from_date` - Filter by creation date (ISO 8601)
- `to_date` - Filter by creation date (ISO 8601)
- `limit` - Maximum number of results to return (default: 20)
- `offset` - Offset for pagination (default: 0)

**Response:**

```json
{
  "items": [
    {
      "deployment_id": "string",
      "project_id": "string",
      "project_name": "string",
      "status": "string",
      "progress_percentage": "number",
      "created_at": "string",
      "updated_at": "string",
      "_links": {
        "self": "/api/system/deployments/deployment-123",
        "project": "/api/projects/project-456"
      }
    }
  ],
  "total": "integer",
  "limit": "integer",
  "offset": "integer"
}
```

## Testing

You can test the API using the provided `test_deployment_api.py` script:

```bash
# Create a new deployment
./test_deployment_api.py --action create --project-id my-project-123 --project-name "My Project" --region-id westeurope-123

# Get a deployment
./test_deployment_api.py --action get --deployment-id deployment-123

# List deployments
./test_deployment_api.py --action list

# List deployments for a specific project
./test_deployment_api.py --action list --project-id my-project-123
```

## Implementation Details

The Deployment API is implemented using:

1. **Quart Blueprint** - For handling HTTP requests
2. **CosmosDB** - For storing deployment data
3. **Background Workers** - For executing deployments asynchronously
4. **Status Synchronization** - For keeping project deployment status in sync

## Authentication and Authorization

The API requires authentication and authorization:

- Only administrators (SUPER_ADMIN, REGIONAL_ADMIN) can create deployments
- Users can only view deployments for projects they have access to
- System processes can use Azure CLI credentials for deployment operations

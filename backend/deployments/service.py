# backend/deployments/service.py
"""
Service for managing deployments.
"""

import asyncio
import json
import logging
import os
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple

from backend.deployments.models import (
    DeploymentStatus,
    ResourceStatus,
    DeploymentRequest,
    DeploymentResponse,
    ResourceDetail,
    LogEntry
)
from backend.deployments.status_sync import sync_deployment_with_project

# Import the deployment function
from deploy_project_resources import deploy_project_resources

# Initialize logger
logger = logging.getLogger(__name__)


class DeploymentService:
    """Service for managing deployments."""

    def __init__(self, cosmos_client):
        """Initialize the deployment service."""
        self.cosmos_client = cosmos_client
        self.active_deployments = {}  # In-memory cache of active deployments
        self.deployment_queue = asyncio.Queue()  # Queue for pending deployments
        self.worker_task = None  # Background worker task

        # Start the worker
        self._start_worker()

    def _start_worker(self):
        """Start the background worker."""
        if self.worker_task is None or self.worker_task.done():
            self.worker_task = asyncio.create_task(self._process_deployment_queue())
            logger.info("Deployment worker started")

    async def _process_deployment_queue(self):
        """Process the deployment queue."""
        logger.info("Deployment queue processor started")

        while True:
            try:
                # Get the next deployment from the queue
                deployment_id = await self.deployment_queue.get()

                # Get the deployment from the database
                deployment_data = await self._get_deployment_from_db(deployment_id)

                if not deployment_data:
                    logger.warning(f"Deployment {deployment_id} not found in database")
                    self.deployment_queue.task_done()
                    continue

                # Create deployment response object
                deployment = DeploymentResponse.from_dict(deployment_data)

                # Skip if not in queued status
                if deployment.status != DeploymentStatus.QUEUED:
                    logger.warning(f"Deployment {deployment_id} is not in queued status: {deployment.status}")
                    self.deployment_queue.task_done()
                    continue

                # Update status to in_progress
                deployment.status = DeploymentStatus.IN_PROGRESS
                deployment.started_at = datetime.now(timezone.utc).isoformat()
                deployment.updated_at = deployment.started_at

                # Add log entry
                deployment.logs.append(LogEntry(
                    timestamp=deployment.started_at,
                    level="info",
                    message=f"Starting deployment for project {deployment.project_name}"
                ))

                # Update storage resource status
                deployment.resources["storage"].status = ResourceStatus.IN_PROGRESS
                deployment.resources["storage"].started_at = deployment.started_at

                # Update the deployment in the database
                await self._update_deployment_in_db(deployment)

                # Add to active deployments
                self.active_deployments[deployment_id] = deployment

                # Start the deployment in a separate task
                asyncio.create_task(self._execute_deployment(deployment))

                # Mark the task as done in the queue
                self.deployment_queue.task_done()

            except Exception as e:
                logger.exception(f"Error processing deployment queue: {str(e)}")
                await asyncio.sleep(5)  # Wait before retrying

    async def _execute_deployment(self, deployment: DeploymentResponse):
        """Execute the deployment."""
        try:
            logger.info(f"Executing deployment {deployment.deployment_id} for project {deployment.project_id}")

            # Get the base URL for API callbacks
            api_url = f"http://localhost:{os.environ.get('API_PORT', '50505')}"  # Use environment variable or default

            # Execute the deployment
            await deploy_project_resources(
                project_id=deployment.project_id,
                project_name=deployment.project_name,
                region_id=deployment.project_id,  # Using project_id as region_id for now
                api_url=api_url,
                resource_group="rg-internal-ai",
                location="westeurope"
            )

            # Update the deployment status
            deployment.status = DeploymentStatus.COMPLETED
            deployment.completed_at = datetime.now(timezone.utc).isoformat()
            deployment.updated_at = deployment.completed_at
            deployment.duration_seconds = int((datetime.fromisoformat(deployment.completed_at) -
                                              datetime.fromisoformat(deployment.started_at)).total_seconds())
            deployment.progress_percentage = 100.0

            # Update all resources to completed
            for resource_key, resource in deployment.resources.items():
                if resource.status != ResourceStatus.COMPLETED:
                    resource.status = ResourceStatus.COMPLETED
                    resource.completed_at = deployment.completed_at
                    if resource.started_at:
                        resource.duration_seconds = int((datetime.fromisoformat(deployment.completed_at) -
                                                        datetime.fromisoformat(resource.started_at)).total_seconds())

            # Add log entry
            deployment.logs.append(LogEntry(
                timestamp=deployment.completed_at,
                level="info",
                message=f"Deployment completed successfully for project {deployment.project_name}"
            ))

            # Update the deployment in the database
            await self._update_deployment_in_db(deployment)

            # Sync with project deployment status
            await sync_deployment_with_project(
                deployment_id=deployment.deployment_id,
                project_id=deployment.project_id,
                deployment_service=self,
                api_url="http://localhost:50505"  # Default for local development
            )

            # Remove from active deployments
            if deployment.deployment_id in self.active_deployments:
                del self.active_deployments[deployment.deployment_id]

            logger.info(f"Deployment {deployment.deployment_id} completed successfully")

        except Exception as e:
            logger.exception(f"Error executing deployment {deployment.deployment_id}: {str(e)}")

            # Update the deployment status
            deployment.status = DeploymentStatus.FAILED
            deployment.completed_at = datetime.now(timezone.utc).isoformat()
            deployment.updated_at = deployment.completed_at
            deployment.duration_seconds = int((datetime.fromisoformat(deployment.completed_at) -
                                              datetime.fromisoformat(deployment.started_at)).total_seconds())

            # Add log entry
            deployment.logs.append(LogEntry(
                timestamp=deployment.completed_at,
                level="error",
                message=f"Deployment failed: {str(e)}"
            ))

            # Update the deployment in the database
            await self._update_deployment_in_db(deployment)

            # Sync with project deployment status
            await sync_deployment_with_project(
                deployment_id=deployment.deployment_id,
                project_id=deployment.project_id,
                deployment_service=self,
                api_url="http://localhost:50505"  # Default for local development
            )

            # Remove from active deployments
            if deployment.deployment_id in self.active_deployments:
                del self.active_deployments[deployment.deployment_id]

    async def _get_deployment_from_db(self, deployment_id: str) -> Optional[Dict[str, Any]]:
        """Get deployment from database."""
        try:
            # Get the deployment from the deployments container
            deployment_data = await self.cosmos_client.get_deployment(deployment_id)
            return deployment_data
        except Exception as e:
            logger.exception(f"Error getting deployment {deployment_id} from database: {str(e)}")
            return None

    async def _update_deployment_in_db(self, deployment: DeploymentResponse) -> bool:
        """Update deployment in database."""
        try:
            # Convert deployment to dictionary
            deployment_dict = deployment.to_dict()

            # Update the deployment in the deployments container
            await self.cosmos_client.update_deployment(deployment.deployment_id, deployment_dict)
            return True
        except Exception as e:
            logger.exception(f"Error updating deployment {deployment.deployment_id} in database: {str(e)}")
            return False

    async def create_deployment(
        self,
        deployment_request: DeploymentRequest,
        user_id: str
    ) -> DeploymentResponse:
        """Create a new deployment."""
        # Generate deployment ID
        deployment_id = str(uuid.uuid4())

        # Create timestamp
        timestamp = datetime.now(timezone.utc).isoformat()

        # Create deployment response
        deployment = DeploymentResponse(
            deployment_id=deployment_id,
            project_id=deployment_request.project_id,
            project_name=deployment_request.project_name,
            status=DeploymentStatus.QUEUED,
            created_at=timestamp,
            updated_at=timestamp,
            queue_position=await self.deployment_queue.qsize() + 1,
            estimated_completion_time=(datetime.now(timezone.utc) + timedelta(minutes=15)).isoformat()
        )

        # Add initial log entry
        deployment.logs.append(LogEntry(
            timestamp=timestamp,
            level="info",
            message=f"Deployment queued for project {deployment_request.project_name}"
        ))

        # Store in database
        try:
            # Convert deployment to dictionary
            deployment_dict = deployment.to_dict()

            # Add user_id for authorization
            deployment_dict["user_id"] = user_id

            # Add deployment request data
            deployment_dict["request"] = deployment_request.to_dict()

            # Create the deployment in the deployments container
            await self.cosmos_client.create_deployment(deployment_dict)

            # Add to queue
            await self.deployment_queue.put(deployment_id)

            return deployment

        except Exception as e:
            logger.exception(f"Error creating deployment: {str(e)}")
            raise

    async def get_deployment(
        self,
        deployment_id: str,
        user_id: str
    ) -> Optional[DeploymentResponse]:
        """Get a deployment."""
        # Check if deployment is in active deployments
        if deployment_id in self.active_deployments:
            return self.active_deployments[deployment_id]

        # Get from database
        deployment_data = await self._get_deployment_from_db(deployment_id)

        if not deployment_data:
            return None

        # Check authorization
        if deployment_data.get("user_id") != user_id:
            # TODO: Check if user has access to the project
            return None

        # Create deployment response
        return DeploymentResponse.from_dict(deployment_data)

    async def list_deployments(
        self,
        user_id: str,
        project_id: Optional[str] = None,
        status: Optional[str] = None,
        from_date: Optional[str] = None,
        to_date: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> Tuple[List[DeploymentResponse], int]:
        """List deployments."""
        # Get deployments from database
        try:
            # TODO: Implement filtering in CosmosDB query
            deployments_data = await self.cosmos_client.list_deployments(user_id)

            # Filter deployments
            filtered_deployments = []
            for deployment_data in deployments_data:
                # Filter by project_id
                if project_id and deployment_data.get("project_id") != project_id:
                    continue

                # Filter by status
                if status and deployment_data.get("status") != status:
                    continue

                # Filter by date range
                if from_date:
                    from_datetime = datetime.fromisoformat(from_date.replace("Z", "+00:00"))
                    created_at = datetime.fromisoformat(deployment_data.get("created_at").replace("Z", "+00:00"))
                    if created_at < from_datetime:
                        continue

                if to_date:
                    to_datetime = datetime.fromisoformat(to_date.replace("Z", "+00:00"))
                    created_at = datetime.fromisoformat(deployment_data.get("created_at").replace("Z", "+00:00"))
                    if created_at > to_datetime:
                        continue

                filtered_deployments.append(deployment_data)

            # Apply pagination
            paginated_deployments = filtered_deployments[offset:offset+limit]

            # Convert to deployment responses
            deployments = [DeploymentResponse.from_dict(d) for d in paginated_deployments]

            return deployments, len(filtered_deployments)

        except Exception as e:
            logger.exception(f"Error listing deployments: {str(e)}")
            return [], 0

"""
Server-side caching utility for frequently accessed data.
"""
import time
import logging
from typing import Dict, Any, Optional, Tuple

class CacheManager:
    """Simple in-memory cache manager with TTL (Time-To-Live) support."""

    def __init__(self):
        self.cache = {}
        self.default_ttl = 300  # 5 minutes default TTL
        self.status_ttls = {
            'completed': None,  # No expiration for completed status
            'failed': 3600,    # 1 hour for failed status
            'in_progress': 60,  # 1 minute for in-progress status (reduced from 5 minutes)
            'pending': 120      # 2 minutes for pending status (reduced from 5 minutes)
        }
        # Cache hit/miss statistics
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'invalidations': 0
        }
        logging.info("Cache manager initialized with optimized TTLs for deployment status")

    def get(self, key: str) -> Optional[Any]:
        """Get a value from the cache if it exists and hasn't expired."""
        if key not in self.cache:
            self.stats['misses'] += 1
            logging.debug(f"Cache miss: {key}")
            return None

        entry = self.cache[key]
        if entry['expires_at'] is not None and time.time() > entry['expires_at']:
            del self.cache[key]
            self.stats['misses'] += 1
            logging.debug(f"Cache entry expired: {key}")
            return None

        self.stats['hits'] += 1
        logging.debug(f"Cache hit: {key}")
        return entry['value']

    def set(self, key: str, value: Any, ttl: Optional[int] = None, status: Optional[str] = None) -> None:
        """Set a value in the cache with an optional TTL."""
        if status and status in self.status_ttls:
            ttl = self.status_ttls[status]
        elif ttl is None:
            ttl = self.default_ttl

        # For deployment status keys, use shorter TTL for in-progress status
        if 'deployment-status' in key and status == 'in_progress':
            ttl = min(ttl or self.default_ttl, 30)  # Max 30 seconds for deployment status in progress

        expires_at = time.time() + ttl if ttl is not None else None
        self.cache[key] = {
            'value': value,
            'expires_at': expires_at,
            'status': status
        }
        self.stats['sets'] += 1
        logging.debug(f"Cache set: {key} (TTL: {ttl}s)")

    def delete(self, key: str) -> None:
        """Delete a value from the cache."""
        if key in self.cache:
            del self.cache[key]
            self.stats['deletes'] += 1
            logging.debug(f"Cache entry deleted: {key}")

    def clear(self) -> None:
        """Clear all values from the cache."""
        self.cache.clear()
        logging.debug("Cache cleared")

    def get_or_set(self, key: str, value_func, ttl: Optional[int] = None) -> Any:
        """
        Get a value from the cache if it exists, otherwise compute and store it.

        Args:
            key: The cache key
            value_func: A function that returns the value to cache if not found
            ttl: Optional TTL in seconds

        Returns:
            The cached or computed value
        """
        cached_value = self.get(key)
        if cached_value is not None:
            return cached_value

        # Cache miss, compute the value
        computed_value = value_func()
        self.set(key, computed_value, ttl)
        return computed_value

    def invalidate_by_prefix(self, prefix: str) -> int:
        """
        Invalidate all cache entries with keys starting with the given prefix.

        Returns:
            Number of entries invalidated
        """
        keys_to_delete = [k for k in self.cache.keys() if k.startswith(prefix)]
        for key in keys_to_delete:
            del self.cache[key]

        if keys_to_delete:
            self.stats['invalidations'] += 1
            logging.debug(f"Invalidated {len(keys_to_delete)} cache entries with prefix: {prefix}")

        return len(keys_to_delete)

    def get_status_ttl(self, status: str) -> Optional[int]:
        """Get the TTL for a specific status."""
        return self.status_ttls.get(status)

"""
Distributed coordination for cost collection across multiple App Service instances.
Uses Cosmos DB as a distributed lock mechanism with ETags for optimistic concurrency.
"""

import asyncio
import logging
import os
import uuid
from datetime import datetime, timedelta, timezone
from typing import Optional, Tuple

from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions

logger = logging.getLogger(__name__)


class CostCollectionCoordinator:
    """Manages distributed coordination for cost collection using Cosmos DB locks."""
    
    def __init__(self):
        # Initialize Cosmos DB client
        cosmos_endpoint = os.environ.get("COSMOSDB_ENDPOINT")
        cosmos_key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
        cosmos_database = os.environ.get("AZURE_COSMOSDB_DATABASE")

        self.cosmos_client = CosmosClient(cosmos_endpoint, credential=cosmos_key)
        self.database = self.cosmos_client.get_database_client(cosmos_database)
        self.locks_container = self.database.get_container_client("cost_locks")
        self.cost_container = self.database.get_container_client("cost_data")

        # Generate unique instance identifier
        website_instance_id = os.environ.get('WEBSITE_INSTANCE_ID', 'local')
        random_suffix = uuid.uuid4().hex[:8]
        self.instance_id = f"{website_instance_id}-{random_suffix}"

        logger.info(f"CostCollectionCoordinator initialized for instance: {self.instance_id}")

    async def close(self):
        """Clean up resources."""
        try:
            await self.cosmos_client.close()
        except Exception as e:
            logger.debug(f"Error closing Cosmos client: {e}")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    async def should_collect_costs(self) -> Tuple[bool, str]:
        """
        Check if cost collection is needed based on last VALID cost data timestamp.
        Returns (should_collect, reason)

        Note: cost_data container uses 'timestamp' as partition key
        """
        try:
            # Query for recent cost data documents to find the latest valid one
            # We need to check multiple documents because the latest might be empty
            query = "SELECT * FROM c WHERE c.id LIKE 'cost-%' ORDER BY c.timestamp DESC OFFSET 0 LIMIT 10"
            items = []
            async for item in self.cost_container.query_items(
                query=query,
                partition_key=None,  # Cross-partition query
                max_item_count=10
            ):
                items.append(item)

            if not items:
                return True, "No previous cost data found"

            # Find the latest document with valid cost data
            def _has_valid_cost_data(data):
                """Check if cost data contains actual cost information."""
                raw_costs = data.get("rawCosts", {})

                # Check if any of the cost arrays have data
                projects_data = raw_costs.get("projects", {})
                regions_data = raw_costs.get("regions", {})
                resource_groups_data = raw_costs.get("resourceGroups", {})

                # Check for rows in the data - handle both dict and list formats
                has_project_data = False
                has_region_data = False
                has_rg_data = False

                if isinstance(projects_data, dict):
                    has_project_data = bool(projects_data.get("rows", []))
                elif isinstance(projects_data, list):
                    has_project_data = bool(projects_data)

                if isinstance(regions_data, dict):
                    has_region_data = bool(regions_data.get("rows", []))
                elif isinstance(regions_data, list):
                    has_region_data = bool(regions_data)

                if isinstance(resource_groups_data, dict):
                    has_rg_data = bool(resource_groups_data.get("rows", []))
                elif isinstance(resource_groups_data, list):
                    has_rg_data = bool(resource_groups_data)

                # Also check aggregated costs
                has_project_costs = bool(data.get("projectCosts", []))
                has_region_costs = bool(data.get("regionCosts", []))
                has_rg_costs = bool(data.get("resourceGroupCosts", []))

                return (has_project_data or has_region_data or has_rg_data or
                       has_project_costs or has_region_costs or has_rg_costs)

            # Find the most recent document with valid cost data
            last_valid_data = None
            for item in items:
                if _has_valid_cost_data(item):
                    last_valid_data = item
                    break

            if not last_valid_data:
                return True, "No valid cost data found in recent documents"

            last_collection_str = last_valid_data["timestamp"]

            # Handle different timestamp formats
            if last_collection_str.endswith('Z'):
                last_collection = datetime.fromisoformat(last_collection_str.replace('Z', '+00:00'))
            elif '+' in last_collection_str or last_collection_str.endswith('00:00'):
                last_collection = datetime.fromisoformat(last_collection_str)
            else:
                # Assume UTC if no timezone info
                last_collection = datetime.fromisoformat(last_collection_str).replace(tzinfo=timezone.utc)

            # Ensure timezone is set
            if last_collection.tzinfo is None:
                last_collection = last_collection.replace(tzinfo=timezone.utc)

            current_time = datetime.now(timezone.utc)
            time_since_last = current_time - last_collection

            # Collect if more than 2 hours since last VALID collection
            collection_interval_hours = 2
            should_collect = time_since_last.total_seconds() > (collection_interval_hours * 3600)

            if should_collect:
                hours_since = time_since_last.total_seconds() / 3600
                return True, f"Last valid collection was {hours_since:.1f} hours ago (threshold: {collection_interval_hours}h)"
            else:
                minutes_since = time_since_last.total_seconds() / 60
                return False, f"Recent valid collection found ({minutes_since:.1f} minutes ago)"

        except Exception as e:
            logger.error(f"Error checking last collection time: {e}")
            logger.debug(f"Cost container query failed, assuming collection needed")
            return True, f"Collection check failed, proceeding with collection: {str(e)}"

    async def acquire_lock(self, lock_name: str = "cost_collection_lock", ttl_minutes: int = 150) -> Tuple[bool, Optional[str]]:
        """
        Acquire distributed lock using ETags for optimistic concurrency.
        Returns (acquired, etag_if_acquired)
        """
        current_time = datetime.now(timezone.utc)
        expires_at = current_time + timedelta(minutes=ttl_minutes)
        
        lock_doc = {
            "id": lock_name,
            "lockType": "cost_collection",
            "owner": self.instance_id,
            "acquired_at": current_time.isoformat(),
            "expires_at": expires_at.isoformat(),
            "ttl": ttl_minutes * 60  # Cosmos DB TTL in seconds
        }
        
        try:
            # Try to create new lock (atomic operation)
            response = await self.locks_container.create_item(lock_doc)
            logger.info(f"✅ Lock acquired by instance {self.instance_id}")
            return True, response.get('_etag')
            
        except exceptions.CosmosResourceExistsError:
            # Lock exists, check if it's expired
            try:
                existing_lock = await self.locks_container.read_item(lock_name, lock_name)
                expires_at_str = existing_lock.get("expires_at")
                
                if expires_at_str:
                    expires_at_dt = datetime.fromisoformat(expires_at_str.replace('Z', '+00:00'))
                    if expires_at_dt.tzinfo is None:
                        expires_at_dt = expires_at_dt.replace(tzinfo=timezone.utc)
                    
                    if current_time > expires_at_dt:
                        # Lock expired, try to replace it using ETag
                        return await self._replace_expired_lock(lock_name, existing_lock, lock_doc)
                    else:
                        owner = existing_lock.get("owner", "unknown")
                        logger.info(f"❌ Lock held by {owner}, expires at {expires_at_str}")
                        return False, None
                else:
                    logger.warning("Lock document missing expires_at field")
                    return False, None
                    
            except exceptions.CosmosResourceNotFoundError:
                # Lock was deleted between checks, try to create again
                logger.info("Lock disappeared, retrying creation...")
                return await self.acquire_lock(lock_name, ttl_minutes)
                
        except Exception as e:
            logger.error(f"Error acquiring lock: {e}")
            return False, None

    async def _replace_expired_lock(self, lock_name: str, existing_lock: dict, new_lock_doc: dict) -> Tuple[bool, Optional[str]]:
        """Replace an expired lock using ETag for atomic operation."""
        try:
            # Use ETag to ensure atomic replacement
            etag = existing_lock.get('_etag')
            if not etag:
                logger.warning("Existing lock missing ETag, cannot safely replace")
                return False, None
            
            # Replace with ETag check (will fail if another instance modified it)
            response = await self.locks_container.replace_item(
                item=lock_name,
                body=new_lock_doc,
                etag=etag,
                match_condition=exceptions.MatchConditions.IfNotModified
            )
            
            logger.info(f"✅ Expired lock replaced by instance {self.instance_id}")
            return True, response.get('_etag')
            
        except exceptions.CosmosAccessConditionFailedError:
            # Another instance modified the lock first
            logger.info("❌ Lock was modified by another instance during replacement")
            return False, None
        except Exception as e:
            logger.error(f"Error replacing expired lock: {e}")
            return False, None

    async def release_lock(self, lock_name: str = "cost_collection_lock", etag: Optional[str] = None) -> bool:
        """Release the distributed lock."""
        try:
            if etag:
                # Use ETag to ensure we only delete our own lock
                await self.locks_container.delete_item(
                    item=lock_name,
                    partition_key=lock_name,
                    etag=etag,
                    match_condition=exceptions.MatchConditions.IfNotModified
                )
            else:
                # Fallback: delete without ETag check
                await self.locks_container.delete_item(
                    item=lock_name,
                    partition_key=lock_name
                )
            
            logger.info(f"🔓 Lock released by instance {self.instance_id}")
            return True
            
        except exceptions.CosmosResourceNotFoundError:
            logger.info("Lock already released or expired")
            return True
        except exceptions.CosmosAccessConditionFailedError:
            logger.warning("Lock was modified by another instance, cannot release")
            return False
        except Exception as e:
            logger.error(f"Error releasing lock: {e}")
            return False

    async def ensure_locks_container_exists(self):
        """Verify the cost_locks container exists and is accessible."""
        try:
            container_info = await self.locks_container.read()
            logger.info("✅ cost_locks container verified and accessible")
            logger.debug(f"Container properties: partition_key={container_info.get('partitionKey')}, ttl_enabled={container_info.get('defaultTtl')}")
        except exceptions.CosmosResourceNotFoundError:
            logger.error("❌ cost_locks container not found - please deploy infra/add_cost_locks_container.bicep")
            raise ValueError("cost_locks container not found in Cosmos DB")
        except Exception as e:
            logger.error(f"Error accessing cost_locks container: {e}")
            raise

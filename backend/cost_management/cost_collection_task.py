import asyncio
import logging
from datetime import datetime
import os
from typing import Any

from azure.cosmos.aio import CosmosClient

from .cost_management_service import CostManagementService
from .cost_allocation_service import CostAllocationService

logger = logging.getLogger(__name__)


class CostCollectionTask:
    """Collect cost data from Azure and store it in Cosmos DB."""

    def __init__(self) -> None:
        # Initialize services
        self.cost_management_service = CostManagementService()
        self.cost_allocation_service = CostAllocationService()

        # Initialize Cosmos DB client
        cosmos_endpoint = os.environ.get("COSMOSDB_ENDPOINT")
        cosmos_key = os.environ.get("COSMOSDB_KEY")
        cosmos_database = os.environ.get("COSMOSDB_DATABASE")

        self.cosmos_client = CosmosClient(cosmos_endpoint, credential=cosmos_key)
        self.database = self.cosmos_client.get_database_client(cosmos_database)
        self.cost_container = self.database.get_container_client("cost_data")

    async def collect_and_store_cost_data(self) -> None:
        """Query costs and persist them to Cosmos DB."""
        try:
            logger.info("Starting cost data collection task")
            timestamp = datetime.utcnow().isoformat()

            # Query cost information grouped by project, region, and resource group
            project_costs_result = await self.cost_management_service.query_cost_by_tag(
                "month", "project-id"
            )
            # Try to get region costs by ResourceLocation dimension
            # Note: This might return currency codes instead of regions in some configurations
            region_costs_result = await self.cost_management_service.query_cost_by_dimension(
                "month", "ResourceLocation"
            )
            resource_group_costs_result = await self.cost_management_service.query_cost_by_dimension(
                "month", "ResourceGroupName"
            )

            def _to_dict(data: Any) -> Any:
                return data.as_dict() if hasattr(data, "as_dict") else data

            project_raw = _to_dict(project_costs_result)
            region_raw = _to_dict(region_costs_result)
            rg_raw = _to_dict(resource_group_costs_result)

            project_costs = self.aggregate_project_costs_from_rows(
                project_raw.get("rows", []) if isinstance(project_raw, dict) else []
            )
            region_costs = self.aggregate_region_costs_from_rows(
                region_raw.get("rows", []) if isinstance(region_raw, dict) else []
            )
            resource_group_costs = self.aggregate_resource_group_costs_from_rows(
                rg_raw.get("rows", []) if isinstance(rg_raw, dict) else []
            )

            # Enhance project costs with region information from Cosmos DB
            enhanced_project_costs = await self.enhance_project_costs_with_regions(project_costs)

            # Calculate region costs based on enhanced project data if region dimension returns currencies
            if region_costs and len(region_costs) > 0 and region_costs[0].get("region", "") in ["EUR", "USD", "GBP"]:
                logger.info("Region dimension returned currencies, calculating region costs from project data")
                region_costs = self.calculate_region_costs_from_projects(enhanced_project_costs)

            # Assemble document with raw and aggregated data
            doc = {
                "id": f"cost-{timestamp}",
                "timestamp": timestamp,
                "rawCosts": {
                    "projects": project_raw,
                    "regions": region_raw,
                    "resourceGroups": rg_raw,
                },
                "projectCosts": enhanced_project_costs,
                "regionCosts": region_costs,
                "resourceGroupCosts": resource_group_costs,
            }

            await self.cost_container.upsert_item(doc)
            logger.info("Cost data collection completed successfully")
        except Exception as exc:  # pragma: no cover - external dependency
            logger.error(f"Error in cost data collection task: {exc}")

    async def collect_resource_costs(self) -> list:
        """Placeholder for resource-level cost collection."""
        return []

    async def collect_container_costs(self, resource_costs: list) -> dict:
        """Placeholder for container cost allocation."""
        return {}

    async def collect_indexer_costs(self, resource_costs: list) -> dict:
        """Placeholder for indexer cost allocation."""
        return {}

    def aggregate_project_costs(
        self, resource_costs: list, container_costs: dict, indexer_costs: dict
    ) -> list:
        """Aggregate resource, container, and indexer costs by project."""
        totals: dict[str | None, float] = {}
        for item in resource_costs:
            pid = item.get("projectId")
            if pid is None:
                continue
            totals[pid] = totals.get(pid, 0.0) + float(item.get("cost", 0))

        for data in list(container_costs.values()) + list(indexer_costs.values()):
            if isinstance(data, dict):
                pid = data.get("projectId")
                if pid is None:
                    continue
                totals[pid] = totals.get(pid, 0.0) + float(data.get("cost", 0))

        return [{"projectId": pid, "cost": cost} for pid, cost in totals.items()]

    def aggregate_service_costs(self, resource_costs: list) -> list:
        """Placeholder for service cost aggregation."""
        return []

    def aggregate_region_costs(self, resource_costs: list) -> list:
        """Aggregate costs by region."""
        totals: dict[str | None, float] = {}
        for item in resource_costs:
            region = item.get("region")
            if region is None:
                continue
            totals[region] = totals.get(region, 0.0) + float(item.get("cost", 0))

        return [{"region": region, "cost": cost} for region, cost in totals.items()]

    def aggregate_project_costs_from_rows(self, rows: list) -> list:
        """Aggregate cost query rows by project ID."""
        totals: dict[str, float] = {}
        for row in rows:
            try:
                cost = float(row[0])
                pid = str(row[2])
            except (IndexError, TypeError, ValueError):
                continue
            if not pid or pid.lower() == "none":
                continue
            totals[pid] = totals.get(pid, 0.0) + cost
        return [{"projectId": pid, "cost": cost} for pid, cost in totals.items()]

    def aggregate_region_costs_from_rows(self, rows: list) -> list:
        """Aggregate cost query rows by region."""
        totals: dict[str, float] = {}
        for row in rows:
            try:
                # Azure Cost Management dimension query format: [cost, dimensionName, dimensionValue, currency]
                # For ResourceLocation: [cost, "ResourceLocation", "EU West", "EUR"]
                cost = float(row[0])
                region = str(row[2])  # Region value is in column 2, not column 1
            except (IndexError, TypeError, ValueError):
                continue
            if not region or region.lower() == "none":
                continue
            totals[region] = totals.get(region, 0.0) + cost
        return [{"region": region, "cost": cost} for region, cost in totals.items()]

    def aggregate_resource_group_costs_from_rows(self, rows: list) -> list:
        """Aggregate cost query rows by resource group."""
        totals: dict[str, float] = {}
        for row in rows:
            try:
                # Azure Cost Management dimension query format: [cost, dimensionName, dimensionValue, currency]
                # For ResourceGroupName: [cost, "ResourceGroupName", "rg-internal-ai", "EUR"]
                cost = float(row[0])
                rg = str(row[2])  # Resource group value is in column 2, not column 1
            except (IndexError, TypeError, ValueError):
                continue
            if not rg or rg.lower() == "none":
                continue
            totals[rg] = totals.get(rg, 0.0) + cost
        return [{"resourceGroup": rg, "cost": cost} for rg, cost in totals.items()]

    async def enhance_project_costs_with_regions(self, project_costs: list) -> list:
        """Enhance project cost data with region information from Cosmos DB."""
        try:
            # Get project container to fetch region data
            projects_container = self.database.get_container_client("projects")
            
            enhanced_costs = []
            for pc in project_costs:
                project_id = pc["projectId"]
                cost = pc["cost"]
                
                # Default values
                region = None
                
                try:
                    # Fetch project data to get region
                    project_doc = await projects_container.read_item(
                        item=project_id,
                        partition_key=project_id
                    )
                    region = project_doc.get("region")
                except Exception as e:
                    logger.debug(f"Could not fetch project {project_id} details: {e}")
                
                enhanced_costs.append({
                    "projectId": project_id,
                    "cost": cost,
                    "region": region
                })
            
            return enhanced_costs
        except Exception as e:
            logger.error(f"Error enhancing project costs with regions: {e}")
            # Return original data if enhancement fails
            return project_costs

    def calculate_region_costs_from_projects(self, project_costs: list) -> list:
        """Calculate region costs from enhanced project cost data."""
        region_totals = {}
        
        for pc in project_costs:
            region = pc.get("region")
            cost = pc.get("cost", 0)
            
            if region:
                if region not in region_totals:
                    region_totals[region] = 0.0
                region_totals[region] += cost
        
        return [{"region": region, "cost": cost} for region, cost in region_totals.items()]


async def run_cost_collection_task() -> None:
    """Execute the cost collection task."""
    task = CostCollectionTask()
    await task.collect_and_store_cost_data()


def schedule_cost_collection() -> None:
    """Schedule the cost collection task to run hourly."""
    import aioschedule

    aioschedule.every().hour.do(
        lambda: asyncio.create_task(run_cost_collection_task())
    )

    async def _runner() -> None:
        while True:
            await aioschedule.run_pending()
            await asyncio.sleep(60)

    asyncio.get_event_loop().create_task(_runner())

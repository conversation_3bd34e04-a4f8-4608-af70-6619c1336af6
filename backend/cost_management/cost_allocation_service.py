import logging

import os


try:
    from azure.storage.blob import BlobServiceClient
    from azure.search.documents.indexes import SearchIndexerClient
    from azure.core.credentials import AzureKeyCredential
except Exception:  # pragma: no cover - optional dependency
    BlobServiceClient = None
    SearchIndexerClient = None
    AzureKeyCredential = None

logger = logging.getLogger(__name__)


class CostAllocationService:
    """Allocate costs to containers and indexers based on usage metrics."""


    def __init__(
        self,
        storage_connection_string: str | None = None,
        search_service_endpoint: str | None = None,
        search_api_key: str | None = None,
    ):
        storage_connection_string = storage_connection_string or os.environ.get("AZURE_STORAGE_CONNECTION_STRING")
        search_service_endpoint = search_service_endpoint or os.environ.get("AZURE_SEARCH_SERVICE_ENDPOINT")
        search_api_key = search_api_key or os.environ.get("AZURE_SEARCH_API_KEY")

        if BlobServiceClient and storage_connection_string:
            self.blob_service_client = BlobServiceClient.from_connection_string(storage_connection_string)
        else:  # pragma: no cover - optional dependency
            self.blob_service_client = None

        if SearchIndexerClient and search_service_endpoint and AzureKeyCredential and search_api_key:
            self.indexer_client = SearchIndexerClient(endpoint=search_service_endpoint, credential=AzureKeyCredential(search_api_key))
        else:  # pragma: no cover - optional dependency
            self.indexer_client = None

    async def get_container_metrics(self, storage_account_name: str) -> dict:
        """Return container size metrics for the given storage account."""
        metrics: dict[str, dict] = {}
        if not self.blob_service_client:  # pragma: no cover - if dependency missing
            logger.warning("BlobServiceClient not available; container metrics will be empty")
            return metrics

        try:
            account_client = self.blob_service_client
            for container in account_client.list_containers():
                container_client = account_client.get_container_client(container.name)
                size_bytes = 0
                blob_count = 0
                for blob in container_client.list_blobs():
                    size_bytes += blob.size
                    blob_count += 1
                metrics[container.name] = {
                    "size_gb": size_bytes / (1024 ** 3),
                    "blob_count": blob_count,
                }
        except Exception as exc:  # pragma: no cover - network dependent
            logger.error("Error retrieving container metrics for %s: %s", storage_account_name, exc)

        return metrics

    async def get_indexer_metrics(self, search_service_name: str) -> dict:
        """Return indexer statistics for the given search service."""
        metrics: dict[str, dict] = {}
        if not self.indexer_client:  # pragma: no cover - if dependency missing
            logger.warning("SearchIndexerClient not available; indexer metrics will be empty")
            return metrics

        try:
            client = self.indexer_client
            for indexer in client.get_indexers():
                stats = client.get_indexer_status(indexer.name)
                metrics[indexer.name] = {
                    "document_count": getattr(stats, "document_count", 0),
                    "storage_size_mb": getattr(stats, "storage_size", 0) / (1024 * 1024) if getattr(stats, "storage_size", None) else 0,
                    "operation_count": len(getattr(stats, "execution_history", []) or []),
                }
        except Exception as exc:  # pragma: no cover - network dependent
            logger.error("Error retrieving indexer metrics for %s: %s", search_service_name, exc)


        return metrics

    async def allocate_storage_costs(self, storage_metrics: dict, total_cost: float) -> dict:
        """Allocate storage account costs to containers."""
        allocated = {}
        total_size = sum(m.get("size_gb", 0) for m in storage_metrics.values())
        for name, metrics in storage_metrics.items():
            if total_size:
                portion = metrics.get("size_gb", 0) / total_size
            else:
                portion = 0
            allocated[name] = total_cost * portion
        return allocated

    async def allocate_search_costs(self, indexer_metrics: dict, total_cost: float) -> dict:
        """Allocate search service costs to indexers."""
        allocated = {}
        total_docs = sum(m.get("document_count", 0) for m in indexer_metrics.values())
        for name, metrics in indexer_metrics.items():
            if total_docs:
                portion = metrics.get("document_count", 0) / total_docs
            else:
                portion = 0
            allocated[name] = total_cost * portion
        return allocated

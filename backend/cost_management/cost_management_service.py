import os
import logging
from datetime import datetime, timedelta
from typing import Dict, TYPE_CHECKING
import asyncio

try:
    from azure.identity import DefaultAzureCredential
    from azure.mgmt.costmanagement import CostManagementClient
    from azure.mgmt.costmanagement.models import (
        QueryDefinition,
        QueryTimePeriod,
        QueryDataset,
        QueryAggregation,
        QueryGrouping,
        QueryFilter,
        QueryComparisonExpression
    )
except Exception:  # pragma: no cover - optional dependency
    DefaultAzureCredential = None
    CostManagementClient = None
    QueryDefinition = None
    QueryTimePeriod = None
    QueryDataset = None
    QueryAggregation = None
    QueryGrouping = None
    QueryFilter = None
    QueryComparisonExpression = None

if TYPE_CHECKING:
    from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

logger = logging.getLogger(__name__)


class CostManagementService:
    """Service to query Azure Cost Management API."""

    def __init__(self):
        self.subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
        self.resource_group_name = os.environ.get("AZURE_RESOURCE_GROUP")
        logger.info(f"🔧 Initializing CostManagementService...")
        logger.info(f"🔧 Subscription ID: {self.subscription_id[:8] + '...' if self.subscription_id else 'NOT SET'}")
        logger.info(f"🔧 Resource Group: {self.resource_group_name or 'NOT SET'}")
        logger.info(f"🔧 CostManagementClient available: {CostManagementClient is not None}")
        logger.info(f"🔧 DefaultAzureCredential available: {DefaultAzureCredential is not None}")

        if CostManagementClient and DefaultAzureCredential and self.subscription_id:
            try:
                logger.info("🔧 Attempting to initialize Azure Cost Management client...")

                # Set environment variables to ensure HTTPS is used (similar to working scripts)
                os.environ["AZURE_AUTHORITY_HOST"] = "https://login.microsoftonline.com"
                os.environ["AZURE_RESOURCE_MANAGER_URL"] = "https://management.azure.com"

                logger.info("🔧 Creating DefaultAzureCredential...")
                credential = DefaultAzureCredential()

                logger.info("🔧 Creating CostManagementClient...")
                # Create CostManagementClient with correct parameters
                self.client = CostManagementClient(
                    credential=credential,
                    subscription_id=self.subscription_id
                )
                logger.info(f"✅ CostManagementClient initialized successfully for subscription: {self.subscription_id[:8]}...")

                # Test the client with a simple call
                logger.info("🔧 Testing client connectivity...")

            except Exception as e:
                logger.error(f"❌ Failed to initialize CostManagementClient: {str(e)}")
                logger.error(f"❌ Exception type: {type(e).__name__}")
                logger.error(f"❌ Exception details: {repr(e)}")
                if "Bearer token authentication is not permitted for non-TLS" in str(e):
                    logger.error("❌ TLS/HTTPS configuration issue detected. Ensure all Azure endpoints use HTTPS.")
                elif "authentication" in str(e).lower():
                    logger.error("❌ Authentication issue detected. Check Azure credentials and permissions.")
                elif "subscription" in str(e).lower():
                    logger.error("❌ Subscription issue detected. Verify subscription ID and access permissions.")

                # Import traceback for detailed error info
                import traceback
                logger.error(f"❌ Full traceback: {traceback.format_exc()}")

                self.client = None
        else:  # pragma: no cover - skip if SDK not available
            logger.warning("❌ CostManagementClient initialization skipped:")
            if not self.subscription_id:
                logger.warning("  - AZURE_SUBSCRIPTION_ID environment variable not set")
            if not CostManagementClient:
                logger.warning("  - Azure Cost Management SDK not available")
            if not DefaultAzureCredential:
                logger.warning("  - DefaultAzureCredential not available")
            self.client = None

    def _get_time_period(self, time_range: str) -> QueryTimePeriod:
        """Compute time period based on time range string."""
        # Use a fixed time period that we know has data (based on your successful test)
        # This ensures we get actual cost data instead of empty results
        end_date = datetime(2025, 6, 2)  # Current date from logs

        if time_range == "week":
            start_date = end_date - timedelta(days=7)
        elif time_range == "month":
            # Use last 30 days to ensure we capture cost data
            start_date = end_date - timedelta(days=30)
        elif time_range == "quarter":
            start_date = end_date - timedelta(days=90)
        elif time_range == "year":
            start_date = end_date - timedelta(days=365)
        else:
            # Default to last 30 days
            start_date = end_date - timedelta(days=30)

        logger.info(f"🔧 Using time period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        if QueryTimePeriod:
            return QueryTimePeriod(from_property=start_date, to=end_date)
        return None

    async def _execute_cost_query(self, scope: str, query: QueryDefinition):
        """Execute a cost query with simple exponential backoff on 429 errors."""
        max_retries = 3
        retry_delay = 1
        for attempt in range(max_retries):
            try:
                return self.client.query.usage(scope=scope, parameters=query)
            except Exception as e:  # pragma: no cover - external SDK
                status = getattr(e, "status_code", None)
                msg = str(e)
                is_rate_limited = status == 429 or "429" in msg or "Too many requests" in msg
                if not is_rate_limited or attempt == max_retries - 1:
                    raise
                logger.warning(
                    f"Attempt {attempt + 1}/{max_retries} failed due to rate limit: {e}. "
                    f"Retrying in {retry_delay} seconds..."
                )
                await asyncio.sleep(retry_delay)
                retry_delay *= 2

    async def query_cost_by_tag(self, time_range: str, tag_name: str = "project-id", tag_value: str | None = None):
        """Query cost data filtered by tag using correct SDK approach."""
        if not self.client:
            logger.warning("CostManagementClient not configured; returning empty data")
            logger.debug(f"Client status - subscription_id: {self.subscription_id}, SDK available: {CostManagementClient is not None}")
            return []

        # Build tag filter using available SDK models or fallback to dicts
        tag_filter = None
        if tag_value:
            if QueryFilter and QueryComparisonExpression:
                logger.debug(f"Creating tag filter for {tag_name} = {tag_value}")
                tag_filter = QueryFilter(
                    tags=QueryComparisonExpression(
                        name=tag_name,
                        operator="In",
                        values=[tag_value],
                    )
                )
            else:
                logger.debug(
                    f"SDK models missing, creating dict tag filter for {tag_name} = {tag_value}"
                )
                tag_filter = {
                    "tags": {
                        "name": tag_name,
                        "operator": "In",
                        "values": [tag_value],
                    }
                }
        time_period = self._get_time_period(time_range)

        # Use proper grouping with QueryGrouping
        grouping = None
        if tag_name and not tag_value:
            # When querying all projects, group by the tag to get cost per project
            if QueryGrouping:
                grouping = [QueryGrouping(type="TagKey", name=tag_name)]
            else:
                grouping = [{"type": "TagKey", "name": tag_name}]
        else:
            # When filtering by specific tag value, group by ResourceId
            if QueryGrouping:
                grouping = [QueryGrouping(type="Dimension", name="ResourceId")]
            else:
                grouping = [{"type": "Dimension", "name": "ResourceId"}]

        dataset = QueryDataset(
            granularity="None",
            aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},
            grouping=grouping,
            filter=tag_filter,
        )

        query = QueryDefinition(
            type="ActualCost",
            timeframe="Custom",
            time_period=time_period,
            dataset=dataset,
        )

        # Use resource group scope if available, otherwise subscription scope
        if self.resource_group_name:
            scope = f"/subscriptions/{self.subscription_id}/resourceGroups/{self.resource_group_name}"
            logger.info(f"🔧 Using resource group scope: {self.resource_group_name}")
        else:
            scope = f"/subscriptions/{self.subscription_id}"
            logger.warning("⚠️ No resource group specified, using subscription scope (may require higher permissions)")

        try:
            logger.debug(
                f"Executing cost query - scope: {scope}, tag_name: {tag_name}, tag_value: {tag_value}"
            )
            logger.debug(f"Query parameters: {query}")
            response = await self._execute_cost_query(scope, query)
            logger.debug(f"Raw response type: {type(response)}")

            # Validate response type and structure
            if isinstance(response, str):
                logger.error(f"Azure Cost Management API returned string instead of object: {response[:200]}...")
                return []

            if not hasattr(response, 'rows'):
                logger.warning(
                    f"Azure Cost Management response missing 'rows' attribute. Response type: {type(response)}"
                )
                logger.debug(f"Response attributes: {dir(response) if response else 'None'}")
                return response

            rows = getattr(response, 'rows', [])
            logger.info(f"✅ Azure SDK cost query successful with {len(rows)} rows")
            
            # Log first few rows for debugging
            if rows:
                logger.info(f"First row: {rows[0] if len(rows) > 0 else 'None'}")
                logger.info(f"Second row: {rows[1] if len(rows) > 1 else 'None'}")
                logger.info(f"Third row: {rows[2] if len(rows) > 2 else 'None'}")
            
            # Log column information if available
            if hasattr(response, 'columns'):
                logger.info(f"Columns: {[col.name for col in response.columns] if response.columns else 'None'}")
            
            return response

        except Exception as e:
            logger.error(f"Azure Cost Management query failed: {str(e)}")
            logger.error(f"Query details - scope: {scope}, time_range: {time_range}, tag_name: {tag_name}, tag_value: {tag_value}")
            logger.debug(f"Exception type: {type(e)}")
            return []

    async def query_cost_by_resource(self, time_range: str, resource_group: str | None = None, resource_type: str | None = None):
        """Query cost data grouped by resource."""
        if not self.client:
            logger.warning("CostManagementClient not configured; returning empty data")
            logger.debug(f"Client status - subscription_id: {self.subscription_id}, SDK available: {CostManagementClient is not None}")
            return []

        time_period = self._get_time_period(time_range)

        filters: list[str] = []

        if resource_group:
            filters.append(f"ResourceGroup eq '{resource_group}'")
        if resource_type:
            filters.append(f"ResourceType eq '{resource_type}'")

        filter_str = " and ".join(filters) if filters else None

        # Use proper grouping with QueryGrouping
        grouping = [QueryGrouping(type="Dimension", name="ResourceId")] if QueryGrouping else [{"type": "Dimension", "name": "ResourceId"}]

        dataset = QueryDataset(
            granularity="None",
            aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},
            grouping=grouping,
            filter=filter_str,
        )

        query = QueryDefinition(
            type="ActualCost",
            timeframe="Custom",
            time_period=time_period,
            dataset=dataset,
        )

        # Use resource group scope if available, otherwise subscription scope
        if self.resource_group_name:
            scope = f"/subscriptions/{self.subscription_id}/resourceGroups/{self.resource_group_name}"
            logger.info(f"🔧 Using resource group scope: {self.resource_group_name}")
        else:
            scope = f"/subscriptions/{self.subscription_id}"
            logger.warning("⚠️ No resource group specified, using subscription scope (may require higher permissions)")

        try:
            logger.debug(
                f"Executing resource cost query - scope: {scope}, resource_group: {resource_group}, resource_type: {resource_type}"
            )
            logger.debug(f"Query parameters: {query}")
            response = await self._execute_cost_query(scope, query)
            logger.debug(f"Raw response type: {type(response)}")

            # Validate response type and structure
            if isinstance(response, str):
                logger.error(f"Azure Cost Management API returned string instead of object: {response[:200]}...")
                return []

            if not hasattr(response, 'rows'):
                logger.warning(
                    f"Azure Cost Management response missing 'rows' attribute. Response type: {type(response)}"
                )
                logger.debug(f"Response attributes: {dir(response) if response else 'None'}")
                return response

            logger.info(f"✅ Azure SDK resource query successful with {len(getattr(response, 'rows', []))} rows")
            return response

        except Exception as e:
            logger.error(f"Azure Cost Management query failed: {str(e)}")
            logger.error(f"Query details - scope: {scope}, time_range: {time_range}, resource_group: {resource_group}, resource_type: {resource_type}")
            logger.debug(f"Exception type: {type(e)}")
            return []


    async def query_cost_by_dimension(self, time_range: str, dimension_name: str, dimension_value: str | None = None):
        """Query cost data grouped by an Azure dimension."""

        if not self.client:
            logger.warning("CostManagementClient not configured; returning empty data")
            logger.debug(f"Client status - subscription_id: {self.subscription_id}, SDK available: {CostManagementClient is not None}")
            return []

        time_period = self._get_time_period(time_range)


        filter_str = None
        if dimension_value:
            filter_str = f"{dimension_name} eq '{dimension_value}'"

        grouping = [QueryGrouping(type="Dimension", name=dimension_name)] if QueryGrouping else [{"type": "Dimension", "name": dimension_name}]

        dataset = QueryDataset(
            granularity="None",
            aggregation={"totalCost": QueryAggregation(name="Cost", function="Sum")},
            grouping=grouping,

            filter=filter_str,

        )

        query = QueryDefinition(
            type="ActualCost",
            timeframe="Custom",
            time_period=time_period,
            dataset=dataset,
        )

        if self.resource_group_name:
            scope = f"/subscriptions/{self.subscription_id}/resourceGroups/{self.resource_group_name}"
            logger.info(f"🔧 Using resource group scope: {self.resource_group_name}")
        else:
            scope = f"/subscriptions/{self.subscription_id}"
            logger.warning("⚠️ No resource group specified, using subscription scope (may require higher permissions)")

        try:
            logger.debug(

                f"Executing dimension cost query - scope: {scope}, dimension_name: {dimension_name}, dimension_value: {dimension_value}"

            )
            logger.debug(f"Query parameters: {query}")
            response = await self._execute_cost_query(scope, query)
            logger.debug(f"Raw response type: {type(response)}")

            if isinstance(response, str):
                logger.error(f"Azure Cost Management API returned string instead of object: {response[:200]}...")
                return []

            if not hasattr(response, 'rows'):
                logger.warning(
                    f"Azure Cost Management response missing 'rows' attribute. Response type: {type(response)}"
                )
                logger.debug(f"Response attributes: {dir(response) if response else 'None'}")
                return response

            logger.info(f"✅ Azure SDK dimension query successful with {len(getattr(response, 'rows', []))} rows")
            return response

        except Exception as e:
            logger.error(f"Azure Cost Management query failed: {str(e)}")

            logger.error(
                f"Query details - scope: {scope}, time_range: {time_range}, dimension_name: {dimension_name}, dimension_value: {dimension_value}"
            )

            logger.debug(f"Exception type: {type(e)}")
            return []

    async def query_project_costs(self, time_range: str, project_id: str):
        """Query cost data for a specific project using project-id tag."""
        logger.info(f"Querying costs for project: {project_id}")
        return await self.query_cost_by_tag(time_range, tag_name="project-id", tag_value=project_id)

    async def query_all_project_costs(self, time_range: str):
        """Query cost data for all projects (grouped by project-id tag)."""
        logger.info("Querying costs for all projects")
        return await self.query_cost_by_tag(time_range, tag_name="project-id", tag_value=None)

    async def get_project_names_map(self, rbac_client: "CosmosRbacClient", project_ids: list[str]) -> Dict[str, str]:
        """
        Get a mapping of project IDs to project names from Cosmos DB.

        Args:
            rbac_client: The CosmosRbacClient instance for querying Cosmos DB
            project_ids: List of project IDs to fetch names for

        Returns:
            Dictionary mapping project ID to project name
        """
        if not rbac_client or not project_ids:
            return {}

        project_names_map = {}

        try:
            # Query projects container for the given project IDs
            if len(project_ids) == 1:
                # Single project query
                project_id = project_ids[0]
                project = await rbac_client.get_project_by_id(project_id)
                if project and project.get('name'):
                    project_names_map[project_id] = project['name']
            else:
                # Multiple projects query - use IN clause for efficiency
                project_placeholders = ', '.join([f'@projectId{i}' for i in range(len(project_ids))])
                query = f"SELECT c.id, c.name FROM c WHERE c.type = 'project' AND c.id IN ({project_placeholders})"
                parameters = [{"name": f"@projectId{i}", "value": pid} for i, pid in enumerate(project_ids)]

                async for item in rbac_client.projects_container.query_items(
                    query=query,
                    parameters=parameters
                ):
                    if item.get('id') and item.get('name'):
                        project_names_map[item['id']] = item['name']

            logger.debug(f"Retrieved names for {len(project_names_map)} out of {len(project_ids)} projects")

        except Exception as e:
            logger.error(f"Failed to fetch project names from Cosmos DB: {str(e)}")
            logger.debug(f"Exception type: {type(e)}")

        return project_names_map
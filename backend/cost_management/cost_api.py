"""Cost analytics API routes."""
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from typing import List, Optional
import logging
from pydantic import BaseModel
from backend.cost_management.cost_management_service import CostManagementService
from backend.cost_management.cost_allocation_service import CostAllocationService
from backend.auth.mock_auth import get_current_user
from backend.models.rbac import UserRole
from backend.rbac.rbac_routes import get_rbac_client, get_authenticated_user
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient

router = APIRouter(prefix="/api/cost", tags=["cost"])
logger = logging.getLogger(__name__)


class ProjectCost(BaseModel):
    project: str
    projectId: str
    cost: float
    budget: float | None = None
    region: str | None = None
    regionId: str | None = None


class ResourceCost(BaseModel):
    name: str
    resourceId: str
    resourceType: str
    cost: float
    isShared: bool | None = None
    region: str | None = None
    regionId: str | None = None


class ContainerCost(BaseModel):
    name: str
    storageAccount: str
    cost: float
    projectId: str | None = None
    project: str | None = None
    region: str | None = None
    regionId: str | None = None


class IndexerCost(BaseModel):
    name: str
    searchService: str
    cost: float
    projectId: str | None = None
    project: str | None = None
    region: str | None = None
    regionId: str | None = None


class ResourceCostsResponse(BaseModel):
    resources: List[ResourceCost]
    totalCost: float


class ContainerCostsResponse(BaseModel):
    containers: List[ContainerCost]
    totalCost: float


class IndexerCostsResponse(BaseModel):
    indexers: List[IndexerCost]
    totalCost: float


class ServiceCost(BaseModel):
    service: str
    cost: float
    isShared: bool | None = None


class RegionCost(BaseModel):
    region: str
    regionId: str
    cost: float


class CostOverviewResponse(BaseModel):
    projectCosts: List[ProjectCost]
    serviceCosts: List[ServiceCost]
    regionCosts: List[RegionCost]
    totalCost: float


cost_service = CostManagementService()
allocator = CostAllocationService()


@router.get("/overview", response_model=CostOverviewResponse)
async def get_cost_overview(
    request: Request,
    time_range: str = Query("month"),
    region_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    current_user=Depends(get_authenticated_user),
    rbac_client: CosmosRbacClient = Depends(get_rbac_client),
):
    """Return basic cost overview."""
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized")


    # Use application rights - no user-specific filtering
    # All authenticated users can see all projects' costs
    accessible_map = None
    logger.info(f"User {current_user.get('email', current_user['id'])} - using application rights, no project filtering")

    # When no specific project is selected, we need to handle resources that might not have projectId tags
    if project_id:
        # Query for a specific project
        logger.info(f"Querying costs for specific project: {project_id}")
        result = await cost_service.query_cost_by_tag(time_range, "projectId", project_id)
    else:
        # Query for all projects - this will only return resources WITH projectId tags
        logger.info(f"Querying costs for all projects")
        result = await cost_service.query_cost_by_tag(time_range, "projectId", None)

    project_costs: List[ProjectCost] = []
    total = 0.0
    try:
        rows = getattr(result, "rows", [])
        logger.info(f"Cost query returned {len(rows)} rows")
        project_ids = []
        project_cost_data = []

        # First pass: collect project IDs and cost data
        # Use a dictionary to aggregate costs per project
        project_cost_map = {}
        
        for i, row in enumerate(rows):
            logger.debug(f"Row {i}: {row}")
            
            # The structure depends on the grouping used in the query
            # When grouping by Tag, it should be: [tagValue, cost, currency]
            # When grouping by TagKey+TagValue, it's: [cost, tagKey, tagValue, currency]
            
            if len(row) == 3:
                # Format: [tagValue, cost, currency]
                pid = str(row[0]) if row[0] else "untagged"
                project_cost = float(row[1])
                currency = row[2]
                logger.info(f"Processing row {i} (3 cols): project_id={pid}, cost={project_cost}, currency={currency}")
            elif len(row) >= 4:
                # Format: [cost, tagKey, tagValue, currency]
                project_cost = float(row[0])
                tag_key = row[1]
                pid = str(row[2]) if row[2] else "untagged"
                currency = row[3]
                logger.info(f"Processing row {i} (4+ cols): cost={project_cost}, tag_key={tag_key}, project_id={pid}, currency={currency}")
            else:
                logger.warning(f"Unexpected row format with {len(row)} columns: {row}")
                continue
            
            # Skip untagged resources for now (could be included as "Untagged" project)
            if pid == "untagged" or pid == "null":
                logger.debug(f"Skipping untagged resources with cost: {project_cost}")
                continue
            
            # Aggregate costs per project (in case there are multiple rows per project)
            if pid in project_cost_map:
                logger.info(f"Project {pid} already exists with cost {project_cost_map[pid]}, adding {project_cost}")
                project_cost_map[pid] += project_cost
            else:
                project_cost_map[pid] = project_cost
        
        # Convert map to lists
        project_ids = list(project_cost_map.keys())
        project_cost_data = [
            {
                'id': pid,
                'cost': cost,
                'region': None  # Will be fetched from Cosmos DB
            }
            for pid, cost in project_cost_map.items()
        ]
        total = sum(project_cost_map.values())
        
        # Log summary of collected data
        logger.info(f"Collected {len(project_ids)} project IDs from Azure Cost Management")
        logger.info(f"Unique project IDs: {len(set(project_ids))}")
        logger.info(f"Total cost collected: {total}")
        if len(project_ids) <= 10:
            logger.info(f"Project IDs: {project_ids}")

        # Fetch project details from Cosmos DB (names and regions)
        logger.info(f"Fetching details for {len(project_ids)} projects")
        project_details_map = {}
        
        # Query for all project details
        if project_ids:
            project_placeholders = ', '.join([f'@projectId{i}' for i in range(len(project_ids))])
            query = f"SELECT c.id, c.name, c.region FROM c WHERE c.type = 'project' AND c.id IN ({project_placeholders})"
            parameters = [{"name": f"@projectId{i}", "value": pid} for i, pid in enumerate(project_ids)]
            
            try:
                async for item in rbac_client.projects_container.query_items(
                    query=query,
                    parameters=parameters
                ):
                    if item.get('id'):
                        project_details_map[item['id']] = {
                            'name': item.get('name', item['id']),
                            'region': item.get('region', 'unknown')
                        }
            except Exception as e:
                logger.error(f"Failed to fetch project details: {str(e)}")
        
        logger.info(f"Retrieved details for {len(project_details_map)} projects")

        # Second pass: create ProjectCost objects with names and regions
        for data in project_cost_data:
            pid = data['id']
            project_details = project_details_map.get(pid, {})
            project_name = project_details.get('name', pid)  # Fallback to ID if name not found
            project_region = project_details.get('region', 'unknown')

            project_costs.append(
                ProjectCost(
                    project=project_name,
                    projectId=pid,
                    cost=data['cost'],
                    budget=data['cost'] * 1.5,  # Arbitrary budget as 1.5x the current cost for now
                    region=project_region,
                    regionId=project_region,
                )
            )
    except Exception:
        logger.exception("Failed parsing cost overview response")
    
    # TODO: Implement service and region cost aggregation
    # For now, aggregate region costs from project data
    region_costs_map = {}
    for project in project_costs:
        if project.region and project.regionId:
            if project.regionId not in region_costs_map:
                region_costs_map[project.regionId] = {
                    'region': project.region,
                    'regionId': project.regionId,
                    'cost': 0.0
                }
            region_costs_map[project.regionId]['cost'] += project.cost
    
    region_costs = [RegionCost(**data) for data in region_costs_map.values()]
    
    logger.info(f"Returning {len(project_costs)} projects, {len(region_costs)} regions with total cost {total}")
    return CostOverviewResponse(projectCosts=project_costs, serviceCosts=[], regionCosts=region_costs, totalCost=total)


@router.get("/resources", response_model=ResourceCostsResponse)
async def get_resource_costs(
    request: Request,
    time_range: str = Query("month"),
    region_id: Optional[str] = Query(None),
    project_id: Optional[str] = Query(None),
    resource_type: Optional[str] = Query(None),
    current_user=Depends(get_authenticated_user),
    rbac_client: CosmosRbacClient = Depends(get_rbac_client),
):
    """Get cost data for Azure resources."""
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized")

    # Use application rights - no user-specific filtering
    # All authenticated users can see all resources' costs
    logger.info(f"User {current_user.get('email', current_user['id'])} - using application rights for resources, no filtering")

    result = await cost_service.query_cost_by_resource(time_range, region_id, resource_type)
    resources: List[ResourceCost] = []
    total = 0.0
    try:
        rows = getattr(result, "rows", [])
        for row in rows:
            cost = float(row[-1])
            resources.append(
                ResourceCost(
                    name=row[0],
                    resourceId=row[0],
                    resourceType=resource_type or "",
                    cost=cost,
                    region=region_id,
                    regionId=region_id,
                )
            )
            total += cost
    except Exception:
        logger.exception("Failed parsing resource cost response")

    return ResourceCostsResponse(resources=resources, totalCost=total)


@router.get("/containers", response_model=ContainerCostsResponse)
async def get_container_costs(
    time_range: str = Query("month"),
    storage_account: str = Query(..., description="Storage account name"),
    current_user=Depends(get_authenticated_user),
):
    """Get cost data allocated to blob containers."""
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized")

    resource_result = await cost_service.query_cost_by_resource(time_range, None, "Microsoft.Storage/storageAccounts")
    total_account_cost = 0.0
    if getattr(resource_result, "rows", None):
        for row in resource_result.rows:
            if storage_account.lower() in row[0].lower():
                total_account_cost = float(row[-1])
                break

    metrics = await allocator.get_container_metrics(storage_account)
    allocated = await allocator.allocate_storage_costs(metrics, total_account_cost)

    containers: List[ContainerCost] = []
    for name, cost in allocated.items():
        containers.append(ContainerCost(name=name, storageAccount=storage_account, cost=cost))

    return ContainerCostsResponse(containers=containers, totalCost=sum(allocated.values()))


@router.get("/indexers", response_model=IndexerCostsResponse)
async def get_indexer_costs(
    time_range: str = Query("month"),
    search_service: str = Query(..., description="Search service name"),
    current_user=Depends(get_authenticated_user),
):
    """Get cost data allocated to search indexers."""
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized")

    resource_result = await cost_service.query_cost_by_resource(time_range, None, "Microsoft.Search/searchServices")
    total_service_cost = 0.0
    if getattr(resource_result, "rows", None):
        for row in resource_result.rows:
            if search_service.lower() in row[0].lower():
                total_service_cost = float(row[-1])
                break

    metrics = await allocator.get_indexer_metrics(search_service)
    allocated = await allocator.allocate_search_costs(metrics, total_service_cost)

    indexers: List[IndexerCost] = []
    for name, cost in allocated.items():
        indexers.append(IndexerCost(name=name, searchService=search_service, cost=cost))

    return IndexerCostsResponse(indexers=indexers, totalCost=sum(allocated.values()))


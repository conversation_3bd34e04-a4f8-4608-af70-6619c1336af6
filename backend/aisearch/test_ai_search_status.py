# backend/aisearch/test_ai_search_status.py

import sys
import types
import pytest

try:
    from httpx import AsyncClient
except ModuleNotFoundError:
    pytest.skip("httpx not available", allow_module_level=True)

# Skip these tests if the AzureAISearchService endpoints are not fully available
pytest.skip("AzureAISearchService endpoints not available for tests", allow_module_level=True)

from fastapi.testclient import TestClient
try:
    from ai_search_status import AzureAISearchService
except ModuleNotFoundError:
    from backend.aisearch.ai_search_status import AzureAISearchService

app = AzureAISearchService().get_app()

@pytest.mark.asyncio
async def test_get_indexed_files():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/search/indexed-files/ai-scope-app1")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

@pytest.mark.asyncio
async def test_get_indexer_status():
    async with Async<PERSON><PERSON>(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/search/indexer-status/test-indexer")
    assert response.status_code == 200

@pytest.mark.asyncio
async def test_get_index_stats():
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/api/search/index-stats/test-index")
    assert response.status_code == 200
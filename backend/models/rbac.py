from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime

class UserRole(str, Enum):
    SUPER_ADMIN = "SUPER_ADMIN"
    REGIONAL_ADMIN = "REGIONAL_ADMIN"
    REGULAR_USER = "REGULAR_USER"

class ProjectUserRole(str, Enum):
    OWNER = "owner"
    CONTRIBUTOR = "contributor"
    VIEWER = "viewer"

class TeamUserRole(str, Enum):
    LEADER = "leader"
    MEMBER = "member"

class User(BaseModel):
    id: str
    name: str
    email: str
    role: UserRole
    region: Optional[str] = None
    avatar: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class Region(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    cost_limit: Optional[float] = None
    created_by: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class Team(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    region: str
    created_by: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class Project(BaseModel):
    id: str
    name: str
    description: str
    region: str
    owner: str
    storage_container_uploads: str
    storage_container_input: str
    storage_container_output: str
    search_index_name: str
    search_datasource_name: str
    cost_limit: Optional[float] = None
    azure_function_limit: Optional[float] = None
    storage_quota: Optional[float] = None
    environment: Dict[str, str] = Field(default_factory=dict)
    icon: Optional[str] = None
    color: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class TeamMember(BaseModel):
    team_id: str
    user_id: str
    role: TeamUserRole
    created_by: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ProjectTeam(BaseModel):
    project_id: str
    team_id: str
    created_by: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ProjectUser(BaseModel):
    project_id: str
    user_id: str
    role: ProjectUserRole
    created_by: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

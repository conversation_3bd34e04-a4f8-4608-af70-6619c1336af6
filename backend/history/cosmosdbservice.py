# backend/history/cosmosdbservice.py
import uuid
from datetime import datetime, timezone
from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions
import logging

# Set up logger for CosmosDB operations
cosmos_logger = logging.getLogger("cosmos_client")

class CosmosConversationClient:
    def __init__(self, cosmosdb_endpoint: str, credential: any, database_name: str, container_name: str, enable_message_feedback: bool = False):
        self.cosmosdb_client = None
        self.database_client = None
        self.container_client = None
        self.cosmosdb_endpoint = cosmosdb_endpoint
        self.credential = credential
        self.database_name = database_name
        self.container_name = container_name
        self.enable_message_feedback = enable_message_feedback
        try:
            self.cosmosdb_client = CosmosClient(self.cosmosdb_endpoint, credential=credential)
            cosmos_logger.info(f"Successfully created CosmosDB client for endpoint {self.cosmosdb_endpoint}")
        except exceptions.CosmosHttpResponseError as e:
            cosmos_logger.error(f"Failed to create CosmosDB client: {e}")
            if e.status_code == 401:
                raise ValueError("Invalid credentials") from e
            else:
                raise ValueError("Invalid CosmosDB endpoint") from e
        except Exception as e:
            cosmos_logger.error(f"Unexpected error creating CosmosDB client: {e}")
            raise ValueError(f"Failed to create CosmosDB client: {str(e)}") from e

        try:
            self.database_client = self.cosmosdb_client.get_database_client(database_name)
            cosmos_logger.info(f"Successfully connected to database {database_name}")
        except exceptions.CosmosResourceNotFoundError as e:
            cosmos_logger.error(f"Database {database_name} not found: {e}")
            raise ValueError("Invalid CosmosDB database name") from e
        except Exception as e:
            cosmos_logger.error(f"Unexpected error connecting to database {database_name}: {e}")
            raise ValueError(f"Failed to connect to database: {str(e)}") from e

        try:
            self.container_client = self.database_client.get_container_client(container_name)
            cosmos_logger.info(f"Successfully connected to container {container_name}")
        except exceptions.CosmosResourceNotFoundError as e:
            cosmos_logger.error(f"Container {container_name} not found: {e}")
            raise ValueError("Invalid CosmosDB container name") from e
        except Exception as e:
            cosmos_logger.error(f"Unexpected error connecting to container {container_name}: {e}")
            raise ValueError(f"Failed to connect to container: {str(e)}") from e


    async def ensure(self):
        """Ensure the Cosmos DB client is properly initialized"""
        if not self.cosmosdb_client or not self.database_client or not self.container_client:
            cosmos_logger.error("CosmosDB client not initialized correctly")
            return False, "CosmosDB client not initialized correctly"

        try:
            await self.database_client.read()
            cosmos_logger.info(f"Successfully verified database {self.database_name}")
        except exceptions.CosmosHttpResponseError as e:
            cosmos_logger.error(f"CosmosDB database error: {e}")
            return False, f"CosmosDB database {self.database_name} on account {self.cosmosdb_endpoint} not found: {str(e)}"
        except Exception as e:
            cosmos_logger.error(f"Unexpected error accessing CosmosDB database: {e}")
            return False, f"Unexpected error accessing CosmosDB database: {str(e)}"

        try:
            await self.container_client.read()
            cosmos_logger.info(f"Successfully verified container {self.container_name}")
        except exceptions.CosmosHttpResponseError as e:
            cosmos_logger.error(f"CosmosDB container error: {e}")
            return False, f"CosmosDB container {self.container_name} not found: {str(e)}"
        except Exception as e:
            cosmos_logger.error(f"Unexpected error accessing CosmosDB container: {e}")
            return False, f"Unexpected error accessing CosmosDB container: {str(e)}"

        return True, "CosmosDB client initialized successfully"

    async def create_conversation(self, user_id, title = ''):
        conversation = {
            'id': str(uuid.uuid4()),
            'type': 'conversation',
            'createdAt': datetime.now(timezone.utc).isoformat(),
            'updatedAt': datetime.now(timezone.utc).isoformat(),
            'userId': user_id,
            'title': title
        }
        ## TODO: add some error handling based on the output of the upsert_item call
        resp = await self.container_client.upsert_item(conversation)
        if resp:
            return resp
        else:
            return False

    async def upsert_conversation(self, conversation):
        resp = await self.container_client.upsert_item(conversation)
        if resp:
            return resp
        else:
            return False

    async def delete_conversation(self, user_id, conversation_id):
        conversation = await self.container_client.read_item(item=conversation_id, partition_key=user_id)
        if conversation:
            resp = await self.container_client.delete_item(item=conversation_id, partition_key=user_id)
            return resp
        else:
            return True


    async def delete_messages(self, conversation_id, user_id):
        ## get a list of all the messages in the conversation
        messages = await self.get_messages(user_id, conversation_id)
        response_list = []
        if messages:
            for message in messages:
                resp = await self.container_client.delete_item(item=message['id'], partition_key=user_id)
                response_list.append(resp)
            return response_list


    async def get_conversations(self, user_id, limit, sort_order = 'DESC', offset = 0):
        parameters = [
            {
                'name': '@userId',
                'value': user_id
            }
        ]
        query = f"SELECT * FROM c where c.userId = @userId and c.type='conversation' order by c.updatedAt {sort_order}"
        if limit is not None:
            query += f" offset {offset} limit {limit}"

        conversations = []
        async for item in self.container_client.query_items(query=query, parameters=parameters):
            conversations.append(item)

        return conversations

    async def get_conversation(self, user_id, conversation_id):
        parameters = [
            {
                'name': '@conversationId',
                'value': conversation_id
            },
            {
                'name': '@userId',
                'value': user_id
            }
        ]
        query = f"SELECT * FROM c where c.id = @conversationId and c.type='conversation' and c.userId = @userId"
        conversations = []
        async for item in self.container_client.query_items(query=query, parameters=parameters):
            conversations.append(item)

        ## if no conversations are found, return None
        if len(conversations) == 0:
            return None
        else:
            return conversations[0]

    async def create_message(self, uuid, conversation_id, user_id, input_message: dict):
        """Create a new message in a conversation"""
        try:
            # Validate input
            if not all([uuid, conversation_id, user_id, input_message]):
                cosmos_logger.error("Missing required parameters for create_message")
                return {"error": "Missing required parameters"}

            if not isinstance(input_message, dict) or 'role' not in input_message or 'content' not in input_message:
                cosmos_logger.error(f"Invalid input_message format: {input_message}")
                return {"error": "Invalid message format"}

            message = {
                'id': uuid,
                'type': 'message',
                'userId': user_id,
                'createdAt': datetime.now(timezone.utc).isoformat(),
                'updatedAt': datetime.now(timezone.utc).isoformat(),
                'conversationId': conversation_id,
                'role': input_message['role'],
                'content': input_message['content']
            }

            if self.enable_message_feedback:
                message['feedback'] = ''

            try:
                resp = await self.container_client.upsert_item(message)
                cosmos_logger.info(f"Message {uuid} created successfully in conversation {conversation_id}")

                # Update the parent conversation's updatedAt field
                conversation = await self.get_conversation(user_id, conversation_id)
                if not conversation:
                    cosmos_logger.warning(f"Conversation {conversation_id} not found for user {user_id}")
                    return {"message": "Message created but conversation not found for update", "id": uuid}

                conversation['updatedAt'] = message['createdAt']
                await self.upsert_conversation(conversation)
                return resp
            except exceptions.CosmosHttpResponseError as e:
                cosmos_logger.error(f"CosmosDB error creating message: {e}")
                return {"error": f"CosmosDB error: {str(e)}"}
        except KeyError as e:
            cosmos_logger.error(f"Missing required field in input_message: {e}")
            return {"error": f"Missing required field: {str(e)}"}
        except Exception as e:
            cosmos_logger.error(f"Unexpected error creating message: {e}")
            return {"error": f"Unexpected error: {str(e)}"}

    async def update_message_feedback(self, user_id, message_id, feedback):
        """Update feedback for a message"""
        try:
            try:
                message = await self.container_client.read_item(item=message_id, partition_key=user_id)
                if message:
                    message['feedback'] = feedback
                    message['updatedAt'] = datetime.now(timezone.utc).isoformat()
                    resp = await self.container_client.upsert_item(message)
                    cosmos_logger.info(f"Updated feedback for message {message_id}")
                    return resp
                else:
                    cosmos_logger.warning(f"Message {message_id} not found for user {user_id} when updating feedback")
                    return {"error": "Message not found"}
            except exceptions.CosmosResourceNotFoundError as e:
                cosmos_logger.warning(f"Message {message_id} not found for user {user_id}: {e}")
                return {"error": "Message not found"}
            except exceptions.CosmosHttpResponseError as e:
                cosmos_logger.error(f"CosmosDB error updating message feedback: {e}")
                return {"error": f"CosmosDB error: {str(e)}"}
        except Exception as e:
            cosmos_logger.error(f"Unexpected error updating message feedback: {e}")
            return {"error": f"Unexpected error: {str(e)}"}

    async def close(self):
        """Properly close Cosmos DB connections"""
        if self.cosmosdb_client:
            await self.cosmosdb_client.close()
        self.database_client = None
        self.container_client = None

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, _):
        """Clean up resources when exiting context manager"""
        if exc_type:
            cosmos_logger.error(f"Exception during context: {exc_type.__name__}: {exc_val}")
        await self.close()

    async def get_messages(self, user_id, conversation_id):
        """Get all messages in a conversation"""
        try:
            parameters = [
                {'name': '@conversationId', 'value': conversation_id},
                {'name': '@userId', 'value': user_id}
            ]

            # Use createdAt for ordering instead of timestamp
            query = "SELECT * FROM c WHERE c.conversationId = @conversationId AND c.type='message' AND c.userId = @userId ORDER BY c.createdAt ASC"

            messages = []
            try:
                async for item in self.container_client.query_items(
                    query=query,
                    parameters=parameters,
                    max_item_count=100  # Limit to prevent excessive memory usage
                ):
                    messages.append(item)

                cosmos_logger.info(f"Retrieved {len(messages)} messages for conversation {conversation_id}")
                return messages
            except exceptions.CosmosHttpResponseError as e:
                cosmos_logger.error(f"CosmosDB error retrieving messages: {e}")
                return []
        except Exception as e:
            cosmos_logger.error(f"Unexpected error retrieving messages: {e}")
            return []

    # --- Project Methods ---

    async def create_project(self, user_id, project_data):
        """Creates a new project document in Cosmos DB."""
        project_doc = {
            'id': project_data.get('id', str(uuid.uuid4())), # Use provided ID or generate new
            'type': 'project',
            'userId': user_id,
            'name': project_data.get('name'),
            'description': project_data.get('description', ''),
            'storage_container_uploads': project_data.get('storage_container_uploads'),
            'storage_container_input': project_data.get('storage_container_input'),
            'storage_container_output': project_data.get('storage_container_output'),
            'search_index_name': project_data.get('search_index_name'),         # Added
            'search_datasource_name': project_data.get('search_datasource_name'), # Added
            'search_indexer_name': project_data.get('search_indexer_name'),     # Added
            'icon': project_data.get('icon'),                                   # Added for project icon
            'role': project_data.get('role', 'owner'), # Example field
            'environment': project_data.get('environment', {}), # Example field
            'createdAt': project_data.get('created_at', datetime.now(timezone.utc).isoformat()),
            'updatedAt': project_data.get('updated_at', datetime.now(timezone.utc).isoformat()),
        }
        # Validate required fields
        if not all([
            project_doc.get('name'),
            project_doc.get('storage_container_uploads'),
            project_doc.get('storage_container_input'),
            project_doc.get('storage_container_output'),
            project_doc.get('search_index_name'),
            project_doc.get('search_datasource_name'),
            project_doc.get('search_indexer_name')
        ]):
             # Log which fields are missing for easier debugging
             missing = [k for k, v in project_doc.items() if not v and k in [
                 'name', 'storage_container_uploads', 'storage_container_input',
                 'storage_container_output', 'search_index_name',
                 'search_datasource_name', 'search_indexer_name']]
             raise ValueError(f"Project data is incomplete. Missing required fields: {', '.join(missing)}")

        resp = await self.container_client.upsert_item(project_doc)
        return resp

    async def get_projects(self, user_id):
        """Retrieves all projects for a given user."""
        # Check if container client is initialized
        if not self.container_client:
            print(f"Warning: Container client not initialized when fetching projects for user {user_id}")
            return []

        try:
            parameters = [
                {'name': '@userId', 'value': user_id}
            ]
            query = "SELECT * FROM c WHERE c.userId = @userId ORDER BY c.createdAt DESC"
            projects = []
            async for item in self.container_client.query_items(query=query, parameters=parameters):
                projects.append(item)
            print(f"Successfully fetched {len(projects)} projects for user {user_id}")
            return projects
        except Exception as e:
            print(f"Error fetching projects for user {user_id}: {e}")
            # Return empty list instead of raising exception
            return []

    async def get_project(self, user_id, project_id):
        """Retrieves a specific project by ID for a user."""
        # Check if container client is initialized
        if not self.container_client:
            print(f"Warning: Container client not initialized when fetching project {project_id} for user {user_id}")
            return None

        try:
            parameters = [
                {'name': '@projectId', 'value': project_id},
                {'name': '@userId', 'value': user_id}
            ]
            query = "SELECT * FROM c WHERE c.id = @projectId AND c.userId = @userId"
            projects = []
            async for item in self.container_client.query_items(query=query, parameters=parameters):
                projects.append(item)

            if len(projects) == 0:
                return None
            else:
                return projects[0]
        except Exception as e:
            print(f"Error fetching project {project_id} for user {user_id}: {e}")
            return None

    async def get_project_by_id(self, project_id):
        """
        Retrieves a specific project by ID regardless of user ID.
        This is useful for deployment scripts that need to update project resources.
        """
        # Check if container client is initialized
        if not self.container_client:
            cosmos_logger.warning(f"Warning: Container client not initialized when fetching project {project_id}")
            return None

        try:
            parameters = [
                {'name': '@projectId', 'value': project_id}
            ]
            query = "SELECT * FROM c WHERE c.id = @projectId"
            projects = []
            async for item in self.container_client.query_items(
                query=query,
                parameters=parameters,
                partition_key=None  # Query across all partition keys
            ):
                projects.append(item)

            if len(projects) == 0:
                cosmos_logger.warning(f"Project not found for ID: {project_id}")
                return None
            else:
                cosmos_logger.info(f"Found project {project_id} by ID")
                return projects[0]
        except Exception as e:
            cosmos_logger.error(f"Error fetching project {project_id}: {e}")
            return None

    async def update_project(self, user_id, project_id, project_data):
        """Updates an existing project document in Cosmos DB."""
        # Ensure the project exists and belongs to the user
        existing_project = await self.get_project(user_id, project_id)
        if not existing_project:
            return None

        # Update the project document
        project_data['updatedAt'] = datetime.now(timezone.utc).isoformat()

        # Ensure required fields are present
        project_data['type'] = 'project'
        project_data['userId'] = user_id

        # Upsert the updated project document
        resp = await self.container_client.upsert_item(project_data)
        return resp

    async def create_document(self, document_data):
        """Creates a generic document in Cosmos DB."""
        # Ensure the document has an ID
        if 'id' not in document_data:
            document_data['id'] = str(uuid.uuid4())

        # Add timestamps if not present
        if 'createdAt' not in document_data:
            document_data['createdAt'] = datetime.now(timezone.utc).isoformat()
        if 'updatedAt' not in document_data:
            document_data['updatedAt'] = datetime.now(timezone.utc).isoformat()

        # Upsert the document
        resp = await self.container_client.upsert_item(document_data)
        return resp

    async def delete_project(self, user_id, project_id):
        """Deletes a project document."""
        # Note: Deleting the project document doesn't delete associated Azure resources (like container)
        # Additional cleanup logic would be needed elsewhere if required.
        try:
            resp = await self.container_client.delete_item(item=project_id, partition_key=user_id)
            return resp
        except exceptions.CosmosResourceNotFoundError:
            # If item doesn't exist, consider it successfully deleted in this context
            return True
        except Exception as e:
            # Log or handle other potential errors
            print(f"Error deleting project {project_id} for user {user_id}: {e}")
            raise

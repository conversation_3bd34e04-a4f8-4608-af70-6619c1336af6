"""
WebSocket service for monitoring blob storage and search index status.
This module provides a FastAPI application with WebSocket endpoints , it can be independent or integrated with the qurant app , as
done in this setup.
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Set, Any, Optional

import httpx
from azure.storage.blob.aio import BlobServiceClient
# Import necessary client
from backend.history.cosmosdbservice import CosmosConversationClient
# from quart import current_app # Keep commented if not using Quart context directly
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, status, Query # Added Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from starlette.websockets import WebSocketState

# Configure logging
logger = logging.getLogger(__name__)

# Configuration classes
class WebSocketConfig(BaseModel):
    """Configuration for WebSocket service (Global Fallback/Base)"""
    storage_account: str
    container_name: str # Default/Global Uploads container (used by update_blobs_for_project if proj_config lacks it)
    container_name_input: Optional[str] = "" # Default/Global Input container
    container_name_output: Optional[str] = "" # Default/Global Output container
    sas_token: str
    search_service: str
    index_name: str # Default/Global Index name
    indexer_name: Optional[str] = "" # Default/Global Indexer name
    search_api_key: str

# Create FastAPI app
app = FastAPI(title="File Monitor WebSocket Service")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# --- Global State ---
# Holds the global configuration passed during initialization
global_config: Optional[WebSocketConfig] = None
# Stores active WebSocket connections, keyed by type ('blob', 'index', 'input', 'output')
# then by projectId (Optional[str] - None for global/unspecified), value is a Set of WebSockets
project_connections: Dict[str, Dict[Optional[str], Set[WebSocket]]] = {
    "blob": {}, "index": {}, "input": {}, "output": {}
}
# Stores last activity time for heartbeat/timeout management
connection_last_activity: Dict[WebSocket, datetime] = {}
# Stores cached data per project
# Outer key: projectId (Optional[str]), Inner key: cache type (str), Value: cache data
project_caches: Dict[Optional[str], Dict[str, Any]] = {}
# Background task handle
update_task: Optional[asyncio.Task] = None
# Global variable to hold the CosmosDB client instance
cosmos_client: Optional[CosmosConversationClient] = None
# Map project IDs to user IDs
project_user_map: Dict[str, str] = {}
# --- End Global State ---

# --- Helper Functions ---
def add_connection(ws_type: str, project_id: Optional[str], websocket: WebSocket):
    """Adds a WebSocket connection to the project-specific set."""
    if project_id not in project_connections[ws_type]:
        project_connections[ws_type][project_id] = set()
    project_connections[ws_type][project_id].add(websocket)
    logger.info(f"Client connected to {ws_type} WebSocket (Project: {project_id}). Total for project: {len(project_connections[ws_type][project_id])}")

def remove_connection(ws_type: str, project_id: Optional[str], websocket: WebSocket):
    """Removes a WebSocket connection from the project-specific set."""
    if project_id in project_connections[ws_type]:
        project_connections[ws_type][project_id].discard(websocket)
        if not project_connections[ws_type][project_id]: # Remove project ID if set is empty
            del project_connections[ws_type][project_id]
    logger.info(f"Client disconnected from {ws_type} WebSocket (Project: {project_id}).")
    if websocket in connection_last_activity:
        del connection_last_activity[websocket]

def get_project_cache(project_id: Optional[str]) -> Dict[str, Any]:
    """Gets or initializes the cache dictionary for a given project ID."""
    if project_id not in project_caches:
        project_caches[project_id] = {
            "blob": {},      # Dict[str, Dict[str, Any]] - Cache for the main 'uploads' container
            "index": set(),      # Set[str] - Cache for indexed document keys/ids
            "input": [],     # List[Dict[str, Any]] - Cache for the 'input' container
            "output": []     # List[Dict[str, Any]] - Cache for the 'output' container
        }
    return project_caches[project_id]

async def get_project_config_from_db(user_id: str, project_id: str) -> Optional[Dict[str, Any]]:
    """
    Fetches project-specific config (container names, index name) using the global cosmos_client.
    Uses get_project_by_id which doesn't require user ownership check, as WebSocket service
    needs read-only access to project configurations for all connected clients.
    """
    if not cosmos_client:
        logger.error("CosmosDB client not available in WebSocket service.")
        return None

    try:
        logger.debug(f"Attempting to fetch config for project {project_id} (user: {user_id}) from DB.")
        
        # Always use get_project_by_id for the WebSocket service
        # This is appropriate because:
        # 1. WebSocket service only needs read access to project configuration
        # 2. The service needs to monitor resources for all connected projects
        # 3. User authentication/authorization is handled at the WebSocket connection level
        logger.debug(f"Using get_project_by_id for project {project_id}")
        project_doc = await cosmos_client.get_project_by_id(project_id=project_id)

        if project_doc:
            logger.debug(f"Fetched config for project {project_id}: {project_doc}")

            # Get environment variables from the project document
            env_vars = project_doc.get("environment", {})

            # Map DB fields to expected config keys, using project-specific values first, then falling back to global
            return {
                # Storage-related configuration
                "storage_account": project_doc.get("storage_account_name") or
                                  env_vars.get("STORAGE_ACCOUNT_NAME") or
                                  (global_config.storage_account if global_config else None),

                "container_name": project_doc.get("storage_container_uploads") or
                                 env_vars.get("STORAGE_CONTAINER_UPLOADS"),

                "container_name_input": project_doc.get("storage_container_input") or
                                       env_vars.get("STORAGE_CONTAINER_INPUT"),

                "container_name_output": project_doc.get("storage_container_output") or
                                        env_vars.get("STORAGE_CONTAINER_OUTPUT"),

                "sas_token": env_vars.get("STORAGE_ACCOUNT_SAS_TOKEN") or
                            (global_config.sas_token if global_config else None),

                # Search-related configuration
                "search_service": project_doc.get("search_service_name") or
                                 env_vars.get("SEARCH_SERVICE_NAME") or
                                 (global_config.search_service if global_config else None),

                "index_name": project_doc.get("search_index_name") or
                             env_vars.get("SEARCH_INDEX_NAME"),

                "indexer_name": project_doc.get("search_indexer_name") or
                               env_vars.get("SEARCH_INDEXER_NAME"),

                "search_api_key": env_vars.get("AZURE_SEARCH_KEY") or
                                 (global_config.search_api_key if global_config else None),
            }

        logger.warning(f"Project {project_id} not found in Cosmos DB (user: {user_id}).")
        return None
    except AttributeError as e:
         logger.error(f"CosmosDB client does not have the expected method for project {project_id}: {e}")
         return None
    except Exception as e:
        logger.error(f"Failed to fetch config for project {project_id} from DB: {e}")
        import traceback
        logger.debug(f"Traceback: {traceback.format_exc()}")
        return None

def formatSasToken(sasToken: str):
    """Ensures the SAS token starts with a '?'."""
    if not sasToken: return ''
    return sasToken if sasToken.startswith('?') else f"?{sasToken}"

async def update_blobs_for_project(project_id: Optional[str], proj_config: Dict[str, Any]):
    """Update blob cache for a specific project's 'uploads' container and notify relevant clients"""
    # Ensure project_id is not None before using it in log messages
    if project_id is None:
        logger.warning("Cannot update blobs: project_id is None")
        return

    proj_cache = get_project_cache(project_id)
    current_blob_cache = proj_cache["blob"]
    current_index_cache = proj_cache["index"]

    container_name = proj_config.get("container_name")
    if not container_name:
        logger.warning(f"No 'uploads' container name configured for project {project_id}. Skipping blob update.")
        return

    blob_service = None
    try:
        # Use project-specific storage account and SAS token if available, otherwise fall back to global
        storage_account = proj_config.get("storage_account")
        sas_token = proj_config.get("sas_token")

        # Fall back to global config if project-specific values are not available
        if not storage_account:
            storage_account = global_config.storage_account if global_config else ""
            logger.warning(f"Project {project_id} does not have a storage account specified, using global: {storage_account}")

        if not sas_token:
            sas_token = global_config.sas_token if global_config else ""
            logger.warning(f"Project {project_id} does not have a SAS token specified, using global token")

        if not sas_token or not storage_account:
            logger.error(f"Storage account/SAS token missing for project {project_id}. Cannot update blobs.")
            return

        sas_token = formatSasToken(sas_token)

        logger.info(f"Accessing storage account {storage_account} for project {project_id}")
        blob_service = BlobServiceClient(f"https://{storage_account}.blob.core.windows.net{sas_token}")
        container = blob_service.get_container_client(container_name)

        try:
            blobs = []
            async for blob in container.list_blobs():
                is_indexed = blob.name in current_index_cache
                blobs.append({
                    "name": blob.name,
                    "size": blob.size,
                    "lastModified": blob.last_modified.isoformat() if blob.last_modified else None,
                    "indexed": is_indexed
                })

            current_blob_dict = {blob["name"]: blob for blob in blobs}
            if current_blob_dict != current_blob_cache:
                proj_cache["blob"] = current_blob_dict
                message = {
                    "type": "blob_update",
                    "data": list(proj_cache["blob"].values()),
                    "timestamp": datetime.now().isoformat()
                }
                connections_to_notify = project_connections["blob"].get(project_id, set())
                if connections_to_notify:
                    logger.info(f"Sending blob updates to {len(connections_to_notify)} clients for project {project_id}")
                    for conn in list(connections_to_notify):
                         try:
                             if conn.client_state == WebSocketState.CONNECTED:
                                 await conn.send_json(message)
                             else:
                                 remove_connection("blob", project_id, conn)
                         except Exception as e:
                             logger.error(f"Error sending blob update to client {id(conn)} for project {project_id}: {e}")
                             remove_connection("blob", project_id, conn)
        finally:
            await container.close()
    except Exception as e:
        logger.error(f"Error updating blobs for project {project_id}: {e}")
    finally:
        if blob_service:
            await blob_service.close()

async def update_indexes_for_project(project_id: Optional[str], proj_config: Dict[str, Any]):
    """Update index cache for a specific project and notify relevant clients"""
    # Ensure project_id is not None before using it in log messages
    if project_id is None:
        logger.warning("Cannot update indexes: project_id is None")
        return

    proj_cache = get_project_cache(project_id)
    current_index_cache = proj_cache["index"]
    current_blob_cache = proj_cache["blob"]

    # Get project-specific search configuration
    index_name = proj_config.get("index_name")
    search_service = proj_config.get("search_service")
    search_api_key = proj_config.get("search_api_key")

    # Fall back to global config if project-specific values are not available
    if not search_service:
        search_service = global_config.search_service if global_config else None
        logger.warning(f"Project {project_id} does not have a search service specified, using global: {search_service}")

    if not search_api_key:
        search_api_key = global_config.search_api_key if global_config else None
        logger.warning(f"Project {project_id} does not have a search API key specified, using global key")

    if not index_name or not search_service or not search_api_key:
        logger.warning(f"Search configuration missing for project {project_id}. Skipping index update.")
        return

    logger.info(f"Accessing search service {search_service} with index {index_name} for project {project_id}")

    try:
        # Get the document keys without using $select to avoid field name issues
        url = f"https://{search_service}.search.windows.net/indexes/{index_name}/docs"

        # Get the container name for this project (for logging only)
        container_name = proj_config.get("container_name")
        logger.info(f"Project {project_id} uses container: {container_name}")

        # Get the list of blob names from the project's blob cache
        blob_names = set(current_blob_cache.keys())
        logger.info(f"Project {project_id} has {len(blob_names)} blobs in cache")

        # Set up the search parameters
        params = {
            "api-version": "2023-11-01",
            "search": "*",
            "$top": 1000
        }

        headers = {"Content-Type": "application/json", "api-key": search_api_key}

        async with httpx.AsyncClient(timeout=30.0) as client:
            client._limits = httpx.Limits(max_keepalive_connections=5, max_connections=10)
            response = await client.get(url, params=params, headers=headers)

            if response.status_code == 200:
                data = response.json()
                total_docs = len(data.get("value", []))
                logger.info(f"Retrieved {total_docs} documents from search index {index_name} for project {project_id}")

                # Extract unique titles from the documents
                indexed_doc_keys = set()

                # Process each document from the search results
                for doc in data.get("value", []):
                    # Try different possible field names for the filename
                    filename = None

                    # Try to extract the filename from various fields
                    for field_name in ["title", "metadata_storage_name", "filename", "name", "id", "filepath"]:
                        if field_name in doc and doc[field_name]:
                            filename = doc[field_name]
                            # If it's a path, extract just the filename
                            if "/" in filename:
                                filename = filename.split("/")[-1]
                            break

                    # If we found a filename, add it to our set
                    if filename:
                        indexed_doc_keys.add(filename)
                    else:
                        # Last resort: try to find any field that might be a filename
                        for key, value in doc.items():
                            if isinstance(value, str) and ("." in value or len(value) > 5):
                                indexed_doc_keys.add(value)
                                break

                logger.info(f"Found {len(indexed_doc_keys)} unique document titles in index {index_name} for project {project_id}")

                # Only update and notify if the index cache has changed
                if indexed_doc_keys != current_index_cache:
                    proj_cache["index"] = indexed_doc_keys
                    changed_blob_status = False

                    # Update the indexed status of each blob
                    for blob_name, blob_data in current_blob_cache.items():
                        is_indexed = blob_name in indexed_doc_keys
                        if blob_data.get("indexed") != is_indexed:
                            blob_data["indexed"] = is_indexed
                            changed_blob_status = True

                    # Send index updates to connected clients
                    index_message = {
                        "type": "index_update",
                        "data": list(proj_cache["index"]),
                        "timestamp": datetime.now().isoformat()
                    }
                    connections_to_notify_index = project_connections["index"].get(project_id, set())
                    if connections_to_notify_index:
                        logger.info(f"Sending index updates to {len(connections_to_notify_index)} clients for project {project_id}")
                        for conn in list(connections_to_notify_index):
                            try:
                                if conn.client_state == WebSocketState.CONNECTED:
                                    await conn.send_json(index_message)
                                else:
                                    remove_connection("index", project_id, conn)
                            except Exception as e:
                                logger.error(f"Error sending index update to client {id(conn)} for project {project_id}: {e}")
                                remove_connection("index", project_id, conn)

                    # If blob status changed, send blob updates to connected clients
                    if changed_blob_status:
                        blob_message = {
                            "type": "blob_update",
                            "data": list(proj_cache["blob"].values()),
                            "timestamp": datetime.now().isoformat()
                        }
                        connections_to_notify_blob = project_connections["blob"].get(project_id, set())
                        if connections_to_notify_blob:
                            logger.info(f"Sending blob status updates to {len(connections_to_notify_blob)} clients for project {project_id} due to index change.")
                            for conn in list(connections_to_notify_blob):
                                try:
                                    if conn.client_state == WebSocketState.CONNECTED:
                                        await conn.send_json(blob_message)
                                    else:
                                        remove_connection("blob", project_id, conn)
                                except Exception as e:
                                    logger.error(f"Error sending blob+index update to client {id(conn)} for project {project_id}: {e}")
                                    remove_connection("blob", project_id, conn)
            else:
                logger.error(f"Error fetching index docs for project {project_id}: {response.status_code} - {response.text}")
    except Exception as e:
        logger.error(f"Error updating index for project {project_id}: {e}")

async def update_container_for_project(project_id: Optional[str], proj_config: Dict[str, Any], container_type: str):
    """Generic function to update a specific container cache ('input' or 'output') for a project."""
    if container_type not in ["input", "output"]:
        logger.error(f"Invalid container type specified: {container_type}")
        return

    # Ensure project_id is not None before using it in log messages
    if project_id is None:
        logger.warning(f"Cannot update {container_type} container: project_id is None")
        return

    proj_cache = get_project_cache(project_id)
    current_cache = proj_cache[container_type]
    container_key = f"container_name_{container_type}"
    container_name = proj_config.get(container_key)

    if not container_name:
        logger.warning(f"No '{container_type}' container name configured for project {project_id}. Skipping update.")
        return

    blob_service = None
    try:
        # Use project-specific storage account and SAS token if available, otherwise fall back to global
        storage_account = proj_config.get("storage_account")
        sas_token = proj_config.get("sas_token")

        # Fall back to global config if project-specific values are not available
        if not storage_account:
            storage_account = global_config.storage_account if global_config else ""
            logger.warning(f"Project {project_id} does not have a storage account specified for {container_type} container, using global: {storage_account}")

        if not sas_token:
            sas_token = global_config.sas_token if global_config else ""
            logger.warning(f"Project {project_id} does not have a SAS token specified for {container_type} container, using global token")

        if not sas_token or not storage_account:
            logger.error(f"Storage account/SAS token missing for project {project_id}. Cannot update {container_type} container.")
            return

        sas_token = formatSasToken(sas_token)

        logger.info(f"Accessing storage account {storage_account} for project {project_id} {container_type} container")
        blob_service = BlobServiceClient(f"https://{storage_account}.blob.core.windows.net{sas_token}")
        container = blob_service.get_container_client(container_name)

        try:
            blobs = []
            async for blob in container.list_blobs():
                blobs.append({
                    "name": blob.name,
                    "size": blob.size,
                    "lastModified": blob.last_modified.isoformat() if blob.last_modified else None
                })

            if container_type == "output":
                 blobs.sort(key=lambda x: x.get("lastModified", ""), reverse=True)

            if blobs != current_cache:
                proj_cache[container_type] = blobs
                message = {
                    "type": f"{container_type}_container_update",
                    "data": proj_cache[container_type],
                    "timestamp": datetime.now().isoformat()
                }
                connections_to_notify = project_connections[container_type].get(project_id, set())
                if connections_to_notify:
                    logger.info(f"Sent {container_type} container updates to {len(connections_to_notify)} clients for project {project_id}")
                    for conn in list(connections_to_notify):
                        try:
                            if conn.client_state == WebSocketState.CONNECTED:
                                await conn.send_json(message)
                            else:
                                remove_connection(container_type, project_id, conn)
                        except Exception as e:
                            logger.error(f"Error sending {container_type} container update to client {id(conn)} for project {project_id}: {e}")
                            remove_connection(container_type, project_id, conn)
        finally:
            await container.close()
    except Exception as e:
        logger.error(f"Error updating {container_type} container for project {project_id}: {e}")
    finally:
        if blob_service:
            await blob_service.close()

async def update_input_container_for_project(project_id: Optional[str], proj_config: Dict[str, Any]):
     await update_container_for_project(project_id, proj_config, "input")

async def update_output_container_for_project(project_id: Optional[str], proj_config: Dict[str, Any]):
     await update_container_for_project(project_id, proj_config, "output")

async def update_loop():
    """Periodic update loop that handles multiple projects."""
    while True:
        try:
            active_projects = set()
            for ws_type in project_connections:
                active_projects.update(project_connections[ws_type].keys())

            logger.debug(f"Active projects with WebSocket connections: {active_projects}")

            update_tasks = []
            for project_id in active_projects:
                if project_id is None:
                    logger.debug("Skipping updates for global/None project ID in this loop.")
                    continue

                # Get the user_id associated with this project_id
                user_id_for_project = project_user_map.get(project_id)

                if not user_id_for_project:
                    logger.warning(f"No user_id found mapped to project {project_id}. Cannot fetch config.")
                    continue

                proj_config_dict = await get_project_config_from_db(user_id_for_project, project_id)

                if proj_config_dict and global_config:
                    update_tasks.append(update_blobs_for_project(project_id, proj_config_dict))
                    update_tasks.append(update_indexes_for_project(project_id, proj_config_dict))
                    update_tasks.append(update_input_container_for_project(project_id, proj_config_dict))
                    update_tasks.append(update_output_container_for_project(project_id, proj_config_dict))
                else:
                    logger.warning(f"Could not retrieve configuration for project {project_id}. Skipping updates.")

            if update_tasks:
                await asyncio.gather(*update_tasks)
            else:
                 logger.debug("No active projects with connections found to update.")

        except Exception as e:
            logger.error(f"Error in main update loop: {e}")

        await asyncio.sleep(10)

# Heartbeat function defined before it's used in handle_websocket_connection
async def websocket_heartbeat(websocket: WebSocket):
    """Send periodic heartbeats to keep connection alive"""
    try:
        while True:
            await asyncio.sleep(50) # Send heartbeat every 50 seconds
            if websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await websocket.send_json({
                        "type": "heartbeat",
                        "timestamp": datetime.now().isoformat()
                    })
                    logger.debug(f"Sent heartbeat to {id(websocket)}")
                    connection_last_activity[websocket] = datetime.now()
                except Exception as e:
                    logger.error(f"Error sending heartbeat to {id(websocket)}: {e}")
                    break # Stop sending on error
            else:
                break # Stop if connection is closed
    except asyncio.CancelledError:
        logger.debug(f"Heartbeat task cancelled for {id(websocket)}")
    except Exception as e:
        logger.error(f"Error in heartbeat task for {id(websocket)}: {e}")
    finally:
        # Clean up activity tracking if task ends unexpectedly
        # remove_connection already handles this on normal disconnect
        if websocket in connection_last_activity:
             pass # Let remove_connection handle cleanup

# --- WebSocket Endpoints ---
async def handle_websocket_connection(websocket: WebSocket, ws_type: str, project_id: Optional[str]):
    """Handles common logic for WebSocket connections."""
    await websocket.accept()

    # --- User Authentication for WebSocket ---
    # In development mode, we'll use a default user ID to allow the websocket to work
    # In production, this should be replaced with proper authentication
    deployment_env = os.environ.get("DEPLOYMENT_ENV", "development")

    if deployment_env.lower() == "development":
        # Use a default user ID for development
        user_id = "development_user"
        logger.info(f"Using development user ID for WebSocket: {user_id}")
    else:
        # In production, we should extract the user ID from authentication
        # This is a placeholder that should be replaced with proper auth logic
        user_id = "placeholder_user_id"
        logger.warning("Using placeholder user ID in production environment. This should be replaced with proper authentication.")

    # Map the project ID to the user ID
    # Note: This mapping is informational only since we use get_project_by_id
    # which doesn't require user ownership verification
    if project_id:
        project_user_map[project_id] = user_id
        logger.info(f"Mapped project {project_id} to user {user_id} (informational only)")


    add_connection(ws_type, project_id, websocket)
    connection_last_activity[websocket] = datetime.now()
    heartbeat_task = None # Initialize heartbeat_task variable

    try:
        await websocket.send_json({
            "type": "connection_established",
            "message": f"Connected to {ws_type} WebSocket",
            "timestamp": datetime.now().isoformat()
        })

        # Send initial cache data for the specific project
        proj_cache = get_project_cache(project_id)
        initial_cache_data = proj_cache.get(ws_type, [])
        initial_message_type = f"{ws_type}_container_update" if ws_type in ["input", "output"] else f"{ws_type}_update"

        # Convert set to list for index data
        if ws_type == "index":
            initial_cache_data = list(initial_cache_data)
            logger.info(f"Sending initial {len(initial_cache_data)} indexed files to client for project {project_id}")

        # Convert dict values to list for blob data
        if ws_type == "blob":
            initial_cache_data = list(initial_cache_data.values())
            logger.info(f"Sending initial {len(initial_cache_data)} blobs to client for project {project_id}")

        await websocket.send_json({
            "type": initial_message_type,
            "data": initial_cache_data,
            "timestamp": datetime.now().isoformat()
        })

        # Create heartbeat task using the now-defined function
        heartbeat_task = asyncio.create_task(websocket_heartbeat(websocket))

        while True:
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=120)
                connection_last_activity[websocket] = datetime.now()

                if data == "ping" or data.startswith('{"type":"ping"'):
                    await websocket.send_json({"type": "pong", "timestamp": datetime.now().isoformat()})
                elif data == "refresh":
                    logger.info(f"Received refresh request for {ws_type} (Project: {project_id})")
                    # Get the user_id associated with this project_id
                    user_id_for_project = project_user_map.get(project_id)
                    if not user_id_for_project:
                        logger.warning(f"No user_id found mapped to project {project_id}. Cannot fetch config.")
                        continue

                    proj_config_dict = await get_project_config_from_db(user_id_for_project, project_id) if project_id else None
                    if proj_config_dict and global_config:
                         if ws_type == "blob": await update_blobs_for_project(project_id, proj_config_dict)
                         elif ws_type == "index": await update_indexes_for_project(project_id, proj_config_dict)
                         elif ws_type == "input": await update_input_container_for_project(project_id, proj_config_dict)
                         elif ws_type == "output": await update_output_container_for_project(project_id, proj_config_dict)
                    else:
                         logger.warning(f"Cannot refresh {ws_type} for project {project_id}: Config not found.")

            except asyncio.TimeoutError:
                if websocket.client_state != WebSocketState.CONNECTED:
                    logger.warning(f"WebSocket client {id(websocket)} timed out and disconnected")
                    break
            except WebSocketDisconnect:
                break
    except WebSocketDisconnect:
         logger.info(f"Client {id(websocket)} disconnected from {ws_type} WebSocket (Project: {project_id})")
    except Exception as e:
        logger.error(f"Error in {ws_type} WebSocket (Project: {project_id}): {e}")
    finally:
        # Clean up user mapping when connection closes
        if project_id and project_id in project_user_map:
             # Check if any other connection uses this mapping before deleting
             any_other_connection = False
             for type_connections in project_connections.values():
                 if project_id in type_connections and len(type_connections[project_id]) > 1: # More than just the one being removed
                     any_other_connection = True
                     break
             if not any_other_connection:
                 logger.info(f"Removing user mapping for project {project_id}")
                 del project_user_map[project_id]

        remove_connection(ws_type, project_id, websocket)
        if heartbeat_task and not heartbeat_task.done():
            heartbeat_task.cancel()

@app.websocket("/ws/blobs")
async def blobs_websocket(websocket: WebSocket, projectId: Optional[str] = Query(None)):
    await handle_websocket_connection(websocket, "blob", projectId)

@app.websocket("/ws/indexes")
async def indexes_websocket(websocket: WebSocket, projectId: Optional[str] = Query(None)):
     await handle_websocket_connection(websocket, "index", projectId)

@app.websocket("/ws/input-container")
async def input_container_websocket(websocket: WebSocket, projectId: Optional[str] = Query(None)):
     await handle_websocket_connection(websocket, "input", projectId)

@app.websocket("/ws/output-container")
async def output_container_websocket(websocket: WebSocket, projectId: Optional[str] = Query(None)):
     await handle_websocket_connection(websocket, "output", projectId)

# --- FastAPI Endpoints ---
@app.post("/api/configure")
async def configure(websocket_config: WebSocketConfig):
    """Configure the WebSocket service (sets global fallback config)"""
    global global_config, update_task
    logger.info(f"Configuring GLOBAL WebSocket settings with account {websocket_config.storage_account}")
    global_config = websocket_config

    if update_task is None or update_task.done():
        update_task = asyncio.create_task(update_loop())
        logger.info("Started update loop")

    return {"status": "success", "message": "WebSocket service GLOBAL config set"}

@app.post("/api/refresh")
async def refresh(project_id: Optional[str] = None, user_id: Optional[str] = None):
    """Force refresh caches for a specific project or all projects"""
    logger.info(f"Received refresh request for project: {project_id if project_id else 'ALL'}")

    # Get the deployment environment
    deployment_env = os.environ.get("DEPLOYMENT_ENV", "development")

    # Use provided user_id if available, otherwise use default based on environment
    user_id_for_refresh = user_id

    if not user_id_for_refresh:
        # In development mode, use a default user ID
        if deployment_env.lower() == "development":
            user_id_for_refresh = "development_user"
            logger.info(f"Using development user ID for refresh API: {user_id_for_refresh}")
        else:
            # In production, this should be replaced with proper authentication
            # This is a placeholder that should be replaced with proper auth logic
            user_id_for_refresh = "placeholder_user_id"
            logger.warning("Using placeholder user ID in production environment. This should be replaced with proper authentication.")

    if project_id:
        # Use trigger_project_update function to handle the refresh
        result = await trigger_project_update(project_id, update_type=None, user_id=user_id_for_refresh)
        return result
    else:
        logger.warning("Refresh ALL projects not fully implemented yet.")
        return {"status": "pending", "message": "Refresh for all projects needs implementation"}

@app.post("/api/project-update")
async def project_update(project_id: str, user_id: Optional[str] = None):
    """
    Force update of a project's configuration and refresh all caches.
    This endpoint is intended to be called after a project is deployed or updated.
    """
    logger.info(f"Received project update request for project: {project_id} with user_id: {user_id}")

    if not project_id:
        return {"status": "error", "message": "Project ID is required"}

    # Clear any existing cache for this project
    if project_id in project_caches:
        logger.info(f"Clearing cache for project {project_id} to force refresh")
        del project_caches[project_id]

    # Use trigger_project_update function to handle the update
    result = await trigger_project_update(project_id, update_type=None, user_id=user_id)
    return result

@app.get("/api/status")
async def status():
    """Get current status"""
    return {
        "configured": global_config is not None,
        "connections": {
            "blobs": sum(len(s) for s in project_connections["blob"].values()),
            "indexes": sum(len(s) for s in project_connections["index"].values()),
            "input_container": sum(len(s) for s in project_connections["input"].values()),
            "output_container": sum(len(s) for s in project_connections["output"].values()),
        },
        "projects_tracked": {
            "blob": list(project_connections["blob"].keys()),
            "index": list(project_connections["index"].keys()),
            "input": list(project_connections["input"].keys()),
            "output": list(project_connections["output"].keys()),
        },
        "cache_counts": {
            "blobs": sum(len(cache.get("blob", {})) for cache in project_caches.values()),
            "indexes": sum(len(cache.get("index", set())) for cache in project_caches.values()),
            "input_container": sum(len(cache.get("input", [])) for cache in project_caches.values()),
            "output_container": sum(len(cache.get("output", [])) for cache in project_caches.values()),
        },
        "update_task_running": update_task is not None and not update_task.done()
    }

# --- Manual Trigger Function ---
async def trigger_project_update(project_id: str, update_type: str = None, user_id: str = None):
    """
    Manually triggers an update check for a specific project and data type.
    Args:
        project_id: The ID of the project to update.
        update_type: The type of update ('uploads', 'index', 'input', 'output'). If None, update all.
        user_id: Optional user ID to use for the update. If None, use the mapped user ID.
    """
    if not project_id:
        logger.error("Cannot trigger update for empty project ID")
        return {"status": "error", "message": "Project ID is required"}

    # This function is called internally by app.py after uploads etc.
    # First, check if we have a user ID mapped to this project or use the provided one
    user_id_for_trigger = user_id or project_user_map.get(project_id)

    # If no user ID is mapped, use a default one based on the environment
    if not user_id_for_trigger:
        deployment_env = os.environ.get("DEPLOYMENT_ENV", "development")

        if deployment_env.lower() == "development":
            # Use a default user ID for development
            user_id_for_trigger = "development_user"
            logger.info(f"Using development user ID for trigger: {user_id_for_trigger}")
            # Add this mapping for future use
            project_user_map[project_id] = user_id_for_trigger
        else:
            # In production, we should have a proper user ID
            logger.error(f"Cannot trigger update for project {project_id}: User ID mapping not found.")
            return {"status": "error", "message": "User ID mapping not found"} # Cannot proceed without a user ID in production

    # Clear any existing cache for this project to force a fresh fetch
    if project_id in project_caches:
        logger.info(f"Clearing cache for project {project_id} to force refresh")
        del project_caches[project_id]

    logger.info(f"Manual trigger received for project {project_id}, type: {update_type or 'ALL'} (user: {user_id_for_trigger})")
    proj_config_dict = await get_project_config_from_db(user_id_for_trigger, project_id)

    if not proj_config_dict:
        logger.error(f"Cannot trigger update for project {project_id}: Configuration not found.")
        return {"status": "error", "message": f"Configuration not found for project {project_id}"}

    # Log the project configuration for debugging
    logger.info(f"Project {project_id} configuration:")
    for key, value in proj_config_dict.items():
        if key in ["search_api_key", "sas_token"]:
            # Mask sensitive values
            masked_value = value[:5] + "..." if value and len(value) > 8 else "***"
            logger.info(f"  {key}: {masked_value}")
        else:
            logger.info(f"  {key}: {value}")

    try:
        # Update the project_user_map to ensure future updates use the correct user_id
        project_user_map[project_id] = user_id_for_trigger

        if update_type is None:
            # Update all types
            update_tasks = [
                update_blobs_for_project(project_id, proj_config_dict),
                update_indexes_for_project(project_id, proj_config_dict),
                update_input_container_for_project(project_id, proj_config_dict),
                update_output_container_for_project(project_id, proj_config_dict)
            ]
            await asyncio.gather(*update_tasks)
            return {"status": "success", "message": f"All updates triggered for project {project_id}"}
        elif update_type == 'uploads':
            await update_blobs_for_project(project_id, proj_config_dict)
        elif update_type == 'index':
            # Updating index might also affect blob status (indexed flag)
            await update_indexes_for_project(project_id, proj_config_dict)
        elif update_type == 'input':
            await update_input_container_for_project(project_id, proj_config_dict)
        elif update_type == 'output':
            await update_output_container_for_project(project_id, proj_config_dict)
        else:
            logger.warning(f"Unknown update type '{update_type}' requested for project {project_id}")
            return {"status": "error", "message": f"Unknown update type '{update_type}'"}

        return {"status": "success", "message": f"Update type '{update_type}' triggered for project {project_id}"}
    except Exception as e:
        logger.error(f"Error during triggered update for project {project_id} (type: {update_type}): {e}")
        return {"status": "error", "message": f"Error: {str(e)}"}


# --- Polling Fallbacks ---
@app.get("/api/poll/blobs")
async def poll_blobs(projectId: Optional[str] = None):
    """HTTP polling fallback for blob updates"""
    data = get_project_cache(projectId)["blob"]
    return {"type": "blob_update", "data": list(data.values()), "timestamp": datetime.now().isoformat()}

@app.get("/api/poll/indexes")
async def poll_indexes(projectId: Optional[str] = None):
    """HTTP polling fallback for index updates"""
    if projectId is None:
        logger.warning("No projectId provided for poll_indexes, returning empty list")
        return {"type": "index_update", "data": [], "timestamp": datetime.now().isoformat()}

    # Get the project cache
    proj_cache = get_project_cache(projectId)
    data = proj_cache["index"]

    # Log the number of indexed files being returned
    logger.info(f"Returning {len(data)} unique document titles for project {projectId} via HTTP polling")

    # If we have blob data, also log the total number of blobs for comparison
    blob_count = len(proj_cache["blob"]) if "blob" in proj_cache else 0
    if blob_count > 0:
        logger.info(f"Project {projectId} has {blob_count} blobs and {len(data)} indexed files")

    return {"type": "index_update", "data": list(data), "timestamp": datetime.now().isoformat()}

@app.get("/api/poll/input-container")
async def poll_input_container(projectId: Optional[str] = None):
    """HTTP polling fallback for input container updates"""
    data = get_project_cache(projectId)["input"]
    return {"type": "input_container_update", "data": data, "timestamp": datetime.now().isoformat()}

@app.get("/api/poll/output-container")
async def poll_output_container(projectId: Optional[str] = None):
    """HTTP polling fallback for output container updates"""
    data = get_project_cache(projectId)["output"]
    return {"type": "output_container_update", "data": data, "timestamp": datetime.now().isoformat()}

# --- Service Initialization and Shutdown ---
async def initialize_websocket_service(
    storage_account: str,
    container_name: str,
    container_name_input: str,
    container_name_output: str,
    sas_token: str,
    search_service: str,
    index_name: str,
    indexer_name: str,
    search_api_key: str,
    cosmos_db_client: Any # Pass the initialized CosmosDB client
):
    """Initialize the WebSocket service with GLOBAL configuration and DB client"""
    global global_config, update_task, cosmos_client

    cosmos_client = cosmos_db_client
    if not cosmos_client:
         logger.error("CosmosDB client was not provided to WebSocket service during initialization!")

    global_config = WebSocketConfig(
        storage_account=storage_account,
        container_name=container_name,
        container_name_input=container_name_input,
        container_name_output=container_name_output,
        sas_token=sas_token,
        search_service=search_service,
        index_name=index_name,
        indexer_name=indexer_name,
        search_api_key=search_api_key
    )
    logger.info(f"Initialized GLOBAL WebSocket config: Account={storage_account}, Input={container_name_input}, Output={container_name_output}, Index={index_name}")

    if update_task is None or update_task.done():
        update_task = asyncio.create_task(update_loop())
        logger.info("Started WebSocket service update loop")

async def shutdown_websocket_service():
    """Shutdown the WebSocket service"""
    global update_task

    all_connections = set().union(*[s for proj_dict in project_connections.values() for s in proj_dict.values()])
    logger.info(f"Shutting down WebSocket service. Closing {len(all_connections)} connections.")

    for ws in all_connections:
        try:
            if ws.client_state == WebSocketState.CONNECTED:
                await ws.send_json({
                    "type": "server_shutdown",
                    "message": "Server is shutting down, please reconnect later",
                    "timestamp": datetime.now().isoformat()
                })
                await ws.close(code=status.WS_1001_GOING_AWAY)
        except Exception as e:
            logger.error(f"Error closing WebSocket connection {id(ws)}: {e}")

    if update_task and not update_task.done():
        update_task.cancel()
        try:
            await update_task
        except asyncio.CancelledError:
            logger.info("WebSocket service update loop cancelled.")
        except Exception as e:
             logger.error(f"Error during update task cancellation: {e}")
    logger.info("WebSocket service shutdown complete.")

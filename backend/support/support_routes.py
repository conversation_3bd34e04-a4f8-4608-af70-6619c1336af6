from fastapi import APIRouter, HTTPException, Depends, Request, Form, File, UploadFile
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Optional
import logging
import json
import os
from azure.storage.blob.aio import BlobServiceClient
from azure.identity.aio import DefaultAzureCredential
from backend.support.support_service import SupportTicketService
from backend.support.email_service import SupportEmailService
from backend.auth.token_utils import extract_token_from_request, validate_token
from backend.auth.entra_auth import extract_token_from_request_with_easy_auth
from backend.settings import app_settings
from pydantic import BaseModel

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/support", tags=["support"])

# Security - make it optional to allow Azure Easy Auth
security = HTTPBearer(auto_error=False)

# Pydantic models
class SupportTicketCreate(BaseModel):
    title: str
    description: str
    category: str
    priority: str
    userId: str
    userEmail: str
    userName: str

# Global service instance
support_service: Optional[SupportTicketService] = None

async def get_support_service() -> SupportTicketService:
    """Get or create the support service instance"""
    global support_service
    
    if support_service is None:
        # Check CosmosDB settings
        if not app_settings.chat_history:
            raise HTTPException(status_code=500, detail="CosmosDB settings not configured")
        
        # Use the same configuration as RBAC service
        cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
        account_key = app_settings.chat_history.account_key
        database_name = app_settings.chat_history.database
        
        support_service = SupportTicketService(
            cosmosdb_endpoint=cosmos_endpoint,
            account_key=account_key,
            database_name=database_name
        )
        
        # Initialize the service
        initialized = await support_service.initialize()
        if not initialized:
            raise HTTPException(status_code=500, detail="Failed to initialize support service")
    
    return support_service

async def get_current_user(request: Request, credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """Extract user information from token - handles both standard Bearer tokens and Azure Easy Auth"""
    try:
        # First try to get token using Azure Easy Auth aware extraction
        token = extract_token_from_request_with_easy_auth(request)
        
        if not token:
            # If no token found via Easy Auth, fall back to standard Bearer token
            if credentials:
                token = credentials.credentials
            else:
                logger.error("No authentication token found in request")
                raise HTTPException(status_code=401, detail="No authentication token provided")
        
        logger.info(f"Token extracted successfully, validating...")
        is_valid, claims = await validate_token(token)
        
        if not is_valid or not claims:
            logger.error(f"Token validation failed - is_valid: {is_valid}, claims: {claims}")
            raise HTTPException(status_code=401, detail="Invalid or expired token")
        
        logger.info(f"Token validated successfully for user: {claims.get('email', 'unknown')}")
        return claims
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token verification failed with error: {type(e).__name__}: {e}")
        raise HTTPException(status_code=401, detail="Authentication failed")

async def save_attachment_to_blob(file: UploadFile, ticket_id: str) -> str:
    """Save file attachment to Azure Blob Storage"""
    try:
        # Get blob storage configuration
        storage_account_name = os.getenv("AZURE_STORAGE_ACCOUNT_NAME")
        sas_token = os.getenv("AZURE_STORAGE_CONTAINER_SAS_TOKEN")
        container_name = "support-attachments"
        
        if not storage_account_name or not sas_token:
            logger.warning("Azure Storage not configured, skipping file upload")
            return ""
        
        # Create blob service client with SAS token
        # Ensure SAS token starts with '?' if not already present
        if not sas_token.startswith('?'):
            sas_token = '?' + sas_token

        blob_service_client = BlobServiceClient(
            account_url=f"https://{storage_account_name}.blob.core.windows.net{sas_token}"
        )
        
        # Generate unique blob name
        blob_name = f"{ticket_id}/{file.filename}"
        
        # Upload file
        blob_client = blob_service_client.get_blob_client(
            container=container_name,
            blob=blob_name
        )
        
        # Read file content
        file_content = await file.read()
        
        # Upload to blob storage
        await blob_client.upload_blob(file_content, overwrite=True)
        
        # Return blob URL with SAS token for access
        blob_url = f"https://{storage_account_name}.blob.core.windows.net/{container_name}/{blob_name}{sas_token}"
        logger.info(f"Uploaded attachment {file.filename} for ticket {ticket_id}")
        return blob_url
        
    except Exception as e:
        logger.error(f"Error uploading attachment: {e}")
        return ""

@router.post("/tickets")
async def create_support_ticket(
    request: Request,
    title: str = Form(...),
    description: str = Form(...),
    category: str = Form(...),
    priority: str = Form(...),
    userId: str = Form(...),
    userEmail: str = Form(...),
    userName: str = Form(...),
    fileCount: str = Form("0"),
    files: List[UploadFile] = File(default=[]),
    current_user = Depends(get_current_user),
    service: SupportTicketService = Depends(get_support_service)
):
    """Create a new support ticket"""
    # Log request headers for debugging
    logger.info(f"Support ticket creation request from {userEmail}")
    logger.debug(f"Request headers: {dict(request.headers)}")
    
    try:
        # Verify user identity matches token
        token_user_id = current_user.get('oid') or current_user.get('sub') or current_user.get('email')
        
        # Log user ID comparison for debugging
        logger.info(f"Token user ID: {token_user_id}, Form user ID: {userId}")
        logger.info(f"Current user claims: {current_user}")
        
        # In production with Easy Auth, the user ID might be in email format
        # Allow matching by email if the token user ID is an email
        if token_user_id != userId:
            # Check if we can match by email instead
            token_email = current_user.get('email') or current_user.get('preferred_username')
            if token_email and (token_email == userId or token_email == userEmail):
                logger.info(f"User ID mismatch but email matches, allowing request")
                # Update userId to match token for consistency
                userId = token_user_id or token_email
            else:
                logger.error(f"User ID mismatch - token: {token_user_id}, form: {userId}, email: {userEmail}")
                raise HTTPException(status_code=403, detail="User ID mismatch")
        
        # Create ticket data first without attachments
        ticket_data = {
            'userId': userId,
            'userEmail': userEmail,
            'userName': userName,
            'title': title,
            'description': description,
            'category': category,
            'priority': priority,
            'attachments': []  # Start with empty attachments
        }

        # Create the ticket to get the ticket ID
        result = await service.create_ticket(ticket_data)
        ticket_id = result['ticketId']

        # Now process file uploads with the actual ticket ID
        attachments = []
        if files and int(fileCount) > 0:
            for file in files:
                if file.filename:  # Check if file is not empty
                    # Upload directly to the final location using ticket ID
                    blob_url = await save_attachment_to_blob(file, ticket_id)
                    if blob_url:
                        attachments.append({
                            'filename': file.filename,
                            'url': blob_url,
                            'size': file.size,
                            'contentType': file.content_type
                        })

        # Update ticket with attachments if any were uploaded
        if attachments:
            ticket = await service.get_ticket_by_id(ticket_id, userId)
            if ticket:
                ticket['attachments'] = attachments
                await service.container_client.upsert_item(ticket)

        # Send email notification if enabled
        if os.getenv("SUPPORT_EMAIL_ENABLED", "false").lower() == "true":
            try:
                # Get the updated ticket with correct attachment URLs
                ticket_id = result['ticketId']
                updated_ticket = await service.get_ticket_by_id(ticket_id, userId)

                # Use updated attachments if available, otherwise use original
                email_attachments = updated_ticket.get('attachments', []) if updated_ticket else attachments

                email_service = SupportEmailService()
                await email_service.send_ticket_created_notification({
                    'ticketId': ticket_id,
                    'title': title,
                    'description': description,
                    'category': category,
                    'priority': priority,
                    'userName': userName,
                    'userEmail': userEmail,
                    'attachments': email_attachments,
                    'createdAt': result['createdAt']
                })
                email_service.close()
                logger.info(f"Email notification sent for ticket {ticket_id}")
            except Exception as e:
                logger.warning(f"Failed to send email notification: {e}")

        logger.info(f"Support ticket created successfully: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Error creating support ticket: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tickets")
async def get_user_tickets(
    request: Request,
    limit: int = 50,
    offset: int = 0,
    current_user = Depends(get_current_user),
    service: SupportTicketService = Depends(get_support_service)
):
    """Get support tickets for the current user"""
    try:
        user_id = current_user.get('oid') or current_user.get('sub') or current_user.get('email')
        if not user_id:
            logger.error(f"No user ID found in token claims: {current_user}")
            raise HTTPException(status_code=400, detail="User ID not found in token")
        
        logger.info(f"Getting tickets for user: {user_id}")
        
        tickets = await service.get_tickets_by_user(user_id, limit, offset)
        return {"tickets": tickets, "count": len(tickets)}
        
    except Exception as e:
        logger.error(f"Error retrieving user tickets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/tickets/{ticket_id}")
async def get_ticket(
    request: Request,
    ticket_id: str,
    current_user = Depends(get_current_user),
    service: SupportTicketService = Depends(get_support_service)
):
    """Get a specific support ticket"""
    try:
        user_id = current_user.get('oid') or current_user.get('sub')
        if not user_id:
            raise HTTPException(status_code=400, detail="User ID not found in token")
        
        ticket = await service.get_ticket_by_id(ticket_id, user_id)
        if not ticket:
            raise HTTPException(status_code=404, detail="Ticket not found")
        
        return ticket
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving ticket {ticket_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/tickets/{ticket_id}/status")
async def update_ticket_status(
    request: Request,
    ticket_id: str,
    status: str,
    resolution: Optional[str] = None,
    current_user = Depends(get_current_user),
    service: SupportTicketService = Depends(get_support_service)
):
    """Update support ticket status (admin only)"""
    try:
        # Check if user has admin permissions (implement based on your role system)
        user_role = current_user.get('role', 'user')
        if user_role not in ['admin', 'super_admin']:
            raise HTTPException(status_code=403, detail="Admin permissions required")
        
        user_id = current_user.get('oid') or current_user.get('sub') or current_user.get('email')
        success = await service.update_ticket_status(ticket_id, user_id, status, resolution)

        if not success:
            raise HTTPException(status_code=404, detail="Ticket not found")

        # Send email notification if enabled
        if os.getenv("SUPPORT_EMAIL_ENABLED", "false").lower() == "true":
            try:
                # Get updated ticket details for email
                ticket = await service.get_ticket_by_id(ticket_id, user_id)
                if ticket:
                    email_service = SupportEmailService()
                    email_service.send_ticket_update_notification({
                        'ticketId': ticket_id,
                        'title': ticket['title'],
                        'status': status,
                        'resolution': resolution,
                        'userEmail': ticket['userEmail'],
                        'updatedAt': ticket['updatedAt']
                    })
                    email_service.close()
                    logger.info(f"Update notification sent for ticket {ticket_id}")
            except Exception as e:
                logger.warning(f"Failed to send update notification: {e}")

        return {"message": "Ticket status updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating ticket status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/tickets")
async def get_all_tickets(
    request: Request,
    limit: int = 100,
    offset: int = 0,
    status: Optional[str] = None,
    current_user = Depends(get_current_user),
    service: SupportTicketService = Depends(get_support_service)
):
    """Get all support tickets (admin only)"""
    try:
        # Check if user has admin permissions
        user_role = current_user.get('role', 'user')
        if user_role not in ['admin', 'super_admin']:
            raise HTTPException(status_code=403, detail="Admin permissions required")
        
        tickets = await service.get_all_tickets(limit, offset, status)
        return {"tickets": tickets, "count": len(tickets)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving all tickets: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-email")
async def test_email_service(
    request: Request,
    recipient_email: str = Form(...),
    current_user = Depends(get_current_user)
):
    """Test email service functionality (admin only)"""
    try:
        # Check if user has admin permissions
        user_role = current_user.get('role', 'user')
        if user_role not in ['admin', 'super_admin']:
            raise HTTPException(status_code=403, detail="Admin permissions required")

        # Check if email service is enabled
        if os.getenv("SUPPORT_EMAIL_ENABLED", "false").lower() != "true":
            raise HTTPException(status_code=400, detail="Email service is not enabled")

        email_service = SupportEmailService()
        result = email_service.send_test_email(recipient_email)
        email_service.close()

        if result:
            return {
                "message": "Test email sent successfully",
                "messageId": result,
                "recipient": recipient_email
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to send test email")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sending test email: {e}")
        raise HTTPException(status_code=500, detail=str(e))
import uuid
import logging
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions
from azure.storage.blob.aio import BlobServiceClient
from backend.history.cosmosdbservice import CosmosConversationClient

# Configure logging
logger = logging.getLogger(__name__)

class SupportTicketService:
    def __init__(self, cosmosdb_endpoint: str, account_key: str, database_name: str, container_name: str = 'support_tickets'):
        self.cosmosdb_endpoint = cosmosdb_endpoint
        self.account_key = account_key
        self.database_name = database_name
        self.container_name = container_name
        self.cosmos_client = None
        self.database_client = None
        self.container_client = None
        
    async def initialize(self):
        """Initialize the Cosmos DB client and container"""
        try:
            # Use account key directly as string
            self.cosmos_client = CosmosClient(self.cosmosdb_endpoint, credential=self.account_key)
            self.database_client = self.cosmos_client.get_database_client(self.database_name)
            self.container_client = self.database_client.get_container_client(self.container_name)
            logger.info(f"Support ticket service initialized for container {self.container_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize support ticket service: {e}")
            return False

    async def create_ticket(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new support ticket"""
        try:
            ticket_id = str(uuid.uuid4())
            current_time = datetime.now(timezone.utc).isoformat()
            
            ticket = {
                'id': ticket_id,
                'type': 'support_ticket',
                'userId': ticket_data.get('userId'),
                'userEmail': ticket_data.get('userEmail'),
                'userName': ticket_data.get('userName'),
                'title': ticket_data.get('title'),
                'description': ticket_data.get('description'),
                'category': ticket_data.get('category', 'technical_issue'),
                'priority': ticket_data.get('priority', 'medium'),
                'status': 'open',
                'attachments': ticket_data.get('attachments', []),
                'createdAt': current_time,
                'updatedAt': current_time,
                'resolution': None,
                'resolvedAt': None,
                'assignedTo': None,
                'tags': ticket_data.get('tags', [])
            }
            
            # Validate required fields
            if not all([ticket.get('userId'), ticket.get('title'), ticket.get('description')]):
                raise ValueError("Missing required fields: userId, title, and description are required")
            
            # Save to Cosmos DB
            result = await self.container_client.upsert_item(ticket)
            logger.info(f"Support ticket {ticket_id} created successfully for user {ticket['userId']}")
            
            return {
                'ticketId': ticket_id,
                'status': 'created',
                'createdAt': current_time
            }
            
        except Exception as e:
            logger.error(f"Error creating support ticket: {e}")
            raise

    async def get_tickets_by_user(self, user_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """Get support tickets for a specific user"""
        try:
            parameters = [
                {'name': '@userId', 'value': user_id}
            ]
            
            query = """
                SELECT * FROM c 
                WHERE c.userId = @userId AND c.type = 'support_ticket' 
                ORDER BY c.createdAt DESC
                OFFSET @offset LIMIT @limit
            """
            
            parameters.extend([
                {'name': '@offset', 'value': offset},
                {'name': '@limit', 'value': limit}
            ])
            
            tickets = []
            async for item in self.container_client.query_items(
                query=query, 
                parameters=parameters,
                max_item_count=limit
            ):
                tickets.append(item)
            
            logger.info(f"Retrieved {len(tickets)} tickets for user {user_id}")
            return tickets
            
        except Exception as e:
            logger.error(f"Error retrieving tickets for user {user_id}: {e}")
            return []

    async def get_ticket_by_id(self, ticket_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific ticket by ID for a user"""
        try:
            parameters = [
                {'name': '@ticketId', 'value': ticket_id},
                {'name': '@userId', 'value': user_id}
            ]
            
            query = """
                SELECT * FROM c 
                WHERE c.id = @ticketId AND c.userId = @userId AND c.type = 'support_ticket'
            """
            
            tickets = []
            async for item in self.container_client.query_items(query=query, parameters=parameters):
                tickets.append(item)
            
            if tickets:
                logger.info(f"Retrieved ticket {ticket_id} for user {user_id}")
                return tickets[0]
            else:
                logger.warning(f"Ticket {ticket_id} not found for user {user_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving ticket {ticket_id}: {e}")
            return None

    async def update_ticket_status(self, ticket_id: str, user_id: str, status: str, resolution: str = None) -> bool:
        """Update the status of a support ticket"""
        try:
            ticket = await self.get_ticket_by_id(ticket_id, user_id)
            if not ticket:
                return False
            
            ticket['status'] = status
            ticket['updatedAt'] = datetime.now(timezone.utc).isoformat()
            
            if status == 'resolved' and resolution:
                ticket['resolution'] = resolution
                ticket['resolvedAt'] = ticket['updatedAt']
            
            await self.container_client.upsert_item(ticket)
            logger.info(f"Updated ticket {ticket_id} status to {status}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating ticket {ticket_id}: {e}")
            return False

    async def get_all_tickets(self, limit: int = 100, offset: int = 0, status_filter: str = None) -> List[Dict[str, Any]]:
        """Get all support tickets (admin function)"""
        try:
            query = "SELECT * FROM c WHERE c.type = 'support_ticket'"
            parameters = []
            
            if status_filter:
                query += " AND c.status = @status"
                parameters.append({'name': '@status', 'value': status_filter})
            
            query += " ORDER BY c.createdAt DESC OFFSET @offset LIMIT @limit"
            parameters.extend([
                {'name': '@offset', 'value': offset},
                {'name': '@limit', 'value': limit}
            ])
            
            tickets = []
            async for item in self.container_client.query_items(
                query=query, 
                parameters=parameters,
                partition_key=None,  # Query across all partitions
                max_item_count=limit
            ):
                tickets.append(item)
            
            logger.info(f"Retrieved {len(tickets)} total tickets")
            return tickets
            
        except Exception as e:
            logger.error(f"Error retrieving all tickets: {e}")
            return []

    async def close(self):
        """Close the Cosmos DB client"""
        if self.cosmos_client:
            await self.cosmos_client.close()
            logger.info("Support ticket service closed")
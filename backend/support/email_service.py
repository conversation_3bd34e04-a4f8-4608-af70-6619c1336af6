from azure.communication.email import Email<PERSON>lient
import os
import logging
import base64
import httpx
from datetime import datetime
from typing import Dict, Optional, List

logger = logging.getLogger(__name__)

class SupportEmailService:
    """
    Azure Communication Services email service for support ticket notifications.
    Handles sending email notifications for ticket creation and status updates.
    """
    
    def __init__(self):
        """Initialize the email service with Azure Communication Services."""
        connection_string = os.getenv("AZURE_COMMUNICATION_CONNECTION_STRING")
        if not connection_string:
            raise ValueError("AZURE_COMMUNICATION_CONNECTION_STRING not configured")
        
        self.email_client = EmailClient.from_connection_string(connection_string)
        self.from_address = os.getenv("AZURE_EMAIL_FROM_ADDRESS")
        self.support_team_email = os.getenv("SUPPORT_TEAM_EMAIL")
        
        if not self.from_address:
            raise ValueError("AZURE_EMAIL_FROM_ADDRESS not configured")
        
        if not self.support_team_email:
            raise ValueError("SUPPORT_TEAM_EMAIL not configured")
        
        logger.info(f"Email service initialized with from_address: {self.from_address}")
    
    def close(self):
        """Close the email client connection."""
        # EmailClient doesn't have a close method in this version
        pass

    async def _download_attachment(self, attachment_url: str) -> Optional[Dict]:
        """Download attachment from Azure Blob Storage and return base64 encoded content"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(attachment_url)
                response.raise_for_status()

                # Get file content and encode as base64
                file_content = response.content
                base64_content = base64.b64encode(file_content).decode('utf-8')

                # Extract filename from URL
                filename = attachment_url.split('/')[-1].split('?')[0]  # Remove SAS token part

                # Get content type from response headers or guess from filename
                content_type = response.headers.get('content-type', 'application/octet-stream')

                return {
                    'name': filename,
                    'contentInBase64': base64_content,
                    'contentType': content_type
                }

        except Exception as e:
            logger.error(f"Failed to download attachment from {attachment_url}: {e}")
            return None

    async def _prepare_attachments(self, attachments: List[Dict]) -> List[Dict]:
        """Download and prepare attachments for email"""
        email_attachments = []

        for attachment in attachments:
            if attachment.get('url'):
                downloaded = await self._download_attachment(attachment['url'])
                if downloaded:
                    email_attachments.append(downloaded)
                    logger.info(f"Downloaded attachment: {downloaded['name']}")
                else:
                    logger.warning(f"Failed to download attachment: {attachment.get('filename', 'unknown')}")

        return email_attachments

    def _format_attachments_html(self, attachments: List[Dict]) -> str:
        """Format attachments for HTML email content"""
        if not attachments:
            return ""

        html = "<h3>Attachments:</h3><ul>"
        for attachment in attachments:
            filename = attachment.get('filename', 'Unknown file')
            size = attachment.get('size', 0)
            size_str = f" ({self._format_file_size(size)})" if size else ""
            html += f"<li>{filename}{size_str}</li>"
        html += "</ul>"
        return html

    def _format_attachments_text(self, attachments: List[Dict]) -> str:
        """Format attachments for plain text email content"""
        if not attachments:
            return ""

        text = "Attachments:\n"
        for attachment in attachments:
            filename = attachment.get('filename', 'Unknown file')
            size = attachment.get('size', 0)
            size_str = f" ({self._format_file_size(size)})" if size else ""
            text += f"- {filename}{size_str}\n"
        return text

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"

    def _format_attachments_html(self, attachments: List[Dict]) -> str:
        """Format attachments for HTML email content"""
        if not attachments:
            return ""

        html = "<h3>Attachments:</h3><ul>"
        for attachment in attachments:
            filename = attachment.get('filename', 'Unknown file')
            size = attachment.get('size', 0)
            size_str = f" ({self._format_file_size(size)})" if size else ""
            html += f"<li>{filename}{size_str}</li>"
        html += "</ul>"
        return html

    def _format_attachments_text(self, attachments: List[Dict]) -> str:
        """Format attachments for plain text email content"""
        if not attachments:
            return ""

        text = "Attachments:\n"
        for attachment in attachments:
            filename = attachment.get('filename', 'Unknown file')
            size = attachment.get('size', 0)
            size_str = f" ({self._format_file_size(size)})" if size else ""
            text += f"- {filename}{size_str}\n"
        return text

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"

    async def send_ticket_created_notification(self, ticket_data: Dict) -> Optional[str]:
        """
        Send email notification when a new support ticket is created.
        
        Args:
            ticket_data: Dictionary containing ticket information
            
        Returns:
            Message ID if successful, None if failed
        """
        try:
            subject = f"New Support Ticket Created - #{ticket_data['ticketId']}"
            
            # Create HTML content
            html_content = f"""
            <html>
            <body>
                <h2>New Support Ticket Created</h2>
                <p>A new support ticket has been submitted and requires attention.</p>
                
                <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
                    <tr><td><strong>Ticket ID:</strong></td><td>#{ticket_data['ticketId']}</td></tr>
                    <tr><td><strong>From:</strong></td><td>{ticket_data['userName']} ({ticket_data['userEmail']})</td></tr>
                    <tr><td><strong>Category:</strong></td><td>{ticket_data['category']}</td></tr>
                    <tr><td><strong>Priority:</strong></td><td>{ticket_data['priority']}</td></tr>
                    <tr><td><strong>Title:</strong></td><td>{ticket_data['title']}</td></tr>
                    <tr><td><strong>Created:</strong></td><td>{ticket_data['createdAt']}</td></tr>
                </table>
                
                <h3>Description:</h3>
                <p>{ticket_data['description']}</p>
                
                {self._format_attachments_html(ticket_data.get('attachments', []))}
                
                <p>Please review and respond to this ticket as soon as possible.</p>
            </body>
            </html>
            """
            
            # Create plain text content
            text_content = f"""
            New Support Ticket Created
            
            Ticket ID: #{ticket_data['ticketId']}
            From: {ticket_data['userName']} ({ticket_data['userEmail']})
            Category: {ticket_data['category']}
            Priority: {ticket_data['priority']}
            Title: {ticket_data['title']}
            
            Description:
            {ticket_data['description']}
            
            {self._format_attachments_text(ticket_data.get('attachments', []))}
            
            Created: {ticket_data['createdAt']}
            """
            
            # Prepare attachments
            email_attachments = await self._prepare_attachments(ticket_data.get('attachments', []))

            message = {
                "senderAddress": self.from_address,
                "recipients": {
                    "to": [{"address": self.support_team_email}]
                },
                "content": {
                    "subject": subject,
                    "plainText": text_content,
                    "html": html_content
                }
            }

            # Add attachments if any were successfully downloaded
            if email_attachments:
                message["attachments"] = email_attachments
                logger.info(f"Including {len(email_attachments)} attachments in email")

            poller = self.email_client.begin_send(message)
            result = poller.result()
            logger.info(f"Email notification sent for ticket {ticket_data['ticketId']}")

            # Extract message ID from result
            if hasattr(result, 'message_id'):
                return result.message_id
            elif isinstance(result, dict):
                if 'messageId' in result:
                    return result['messageId']
                elif 'id' in result:
                    return result['id']
            return str(result)
            
        except Exception as e:
            logger.error(f"Failed to send ticket creation notification: {e}")
            return None
    
    def send_ticket_update_notification(self, ticket_data: Dict) -> Optional[str]:
        """
        Send email notification when a support ticket is updated.
        
        Args:
            ticket_data: Dictionary containing updated ticket information
            
        Returns:
            Message ID if successful, None if failed
        """
        try:
            subject = f"Support Ticket Update - #{ticket_data['ticketId']} - {ticket_data['status'].title()}"
            
            # Create HTML content
            html_content = f"""
            <html>
            <body>
                <h2>Support Ticket Update</h2>
                <p>Your support ticket has been updated.</p>
                
                <table border="1" cellpadding="5" cellspacing="0" style="border-collapse: collapse;">
                    <tr><td><strong>Ticket ID:</strong></td><td>#{ticket_data['ticketId']}</td></tr>
                    <tr><td><strong>Status:</strong></td><td>{ticket_data['status'].title()}</td></tr>
                    <tr><td><strong>Title:</strong></td><td>{ticket_data['title']}</td></tr>
                    <tr><td><strong>Updated:</strong></td><td>{ticket_data['updatedAt']}</td></tr>
                </table>
                
                {f"<h3>Resolution:</h3><p>{ticket_data['resolution']}</p>" if ticket_data.get('resolution') else ""}
                
                <p>Thank you for using our support system.</p>
            </body>
            </html>
            """
            
            # Create plain text content
            text_content = f"""
            Support Ticket Update
            
            Ticket ID: #{ticket_data['ticketId']}
            Status: {ticket_data['status'].title()}
            Title: {ticket_data['title']}
            
            {f"Resolution: {ticket_data['resolution']}" if ticket_data.get('resolution') else ""}
            
            Updated: {ticket_data['updatedAt']}
            """
            
            message = {
                "senderAddress": self.from_address,
                "recipients": {
                    "to": [{"address": ticket_data['userEmail']}]
                },
                "content": {
                    "subject": subject,
                    "plainText": text_content,
                    "html": html_content
                }
            }

            poller = self.email_client.begin_send(message)
            result = poller.result()
            logger.info(f"Update notification sent for ticket {ticket_data['ticketId']}")

            # Extract message ID from result
            if hasattr(result, 'message_id'):
                return result.message_id
            elif isinstance(result, dict):
                if 'messageId' in result:
                    return result['messageId']
                elif 'id' in result:
                    return result['id']
            return str(result)
            
        except Exception as e:
            logger.error(f"Failed to send update notification: {e}")
            return None
    
    def send_test_email(self, recipient_email: str) -> Optional[str]:
        """
        Send a test email to verify the email service configuration.
        
        Args:
            recipient_email: Email address to send test email to
            
        Returns:
            Message ID if successful, None if failed
        """
        try:
            subject = "Azure Communication Services - Test Email"
            
            html_content = """
            <html>
            <body>
                <h2>Test Email</h2>
                <p>This is a test email from the AI Scope Support System.</p>
                <p>If you received this email, the Azure Communication Services integration is working correctly.</p>
                <p><strong>Configuration Details:</strong></p>
                <ul>
                    <li>Service: Azure Communication Services</li>
                    <li>Email Service: Configured and operational</li>
                    <li>Timestamp: """ + str(datetime.now()) + """</li>
                </ul>
            </body>
            </html>
            """
            
            text_content = """
            Test Email
            
            This is a test email from the AI Scope Support System.
            If you received this email, the Azure Communication Services integration is working correctly.
            
            Configuration Details:
            - Service: Azure Communication Services
            - Email Service: Configured and operational
            """
            
            message = {
                "senderAddress": self.from_address,
                "recipients": {
                    "to": [{"address": recipient_email}]
                },
                "content": {
                    "subject": subject,
                    "plainText": text_content,
                    "html": html_content
                }
            }

            poller = self.email_client.begin_send(message)
            result = poller.result()
            logger.info(f"Test email sent to {recipient_email}")

            # Extract message ID from result
            if hasattr(result, 'message_id'):
                return result.message_id
            elif isinstance(result, dict):
                if 'messageId' in result:
                    return result['messageId']
                elif 'id' in result:
                    return result['id']
            return str(result)
            
        except Exception as e:
            logger.error(f"Failed to send test email: {e}")
            return None

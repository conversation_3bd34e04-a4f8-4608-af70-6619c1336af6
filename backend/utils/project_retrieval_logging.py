"""
Enhanced logging for project retrieval operations.
This module provides specialized logging for project retrieval and deployment status tracking.
"""

import logging
import os
import json
from datetime import datetime
from backend.utils.logging_config import configure_project_retrieval_logger, configure_logger

# Configure a dedicated logger for project retrieval
project_logger = configure_project_retrieval_logger(
    console_level=logging.INFO,  # Less verbose in console
    file_level=logging.DEBUG     # Very detailed in log file
)

# Configure a dedicated logger for API requests
api_logger = configure_logger(
    logger_name="api_requests",
    log_to_console=True,
    log_to_file=True,
    console_level=logging.INFO,
    file_level=logging.DEBUG,
    log_file_name="api_requests.log"
)

def log_project_retrieval_attempt(project_id, user_id, method_name, using_azure_cli=False):
    """Log a project retrieval attempt with detailed information."""
    project_logger.info(f"Attempting to retrieve project {project_id} for user {user_id} using {method_name}")
    project_logger.debug(f"Using Azure CLI credentials: {using_azure_cli}")
    
    # Log timestamp for correlation with other logs
    project_logger.debug(f"Timestamp: {datetime.now().isoformat()}")

def log_project_retrieval_success(project_id, user_id, method_name):
    """Log a successful project retrieval."""
    project_logger.info(f"Successfully retrieved project {project_id} for user {user_id} using {method_name}")

def log_project_retrieval_failure(project_id, user_id, method_name, error=None):
    """Log a failed project retrieval attempt."""
    if error:
        project_logger.error(f"Failed to retrieve project {project_id} for user {user_id} using {method_name}: {error}")
    else:
        project_logger.warning(f"Failed to retrieve project {project_id} for user {user_id} using {method_name}")

def log_api_request(method, url, headers=None, body=None):
    """Log an API request with detailed information."""
    # Redact sensitive information from headers
    if headers:
        redacted_headers = {k: '***REDACTED***' if k.lower() in ['authorization', 'x-api-key', 'api-key'] else v 
                           for k, v in headers.items()}
    else:
        redacted_headers = {}
    
    api_logger.info(f"API Request: {method} {url}")
    api_logger.debug(f"Headers: {json.dumps(redacted_headers)}")
    
    if body:
        # Try to parse body as JSON for better formatting
        try:
            if isinstance(body, str):
                parsed_body = json.loads(body)
                api_logger.debug(f"Body: {json.dumps(parsed_body, indent=2)}")
            else:
                api_logger.debug(f"Body: {body}")
        except:
            api_logger.debug(f"Body: {body}")

def log_api_response(status_code, headers=None, body=None):
    """Log an API response with detailed information."""
    api_logger.info(f"API Response status: {status_code}")
    
    if headers:
        api_logger.debug(f"Response headers: {json.dumps(dict(headers))}")
    
    if body:
        # Try to parse body as JSON for better formatting
        try:
            if isinstance(body, str):
                parsed_body = json.loads(body)
                api_logger.debug(f"Response body: {json.dumps(parsed_body, indent=2)}")
            else:
                api_logger.debug(f"Response body: {body}")
        except:
            api_logger.debug(f"Response body: {body}")

def log_deployment_status_update(project_id, status_data):
    """Log a deployment status update."""
    project_logger.info(f"Deployment status update for project {project_id}: {status_data.get('status')}")
    project_logger.info(f"Message: {status_data.get('message')}")
    
    # Log detailed status information
    if 'details' in status_data:
        details = status_data['details']
        project_logger.debug(f"Deployment details: {json.dumps(details, indent=2)}")
        
        # Log completion percentage
        if 'completion_percentage' in details:
            project_logger.info(f"Completion percentage: {details['completion_percentage']}%")

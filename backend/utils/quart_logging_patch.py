"""
Module to patch Quart's logging configuration.
This module provides functions to disable Quart's access logging.
"""

import logging
import os

def patch_quart_logging():
    """
    Patch Quart's logging configuration to disable access logs.
    This function should be called before creating the Quart app.
    """
    try:
        # Configure Quart's logging directly
        logging.getLogger("quart").setLevel(logging.WARNING)
        logging.getLogger("quart.serving").setLevel(logging.WARNING)
        logging.getLogger("quart.app").setLevel(logging.WARNING)
        
        # Configure Hypercorn's logging directly
        logging.getLogger("hypercorn").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.access").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.error").setLevel(logging.WARNING)
        
        # Log that we've disabled access logging
        logging.info("Quart access logging has been disabled")
        return True
    except Exception as e:
        logging.warning(f"Could not patch Quart logging: {e}")
        return False

def patch_hypercorn_logging():
    """Configure Hypercorn logging and optionally disable access logs."""
    try:
        # Configure Hypercorn's logging levels
        logging.getLogger("hypercorn").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.access").setLevel(logging.WARNING)
        logging.getLogger("hypercorn.error").setLevel(logging.WARNING)
        
        # In newer versions of Hypercorn, access logging is controlled via
        # the logger configuration rather than patching functions
        if os.environ.get("QUART_ACCESS_LOGGING", "1") == "0":
            # Disable the access logger completely
            access_logger = logging.getLogger("hypercorn.access")
            access_logger.disabled = True
            logging.info("Hypercorn access logging has been disabled")
        
        logging.info("Hypercorn logging has been configured")
        return True
    except Exception as e:
        logging.error(f"Error configuring Hypercorn logging: {e}")
        return False


def patch_gunicorn_logging():
    """Configure Gunicorn logging levels."""
    try:
        logging.getLogger("gunicorn").setLevel(logging.WARNING)
        logging.getLogger("gunicorn.error").setLevel(logging.WARNING)
        logging.getLogger("gunicorn.access").setLevel(logging.WARNING)

        logging.info("Gunicorn logging has been configured")
        return True
    except Exception as e:
        logging.error(f"Error configuring Gunicorn logging: {e}")
        return False

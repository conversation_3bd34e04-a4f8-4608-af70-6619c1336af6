"""
Project Selector Logging Utility

This module provides dedicated logging for project selector operations,
including project card actions, edit operations, and icon changes.
"""

import logging
import os
from datetime import datetime
from typing import Optional, Dict, Any
from backend.utils.logging_config import configure_logger


def configure_project_selector_logger():
    """Return the configured logger for project selector operations."""
    log_level = os.environ.get("PROJECT_SELECTOR_LOG_LEVEL", "INFO").upper()
    return configure_logger(
        "project_selector",
        console_level=getattr(logging, log_level, logging.INFO),
        file_level=getattr(logging, log_level, logging.INFO),
    )


# Initialize the logger
project_selector_logger = configure_project_selector_logger()


def log_project_action(
    action: str, project_id: str, user_id: str, details: Optional[Dict[str, Any]] = None
):
    """
    Log a project selector action.

    Args:
        action: The action being performed (e.g., 'edit_project', 'change_icon', 'update_name')
        project_id: The ID of the project being acted upon
        user_id: The ID of the user performing the action
        details: Additional details about the action
    """
    log_entry = {
        "action": action,
        "project_id": project_id,
        "user_id": user_id,
        "timestamp": datetime.utcnow().isoformat(),
        "details": details or {},
    }

    project_selector_logger.info(
        f"Project Action: {action} - Project: {project_id} - User: {user_id} - Details: {details}"
    )

    return log_entry


def log_project_edit(
    project_id: str,
    user_id: str,
    changes: Dict[str, Any],
    success: bool,
    error: Optional[str] = None,
):
    """
    Log a project edit operation.

    Args:
        project_id: The ID of the project being edited
        user_id: The ID of the user performing the edit
        changes: The changes being made (e.g., {'name': 'New Name', 'icon': '📘'})
        success: Whether the operation was successful
        error: Error message if the operation failed
    """
    log_entry = {
        "action": "edit_project",
        "project_id": project_id,
        "user_id": user_id,
        "changes": changes,
        "success": success,
        "error": error,
        "timestamp": datetime.utcnow().isoformat(),
    }

    if success:
        project_selector_logger.info(
            f"Project Edit Success - Project: {project_id} - Changes: {changes}"
        )
    else:
        project_selector_logger.error(
            f"Project Edit Failed - Project: {project_id} - Changes: {changes} - Error: {error}"
        )

    return log_entry


def log_icon_change(
    project_id: str,
    user_id: str,
    old_icon: str,
    new_icon: str,
    success: bool,
    error: Optional[str] = None,
):
    """
    Log a project icon change operation.

    Args:
        project_id: The ID of the project
        user_id: The ID of the user changing the icon
        old_icon: The previous icon
        new_icon: The new icon
        success: Whether the operation was successful
        error: Error message if the operation failed
    """
    log_entry = {
        "action": "change_icon",
        "project_id": project_id,
        "user_id": user_id,
        "old_icon": old_icon,
        "new_icon": new_icon,
        "success": success,
        "error": error,
        "timestamp": datetime.utcnow().isoformat(),
    }

    if success:
        project_selector_logger.info(
            f"Icon Change Success - Project: {project_id} - Icon: {old_icon} → {new_icon}"
        )
    else:
        project_selector_logger.error(
            f"Icon Change Failed - Project: {project_id} - Icon: {old_icon} → {new_icon} - Error: {error}"
        )

    return log_entry


def log_project_card_action(
    action: str, project_id: str, user_id: str, details: Optional[Dict[str, Any]] = None
):
    """
    Log project card actions (open, close, expand, etc.).

    Args:
        action: The card action (e.g., 'open_edit_dialog', 'close_edit_dialog', 'expand_card')
        project_id: The ID of the project
        user_id: The ID of the user
        details: Additional action details
    """
    log_entry = {
        "action": f"project_card_{action}",
        "project_id": project_id,
        "user_id": user_id,
        "timestamp": datetime.utcnow().isoformat(),
        "details": details or {},
    }

    project_selector_logger.debug(
        f"Project Card Action: {action} - Project: {project_id} - User: {user_id}"
    )

    return log_entry

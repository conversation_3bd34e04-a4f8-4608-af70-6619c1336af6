import os
import logging
from typing import Optional
from azure.cosmos.aio import CosmosClient

logger = logging.getLogger(__name__)

_cosmos_client: Optional[CosmosClient] = None


def get_cosmos_client() -> Optional[CosmosClient]:
    """Return a singleton CosmosClient instance."""
    global _cosmos_client
    if _cosmos_client is None:
        account = os.environ.get("AZURE_COSMOSDB_ACCOUNT")
        key = os.environ.get("AZURE_COSMOSDB_ACCOUNT_KEY")
        if not account or not key:
            logger.error("Cosmos DB environment variables not configured")
            return None
        endpoint = f"https://{account}.documents.azure.com:443/"
        _cosmos_client = CosmosClient(endpoint, credential=key)
    return _cosmos_client



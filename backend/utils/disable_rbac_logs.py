"""
Module to disable RBAC logs in the application.
This module provides functions to disable RBAC-related logs.
"""

import logging
import logging.config
import sys
import os

class RBACFilter(logging.Filter):
    """
    A logging filter that filters out RBAC-related log messages.
    """
    def filter(self, record):
        # Check if the message is a string
        if not hasattr(record, 'msg') or not record.msg:
            return True

        # Convert message to string if it's not already
        msg = str(record.msg)

        # Filter out HTTP server logs for RBAC endpoints
        if '/api/rbac/' in msg:
            return False

        # Filter out logs from the RBAC module
        if hasattr(record, 'name') and (record.name == 'rbac' or record.name.startswith('rbac.')):
            return False

        return True

class HTTPAccessFilter(logging.Filter):
    """
    A logging filter that filters out all HTTP access logs.
    """
    def filter(self, record):
        # Check if this is an HTTP access log
        if hasattr(record, 'name'):
            if record.name in ["quart.serving", "hypercorn.access", "hypercorn.error"]:
                return False

        # Check if the message contains HTTP access log patterns
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            msg = record.msg
            # Filter out HTTP access logs (typical format: IP METHOD PATH STATUS)
            if " GET " in msg and " 200 " in msg:
                return False
            if " POST " in msg and " 200 " in msg:
                return False
            if " PUT " in msg and " 200 " in msg:
                return False
            if " DELETE " in msg and " 200 " in msg:
                return False

        return True

def disable_rbac_logs():
    """
    Disable all RBAC-related logs by setting their log level to CRITICAL.
    This affects both the rbac logger and the HTTP server logs for RBAC endpoints.
    """
    # Disable RBAC logger
    rbac_logger = logging.getLogger("rbac")
    rbac_logger.setLevel(logging.CRITICAL)

    # Create filters
    rbac_filter = RBACFilter()
    http_filter = HTTPAccessFilter()

    # Apply filters to root logger
    root_logger = logging.getLogger()
    root_logger.addFilter(rbac_filter)

    # Check if we should disable HTTP access logs
    if os.environ.get("QUART_ACCESS_LOGGING", "1") == "0":
        root_logger.addFilter(http_filter)

    # Apply filters to all handlers
    for handler in root_logger.handlers:
        handler.addFilter(rbac_filter)
        if os.environ.get("QUART_ACCESS_LOGGING", "1") == "0":
            handler.addFilter(http_filter)

    # Disable specific loggers
    loggers_to_disable = [
        "hypercorn.error",
        "hypercorn.access",
        "quart.serving",
        "quart.app",
        "quart.debug",
        "fastapi",
        "uvicorn",
        "uvicorn.access"
    ]

    for logger_name in loggers_to_disable:
        logger = logging.getLogger(logger_name)
        logger.addFilter(rbac_filter)
        if os.environ.get("QUART_ACCESS_LOGGING", "1") == "0":
            logger.addFilter(http_filter)
            logger.setLevel(logging.WARNING)  # Set to WARNING to reduce noise

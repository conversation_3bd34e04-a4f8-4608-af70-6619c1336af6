"""
Centralized logging configuration for the application.
This module provides functions to set up logging with different handlers and levels.
"""

import os
import logging
import sys
from logging.handlers import RotatingFileHandler
from backend.utils.blob_logging import BlobStorageHandler


# Map string log levels to logging module constants
def get_log_level(level_str):
    """Convert string log level to logging module constant"""
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }
    return level_map.get(level_str, logging.INFO)


def ensure_logs_directory(sub_dir: str | None = None) -> str:
    """Create the logs directory in the root (and optional sub-dir) if needed."""
    # Get the root directory (3 levels up from this file)
    root_dir = os.path.dirname(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    )
    logs_dir = os.path.join(root_dir, "logs")

    # If a sub directory is provided create it under logs
    if sub_dir:
        logs_dir = os.path.join(logs_dir, sub_dir)

    os.makedirs(logs_dir, exist_ok=True)
    return logs_dir


def get_configured_logger(
    logger_name: str,
    project_id: str | None = None,
    deployment_id: str | None = None,
    *,
    log_to_console: bool = True,
    log_to_file: bool = True,
    console_level: int = logging.INFO,
    file_level: int = logging.DEBUG,
    log_file_name: str | None = None,
    max_file_size_mb: int = 10,
    backup_count: int = 5,
    sub_dir: str | None = None,
) -> logging.Logger:
    """Return a logger configured according to common settings.

    Parameters mirror :func:`configure_logger` with optional project and
    deployment identifiers. When provided, ``sub_dir`` will automatically
    include these identifiers to help organize log files.
    """

    if project_id:
        sub_dir = os.path.join(sub_dir or "", project_id)
    if deployment_id:
        sub_dir = os.path.join(sub_dir or "", deployment_id)

    if log_file_name is None:
        if deployment_id:
            log_file_name = f"{logger_name}_{deployment_id}.log"
        elif project_id:
            log_file_name = f"{logger_name}_{project_id}.log"
        else:
            log_file_name = f"{logger_name}.log"

    return configure_logger(
        logger_name=logger_name,
        log_to_console=log_to_console,
        log_to_file=log_to_file,
        console_level=console_level,
        file_level=file_level,
        log_file_name=log_file_name,
        max_file_size_mb=max_file_size_mb,
        backup_count=backup_count,
        sub_dir=sub_dir,
    )


def configure_logger(
    logger_name,
    log_to_console=True,
    log_to_file=True,
    console_level=logging.INFO,
    file_level=logging.DEBUG,
    log_file_name=None,
    max_file_size_mb=10,
    backup_count=5,
    sub_dir: str | None = None,
):
    """
    Configure a logger with console and/or file handlers.

    Args:
        logger_name: Name of the logger to configure
        log_to_console: Whether to log to console
        log_to_file: Whether to log to file
        console_level: Logging level for console handler
        file_level: Logging level for file handler
        log_file_name: Name of the log file (if None, uses logger_name.log)
        max_file_size_mb: Maximum size of log file in MB before rotation
        backup_count: Number of backup log files to keep
        sub_dir: Optional subdirectory under ``logs`` for the log file

    Returns:
        The configured logger
    """
    # Check for environment variable override for console log level
    # Format: COMPONENT_CONSOLE_LOG_LEVEL where COMPONENT is the uppercase logger name
    env_var_name = f"{logger_name.upper()}_CONSOLE_LOG_LEVEL"
    env_level = os.environ.get(env_var_name)

    # If specific component level not found, try the default
    if not env_level:
        env_level = os.environ.get("DEFAULT_CONSOLE_LOG_LEVEL")

    # Override console_level if environment variable is set
    if env_level:
        console_level = get_log_level(env_level)

    logger = logging.getLogger(logger_name)
    logger.setLevel(min(console_level, file_level))  # Set to the most verbose level

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create formatter
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")

    # Add console handler if requested
    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(console_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)

    # Add file handler if requested
    if log_to_file:
        logs_dir = ensure_logs_directory(sub_dir)
        if log_file_name is None:
            log_file_name = f"{logger_name}.log"
        log_file_path = os.path.join(logs_dir, log_file_name)

        # Use rotating file handler to prevent log files from growing too large
        file_handler = RotatingFileHandler(
            log_file_path,
            maxBytes=max_file_size_mb * 1024 * 1024,  # Convert MB to bytes
            backupCount=backup_count,
        )
        file_handler.setLevel(file_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger


def configure_deployment_logger(
    project_id, console_level=logging.INFO, file_level=logging.DEBUG
):
    """
    Configure a logger specifically for deployment tracking.

    Args:
        project_id: ID of the project being deployed
        console_level: Logging level for console output
        file_level: Logging level for file output

    Returns:
        The configured logger
    """
    logger_name = f"deployment_{project_id}"
    log_file_name = f"deployment_{project_id}.log"

    return configure_logger(
        logger_name=logger_name,
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name=log_file_name,
    )


def configure_deletion_logger(
    project_id, console_level=logging.INFO, file_level=logging.DEBUG
):
    """Configure a logger for project deletion attempts."""
    logger_name = f"deletion_{project_id}"
    log_file_name = f"{project_id}.log"
    return configure_logger(
        logger_name=logger_name,
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name=log_file_name,
        sub_dir="deletions",
    )


def configure_project_retrieval_logger(
    console_level=logging.INFO, file_level=logging.DEBUG
):
    """
    Configure a logger specifically for project retrieval operations.

    Args:
        console_level: Logging level for console output
        file_level: Logging level for file output

    Returns:
        The configured logger
    """
    return configure_logger(
        logger_name="project_retrieval",
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name="project_retrieval.log",
    )


def configure_cosmos_logger(console_level=logging.INFO, file_level=logging.DEBUG):
    """
    Configure a logger specifically for CosmosDB operations.

    Args:
        console_level: Logging level for console output (default: INFO)
        file_level: Logging level for file output

    Returns:
        The configured logger
    """
    # Get console level from environment variable if set
    env_level = os.environ.get("COSMOS_CONSOLE_LOG_LEVEL")
    if env_level:
        console_level = get_log_level(env_level)

    return configure_logger(
        logger_name="cosmos_db",
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name="cosmos_db.log",
    )


def configure_api_logger(console_level=logging.INFO, file_level=logging.DEBUG):
    """
    Configure a logger specifically for API requests and responses.

    Args:
        console_level: Logging level for console output
        file_level: Logging level for file output

    Returns:
        The configured logger
    """
    return configure_logger(
        logger_name="api",
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name="api.log",
    )


def configure_rbac_logger(console_level=logging.CRITICAL, file_level=logging.DEBUG):
    """
    Configure a logger specifically for RBAC operations.

    Args:
        console_level: Logging level for console output (default: CRITICAL to disable all logs)
        file_level: Logging level for file output

    Returns:
        The configured logger
    """
    # Get console level from environment variable if set
    env_level = os.environ.get("RBAC_CONSOLE_LOG_LEVEL")
    if env_level:
        console_level = get_log_level(env_level)

    return configure_logger(
        logger_name="rbac",
        log_to_console=True,
        log_to_file=True,
        console_level=console_level,
        file_level=file_level,
        log_file_name="rbac.log",
    )


def get_configured_logger(name: str, deployment_id: str, project_id: str) -> logging.Logger:
    """Return a logger configured with console, file and blob handlers."""
    logger = logging.getLogger(name)

    if getattr(logger, "_configured", False):
        return logger

    logger.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # Console output
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setLevel(logging.INFO)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

    enable_local_logging = os.environ.get('ENABLE_LOCAL_LOGGING', 'true').lower() == 'true'
    enable_blob_logging = os.environ.get('ENABLE_BLOB_LOGGING', 'true').lower() == 'true'

    if enable_local_logging:
        logs_dir = ensure_logs_directory()
        file_path = os.path.join(logs_dir, f"{name}.log")
        file_handler = logging.FileHandler(file_path)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    if enable_blob_logging:
        storage_account_name = os.environ.get('DEPLOYMENT_LOG_STORAGE_ACCOUNT')
        container_name = os.environ.get('DEPLOYMENT_LOG_CONTAINER', 'deployment-logs')
        storage_key = os.environ.get('DEPLOYMENT_LOG_STORAGE_KEY')

        if storage_account_name:
            try:
                blob_handler = BlobStorageHandler(
                    storage_account_name=storage_account_name,
                    container_name=container_name,
                    project_id=project_id,
                    deployment_id=deployment_id,
                    storage_account_key=storage_key,
                    use_managed_identity=(storage_key is None),
                    blob_name_suffix=name,
                )
                blob_handler.setLevel(logging.DEBUG)
                blob_handler.setFormatter(formatter)
                logger.addHandler(blob_handler)
            except Exception as exc:
                logger.error(f"Failed to configure blob logging: {exc}")

    logger._configured = True  # type: ignore[attr-defined]
    return logger

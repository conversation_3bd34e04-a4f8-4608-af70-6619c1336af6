"""
Blob Storage Logging Handler for deployment logs.
Stores deployment logs in Azure Blob Storage with proper folder structure.
"""

import os
import logging
import json
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from azure.storage.blob import BlobServiceClient, ContentSettings
from azure.identity import DefaultAzureCredential
from azure.core.exceptions import ResourceNotFoundError, AzureError


class BlobStorageHandler(logging.Handler):
    """Custom logging handler that writes logs to Azure Blob Storage."""

    def __init__(
        self,
        storage_account_name: str,
        container_name: str,
        project_id: str,
        deployment_id: str,
        storage_account_key: Optional[str] = None,
        use_managed_identity: bool = True,
        blob_name_suffix: str = "deployment",
    ):
        """
        Initialize the Blob Storage logging handler.

        Args:
            storage_account_name: Name of the Azure Storage Account
            container_name: Name of the blob container for logs
            project_id: Project ID for organizing logs
            deployment_id: Unique deployment ID
            storage_account_key: Storage account key (optional, uses managed identity if not provided)
            use_managed_identity: Whether to use managed identity for authentication
            blob_name_suffix: Suffix used for the blob file name (default: "deployment")
        """
        super().__init__()
        self.storage_account_name = storage_account_name
        self.container_name = container_name
        self.project_id = project_id
        self.deployment_id = deployment_id

        # Initialize blob service client
        if storage_account_key:
            connection_string = f"DefaultEndpointsProtocol=https;AccountName={storage_account_name};AccountKey={storage_account_key};EndpointSuffix=core.windows.net"
            self.blob_service_client = BlobServiceClient.from_connection_string(
                connection_string
            )
        elif use_managed_identity:
            credential = DefaultAzureCredential()
            blob_url = f"https://{storage_account_name}.blob.core.windows.net"
            self.blob_service_client = BlobServiceClient(
                account_url=blob_url, credential=credential
            )
        else:
            raise ValueError(
                "Either storage_account_key or use_managed_identity must be provided"
            )

        # Create container if it doesn't exist
        self._ensure_container_exists()

        # Build blob path with subfolder structure
        timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
        self.blob_name = (
            f"deployment-logs/{project_id}/{deployment_id}/"
            f"{blob_name_suffix}_{timestamp}.log"
        )
        
        # Initialize blob client
        self.blob_client = self.blob_service_client.get_blob_client(
            container=self.container_name, blob=self.blob_name
        )

        # Buffer for log messages
        self.log_buffer = []
        self.buffer_size = 50  # Flush every 50 messages

    def _ensure_container_exists(self):
        """Ensure the container exists, create if it doesn't."""
        try:
            container_client = self.blob_service_client.get_container_client(
                self.container_name
            )
            container_client.get_container_properties()
        except ResourceNotFoundError:
            try:
                container_client.create_container()
                logging.info(f"Created blob container: {self.container_name}")
            except Exception as e:
                logging.error(f"Failed to create container: {e}")
                raise

    def emit(self, record):
        """
        Emit a log record to blob storage.
        """
        try:
            # Format the log record
            log_entry = self.format(record)
            self.log_buffer.append(log_entry)

            # Flush buffer if it reaches the size limit
            if len(self.log_buffer) >= self.buffer_size:
                self.flush()

        except Exception as e:
            self.handleError(record)

    def flush(self):
        """Flush the log buffer to blob storage."""
        if not self.log_buffer:
            return

        try:
            # Join all buffered logs
            logs_to_write = "\n".join(self.log_buffer) + "\n"

            # Check if blob exists
            try:
                existing_content = (
                    self.blob_client.download_blob().readall().decode("utf-8")
                )
                logs_to_write = existing_content + logs_to_write
            except ResourceNotFoundError:
                # Blob doesn't exist yet, which is fine
                pass

            # Upload to blob storage
            self.blob_client.upload_blob(
                logs_to_write,
                overwrite=True,
                content_settings=ContentSettings(content_type="text/plain"),
            )

            # Clear buffer
            self.log_buffer = []

        except Exception as e:
            logging.error(f"Failed to flush logs to blob storage: {e}")

    def close(self):
        """Close the handler and flush any remaining logs."""
        self.flush()
        super().close()


class DeploymentLogger:
    """DEPRECATED: use :func:`get_configured_logger` for new code.

    Manages deployment logging to both local files and blob storage.
    """

    def __init__(
        self,
        project_id: str,
        deployment_id: str,
        project_name: str,
        storage_account_name: Optional[str] = None,
        storage_account_key: Optional[str] = None,
        container_name: str = "deployment-logs",
        enable_blob_logging: bool = True,
        enable_local_logging: bool = True,
        local_log_dir: str = "logs",
        blob_name_suffix: str = "deployment",
    ):
        """
        Initialize the deployment logger.

        Args:
            project_id: Project ID
            deployment_id: Unique deployment ID
            project_name: Project name for metadata
            storage_account_name: Azure Storage Account name
            storage_account_key: Storage account key (optional)
            container_name: Blob container name
            enable_blob_logging: Whether to enable blob storage logging
            enable_local_logging: Whether to enable local file logging
            local_log_dir: Local directory for logs
            blob_name_suffix: Suffix used for the blob file name (default: "deployment")
        """
        self.project_id = project_id
        self.deployment_id = deployment_id
        self.project_name = project_name
        self.logger = logging.getLogger(f"deployment.{project_id}.{deployment_id}")
        self.logger.setLevel(logging.DEBUG)

        # Remove any existing handlers
        self.logger.handlers = []

        # Add console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # Add local file handler if enabled
        if enable_local_logging:
            os.makedirs(local_log_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            local_log_file = os.path.join(
                local_log_dir, f"deploy_project_resources_detailed_{timestamp}.log"
            )
            file_handler = logging.FileHandler(local_log_file)
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
            self.logger.info(f"Local logging enabled: {local_log_file}")

        # Add blob storage handler if enabled and credentials provided
        self.blob_handler = None
        if enable_blob_logging and storage_account_name:
            try:
                self.blob_handler = BlobStorageHandler(
                    storage_account_name=storage_account_name,
                    container_name=container_name,
                    project_id=project_id,
                    deployment_id=deployment_id,
                    storage_account_key=storage_account_key,
                    use_managed_identity=(storage_account_key is None),
                    blob_name_suffix=blob_name_suffix,
                )
                self.blob_handler.setLevel(logging.DEBUG)
                self.blob_handler.setFormatter(formatter)
                self.logger.addHandler(self.blob_handler)
                self.logger.info(
                    f"Blob storage logging enabled: {self.blob_handler.blob_name}"
                )

                # Log deployment metadata
                self._log_deployment_metadata()

            except Exception as e:
                self.logger.error(f"Failed to initialize blob storage logging: {e}")
                self.logger.warning("Continuing with local logging only")

    def _log_deployment_metadata(self):
        """Log deployment metadata at the start."""
        metadata = {
            "deployment_id": self.deployment_id,
            "project_id": self.project_id,
            "project_name": self.project_name,
            "start_time": datetime.now(timezone.utc).isoformat(),
            "environment": os.environ.get("ENVIRONMENT", "unknown"),
            "deployment_type": "project_resources",
        }
        self.logger.info(f"Deployment metadata: {json.dumps(metadata, indent=2)}")

    def get_logger(self) -> logging.Logger:
        """Get the configured logger instance."""
        return self.logger

    def close(self):
        """Close all handlers and flush logs."""
        if self.blob_handler:
            self.blob_handler.close()

        # Close other handlers
        for handler in self.logger.handlers:
            handler.close()

    def get_blob_url(self) -> Optional[str]:
        """Get the URL of the blob log file."""
        if self.blob_handler:
            return f"https://{self.blob_handler.storage_account_name}.blob.core.windows.net/{self.blob_handler.container_name}/{self.blob_handler.blob_name}"
        return None


def create_deployment_logger(
    project_id: str, deployment_id: str, project_name: str, **kwargs
) -> DeploymentLogger:
    """
    Factory function to create a deployment logger with environment-based configuration.

    Args:
        project_id: Project ID
        deployment_id: Unique deployment ID
        project_name: Project name
        **kwargs: Additional configuration options
            - blob_name_suffix: Suffix for the blob log file (default "deployment")
    
    Returns:
        Configured DeploymentLogger instance
    """
    # Get configuration from environment or kwargs
    storage_account_name = kwargs.get(
        "storage_account_name", os.environ.get("DEPLOYMENT_LOG_STORAGE_ACCOUNT")
    )
    storage_account_key = kwargs.get(
        "storage_account_key", os.environ.get("DEPLOYMENT_LOG_STORAGE_KEY")
    )
    container_name = kwargs.get(
        "container_name", os.environ.get("DEPLOYMENT_LOG_CONTAINER", "deployment-logs")
    )
    enable_blob_logging = kwargs.get(
        "enable_blob_logging",
        os.environ.get("ENABLE_BLOB_LOGGING", "true").lower() == "true",
    )
    enable_local_logging = kwargs.get(
        "enable_local_logging",
        os.environ.get("ENABLE_LOCAL_LOGGING", "true").lower() == "true",
    )
    blob_name_suffix = kwargs.get('blob_name_suffix', 'deployment')
    
    return DeploymentLogger(
        project_id=project_id,
        deployment_id=deployment_id,
        project_name=project_name,
        storage_account_name=storage_account_name,
        storage_account_key=storage_account_key,
        container_name=container_name,
        enable_blob_logging=enable_blob_logging,
        enable_local_logging=enable_local_logging,
        local_log_dir=kwargs.get('local_log_dir', 'logs'),
        blob_name_suffix=blob_name_suffix,
    )

"""
Project Card Deletion Logging Utility

This module provides dedicated logging for project card deletion operations.
All deletion logs are written to logs/project_card_deletion.log
"""

import logging
import os
from datetime import datetime
from typing import Optional, Dict, Any
from backend.utils.logging_config import configure_logger


def configure_project_deletion_logger():
    """Return the configured logger for project deletion events."""
    log_level = os.environ.get("PROJECT_DELETION_LOG_LEVEL", "INFO").upper()
    return configure_logger(
        "project_deletion",
        console_level=getattr(logging, log_level, logging.INFO),
        file_level=getattr(logging, log_level, logging.INFO),
        log_file_name="project_card_deletion.log",
    )


# Initialize the logger
project_deletion_logger = configure_project_deletion_logger()


def log_deletion_attempt(
    project_id: str,
    project_name: str,
    user_id: str,
    user_role: str,
    user_email: Optional[str] = None,
):
    """
    Log a project deletion attempt.

    Args:
        project_id: The ID of the project being deleted
        project_name: The name of the project
        user_id: The ID of the user attempting deletion
        user_role: The role of the user
        user_email: Optional email of the user
    """
    log_entry = {
        "timestamp": datetime.utcnow().isoformat(),
        "action": "deletion_attempt",
        "project_id": project_id,
        "project_name": project_name,
        "user_id": user_id,
        "user_role": user_role,
        "user_email": user_email,
    }

    project_deletion_logger.info(
        f"DELETION ATTEMPT - Project: {project_id} ({project_name}) - "
        f"User: {user_id} ({user_role}) - Email: {user_email or 'N/A'}"
    )

    return log_entry


def log_deletion_success(
    project_id: str,
    project_name: str,
    user_id: str,
    user_role: str,
    region: str,
    deletion_time_ms: Optional[int] = None,
    azure_cleanup_success: Optional[bool] = None,
    cleanup_report: Optional[Dict[str, Any]] = None,
):
    """
    Log a successful project deletion.

    Args:
        project_id: The ID of the deleted project
        project_name: The name of the deleted project
        user_id: The ID of the user who deleted the project
        user_role: The role of the user
        region: The region of the deleted project
        deletion_time_ms: Optional time taken for deletion in milliseconds
        azure_cleanup_success: Whether Azure resource cleanup was successful
        cleanup_report: Detailed report from Azure resource cleanup
    """
    log_entry = {
        "timestamp": datetime.utcnow().isoformat(),
        "action": "deletion_success",
        "project_id": project_id,
        "project_name": project_name,
        "user_id": user_id,
        "user_role": user_role,
        "region": region,
        "deletion_time_ms": deletion_time_ms,
        "azure_cleanup_success": azure_cleanup_success,
        "cleanup_report": cleanup_report,
    }

    time_info = f" - Time: {deletion_time_ms}ms" if deletion_time_ms else ""

    # Base success message
    base_msg = (
        f"DELETION SUCCESS - Project: {project_id} ({project_name}) - "
        f"User: {user_id} ({user_role}) - Region: {region}{time_info}"
    )

    # Add Azure cleanup status if available
    if azure_cleanup_success is not None:
        cleanup_status = "SUCCESS" if azure_cleanup_success else "FAILED"
        base_msg += f" - Azure Cleanup: {cleanup_status}"

    project_deletion_logger.info(base_msg)

    # Log detailed cleanup report if available
    if cleanup_report:
        if cleanup_report.get("resources_deleted"):
            project_deletion_logger.info(
                f"AZURE RESOURCES DELETED - Project: {project_id} - "
                f"Resources: {cleanup_report['resources_deleted']}"
            )
        if cleanup_report.get("errors"):
            project_deletion_logger.error(
                f"AZURE CLEANUP ERRORS - Project: {project_id} - "
                f"Errors: {cleanup_report['errors']}"
            )

    return log_entry


def log_deletion_failure(
    project_id: str,
    user_id: str,
    user_role: str,
    error: str,
    reason: Optional[str] = None,
    project_name: Optional[str] = None,
):
    """
    Log a failed project deletion.

    Args:
        project_id: The ID of the project that failed to delete
        user_id: The ID of the user who attempted deletion
        user_role: The role of the user
        error: The error message
        reason: Optional specific reason for failure
        project_name: Optional project name if known
    """
    log_entry = {
        "timestamp": datetime.utcnow().isoformat(),
        "action": "deletion_failure",
        "project_id": project_id,
        "project_name": project_name or "Unknown",
        "user_id": user_id,
        "user_role": user_role,
        "error": error,
        "reason": reason,
    }

    name_info = f" ({project_name})" if project_name else ""
    reason_info = f" - Reason: {reason}" if reason else ""
    project_deletion_logger.error(
        f"DELETION FAILED - Project: {project_id}{name_info} - "
        f"User: {user_id} ({user_role}) - Error: {error}{reason_info}"
    )

    return log_entry


def log_deletion_permission_denied(
    project_id: str,
    project_name: str,
    user_id: str,
    user_role: str,
    required_permission: str,
):
    """
    Log when a user is denied permission to delete a project.

    Args:
        project_id: The ID of the project
        project_name: The name of the project
        user_id: The ID of the user
        user_role: The current role of the user
        required_permission: The permission required for deletion
    """
    log_entry = {
        "timestamp": datetime.utcnow().isoformat(),
        "action": "deletion_permission_denied",
        "project_id": project_id,
        "project_name": project_name,
        "user_id": user_id,
        "user_role": user_role,
        "required_permission": required_permission,
    }

    project_deletion_logger.warning(
        f"PERMISSION DENIED - Project: {project_id} ({project_name}) - "
        f"User: {user_id} ({user_role}) - Required: {required_permission}"
    )

    return log_entry


def log_deletion_audit_trail(
    project_id: str,
    project_data: Dict[str, Any],
    user_id: str,
    user_data: Dict[str, Any],
):
    """
    Log detailed audit trail for project deletion.

    Args:
        project_id: The ID of the project
        project_data: Complete project data before deletion
        user_id: The ID of the user
        user_data: Complete user data
    """
    log_entry = {
        "timestamp": datetime.utcnow().isoformat(),
        "action": "deletion_audit_trail",
        "project_id": project_id,
        "project_data": project_data,
        "user_id": user_id,
        "user_data": {
            "id": user_data.get("id"),
            "email": user_data.get("email"),
            "role": user_data.get("role"),
            "region": user_data.get("region"),
        },
    }

    project_deletion_logger.info(
        f"AUDIT TRAIL - Deletion of project {project_id} by user {user_id} - "
        f"Project details: {project_data}"
    )

    return log_entry

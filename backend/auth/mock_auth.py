import jwt
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from pydantic import BaseModel
from backend.models.rbac import UserRole

# Secret key for JWT encoding/decoding
SECRET_KEY = "mock_secret_key_for_development_only"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    user_id: Optional[str] = None
    role: Optional[str] = None
    region: Optional[str] = None

# Mock users for testing
mock_users = {
    "1": {
        "id": "1",
        "name": "Super Admin",
        "email": "<EMAIL>",
        "password": "password123",  # In a real app, this would be hashed
        "role": UserRole.SUPER_ADMIN.value,
        "avatar": "https://via.placeholder.com/150"
    },
    "2": {
        "id": "2",
        "name": "Regional Admin",
        "email": "<EMAIL>",
        "password": "password123",
        "role": UserRole.REGIONAL_ADMIN.value,
        "region": "North America"
    },
    "3": {
        "id": "3",
        "name": "Regular User",
        "email": "<EMAIL>",
        "password": "password123",
        "role": UserRole.REGULAR_USER.value,
        "region": "North America"
    }
}

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token"""
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def authenticate_user(email: str, password: str) -> Optional[Dict[str, Any]]:
    """Authenticate a user with email and password"""
    for user_id, user in mock_users.items():
        if user["email"] == email and user["password"] == password:
            return user
    return None

async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """Get the current user from the JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception

        token_data = TokenData(user_id=user_id)
    except jwt.PyJWTError:
        raise credentials_exception

    user = mock_users.get(token_data.user_id)
    if user is None:
        raise credentials_exception

    return user

# For development/testing - get a user without authentication
async def get_mock_user(user_id: str = "1") -> Dict[str, Any]:
    """Get a mock user for testing without authentication"""
    import logging
    logging.info(f"get_mock_user called with user_id: {user_id}")

    user = mock_users.get(user_id)
    if user is None:
        logging.info(f"User {user_id} not found in mock_users, creating temporary user")
        # For testing, if the user doesn't exist in mock_users, create a temporary user
        # with the requested ID and super admin role
        temp_user = {
            "id": user_id,
            "name": f"Temporary User {user_id}",
            "email": f"temp_{user_id}@example.com",
            "role": UserRole.SUPER_ADMIN.value,
            "avatar": "https://via.placeholder.com/150"
        }
        # Add the temporary user to mock_users for future requests
        mock_users[user_id] = temp_user
        logging.info(f"Created temporary user: {temp_user}")
        return temp_user

    logging.info(f"Returning existing user: {user}")
    return user

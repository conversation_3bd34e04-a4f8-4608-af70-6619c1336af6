import logging
from quart import Blueprint, jsonify, request, make_response
import os
import base64
import json
from typing import Dict, Any

async def get_authenticated_user_details(request_headers): # request_headers might be Quart's request.headers
    """
    Get authenticated user details from request headers.

    This function uses the same robust authentication pattern as token_utils.py
    but maintains the legacy return format for backward compatibility.
    """
    # Check if we're in development mode
    is_development = os.environ.get('DEVELOPMENT_MODE', 'false').lower() == 'true'

    # Log the headers for debugging (excluding sensitive information)
    safe_headers = {k: v for k, v in request_headers.items()
                   if k.lower() not in ('authorization', 'cookie', 'x-api-key', 'x-ms-token-aad-id-token', 'x-ms-token-aad-access-token')}
    logging.debug(f"Auth headers (safe): {safe_headers}")

    # Create a mock request object to use with the existing token extraction function
    class MockRequest:
        def __init__(self, headers):
            self.headers = headers
            # Add missing attributes that token extraction expects
            self.method = "GET"  # Default method
            self.url = type('MockURL', (), {'path': '/api/cost'})()  # Mock URL object with path

    mock_request = MockRequest(request_headers)

    try:
        # Use the same authentication pattern as token_utils.py
        from backend.auth.entra_auth import extract_token_from_request_with_easy_auth
        from backend.auth.token_utils import validate_token, get_user_info_from_graph

        # Extract token using Easy Auth-aware function
        token = extract_token_from_request_with_easy_auth(mock_request)

        if not token:
            logging.warning("No authentication token found in request (checked Authorization and Easy Auth headers)")
            # In development mode, use mock user
            if is_development:
                logging.info("Development mode: Using mock user due to missing token")
                return _create_user_object_from_raw(create_development_user())
            else:
                # In production, try to extract user info from Easy Auth principal header as last resort
                principal_header = request_headers.get('x-ms-client-principal')
                if principal_header:
                    try:
                        import base64
                        import json
                        decoded_principal = base64.b64decode(principal_header)
                        principal_data = json.loads(decoded_principal)

                        # Extract email from claims
                        email = None
                        name = None
                        user_id = None

                        for claim in principal_data.get("claims", []):
                            claim_type = claim.get("typ", "")
                            claim_value = claim.get("val", "")

                            if "emailaddress" in claim_type:
                                email = claim_value
                            elif "name" in claim_type:
                                name = claim_value
                            elif "nameidentifier" in claim_type:
                                user_id = claim_value

                        if email:
                            logging.info(f"Production mode: Extracted user from Easy Auth principal: {email}")
                            # Create user object from Easy Auth principal
                            raw_user_object = {
                                'X-Ms-Client-Principal-Idp': 'aad',
                                'X-Ms-Client-Principal-Id': user_id or email,
                                'X-Ms-Client-Principal-Name': name or email.split("@")[0].replace(".", " ").title(),
                                'X-Ms-Client-Principal-Email': email,
                                'X-Ms-Client-Principal-Role': ['User'],
                                'X-Ms-Token-Aad-Id-Token': f"easy_auth_principal:{email}",
                                'access_token': f"easy_auth_principal:{email}"
                            }
                            return _create_user_object_from_raw(raw_user_object)
                    except Exception as e:
                        logging.error(f"Error parsing Easy Auth principal: {e}")

                # If we still can't get user info in production, use fallback
                logging.warning("Production mode: No authentication found, using fallback user")
                return _create_user_object_from_raw(create_development_user())

        # Validate token
        is_valid, claims = await validate_token(token)
        if not is_valid:
            logging.warning("Invalid authentication token")
            if is_development:
                logging.info("Development mode: Using mock user due to invalid token")
                return _create_user_object_from_raw(create_development_user())
            else:
                logging.warning("Production mode: Invalid token, using fallback user")
                return _create_user_object_from_raw(create_development_user())

        # Get user info from Microsoft Graph API or token claims
        user_info = await get_user_info_from_graph(token)
        if user_info:
            # Convert user_info to the legacy format
            raw_user_object = {
                'X-Ms-Client-Principal-Idp': 'aad',
                'X-Ms-Client-Principal-Id': user_info.get('id'),
                'X-Ms-Client-Principal-Name': user_info.get('name'),
                'X-Ms-Client-Principal-Email': user_info.get('email'),
                'X-Ms-Client-Principal-Role': ['User'],  # Default role
                'X-Ms-Token-Aad-Id-Token': token,
                'access_token': token
            }
            logging.info(f"Successfully authenticated user: {user_info.get('email')}")
            return _create_user_object_from_raw(raw_user_object)
        else:
            logging.warning("Failed to get user info from Graph API")
            if is_development:
                logging.info("Development mode: Using mock user due to Graph API failure")
                return _create_user_object_from_raw(create_development_user())
            else:
                logging.warning("Production mode: Graph API failed, using fallback user")
                return _create_user_object_from_raw(create_development_user())

    except Exception as e:
        logging.error(f"Error in authentication: {e}", exc_info=True)
        if is_development:
            logging.info("Development mode: Using mock user due to authentication error")
            return _create_user_object_from_raw(create_development_user())
        else:
            logging.warning("Production mode: Authentication error, using fallback user")
            return _create_user_object_from_raw(create_development_user())

def _create_user_object_from_raw(raw_user_object: dict) -> dict:
    """Helper function to create the legacy user object format"""
    user_object = {}

    # Extract user details into the final user_object
    user_object['user_principal_id'] = raw_user_object.get('X-Ms-Client-Principal-Id')
    user_object['user_name'] = raw_user_object.get('X-Ms-Client-Principal-Name')
    user_object['user_email'] = raw_user_object.get('X-Ms-Client-Principal-Email', '<EMAIL>') # Default email
    user_object['auth_provider'] = raw_user_object.get('X-Ms-Client-Principal-Idp', 'unknown')

    # Use token from raw user object
    user_object['auth_token'] = raw_user_object.get('access_token') or raw_user_object.get('X-Ms-Token-Aad-Id-Token')

    # These fields are kept for backward compatibility but may not be available in all cases
    user_object['client_principal_b64'] = raw_user_object.get('X-Ms-Client-Principal')
    user_object['aad_id_token'] = raw_user_object.get('X-Ms-Token-Aad-Id-Token')

    # Handle roles: if it's a list, use it, otherwise wrap string in a list. Default to ['User']
    roles_from_raw = raw_user_object.get('X-Ms-Client-Principal-Role', 'User')
    if isinstance(roles_from_raw, list):
        user_object['roles'] = roles_from_raw if roles_from_raw else ['User']
    else:
        user_object['roles'] = [roles_from_raw]

    return user_object

def create_development_user() -> Dict[str, Any]:
    """Create a mock user for development purposes"""
    return {
        'X-Ms-Client-Principal-Id': '11111111-1111-1111-1111-111111111111',
        'X-Ms-Client-Principal-Name': 'Super Admin',
        'X-Ms-Client-Principal-Email': '<EMAIL>',
        'X-Ms-Client-Principal-Idp': 'aad',
        'X-Ms-Client-Principal-Role': 'SuperAdmin'
    }


auth_bp = Blueprint("auth", __name__, url_prefix="/api/auth")

@auth_bp.route("/logout", methods=["POST"])
async def logout():
    try:
        # Create a response using await
        response = await make_response(jsonify({"message": "Logged out successfully"}))

        # Set cookies to expire immediately
        response.set_cookie("session", "", expires=0)
        response.set_cookie(".AspNetCore.Cookies", "", expires=0)
        response.set_cookie("AppServiceAuthSession", "", expires=0)

        # Check if AUTH_LOGOUT_ENDPOINT is configured (for production)
        auth_logout_endpoint = os.getenv("AUTH_LOGOUT_ENDPOINT")

        if auth_logout_endpoint:
            # If AUTH_LOGOUT_ENDPOINT is set, use it directly (production mode)
            logout_url = auth_logout_endpoint
            logging.info(f"Using configured AUTH_LOGOUT_ENDPOINT: {logout_url}")
        else:
            # Fallback to provider-specific logout URLs (development mode)
            auth_provider = request.headers.get('X-Ms-Client-Principal-Idp', '').lower()

            # Default logout URL (if no specific provider is detected)
            logout_url = "/"

            # For Azure AD (aad), use the Azure AD logout endpoint
            if auth_provider == 'aad':
                post_logout_redirect = f"{request.host_url.rstrip('/')}"
                logout_url = f"https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri={post_logout_redirect}"
                logging.info(f"Using AAD logout URL: {logout_url}")
            # For Microsoft Account (microsoftaccount)
            elif auth_provider == 'microsoftaccount':
                logout_url = "https://login.live.com/oauth20_logout.srf"
                logging.info(f"Using Microsoft Account logout URL: {logout_url}")
            # If EasyAuth is configured, try the standard EasyAuth logout endpoint
            elif os.getenv("WEBSITE_AUTH_ENABLED") == "True":
                logout_url = "/.auth/logout"
                logging.info(f"Using EasyAuth logout URL: {logout_url}")

        logging.info(f"Final Logout URL: {logout_url}")

        # Include the logout URL in the response body
        response.set_data(json.dumps({
            "message": "Logged out successfully",
            "logoutUrl": logout_url
        }))
        response.mimetype = "application/json"

        return response

    except Exception as e:
        logging.error(f"Logout error: {str(e)}")
        # Add more detailed error logging
        logging.exception("Full logout error details:")
        return jsonify({"error": str(e), "fallbackUrl": "/"}, 500)

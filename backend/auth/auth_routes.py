"""
Authentication routes for handling user sessions and tokens.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import Dict, Any
from backend.auth.entra_auth import get_entra_user_with_delegated_token, extract_token_from_request_with_easy_auth
from backend.auth.token_utils import validate_token
import logging

router = APIRouter(prefix="/api/auth", tags=["Authentication"])

@router.post("/session-login")
async def session_login(request: Request):
    """
    Handle user session login with token validation.

    This endpoint validates the provided token and returns user information.
    """
    try:
        # Extract token from request using Easy Auth-aware function
        token = extract_token_from_request_with_easy_auth(request)
        if not token:
            logging.error("No authentication token found in request")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication token required"
            )

        # Validate token
        is_valid, claims = await validate_token(token)
        if not is_valid:
            logging.error("Invalid authentication token")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token"
            )

        # Get user information
        current_user = await get_entra_user_with_delegated_token(request)
        if not current_user:
            logging.error("No user information returned from auth service")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to get user information"
            )

        # Return user information
        return {
            "user": current_user,
            "token_valid": True
        }

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in session login: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process login: {str(e)}"
        )

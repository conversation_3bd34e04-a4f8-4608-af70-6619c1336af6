"""
Azure Resource Cleanup Service

This service handles the automated cleanup of Azure resources when a project is deleted.
It safely removes all associated Azure resources including storage accounts, search services,
function apps, and event grid resources.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
from azure.identity.aio import DefaultAzureCredential
from azure.mgmt.storage.aio import StorageManagementClient
from azure.mgmt.search.aio import SearchManagementClient
from azure.mgmt.web.aio import WebSiteManagementClient
from azure.mgmt.eventgrid.aio import EventGridManagementClient
from azure.mgmt.resource.resources.aio import ResourceManagementClient
from azure.core.exceptions import ResourceNotFoundError, HttpResponseError
from backend.utils.logging_config import get_configured_logger

# Configure logger
logger = get_configured_logger("azure_resource_cleanup")


class AzureResourceCleanupService:
    """Service for cleaning up Azure resources associated with deleted projects."""

    def __init__(self, subscription_id: str, resource_group_name: str):
        """
        Initialize the Azure Resource Cleanup Service.

        Args:
            subscription_id: Azure subscription ID
            resource_group_name: Resource group containing the resources
        """
        self.subscription_id = subscription_id
        self.resource_group_name = resource_group_name
        self.credential = DefaultAzureCredential()

        # Initialize Azure management clients
        self.storage_client = None
        self.search_client = None
        self.web_client = None
        self.eventgrid_client = None
        self.resource_client = None

        logger.info(
            f"Initialized Azure Resource Cleanup Service for subscription: {subscription_id}"
        )

    async def __aenter__(self):
        """Async context manager entry."""
        await self._initialize_clients()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit - cleanup clients."""
        await self._cleanup_clients()

    async def _initialize_clients(self):
        """Initialize Azure management clients."""
        self.storage_client = StorageManagementClient(
            self.credential, self.subscription_id
        )
        self.search_client = SearchManagementClient(
            self.credential, self.subscription_id
        )
        self.web_client = WebSiteManagementClient(self.credential, self.subscription_id)
        self.eventgrid_client = EventGridManagementClient(
            self.credential, self.subscription_id
        )
        self.resource_client = ResourceManagementClient(
            self.credential, self.subscription_id
        )
        logger.info("Azure management clients initialized")

    async def _cleanup_clients(self):
        """Cleanup Azure management clients."""
        clients = [
            self.storage_client,
            self.search_client,
            self.web_client,
            self.eventgrid_client,
            self.resource_client,
        ]

        for client in clients:
            if client:
                try:
                    await client.close()
                except Exception as e:
                    logger.warning(f"Error closing client: {e}")

        # Also close the credential if it has a close method
        if hasattr(self.credential, "close"):
            try:
                await self.credential.close()
            except Exception as e:
                logger.warning(f"Error closing credential: {e}")

        logger.info("Azure management clients closed")

    async def cleanup_project_resources(
        self, project_data: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Clean up all Azure resources associated with a project.

        Args:
            project_data: Project data containing resource information

        Returns:
            Tuple of (success: bool, cleanup_report: dict)
        """
        project_id = project_data.get("id")
        project_name = project_data.get("name", "Unknown")

        logger.info(
            f"Starting resource cleanup for project: {project_id} ({project_name})"
        )

        cleanup_report = {
            "project_id": project_id,
            "project_name": project_name,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "resources_deleted": {},
            "errors": [],
            "success": True,
        }

        try:
            # Delete resources in order (considering dependencies)

            # 1. Delete Event Grid subscriptions first
            await self._delete_event_grid_resources(project_data, cleanup_report)

            # 2. Delete Function App
            await self._delete_function_app(project_data, cleanup_report)

            # 3. Delete Search Service resources
            await self._delete_search_resources(project_data, cleanup_report)

            # 4. Delete Storage Account (last, as other services might depend on it)
            await self._delete_storage_account(project_data, cleanup_report)

            # 5. Delete any remaining resources by tag
            await self._delete_resources_by_tag(project_id, cleanup_report)

        except Exception as e:
            logger.error(
                f"Error during resource cleanup for project {project_id}: {str(e)}"
            )
            cleanup_report["errors"].append(f"General cleanup error: {str(e)}")
            cleanup_report["success"] = False

        # Log cleanup summary
        self._log_cleanup_summary(cleanup_report)

        return cleanup_report["success"], cleanup_report

    async def _delete_storage_account(
        self, project_data: Dict[str, Any], report: Dict[str, Any]
    ):
        """Delete storage account and containers."""
        storage_account_name = project_data.get("storage_account_name")

        if not storage_account_name:
            logger.warning("No storage account name found in project data")
            return

        try:
            logger.info(f"Attempting to delete storage account: {storage_account_name}")

            # Delete the storage account
            await self.storage_client.storage_accounts.delete(
                self.resource_group_name, storage_account_name
            )

            logger.info(f"Successfully deleted storage account: {storage_account_name}")
            report["resources_deleted"]["storage_account"] = {
                "name": storage_account_name,
                "containers": [
                    project_data.get("storage_container_uploads"),
                    project_data.get("storage_container_input"),
                    project_data.get("storage_container_output"),
                ],
            }

        except ResourceNotFoundError:
            logger.warning(f"Storage account {storage_account_name} not found")
            report["errors"].append(f"Storage account {storage_account_name} not found")
        except Exception as e:
            logger.error(
                f"Error deleting storage account {storage_account_name}: {str(e)}"
            )
            report["errors"].append(f"Failed to delete storage account: {str(e)}")
            report["success"] = False

    async def _delete_search_resources(
        self, project_data: Dict[str, Any], report: Dict[str, Any]
    ):
        """Delete Azure Search Service and related resources."""
        search_service_name = project_data.get("search_service_name")

        if not search_service_name:
            logger.warning("No search service name found in project data")
            return

        try:
            logger.info(f"Attempting to delete search service: {search_service_name}")

            # Delete the search service
            await self.search_client.services.delete(
                self.resource_group_name, search_service_name
            )

            logger.info(f"Successfully deleted search service: {search_service_name}")
            report["resources_deleted"]["search_service"] = {
                "name": search_service_name,
                "index": project_data.get("search_index_name"),
                "datasource": project_data.get("search_datasource_name"),
                "indexer": project_data.get("search_indexer_name"),
            }

        except ResourceNotFoundError:
            logger.warning(f"Search service {search_service_name} not found")
            report["errors"].append(f"Search service {search_service_name} not found")
        except Exception as e:
            logger.error(
                f"Error deleting search service {search_service_name}: {str(e)}"
            )
            report["errors"].append(f"Failed to delete search service: {str(e)}")
            report["success"] = False

    async def _delete_function_app(
        self, project_data: Dict[str, Any], report: Dict[str, Any]
    ):
        """Delete Azure Function App."""
        function_app_name = project_data.get("function_app_name")

        if not function_app_name:
            logger.warning("No function app name found in project data")
            return

        try:
            logger.info(f"Attempting to delete function app: {function_app_name}")

            # Delete the function app
            await self.web_client.web_apps.delete(
                self.resource_group_name, function_app_name
            )

            logger.info(f"Successfully deleted function app: {function_app_name}")
            report["resources_deleted"]["function_app"] = {
                "name": function_app_name,
                "url": project_data.get("function_app_url"),
            }

            # Also try to delete the associated App Service Plan
            # Function apps often create an App Service Plan with a similar name
            try:
                app_service_plans = (
                    self.web_client.app_service_plans.list_by_resource_group(
                        self.resource_group_name
                    )
                )

                async for plan in app_service_plans:
                    if function_app_name in plan.name:
                        logger.info(
                            f"Deleting associated App Service Plan: {plan.name}"
                        )
                        await self.web_client.app_service_plans.delete(
                            self.resource_group_name, plan.name
                        )
                        report["resources_deleted"]["app_service_plan"] = plan.name

            except Exception as e:
                logger.warning(f"Error deleting App Service Plan: {str(e)}")

        except ResourceNotFoundError:
            logger.warning(f"Function app {function_app_name} not found")
            report["errors"].append(f"Function app {function_app_name} not found")
        except Exception as e:
            logger.error(f"Error deleting function app {function_app_name}: {str(e)}")
            report["errors"].append(f"Failed to delete function app: {str(e)}")
            report["success"] = False

    async def _delete_event_grid_resources(
        self, project_data: Dict[str, Any], report: Dict[str, Any]
    ):
        """Delete Event Grid topics and subscriptions."""
        system_topic_name = project_data.get("event_grid_system_topic_name")
        topic_name = project_data.get("event_grid_topic_name")

        deleted_resources = {}

        # Delete system topic
        if system_topic_name:
            try:
                logger.info(
                    f"Attempting to delete Event Grid system topic: {system_topic_name}"
                )

                # First, list and delete all event subscriptions for this topic
                subscriptions = self.eventgrid_client.system_topic_event_subscriptions.list_by_system_topic(
                    self.resource_group_name, system_topic_name
                )

                async for subscription in subscriptions:
                    logger.info(
                        f"Deleting Event Grid subscription: {subscription.name}"
                    )
                    poller = await self.eventgrid_client.system_topic_event_subscriptions.begin_delete(
                        self.resource_group_name, system_topic_name, subscription.name
                    )
                    await poller.result()

                # Delete the system topic
                poller = await self.eventgrid_client.system_topics.begin_delete(
                    self.resource_group_name, system_topic_name
                )
                await poller.result()

                logger.info(
                    f"Successfully deleted Event Grid system topic: {system_topic_name}"
                )
                deleted_resources["system_topic"] = system_topic_name

            except ResourceNotFoundError:
                logger.warning(f"Event Grid system topic {system_topic_name} not found")
            except Exception as e:
                logger.error(f"Error deleting Event Grid system topic: {str(e)}")
                report["errors"].append(
                    f"Failed to delete Event Grid system topic: {str(e)}"
                )

        # Delete regular topic if exists
        if topic_name:
            try:
                logger.info(f"Attempting to delete Event Grid topic: {topic_name}")

                poller = await self.eventgrid_client.topics.begin_delete(
                    self.resource_group_name, topic_name
                )
                await poller.result()

                logger.info(f"Successfully deleted Event Grid topic: {topic_name}")
                deleted_resources["topic"] = topic_name

            except ResourceNotFoundError:
                logger.warning(f"Event Grid topic {topic_name} not found")
            except Exception as e:
                logger.error(f"Error deleting Event Grid topic: {str(e)}")
                report["errors"].append(f"Failed to delete Event Grid topic: {str(e)}")

        if deleted_resources:
            report["resources_deleted"]["event_grid"] = deleted_resources

    async def _delete_resources_by_tag(self, project_id: str, report: Dict[str, Any]):
        """Delete any remaining resources tagged with the project ID."""
        try:
            logger.info(f"Looking for resources tagged with project ID: {project_id}")

            # List all resources in the resource group
            resources = self.resource_client.resources.list_by_resource_group(
                self.resource_group_name,
                filter=f"tagName eq 'project_id' and tagValue eq '{project_id}'",
            )

            deleted_by_tag = []

            async for resource in resources:
                try:
                    logger.info(
                        f"Deleting tagged resource: {resource.name} (Type: {resource.type})"
                    )

                    # Delete the resource
                    await self.resource_client.resources.delete_by_id(
                        resource.id, api_version="2021-04-01"
                    )

                    deleted_by_tag.append(
                        {
                            "name": resource.name,
                            "type": resource.type,
                            "id": resource.id,
                        }
                    )

                except Exception as e:
                    logger.error(
                        f"Error deleting tagged resource {resource.name}: {str(e)}"
                    )
                    report["errors"].append(
                        f"Failed to delete tagged resource {resource.name}: {str(e)}"
                    )

            if deleted_by_tag:
                report["resources_deleted"]["tagged_resources"] = deleted_by_tag
                logger.info(f"Deleted {len(deleted_by_tag)} tagged resources")

        except Exception as e:
            logger.error(f"Error searching for tagged resources: {str(e)}")
            report["errors"].append(f"Failed to search for tagged resources: {str(e)}")

    def _log_cleanup_summary(self, report: Dict[str, Any]):
        """Log a summary of the cleanup operation."""
        logger.info("=" * 80)
        logger.info(
            f"CLEANUP SUMMARY for Project: {report['project_name']} ({report['project_id']})"
        )
        logger.info(f"Timestamp: {report['timestamp']}")
        logger.info(f"Overall Success: {report['success']}")

        logger.info("\nResources Deleted:")
        for resource_type, details in report["resources_deleted"].items():
            logger.info(f"  - {resource_type}: {details}")

        if report["errors"]:
            logger.info("\nErrors Encountered:")
            for error in report["errors"]:
                logger.info(f"  - {error}")

        logger.info("=" * 80)

    async def verify_resource_deletion(
        self, project_data: Dict[str, Any]
    ) -> Dict[str, bool]:
        """
        Verify that all resources have been deleted.

        Args:
            project_data: Project data containing resource information

        Returns:
            Dictionary with resource types and deletion status
        """
        verification_results = {}

        # Check storage account
        if storage_name := project_data.get("storage_account_name"):
            try:
                await self.storage_client.storage_accounts.get_properties(
                    self.resource_group_name, storage_name
                )
                verification_results["storage_account"] = False
            except ResourceNotFoundError:
                verification_results["storage_account"] = True

        # Check search service
        if search_name := project_data.get("search_service_name"):
            try:
                await self.search_client.services.get(
                    self.resource_group_name, search_name
                )
                verification_results["search_service"] = False
            except ResourceNotFoundError:
                verification_results["search_service"] = True

        # Check function app
        if function_name := project_data.get("function_app_name"):
            try:
                await self.web_client.web_apps.get(
                    self.resource_group_name, function_name
                )
                verification_results["function_app"] = False
            except ResourceNotFoundError:
                verification_results["function_app"] = True

        return verification_results


class SafeResourceCleanup:
    """Wrapper class that provides safety mechanisms for resource cleanup."""

    def __init__(self, cleanup_service: AzureResourceCleanupService):
        self.cleanup_service = cleanup_service
        self.dry_run = False

    async def cleanup_with_confirmation(
        self, project_data: Dict[str, Any], confirm_callback=None, dry_run: bool = False
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Clean up resources with optional confirmation and dry run.

        Args:
            project_data: Project data
            confirm_callback: Optional async callback for confirmation
            dry_run: If True, only simulate deletion

        Returns:
            Tuple of (success, report)
        """
        self.dry_run = dry_run

        if dry_run:
            logger.info("DRY RUN MODE - No resources will be deleted")
            return await self._simulate_cleanup(project_data)

        if confirm_callback:
            resources_to_delete = self._list_resources_to_delete(project_data)
            if not await confirm_callback(resources_to_delete):
                logger.info("Resource cleanup cancelled by user")
                return False, {"cancelled": True, "reason": "User cancelled"}

        return await self.cleanup_service.cleanup_project_resources(project_data)

    def _list_resources_to_delete(
        self, project_data: Dict[str, Any]
    ) -> List[Dict[str, str]]:
        """List all resources that will be deleted."""
        resources = []

        if storage := project_data.get("storage_account_name"):
            resources.append({"type": "Storage Account", "name": storage})

        if search := project_data.get("search_service_name"):
            resources.append({"type": "Search Service", "name": search})

        if function := project_data.get("function_app_name"):
            resources.append({"type": "Function App", "name": function})

        if event_grid := project_data.get("event_grid_system_topic_name"):
            resources.append({"type": "Event Grid Topic", "name": event_grid})

        return resources

    async def _simulate_cleanup(
        self, project_data: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Simulate cleanup without actually deleting resources."""
        report = {
            "project_id": project_data.get("id"),
            "project_name": project_data.get("name"),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "dry_run": True,
            "resources_to_delete": self._list_resources_to_delete(project_data),
            "success": True,
        }

        logger.info(
            f"DRY RUN - Would delete {len(report['resources_to_delete'])} resources"
        )

        return True, report

"""
Azure Resource Cleanup Configuration

This module provides configuration for the automated Azure resource cleanup process.
"""

import os
from typing import Optional

class CleanupConfig:
    """Configuration for Azure resource cleanup."""
    
    def __init__(self):
        # Azure subscription and resource group
        self.subscription_id: Optional[str] = os.environ.get("AZURE_SUBSCRIPTION_ID")
        self.resource_group_name: Optional[str] = os.environ.get("AZURE_RESOURCE_GROUP")
        
        # Cleanup behavior settings
        self.enabled: bool = os.environ.get("AZURE_CLEANUP_ENABLED", "true").lower() == "true"
        self.dry_run: bool = os.environ.get("AZURE_CLEANUP_DRY_RUN", "false").lower() == "true"
        self.require_confirmation: bool = os.environ.get("AZURE_CLEANUP_REQUIRE_CONFIRMATION", "false").lower() == "true"
        
        # Resource-specific cleanup flags
        self.cleanup_storage: bool = os.environ.get("AZURE_CLEANUP_STORAGE", "true").lower() == "true"
        self.cleanup_search: bool = os.environ.get("AZURE_CLEANUP_SEARCH", "true").lower() == "true"
        self.cleanup_function_apps: bool = os.environ.get("AZURE_CLEANUP_FUNCTION_APPS", "true").lower() == "true"
        self.cleanup_event_grid: bool = os.environ.get("AZURE_CLEANUP_EVENT_GRID", "true").lower() == "true"
        
        # Cleanup timeout (in seconds)
        self.cleanup_timeout: int = int(os.environ.get("AZURE_CLEANUP_TIMEOUT", "300"))  # 5 minutes default
        
        # Tag-based cleanup
        self.cleanup_by_tags: bool = os.environ.get("AZURE_CLEANUP_BY_TAGS", "true").lower() == "true"
        self.project_id_tag_name: str = os.environ.get("AZURE_PROJECT_ID_TAG", "project_id")
    
    def is_configured(self) -> bool:
        """Check if the cleanup service is properly configured."""
        return bool(self.subscription_id and self.resource_group_name)
    
    def validate(self) -> tuple[bool, Optional[str]]:
        """Validate the configuration.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not self.enabled:
            return True, None
            
        if not self.subscription_id:
            return False, "AZURE_SUBSCRIPTION_ID environment variable not set"
            
        if not self.resource_group_name:
            return False, "AZURE_RESOURCE_GROUP environment variable not set"
            
        return True, None


# Global configuration instance
cleanup_config = CleanupConfig()
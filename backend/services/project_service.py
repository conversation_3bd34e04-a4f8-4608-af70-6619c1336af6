import os
import logging
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from azure.cosmos.aio import CosmosClient
from azure.cosmos import exceptions
from azure.identity import ClientSecretCredential, DefaultAzureCredential

from backend.utils.cosmos import get_cosmos_client


class ProjectDataService:
    """Service layer for Cosmos DB project operations."""

    def __init__(self) -> None:
        self._credential = self._get_credential()
        self.client = get_cosmos_client()
        if not self.client:
            raise RuntimeError("Cosmos DB client is not initialized.")

        self.database_name = os.environ.get("AZURE_COSMOSDB_DATABASE")
        self.container_name = os.environ.get(
            "AZURE_COSMOSDB_PROJECTS_CONTAINER", "projects"
        )
        self.database = self.client.get_database_client(self.database_name)
        self.container = self.database.get_container_client(self.container_name)

    def _get_credential(self):
        """Return the appropriate Azure credential based on environment vars."""
        client_id = os.environ.get("AZURE_CLIENT_ID")
        client_secret = os.environ.get("AZURE_CLIENT_SECRET")
        tenant_id = os.environ.get("AZURE_TENANT_ID")

        if all([client_id, client_secret, tenant_id]):
            logging.info("Using ClientSecretCredential for authentication.")
            return ClientSecretCredential(
                tenant_id=tenant_id,
                client_id=client_id,
                client_secret=client_secret,
            )

        return DefaultAzureCredential()

    async def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a project document by ID across partitions."""
        query = "SELECT * FROM c WHERE c.id = @pid"
        params = [{"name": "@pid", "value": project_id}]
        try:
            # For async operations, cross-partition queries are handled automatically
            items = [
                item
                async for item in self.container.query_items(
                    query=query, 
                    parameters=params
                )
            ]
            return items[0] if items else None
        except exceptions.CosmosHttpResponseError as exc:  # pragma: no cover - network
            print(f"Error retrieving project {project_id}: {exc}")
            return None
        except TypeError as exc:
            # Handle the case where enable_cross_partition_query is not accepted
            print(f"TypeError in get_project: {exc}")
            print("Retrying without enable_cross_partition_query parameter")
            try:
                items = [
                    item
                    async for item in self.container.query_items(
                        query=query, 
                        parameters=params
                    )
                ]
                return items[0] if items else None
            except exceptions.CosmosHttpResponseError as exc2:
                print(f"Error retrieving project {project_id} (retry): {exc2}")
                return None

    async def update_project(self, project_id: str, project_data: Dict[str, Any]) -> bool:
        """Replace a project document using its region as partition key."""
        try:
            partition_key = project_data.get("region")
            if not partition_key:
                print(
                    f"Warning: Project {project_id} is missing a region for the partition key."
                )
                return False

            logging.info(
                f"[ProjectDataService] STARTING: 'replace_item' call for project {project_id}."
            )
            update_start_time = time.time()

            try:
                await self.container.replace_item(
                    item=project_id,
                    body=project_data,
                    partition_key=partition_key,
                )
            except TypeError:
                # Fallback for SDKs that don't require partition_key
                logging.info(
                    "Retrying replace_item without partition_key parameter"
                )
                await self.container.replace_item(item=project_id, body=project_data)

            update_duration = time.time() - update_start_time
            logging.info(
                f"[ProjectDataService] FINISHED: 'replace_item' for project {project_id} took {update_duration:.2f} seconds."
            )
            return True
        except exceptions.CosmosHttpResponseError as exc:  # pragma: no cover - network
            print(f"Error updating project {project_id}: {exc}")
            return False

    async def update_deployment_status(
        self,
        project_id: str,
        status: str,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
    ) -> bool:
        """Update the deployment_status field of a project."""
        project = await self.get_project(project_id)
        if not project:
            return False

        deployment_status = project.setdefault("deployment_status", {})
        deployment_status.update(
            {
                "status": status,
                "message": message,
                "updated_at": datetime.now(timezone.utc).isoformat(),
            }
        )
        if details:
            deployment_status["details"] = details
        if error:
            deployment_status["error"] = error

        project["updated_at"] = datetime.now(timezone.utc).isoformat()

        return await self.update_project(project_id, project)

    async def update_final_deployment_summary(
        self, project_id: str, summary_data: Dict[str, Any]
    ) -> bool:
        """Update the project document with final deployment summary."""
        logging.info(
            f"[ProjectDataService] STARTING: update_final_deployment_summary for project {project_id}."
        )

        project = await self.get_project(project_id)
        if not project:
            return False

        resources = summary_data.get("resources", {})
        for key, value in resources.items():
            if value:
                project[key] = value

        environment = project.setdefault("environment", {})
        environment.update(summary_data.get("environment", {}))

        project["deployment_status"] = {
            "status": summary_data.get("status", "success"),
            "message": "Project deployment completed",
            "updated_at": datetime.now(timezone.utc).isoformat(),
            "deployment_time": summary_data.get("deployment_time"),
            "resource_durations": summary_data.get("resource_durations"),
        }

        project["updated_at"] = datetime.now(timezone.utc).isoformat()

        logging.info(
            f"[ProjectDataService] Data merged. Calling final update_project for {project_id}."
        )

        return await self.update_project(project_id, project)

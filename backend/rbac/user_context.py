"""
User Context API for centralized RBAC permissions.
This module provides endpoints that return comprehensive user context information,
including permissions, accessible resources, and user details.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import Dict, Any, List, Optional
from backend.models.rbac import User<PERSON><PERSON>, Project<PERSON>ser<PERSON>ole, Team<PERSON>ser<PERSON><PERSON>
from backend.auth.mock_auth import get_mock_user
from backend.auth.entra_auth import get_entra_user, get_entra_user_with_delegated_token, is_entra_auth_configured
from backend.rbac.rbac_routes import get_rbac_client, get_authenticated_user, initialize_rbac_client
from backend.auth.token_utils import validate_token
from fastapi import Request, HTTPException, status
import logging
import os
import uuid
import re
from typing import Dict, Any, Optional, List
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
import logging
import os

# Create a router for user context endpoints
router = APIRouter(prefix="/api/user-context", tags=["User Context"])

def normalize_email(email: str) -> str:
    """Normalize email address to prevent duplication of external users.
    
    This function:
    1. Converts email to lowercase
    2. Removes #EXT# suffixes added by Microsoft for external users
    3. Handles common email variations
    4. Maps tenant.onmicrosoft.com emails to keyrus.com emails
    
    Args:
        email: The email address to normalize
        
    Returns:
        The normalized email address
    """
    if not email:
        return email
        
    # Convert to lowercase
    normalized = email.lower().strip()
    
    # Remove Microsoft external user suffix
    # Pattern: username_domain.com#EXT#@tenant.onmicrosoft.com
    # Should become: <EMAIL>
    ext_pattern = r'^(.+?)_([^_]+\.[^_]+)#ext#@.+\.onmicrosoft\.com$'
    match = re.match(ext_pattern, normalized)
    if match:
        username = match.group(1)
        domain = match.group(2).replace('_', '.')
        normalized = f"{username}@{domain}"
    else:
        # Handle cases where the entire email is before #EXT# (with underscores in username)
        # Pattern: username_with_underscores_domain.com#EXT#@tenant.onmicrosoft.com
        full_ext_pattern = r'^(.+)#ext#@.+\.onmicrosoft\.com$'
        match = re.match(full_ext_pattern, normalized)
        if match:
            email_part = match.group(1)
            # Find the last underscore that separates username from domain
            # Assume domain has at least one dot
            parts = email_part.split('_')
            for i in range(len(parts) - 1, 0, -1):
                potential_domain = '_'.join(parts[i:])
                if '.' in potential_domain:
                    username = '_'.join(parts[:i])
                    domain = potential_domain.replace('_', '.')
                    normalized = f"{username}@{domain}"
                    break
        else:
            # Handle direct onmicrosoft.com emails
            # Pattern: <EMAIL> -> <EMAIL>
            onmicrosoft_pattern = r'^(.+?)@(\w+)\.onmicrosoft\.com$'
            match = re.match(onmicrosoft_pattern, normalized)
            if match:
                username = match.group(1)
                tenant = match.group(2)
                # Map known tenants to their domains
                tenant_mapping = {
                    'keyrusbelgium': 'keyrus.com',
                    'keyrus': 'keyrus.com',
                    # Add more mappings as needed
                }
                if tenant in tenant_mapping:
                    normalized = f"{username}@{tenant_mapping[tenant]}"
    
    return normalized

# Define permission structure based on user roles
def get_role_permissions(role: str) -> Dict[str, bool]:
    """
    Get permissions based on user role.
    This centralizes permission logic on the backend instead of the frontend.
    """
    if role == UserRole.SUPER_ADMIN.value:
        return {
            "canCreateProject": True,
            "canEditProject": True,
            "canDeleteProject": True,
            "canAssignUsers": True,
            "canSetCostLimits": True,
            "canAccessAdminPanel": True,
            "canManageUsers": True,
            "canManageGlobalSettings": True,
            "canCreateTeams": True,
            "canAssignProjects": True,
            "canAssignTeams": True,
            "canSetupRegionalAdmins": True,
            "canTagUsers": True,
            "canViewAllRegions": True
        }
    elif role == UserRole.REGIONAL_ADMIN.value:
        return {
            "canCreateProject": True,
            "canEditProject": True,
            "canDeleteProject": True,
            "canAssignUsers": True,
            "canSetCostLimits": True,
            "canAccessAdminPanel": True,
            "canManageUsers": True,
            "canManageGlobalSettings": False,
            "canCreateTeams": True,
            "canAssignProjects": True,
            "canAssignTeams": True,
            "canSetupRegionalAdmins": False,
            "canTagUsers": True,
            "canViewAllRegions": False
        }
    else:  # REGULAR_USER or any other role
        return {
            "canCreateProject": False,
            "canEditProject": False,
            "canDeleteProject": False,
            "canAssignUsers": False,
            "canSetCostLimits": False,
            "canAccessAdminPanel": False,
            "canManageUsers": False,
            "canManageGlobalSettings": False,
            "canCreateTeams": False,
            "canAssignProjects": False,
            "canAssignTeams": False,
            "canSetupRegionalAdmins": False,
            "canTagUsers": False,
            "canViewAllRegions": False
        }

@router.get("/me", response_model=Dict[str, Any])
async def get_user_context(request: Request):
    """
    Get comprehensive user context including:
    - User details
    - Permissions
    - Accessible resources (projects, teams, regions)

    This endpoint replaces multiple frontend RBAC calls with a single call.
    """
    rbac_client = None
    try:
        # Initialize RBAC client first so we can use it throughout the function
        rbac_client = await initialize_rbac_client(request)

        # Try to get user from token first
        try:
            from backend.auth.entra_auth import extract_token_from_request_with_easy_auth
            from backend.auth.token_utils import validate_token, get_user_info_from_graph

            # Extract token from request using Easy Auth-aware function
            token = extract_token_from_request_with_easy_auth(request)
            current_user = None

            if token:
                # Validate token
                is_valid, claims = await validate_token(token)
                if is_valid:
                    # Get user info from Microsoft Graph API
                    user_info = await get_user_info_from_graph(token)
                    if user_info:
                        current_user = user_info
                        logging.info(f"Successfully authenticated user from token: {user_info.get('name')} ({user_info.get('email')})")

                        # For Easy Auth, prioritize lookup by email since user IDs from tokens
                        # might not match database IDs
                        db_user = None
                        if user_info.get("email"):
                            # Normalize the email to handle external users
                            normalized_email = normalize_email(user_info.get("email"))
                            logging.info(f"Looking up user by email: {user_info.get('email')} (normalized: {normalized_email})")
                            
                            # Get all users and filter by normalized email first
                            all_users = await rbac_client.get_users()
                            for user in all_users:
                                if user.get("email"):
                                    # Compare normalized emails
                                    db_normalized_email = normalize_email(user.get("email"))
                                    if db_normalized_email == normalized_email:
                                        db_user = user
                                        logging.info(f"Found user in database by normalized email: {db_user}")
                                        break

                        # If not found by email, try to find by ID as fallback
                        if not db_user and user_info.get("id"):
                            logging.info(f"User not found by email, trying to find by ID: {user_info.get('id')}")
                            db_user = await rbac_client.get_user(user_info.get("id"))
                            if db_user:
                                logging.info(f"Found user in database by ID: {db_user}")

                        if db_user:
                            # Use the database user information
                            logging.info(f"Found user in database: {db_user}")

                            # Update current_user with database values while preserving authentication info
                            for key, value in db_user.items():
                                if key not in current_user:
                                    current_user[key] = value

                            # Ensure role is preserved from database
                            current_user["role"] = db_user.get("role", current_user.get("role", UserRole.REGULAR_USER.value))
                            logging.info(f"Using role from database: {current_user['role']}")
                        else:
                            logging.warning(f"User not found in database by ID or email: {user_info.get('id')} / {user_info.get('email')}")

            # If token authentication failed, try to get user from database using user ID from headers
            if not current_user or not current_user.get("id"):
                # Try to get user ID from headers
                user_id = request.headers.get("X-Current-User-ID")
                if user_id:
                    # Try to get user from database
                    db_user = await rbac_client.get_user(user_id)
                    if db_user:
                        current_user = db_user
                        logging.info(f"Using user from database with ID from headers: {db_user.get('name')} ({db_user.get('email')})")

            # If still no user after header check, raise 401 to force re-authentication
            if not current_user or not current_user.get("id"):
                logging.error("No valid user found in request or database after authentication checks")
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
        except HTTPException:
            # Propagate HTTP exceptions such as the 401 above
            raise
        except Exception as auth_error:
            logging.error(f"Error during authentication: {auth_error}", exc_info=True)
            from backend.auth.mock_auth import get_mock_user
            current_user = await get_mock_user("3")  # Use Regular User instead of Super Admin
            logging.warning(f"Authentication error, using mock user: {current_user.get('name')}")

        # Ensure we have a valid user at this point
        if not current_user or not current_user.get("id"):
            logging.error("No valid user found after all fallback attempts")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User authentication required"
            )

        # RBAC client already initialized above

        # Get user email from authenticated user details
        user_id = current_user.get("id")
        user_email = current_user.get("email")
        user_name = current_user.get("name", "Unknown User")

        # Log the authenticated user details
        logging.info(f"Authenticated user: {user_name} ({user_email})")

        # Try to find user in database by ID first
        db_user = await rbac_client.get_user(user_id)
        
        # If not found by ID, try to find by normalized email to prevent duplicates
        if not db_user and user_email:
            normalized_email = normalize_email(user_email)
            logging.info(f"User not found by ID, checking by normalized email: {normalized_email}")
            
            all_users = await rbac_client.get_users()
            for user in all_users:
                if user.get("email"):
                    db_normalized_email = normalize_email(user.get("email"))
                    if db_normalized_email == normalized_email:
                        db_user = user
                        logging.info(f"Found existing user by normalized email: {db_user}")
                        break

        # If user not found in database by ID or email, create a new Regular User
        if not db_user:
            logging.info(f"User with ID {user_id} not found in database, creating new Regular User")

            # Create a new user with Regular User role
            # Make sure we have valid values for name and email
            if not user_name or user_name == 'Unknown User':
                # Try to create a name from email
                if user_email and '@' in user_email:
                    # Use normalized email for name generation
                    normalized_email = normalize_email(user_email)
                    email_name = normalized_email.split('@')[0]
                    if '.' in email_name:
                        user_name = ' '.join(part.capitalize() for part in email_name.split('.'))
                    else:
                        user_name = email_name.capitalize()
                else:
                    user_name = "Unknown User"

            new_user = {
                "id": user_id,
                "type": "user",
                "name": user_name,
                "email": user_email,  # Store original email as provided by Microsoft
                "normalized_email": normalize_email(user_email),  # Store normalized email for lookup
                "role": UserRole.REGULAR_USER.value,
                "region": "westeurope"  # Default region
            }

            # Save the new user to the database
            db_user = await rbac_client.create_user(new_user)
            logging.info(f"Created new user: {db_user}")

            # Update current_user with database values
            current_user = db_user
        else:
            # Use the database user information
            logging.info(f"Found user in database: {db_user}")

            # Update current_user with database values while preserving authentication info
            for key, value in db_user.items():
                if key not in current_user:
                    current_user[key] = value

            # Ensure role is preserved from database
            current_user["role"] = db_user.get("role", current_user.get("role", UserRole.REGULAR_USER.value))

        # Ensure the user has a role assigned
        if "role" not in current_user or not current_user["role"]:
            logging.info(f"User {current_user.get('email')} has no role, assigning Regular User role")
            current_user["role"] = UserRole.REGULAR_USER.value
            # Update the user in the database
            await rbac_client.update_user(current_user["id"], {"role": UserRole.REGULAR_USER.value})

        # Handle region based on role
        if current_user["role"] == UserRole.SUPER_ADMIN.value:
            # Super admins should not have a region
            if "region" in current_user:
                logging.info(f"Removing region from Super Admin user {current_user.get('email')}")
                del current_user["region"]
                # Update the user in the database to remove region
                await rbac_client.update_user(current_user["id"], {"region": None})
        elif "region" not in current_user or not current_user["region"]:
            # Ensure non-super-admin users have a region
            logging.info(f"User {current_user.get('email')} has no region, assigning default region")
            current_user["region"] = "westeurope"
            # Update the user in the database
            await rbac_client.update_user(current_user["id"], {"region": "westeurope"})

        # Get user permissions based on role
        permissions = get_role_permissions(current_user["role"])

        # Get accessible resources based on user's role and permissions
        accessible_resources = {}

        # Get accessible projects
        try:
            accessible_resources["projects"] = await rbac_client.get_accessible_projects(current_user["id"])
        except Exception as e:
            logging.error(f"Error getting accessible projects: {e}")
            accessible_resources["projects"] = []

        # Get accessible teams
        try:
            accessible_resources["teams"] = await rbac_client.get_accessible_teams(current_user["id"])
        except Exception as e:
            logging.error(f"Error getting accessible teams: {e}")
            accessible_resources["teams"] = []

        # Get accessible regions (based on permissions)
        try:
            if permissions["canViewAllRegions"]:
                accessible_resources["regions"] = await rbac_client.get_regions()
            elif current_user.get("region"):
                # For regional admins and regular users, only get their assigned region
                region = await rbac_client.get_region(current_user["region"])
                accessible_resources["regions"] = [region] if region else []
            else:
                accessible_resources["regions"] = []
        except Exception as e:
            logging.error(f"Error getting accessible regions: {e}")
            accessible_resources["regions"] = []

        # Get accessible users (based on permissions)
        try:
            accessible_resources["users"] = await rbac_client.get_accessible_users(current_user["id"])
        except Exception as e:
            logging.error(f"Error getting accessible users: {e}")
            accessible_resources["users"] = []

        # Construct the complete user context
        user_context = {
            "user": current_user,
            "permissions": permissions,
            "accessibleResources": accessible_resources
        }

        return user_context

    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logging.error(f"Error getting user context: {e}")
        logging.error(f"Traceback: {error_traceback}")

        # Return a basic response instead of raising an exception
        # This will help the frontend get at least some data
        from backend.auth.mock_auth import get_mock_user
        from backend.auth.entra_auth import extract_token_from_request_with_easy_auth
        from backend.auth.token_utils import validate_token

        try:
            # First try to get user email from token claims
            token = extract_token_from_request_with_easy_auth(request)
            if token:
                try:
                    is_valid, claims = await validate_token(token)
                    if is_valid and claims and claims.get("preferred_username") and rbac_client:
                        email = claims.get("preferred_username")
                        logging.info(f"Trying to find user by email from token claims: {email}")

                        # Get all users and filter by email
                        all_users = await rbac_client.get_users()
                        for user in all_users:
                            if user.get("email") and user.get("email").lower() == email.lower():
                                db_user = user
                                logging.info(f"Found user in database by email from claims: {db_user}")

                                return {
                                    "user": db_user,
                                    "permissions": get_role_permissions(db_user["role"]),
                                    "accessibleResources": {
                                        "projects": [],
                                        "teams": [],
                                        "regions": [],
                                        "users": []
                                    }
                                }
                except Exception as token_error:
                    logging.error(f"Error extracting email from token claims: {token_error}")

            # If that fails, try to get user ID from headers
            user_id = request.headers.get("X-Current-User-ID")
            if user_id and rbac_client:
                # Try to get user from database
                db_user = await rbac_client.get_user(user_id)
                if db_user:
                    # Log what we're returning
                    logging.info(f"Returning database user due to error: {db_user}")

                    return {
                        "user": db_user,
                        "permissions": get_role_permissions(db_user["role"]),
                        "accessibleResources": {
                            "projects": [],
                            "teams": [],
                            "regions": [],
                            "users": []
                        }
                    }
        except Exception as inner_e:
            logging.error(f"Error getting user from database in exception handler: {inner_e}")

        # If we couldn't get a user from the database, use a mock user
        mock_user = await get_mock_user("3")  # Use Regular User instead of Super Admin

        # Log what we're returning
        logging.info(f"Returning mock user due to error: {mock_user}")

        return {
            "user": mock_user,
            "permissions": get_role_permissions(mock_user["role"]),
            "accessibleResources": {
                "projects": [],
                "teams": [],
                "regions": [],
                "users": []
            }
        }

    finally:
        # Close the RBAC client
        if rbac_client:
            try:
                await rbac_client.close()
            except Exception as e:
                logging.error(f"Error closing RBAC client: {e}", exc_info=True)

@router.get("/project/{project_id}/permissions", response_model=Dict[str, Any])
async def get_project_permissions(
    project_id: str,
    request: Request,
    current_user: dict = Depends(get_authenticated_user)
):
    """
    Get user permissions for a specific project.
    This endpoint provides project-specific permissions based on the user's role
    and their relationship to the project.
    """
    try:
        # Initialize RBAC client with request context
        rbac_client = await initialize_rbac_client(request)

        # Get user email from authenticated user details
        user_id = current_user.get("id")
        user_email = current_user.get("email")
        user_name = current_user.get("name", "Unknown User")

        # Log the authenticated user details
        logging.info(f"Authenticated user for project permissions: {user_name} ({user_email})")

        # Try to find user in database by ID first
        db_user = await rbac_client.get_user(user_id)
        
        # If not found by ID, try to find by normalized email to prevent duplicates
        if not db_user and user_email:
            normalized_email = normalize_email(user_email)
            logging.info(f"User not found by ID, checking by normalized email: {normalized_email}")
            
            all_users = await rbac_client.get_users()
            for user in all_users:
                if user.get("email"):
                    db_normalized_email = normalize_email(user.get("email"))
                    if db_normalized_email == normalized_email:
                        db_user = user
                        logging.info(f"Found existing user by normalized email: {db_user}")
                        break

        # If user not found in database by ID or email, create a new Regular User
        if not db_user:
            logging.info(f"User with ID {user_id} not found in database, creating new Regular User")

            # Create a new user with Regular User role
            # Make sure we have valid values for name and email
            if not user_name or user_name == 'Unknown User':
                # Try to create a name from email
                if user_email and '@' in user_email:
                    # Use normalized email for name generation
                    normalized_email = normalize_email(user_email)
                    email_name = normalized_email.split('@')[0]
                    if '.' in email_name:
                        user_name = ' '.join(part.capitalize() for part in email_name.split('.'))
                    else:
                        user_name = email_name.capitalize()
                else:
                    user_name = "Unknown User"

            new_user = {
                "id": user_id,
                "type": "user",
                "name": user_name,
                "email": user_email,  # Store original email as provided by Microsoft
                "normalized_email": normalize_email(user_email),  # Store normalized email for lookup
                "role": UserRole.REGULAR_USER.value,
                "region": "westeurope"  # Default region
            }

            # Save the new user to the database
            db_user = await rbac_client.create_user(new_user)
            logging.info(f"Created new user for project permissions: {db_user}")

            # Update current_user with database values
            current_user = db_user
        else:
            # Use the database user information
            logging.info(f"Found user in database: {db_user}")

            # Update current_user with database values while preserving authentication info
            for key, value in db_user.items():
                if key not in current_user:
                    current_user[key] = value

            # Ensure role is preserved from database
            current_user["role"] = db_user.get("role", current_user.get("role", UserRole.REGULAR_USER.value))

        # Ensure the user has a role assigned
        if "role" not in current_user or not current_user["role"]:
            logging.info(f"User {current_user.get('email')} has no role, assigning Regular User role")
            current_user["role"] = UserRole.REGULAR_USER.value
            # Update the user in the database
            await rbac_client.update_user(current_user["id"], {"role": UserRole.REGULAR_USER.value})

        # Handle region based on role
        if current_user["role"] == UserRole.SUPER_ADMIN.value:
            # Super admins should not have a region
            if "region" in current_user:
                logging.info(f"Removing region from Super Admin user {current_user.get('email')}")
                del current_user["region"]
                # Update the user in the database to remove region
                await rbac_client.update_user(current_user["id"], {"region": None})
        elif "region" not in current_user or not current_user["region"]:
            # Ensure non-super-admin users have a region
            logging.info(f"User {current_user.get('email')} has no region, assigning default region")
            current_user["region"] = "westeurope"
            # Update the user in the database
            await rbac_client.update_user(current_user["id"], {"region": "westeurope"})

        # Get the project
        project = await rbac_client.get_project(project_id)
        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

    except Exception as e:
        logging.error(f"Error getting project permissions: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get project permissions: {str(e)}"
        )

    finally:
        # Close the RBAC client
        if rbac_client:
            try:
                await rbac_client.close()
            except Exception as e:
                logging.error(f"Error closing RBAC client: {e}", exc_info=True)

    # Get base permissions from user role
    base_permissions = get_role_permissions(current_user["role"])

    # Initialize project-specific permissions
    project_permissions = {
        "canViewProject": False,
        "canEditProject": False,
        "canDeleteProject": False,
        "canManageProjectUsers": False,
        "canManageProjectTeams": False,
        "canUploadFiles": False,
        "canDownloadFiles": False,
        "canRunAnalysis": False,
        "canViewAnalysisResults": False,
        "projectRole": None
    }

    # Super admin can do everything
    if current_user["role"] == UserRole.SUPER_ADMIN.value:
        project_permissions = {
            "canViewProject": True,
            "canEditProject": True,
            "canDeleteProject": True,
            "canManageProjectUsers": True,
            "canManageProjectTeams": True,
            "canUploadFiles": True,
            "canDownloadFiles": True,
            "canRunAnalysis": True,
            "canViewAnalysisResults": True,
            "projectRole": "SUPER_ADMIN"
        }

    # Regional admin can do everything for projects in their region
    elif current_user["role"] == UserRole.REGIONAL_ADMIN.value:
        if project.get("region") == current_user.get("region"):
            project_permissions = {
                "canViewProject": True,
                "canEditProject": True,
                "canDeleteProject": True,
                "canManageProjectUsers": True,
                "canManageProjectTeams": True,
                "canUploadFiles": True,
                "canDownloadFiles": True,
                "canRunAnalysis": True,
                "canViewAnalysisResults": True,
                "projectRole": "REGIONAL_ADMIN"
            }

    # Regular users need to check project role assignments
    else:
        # Check if user is directly assigned to the project
        project_users = await rbac_client.get_project_users(project_id)
        for user_assignment in project_users:
            if user_assignment.get("userId") == current_user["id"]:
                project_permissions["canViewProject"] = True
                project_permissions["projectRole"] = user_assignment.get("role")

                # Set permissions based on project role
                if user_assignment.get("role") == ProjectUserRole.OWNER.value:
                    project_permissions["canEditProject"] = True
                    project_permissions["canManageProjectUsers"] = True
                    project_permissions["canManageProjectTeams"] = True
                    project_permissions["canUploadFiles"] = True
                    project_permissions["canDownloadFiles"] = True
                    project_permissions["canRunAnalysis"] = True
                    project_permissions["canViewAnalysisResults"] = True

                elif user_assignment.get("role") == ProjectUserRole.CONTRIBUTOR.value:
                    project_permissions["canUploadFiles"] = True
                    project_permissions["canDownloadFiles"] = True
                    project_permissions["canRunAnalysis"] = True
                    project_permissions["canViewAnalysisResults"] = True

                elif user_assignment.get("role") == ProjectUserRole.VIEWER.value:
                    project_permissions["canDownloadFiles"] = True
                    project_permissions["canViewAnalysisResults"] = True

                break

        # If not directly assigned, check team assignments
        if not project_permissions["canViewProject"]:
            # Get user's teams
            user_teams = await rbac_client.get_user_teams(current_user["id"])
            user_team_ids = [team.get("teamId") for team in user_teams]

            # Check if any of the user's teams are assigned to the project
            project_teams = await rbac_client.get_project_teams(project_id)
            for team_assignment in project_teams:
                if team_assignment.get("teamId") in user_team_ids:
                    project_permissions["canViewProject"] = True
                    project_permissions["canDownloadFiles"] = True
                    project_permissions["canViewAnalysisResults"] = True
                    project_permissions["projectRole"] = "TEAM_MEMBER"
                    break

    return {
        "projectId": project_id,
        "userId": current_user["id"],
        "permissions": project_permissions,
        "basePermissions": base_permissions
    }

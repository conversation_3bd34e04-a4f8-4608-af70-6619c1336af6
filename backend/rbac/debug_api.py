# backend/rbac/debug_api.py
import asyncio
import logging
from fastapi import FastAP<PERSON>, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from backend.auth.mock_auth import get_mock_user
from backend.rbac.rbac_routes import router as rbac_router
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.settings import app_settings
import uvicorn

# Create a FastAPI app for debugging
app = FastAPI(title="RBAC Debug API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add a middleware to log all requests
@app.middleware("http")
async def log_requests(request: Request, call_next):
    # Log the request details
    logging.info(f"Request: {request.method} {request.url}")
    logging.info(f"Headers: {request.headers}")
    
    # Get the request body
    body = await request.body()
    if body:
        logging.info(f"Body: {body.decode()}")
    
    # Process the request
    response = await call_next(request)
    
    # Log the response status
    logging.info(f"Response status: {response.status_code}")
    
    return response

# Initialize the RBAC client
async def init_rbac_client():
    # Check if app_settings.chat_history is configured
    if not app_settings.chat_history:
        logging.error("CosmosDB settings not configured. RBAC requires CosmosDB.")
        return None
    
    # Construct the CosmosDB endpoint
    cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
    logging.info(f"CosmosDB endpoint: {cosmos_endpoint}")
    
    # Create a new RBAC client
    client = CosmosRbacClient(
        cosmos_endpoint,
        app_settings.chat_history.account_key,
        app_settings.chat_history.database
    )
    
    # Initialize the client
    init_result = await client.initialize()
    if not init_result:
        logging.error("Failed to initialize RBAC client")
        return None
    else:
        logging.info("RBAC client initialized successfully")
        return client

# Add a startup event to initialize the RBAC client
@app.on_event("startup")
async def startup_event():
    # Initialize the RBAC client
    import backend.rbac.rbac_routes as rbac_routes
    rbac_routes.rbac_client = await init_rbac_client()
    
    if not rbac_routes.rbac_client:
        logging.error("Failed to initialize RBAC client")
    else:
        logging.info("RBAC client initialized successfully")

# Add a debug endpoint to check the current user
@app.get("/debug/current-user")
async def debug_current_user(current_user: dict = Depends(get_mock_user)):
    return {"current_user": current_user}

# Add a debug endpoint to check if a user can be deleted
@app.get("/debug/can-delete-user/{user_id}")
async def debug_can_delete_user(user_id: str, current_user: dict = Depends(get_mock_user)):
    # Check if the user IDs match
    is_same_user = user_id == current_user["id"]
    
    # Get the user from the database
    import backend.rbac.rbac_routes as rbac_routes
    user = None
    if rbac_routes.rbac_client:
        user = await rbac_routes.rbac_client.get_user(user_id)
    
    return {
        "user_id": user_id,
        "current_user_id": current_user["id"],
        "is_same_user": is_same_user,
        "user_exists": user is not None,
        "user": user
    }

# Include the RBAC router
app.include_router(rbac_router)

# Run the app
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the app
    uvicorn.run(app, host="127.0.0.1", port=8000)

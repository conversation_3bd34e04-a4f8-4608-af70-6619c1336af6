# backend/rbac/debug_user_id.py
import asyncio
import logging
import os
from backend.auth.mock_auth import get_mock_user
from backend.rbac.rbac_routes import delete_user, init_rbac_client
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.settings import app_settings

async def debug_user_deletion():
    # Initialize the RBAC client
    print("Initializing RBAC client...")

    # Check if app_settings.chat_history is configured
    if not app_settings.chat_history:
        print("Error: CosmosDB settings not configured. RBAC requires CosmosDB.")
        return

    # Construct the CosmosDB endpoint
    cosmos_endpoint = f"https://{app_settings.chat_history.account}.documents.azure.com:443/"
    print(f"CosmosDB endpoint: {cosmos_endpoint}")

    # Create a new RBAC client
    client = CosmosRbacClient(
        cosmos_endpoint,
        app_settings.chat_history.account_key,
        app_settings.chat_history.database
    )

    # Initialize the client
    init_result = await client.initialize()
    if not init_result:
        print("Failed to initialize RBAC client")
        return
    else:
        print("RBAC client initialized successfully")

    # Set the global rbac_client in rbac_routes.py
    import backend.rbac.rbac_routes as rbac_routes
    rbac_routes.rbac_client = client

    # Test with a super admin user (ID: 1)
    super_admin = await get_mock_user("1")
    print(f"Super Admin User: {super_admin}")

    # Test with a user to delete (ID: ********-b8d3-4b8f-bdbf-69aa35ea476b)
    user_to_delete_id = "********-b8d3-4b8f-bdbf-69aa35ea476b"

    try:
        # Try to delete the user
        print(f"Attempting to delete user with ID: {user_to_delete_id}")
        print(f"Current user ID: {super_admin['id']}")
        print(f"Are they equal? {user_to_delete_id == super_admin['id']}")

        # Check if the user exists in the database
        user = await client.get_user(user_to_delete_id)
        if user:
            print(f"User found in database: {user}")
        else:
            print(f"User not found in database: {user_to_delete_id}")
            return

        # Call the delete_user function directly
        result = await delete_user(user_to_delete_id, super_admin)
        print(f"Delete result: {result}")
    except Exception as e:
        print(f"Error deleting user: {e}")

if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.DEBUG)

    # Run the async function
    asyncio.run(debug_user_deletion())

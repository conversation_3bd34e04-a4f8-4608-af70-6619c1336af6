Jira Task: COST-124
Project: AI Platform Engineering
Issue Type: Bug
Status: To Do
Title: Cost Analytics Dashboard Does Not Refresh with Time Range or Project Filter
Assignee: Virtual Full Stack Engineer
Reporter: Azure Mentor Copilot
Priority: High
Labels: bug, cost-management, backend, quart, cosmos-db, unit-tests, admin-dashboard
Created: 20/06/2025 13:30 CEST
Updated: 20/06/2025 13:30 CEST

Description
The Cost Analytics dashboard, located at /admin/cost-analytics, is not functioning as expected. When an admin user selects different time ranges (e.g., "Last 7 Days", "Last Month") or filters by a specific project, the displayed cost data does not update. The dashboard consistently shows the costs from only the most recent daily data entry and does not correctly filter by project.

The expected behavior is for the dashboard to query all relevant cost documents from the cost_data container in Cosmos DB that fall within the selected time range, aggregate the results, and display the costs for either all projects or the specific project selected by the user.

User Story:

As a platform administrator, I want to be able to filter the cost analytics view by different time ranges (e.g., 7 days, 30 days) and by a specific project, so that I can accurately track and report on Azure spending over time.

Problem Details:

Time Range Filter Not Working: The frontend correctly sends the timeRange parameter to the backend, but the API ignores it.

Project Filter Not Working: When a specific project is selected in the UI (e.g., "US_UAT"), the dashboard incorrectly continues to show data for another project ("Priority_Plot_testing_Project") or a limited, incorrect subset of projects. When "All Projects" is selected, it still only shows a single project's data.

Backend API: The /api/cost/overview endpoint in backend/cost_management/cost_api_quart.py is the source of the issue.

Root Cause: The get_latest_cost_data_from_cosmos function is hardcoded to fetch only the single most recent cost document from Cosmos DB using ORDER BY c.timestamp DESC OFFSET 0 LIMIT 1. This bypasses both the timeRange and projectId parameters sent from the frontend.

Affected Files:

backend/cost_management/cost_api_quart.py: Contains the primary bug in the /api/cost/overview route and the get_latest_cost_data_from_cosmos function.

frontend/src/pages/admin/CostAnalytics.tsx: The React component for the UI.

tests/test_cost_api_quart.py: The test file where new unit tests must be added.

Acceptance Criteria
When a user selects a timeRange of "7d" on the Cost Analytics page, the backend must query all documents from the cost_data container with a timestamp within the last 7 days.

The API must correctly aggregate the projectCosts from all fetched documents to calculate the totalCost.

The get_cost_overview endpoint must return the correctly aggregated total cost and per-project cost breakdown for the selected time period.

When a specific projectId is selected in the filter, the API response must only contain cost data for that single project.

When no projectId is selected (i.e., "All Projects"), the API response must contain an aggregated cost breakdown for all accessible projects.

A comprehensive suite of unit tests must be written in tests/test_cost_api_quart.py that:

Mocks the Cosmos DB client and provides multiple cost documents across different timestamps.

Includes a test that fails with the current buggy implementation but passes with the fix.

Covers different time ranges (e.g., 7 days, 30 days) and filtering scenarios (all projects vs. a single project).

The solution must be performant and adhere to the project's P95 API response time SLA of < 500ms.

Technical Implementation Plan
Write Failing Unit Tests:

In tests/test_cost_api_quart.py, create a new test class for the cost API.

Write a test case test_get_cost_overview_7_days that mocks the get_latest_cost_data_from_cosmos function to return multiple documents within a 7-day window. Assert that the totalCost is the sum of costs from all mocked documents.

Write a test case test_get_cost_overview_single_project that mocks the data and passes a specific projectId to the endpoint. Assert that the response only contains data for that project.

These tests should fail initially.

Refactor Backend Logic:

Modify the get_latest_cost_data_from_cosmos function in backend/cost_management/cost_api_quart.py to accept a time_range string as an argument.

Update the Cosmos DB query inside this function to use the time_range to construct a WHERE clause on the timestamp field. The query should select all documents within the specified date range. For example: SELECT * FROM c WHERE c.timestamp >= @start_date AND c.timestamp <= @end_date.

Update the get_cost_overview endpoint to call the modified function with the timeRange parameter from the request.

In the get_cost_overview endpoint, after fetching the documents, apply the projectId filter if it is provided in the request. Iterate through the projectCosts of each fetched document and include only those that match the requested projectId.

Verify with Tests:

Run the unit tests again. The previously failing tests should now pass.

Add additional tests to cover 30-day ranges and other edge cases to ensure full coverage.

Manual Verification:

Run the application locally and navigate to the Cost Analytics page.

Verify that changing the time range filter correctly updates the displayed cost data.

Verify that filtering by a specific project works as expected in conjunction with the time range filter.

Manual Validation with cURL
After implementing the fix, use these curl commands against the locally running application (http://localhost:50505) to perform integration testing.

Prerequisite: Obtain a valid Bearer token for an admin user and replace YOUR_AUTH_TOKEN in the commands below.

1. Test: Fetch All Project Costs (Last 7 Days)

curl -X GET 'http://localhost:50505/api/cost/overview?timeRange=7d' \
-H 'Authorization: Bearer YOUR_AUTH_TOKEN' \
-H 'Content-Type: application/json' | jq .

Expected Result: The totalCost is the sum of all project costs from the last 7 days. The projectCosts array includes all projects with costs in that period.

2. Test: Fetch Single Project Cost (Last 30 Days)

curl -X GET 'http://localhost:50505/api/cost/overview?timeRange=30d&projectId=US_UAT' \
-H 'Authorization: Bearer YOUR_AUTH_TOKEN' \
-H 'Content-Type: application/json' | jq .

Expected Result: The projectCosts array contains only one item for US_UAT. The totalCost matches the cost of that single item.

3. Test: Trigger Manual Data Collection (Optional)

curl -X POST 'http://localhost:50505/api/cost/collect-now' \
-H 'Authorization: Bearer YOUR_AUTH_TOKEN' \
-H 'Content-Type: application/json'

Expected Result: A success message: {"message": "Cost data collection triggered"}.

Attachments
Screenshot 2025-06-20 at 13.09.14.jpg: Shows the dashboard with "Last 7 days" selected, incorrectly displaying data for a single project/day.

Screenshot 2025-06-20 at 13.10.21.jpg: Shows the dashboard with "Last month" selected, displaying the exact same incorrect data.

Screenshot 2025-06-20 at 13.16.44.png: Shows the project filter not being applied correctly.

CosmosDB_latest_entry.json: Sample document from the cost_data container.
#!/usr/bin/env python3
"""
Script to identify and remove duplicate users based on normalized emails.
Keeps the most recent or most privileged user when duplicates are found.
"""

import asyncio
import os
from dotenv import load_dotenv
from datetime import datetime
from backend.rbac.cosmosdb_rbac_service import CosmosRbacClient
from backend.rbac.user_context import normalize_email
from backend.models.rbac import UserRole

# Role priority (higher number = higher priority)
ROLE_PRIORITY = {
    UserRole.SUPER_ADMIN.value: 3,
    UserRole.REGIONAL_ADMIN.value: 2,
    UserRole.REGULAR_USER.value: 1
}

async def remove_duplicate_users():
    """Identify and remove duplicate users based on normalized emails"""
    load_dotenv()
    
    print("Starting duplicate user removal...")
    
    # Initialize Cosmos client
    cosmos_endpoint = f'https://{os.environ.get("AZURE_COSMOSDB_ACCOUNT")}.documents.azure.com:443/'
    cosmos_client = CosmosRbacClient(
        cosmosdb_endpoint=cosmos_endpoint, 
        credential=os.environ.get('AZURE_COSMOSDB_ACCOUNT_KEY'), 
        database_name=os.environ.get('AZURE_COSMOSDB_DATABASE')
    )
    
    try:
        # Initialize the client
        init_success = await cosmos_client.initialize()
        if not init_success:
            print("Failed to initialize Cosmos client")
            return
        
        # Get all users
        print("Fetching all users...")
        all_users = await cosmos_client.get_users()
        print(f"Found {len(all_users)} total users")
        
        # Group users by normalized email
        user_groups = {}
        for user in all_users:
            email = user.get("email")
            if email:
                # Use stored normalized_email if available, otherwise normalize
                normalized = user.get("normalized_email") or normalize_email(email)
                if normalized not in user_groups:
                    user_groups[normalized] = []
                user_groups[normalized].append(user)
        
        # Find duplicates
        duplicates_found = False
        removed_count = 0
        
        for normalized_email, users in user_groups.items():
            if len(users) > 1:
                duplicates_found = True
                print(f"\nFound {len(users)} users with normalized email: {normalized_email}")
                
                # Sort users by priority (role, then by creation date)
                def user_priority(user):
                    role_priority = ROLE_PRIORITY.get(user.get("role", UserRole.REGULAR_USER.value), 0)
                    created_at = user.get("created_at", "1900-01-01T00:00:00Z")
                    return (role_priority, created_at)
                
                users.sort(key=user_priority, reverse=True)
                
                # Keep the first user (highest priority)
                keep_user = users[0]
                print(f"  Keeping: {keep_user.get('name')} (ID: {keep_user.get('id')}, Role: {keep_user.get('role')})")
                
                # Remove the rest
                for user in users[1:]:
                    print(f"  Removing: {user.get('name')} (ID: {user.get('id')}, Role: {user.get('role')})")
                    
                    # Check for any project/team assignments before deletion
                    user_id = user.get("id")
                    
                    # Get project assignments
                    project_assignments = await cosmos_client.get_project_user_roles(
                        filter_params={"user_id": user_id}
                    )
                    
                    # Get team assignments
                    team_memberships = await cosmos_client.get_team_members(
                        filter_params={"user_id": user_id}
                    )
                    
                    if project_assignments:
                        print(f"    Warning: User has {len(project_assignments)} project assignments")
                        # Transfer assignments to the kept user
                        for assignment in project_assignments:
                            print(f"    Transferring project {assignment.get('project_id')} assignment to {keep_user.get('name')}")
                            # Check if kept user already has this assignment
                            existing_assignments = await cosmos_client.get_project_user_roles(
                                filter_params={
                                    "user_id": keep_user.get("id"),
                                    "project_id": assignment.get("project_id")
                                }
                            )
                            if not existing_assignments:
                                # Create new assignment for kept user
                                new_assignment = {
                                    "user_id": keep_user.get("id"),
                                    "project_id": assignment.get("project_id"),
                                    "role": assignment.get("role"),
                                    "assigned_by": "deduplication_script"
                                }
                                await cosmos_client.create_project_user_role(new_assignment)
                    
                    if team_memberships:
                        print(f"    Warning: User is member of {len(team_memberships)} teams")
                        # Transfer team memberships to the kept user
                        for membership in team_memberships:
                            print(f"    Transferring team {membership.get('team_id')} membership to {keep_user.get('name')}")
                            # Check if kept user already has this membership
                            existing_memberships = await cosmos_client.get_team_members(
                                filter_params={
                                    "user_id": keep_user.get("id"),
                                    "team_id": membership.get("team_id")
                                }
                            )
                            if not existing_memberships:
                                # Create new membership for kept user
                                new_membership = {
                                    "user_id": keep_user.get("id"),
                                    "team_id": membership.get("team_id"),
                                    "role": membership.get("role"),
                                    "added_by": "deduplication_script"
                                }
                                await cosmos_client.create_team_member(new_membership)
                    
                    # Delete the duplicate user
                    success = await cosmos_client.delete_user(user_id)
                    if success:
                        removed_count += 1
                        print(f"    Successfully removed user {user_id}")
                    else:
                        print(f"    Failed to remove user {user_id}")
        
        if not duplicates_found:
            print("\nNo duplicate users found!")
        else:
            print(f"\nRemoved {removed_count} duplicate users.")
        
        # Verify final state
        print("\nVerifying final state...")
        final_users = await cosmos_client.get_users()
        print(f"Total users after deduplication: {len(final_users)}")
        
        # Check for any remaining duplicates
        final_groups = {}
        for user in final_users:
            email = user.get("email")
            if email:
                normalized = user.get("normalized_email") or normalize_email(email)
                if normalized not in final_groups:
                    final_groups[normalized] = 0
                final_groups[normalized] += 1
        
        remaining_duplicates = {k: v for k, v in final_groups.items() if v > 1}
        if remaining_duplicates:
            print("WARNING: Still have duplicates:")
            for email, count in remaining_duplicates.items():
                print(f"  {email}: {count} users")
        else:
            print("Success: No duplicates remain!")
        
    finally:
        await cosmos_client.close()

def main():
    """Run the duplicate removal"""
    print("=== User Deduplication Script ===\n")
    asyncio.run(remove_duplicate_users())
    print("\n=== Deduplication Complete ===")

if __name__ == "__main__":
    main()
2025-06-20 12:07:42,836 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_120742.log
2025-06-20 12:07:42,836 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_120742.log
2025-06-20 12:07:42,837 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_120742.log
2025-06-20 12:07:42,840 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_120742.log
2025-06-20 12:07:42,922 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:07:42,925 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:07:42,930 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:07:42,934 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:07:42,947 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:07:42,948 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:07:42,955 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:07:42,955 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:07:42,992 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:07:42,992 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:07:42,993 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:07:42,993 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:07:42,994 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:07:42,994 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:07:42,995 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:07:42,995 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:07:42,995 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:07:42,995 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:07:42,995 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:07:42,996 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:07:43,190 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:07:42 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:07:43,190 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:07:43,191 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:07:43,192 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:07:43,206 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:07:42 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:07:43,206 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:07:42 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:07:43,206 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:07:43,206 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:07:43,208 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:07:43,207 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:07:43,209 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:07:43,209 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:07:43,215 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:07:43,215 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:07:43,215 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:07:43,215 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:07:43,215 - root - INFO - Using DummyCredential for local development
2025-06-20 12:07:43,216 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:07:43,216 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:07:43,216 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:07:43,216 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:07:43,216 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:07:43,216 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:07:43,217 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:07:43,217 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:07:43,225 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:07:43,225 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:07:43,225 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:07:43,225 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:07:43,225 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:07:43,226 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:07:43,225 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:07:43 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:07:43,226 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:07:43,226 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:07:43,226 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:07:43,226 - root - INFO - Using DummyCredential for local development
2025-06-20 12:07:43,226 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:07:43,226 - root - INFO - Using DummyCredential for local development
2025-06-20 12:07:43,226 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:07:43,227 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:07:43,226 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:07:43,227 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:07:43,227 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:07:43,227 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:07:43,228 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:07:43,227 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:07:43,228 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:07:43,228 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:07:43,228 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:07:43,228 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:07:43,228 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:07:43,229 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:07:43,229 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:07:43,230 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:07:43,231 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:07:43,241 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:07:43,241 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:07:43,241 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:07:43,241 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:07:43,241 - root - INFO - Using DummyCredential for local development
2025-06-20 12:07:43,242 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:07:43,242 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:07:43,242 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:07:43,242 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:07:43,242 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:07:43,242 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:07:43,243 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:07:43,243 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:07:53,219 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:07:53,220 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:07:53,231 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:07:53,231 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:07:53,232 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:07:53,232 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:07:53,244 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:07:53,244 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:03,226 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:03,227 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:03,234 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:03,235 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:03,235 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:03,236 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:03,249 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:03,249 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:13,228 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:13,229 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:13,238 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:13,238 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:13,238 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:13,238 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:13,250 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:13,250 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:20,595 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:08:20,595 - root - INFO - Shutting down WebSocket service
2025-06-20 12:08:20,596 - root - INFO - Closing Cosmos DB client
2025-06-20 12:08:20,596 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:08:20,596 - root - INFO - Closing Storage Management client
2025-06-20 12:08:20,596 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:08:20,596 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:08:20,599 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:08:20,600 - root - INFO - Shutting down WebSocket service
2025-06-20 12:08:20,600 - root - INFO - Closing Cosmos DB client
2025-06-20 12:08:20,600 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:08:20,600 - root - INFO - Closing Storage Management client
2025-06-20 12:08:20,600 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:08:20,600 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:08:20,599 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:08:20,600 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:08:20,603 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:08:20,603 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:08:20,678 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:08:20,678 - root - INFO - Shutting down WebSocket service
2025-06-20 12:08:20,678 - root - INFO - Closing Cosmos DB client
2025-06-20 12:08:20,678 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:08:20,678 - root - INFO - Closing Storage Management client
2025-06-20 12:08:20,678 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:08:20,678 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:08:20,682 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:08:20,682 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:08:20,684 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:08:20,684 - root - INFO - Shutting down WebSocket service
2025-06-20 12:08:20,684 - root - INFO - Closing Cosmos DB client
2025-06-20 12:08:20,684 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:08:20,685 - root - INFO - Closing Storage Management client
2025-06-20 12:08:20,685 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:08:20,685 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:08:20,688 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:08:20,688 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:08:20,852 - root - INFO - Cleanup finished.
2025-06-20 12:08:20,854 - root - INFO - Cleanup finished.
2025-06-20 12:08:20,935 - root - INFO - Cleanup finished.
2025-06-20 12:08:20,940 - root - INFO - Cleanup finished.

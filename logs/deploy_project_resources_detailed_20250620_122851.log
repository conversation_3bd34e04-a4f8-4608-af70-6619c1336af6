2025-06-20 12:28:51,592 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_122851.log
2025-06-20 12:28:51,630 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:28:51,650 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:28:51,674 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:28:51,675 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:28:51,675 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:28:51,856 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:28:51 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:28:51,856 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:28:51,857 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:28:51,858 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:28:51,876 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:28:51,876 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:28:51,876 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:28:51,876 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:28:51,877 - root - INFO - Using DummyCredential for local development
2025-06-20 12:28:51,877 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:28:51,877 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:28:51,877 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:28:51,877 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:28:51,877 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:28:51,878 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:28:51,878 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:28:51,878 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:28:59,146 - backend.deployments.service - INFO - Deployment worker started
2025-06-20 12:28:59,146 - backend.deployments.routes - INFO - Deployment service initialized
2025-06-20 12:28:59,147 - backend.deployments.service - INFO - Deployment queue processor started
2025-06-20 12:28:59,268 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/user-context/me
2025-06-20 12:28:59,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:28:59,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:28:59,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=4'}
2025-06-20 12:28:59,268 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:28:59,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:28:59,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:28:59,268 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:28:59,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:28:59,269 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:28:59,269 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:28:59,269 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:28:59,269 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:28:59,269 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:28:59,269 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:28:59,270 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:28:59,270 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:28:59,333 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/user-context/me
2025-06-20 12:28:59,333 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:28:59,333 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:28:59,333 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=4'}
2025-06-20 12:28:59,333 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:28:59,333 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:28:59,333 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:28:59,333 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:28:59,333 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:28:59,333 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:28:59,334 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:28:59,334 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:28:59,334 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:28:59,334 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:28:59,334 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:28:59,334 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:28:59,334 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:28:59,335 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:28:59,377 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:28:59,377 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:28:59,448 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:28:59,449 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:28:59,449 - rbac - INFO - RBAC client initialized successfully.
2025-06-20 12:28:59,449 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:28:59,449 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:28:59,449 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=4'}
2025-06-20 12:28:59,449 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:28:59,450 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:28:59,450 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:28:59,450 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:28:59,450 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:28:59,450 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:28:59,450 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:28:59,450 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:28:59,450 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:28:59,451 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:28:59,451 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:28:59,451 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:28:59,451 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:28:59,452 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:28:59,590 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:28:59,591 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:28:59,592 - root - INFO - Successfully authenticated user from token: Jean De Mevius (Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com)
2025-06-20 12:28:59,592 - root - INFO - Looking up user by email: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com (normalized: <EMAIL>)
2025-06-20 12:28:59,593 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:28:59,595 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '693'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00002506-0000-0d00-0000-6810c43c0000"'
    'x-ms-schemaversion': 'REDACTED'
    'collection-partition-index': 'REDACTED'
    'collection-service-index': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-max-content-length': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:28:59,596 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:28:59,596 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:28:59,596 - rbac - INFO - RBAC client initialized successfully.
2025-06-20 12:28:59,596 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:28:59,596 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:28:59,596 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=4'}
2025-06-20 12:28:59,597 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:28:59,597 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:28:59,597 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:28:59,597 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:28:59,597 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:28:59,597 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:28:59,597 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:28:59,597 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:28:59,598 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:28:59,598 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:28:59,598 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:28:59,598 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:28:59,598 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:28:59,599 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:28:59,746 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:28:59,747 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:28:59,747 - root - INFO - Successfully authenticated user from token: Jean De Mevius (Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com)
2025-06-20 12:28:59,747 - root - INFO - Looking up user by email: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com (normalized: <EMAIL>)
2025-06-20 12:28:59,748 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:28:59,753 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:28:59,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:28:59,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:28:59,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:28:59,753 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:28:59,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:28:59,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:28:59,753 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:28:59,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:28:59,754 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:28:59,754 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:28:59,754 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:28:59,754 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:28:59,754 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:28:59,754 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:28:59,754 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:28:59,754 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:28:59,755 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:28:59,893 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:28:59,894 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:28:59,894 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:28:59,895 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '205'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:28:59,896 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:28:59,897 - root - INFO - Found user in database by normalized email: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:27:00.925193+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002597-0000-0d00-0000-685553950000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422421}
2025-06-20 12:28:59,897 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:27:00.925193+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002597-0000-0d00-0000-685553950000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422421}
2025-06-20 12:28:59,897 - root - INFO - Using role from database: SUPER_ADMIN
2025-06-20 12:28:59,897 - root - INFO - Authenticated user: Jean De Mevius (Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com)
2025-06-20 12:28:59,897 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:28:59,898 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:28:59,898 - root - INFO - Found user in database by normalized email: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:27:00.925193+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002597-0000-0d00-0000-685553950000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422421}
2025-06-20 12:28:59,898 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:27:00.925193+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002597-0000-0d00-0000-685553950000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422421}
2025-06-20 12:28:59,898 - root - INFO - Using role from database: SUPER_ADMIN
2025-06-20 12:28:59,899 - root - INFO - Authenticated user: Jean De Mevius (Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com)
2025-06-20 12:28:59,899 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:28:59,940 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00002597-0000-0d00-0000-685553950000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:28:59,940 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:27:00.925193+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002597-0000-0d00-0000-685553950000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422421}
2025-06-20 12:28:59,940 - root - INFO - Removing region from Super Admin user Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:28:59,941 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:28:59,943 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '13105'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:28:59,952 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:28:59,953 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:28:59,953 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:28:59,953 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:28:59,953 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:28:59,953 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:28:59,954 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:28:59,954 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:28:59,954 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:28:59,954 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:28:59,954 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:28:59,955 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:28:59,955 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:28:59,955 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:28:59,955 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:28:59,955 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:28:59,955 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:28:59,956 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:00,089 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:00,089 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:00,090 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:00,091 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00002597-0000-0d00-0000-685553950000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,091 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:27:00.925193+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00002597-0000-0d00-0000-685553950000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422421}
2025-06-20 12:29:00,091 - root - INFO - Removing region from Super Admin user Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:00,091 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:00,092 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00002597-0000-0d00-0000-685553950000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,092 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:00,093 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,093 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,094 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:00,094 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:00,094 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:00,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:00,095 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:00,095 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:00,095 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:00,263 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:00,263 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:00,264 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:00,266 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,267 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:00,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:00,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,267 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:00,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:00,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:00,268 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:00,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:00,268 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:00,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:00,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:00,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:00,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:00,269 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:00,269 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:00,269 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:00,269 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:00,423 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:00,424 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:00,425 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:00,426 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00002597-0000-0d00-0000-685553950000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,426 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:00,427 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '673'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00006303-0000-0d00-0000-67fe64ae0000"'
    'x-ms-schemaversion': 'REDACTED'
    'collection-partition-index': 'REDACTED'
    'collection-service-index': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-max-content-length': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:29:00,428 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'PUT'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Content-Type': 'application/json'
    'Accept': 'application/json'
    'Content-Length': '577'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,429 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,430 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:00,430 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:00,430 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,430 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:00,430 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:00,430 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:00,430 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:00,431 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:00,431 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:00,431 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:00,431 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:00,431 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:00,432 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:00,432 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:00,432 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:00,432 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:00,433 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:00,604 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:00,606 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:00,609 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:00,610 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,611 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '673'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00006303-0000-0d00-0000-67fe64ae0000"'
    'x-ms-schemaversion': 'REDACTED'
    'collection-partition-index': 'REDACTED'
    'collection-service-index': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-max-content-length': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:29:00,612 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'PUT'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Content-Type': 'application/json'
    'Accept': 'application/json'
    'Content-Length': '577'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,613 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,614 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,638 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00002f97-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,639 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:00,663 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '575'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,663 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,670 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,670 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:00,700 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,700 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,720 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '646'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,720 - cosmos_db - INFO - Retrieved 1 messages for conversation b1fdd710-229c-463a-861c-2af8730fd920
2025-06-20 12:29:00,724 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:00,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:00,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,725 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:00,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:00,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:00,726 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:00,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:00,726 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:00,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:00,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:00,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:00,727 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:00,727 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:00,727 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:00,727 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:00,728 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:00,871 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:00,872 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:00,872 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:00,874 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,874 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,875 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '66789'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,876 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:00,877 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '575'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,877 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,878 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '582'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,879 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,879 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '586'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,880 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,881 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,922 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,922 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/teams/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,933 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '574'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,933 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:00,934 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '639'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,934 - cosmos_db - INFO - Retrieved 1 messages for conversation 729e39cb-2817-4178-8147-73c55b1312cd
2025-06-20 12:29:00,936 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '15767'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:00,937 - cosmos_db - INFO - Retrieved 3 messages for conversation e6a9f0ea-ea28-4e32-a606-c13b7b79fc4e
2025-06-20 12:29:00,938 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,939 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:00,939 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:00,939 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:00,939 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:00,939 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:00,940 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:00,940 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:00,940 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:00,940 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:00,940 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:00,940 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:00,940 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:00,941 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:00,941 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:00,941 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:00,941 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:00,942 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:01,081 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:01,081 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:01,082 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:01,084 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '35478'
    'Date': 'Fri, 20 Jun 2025 12:28:59 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,084 - cosmos_db - INFO - Retrieved 6 messages for conversation a919b2c2-aa6f-44ce-948c-74fd3b5d03dc
2025-06-20 12:29:01,085 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '66789'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,087 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:01,087 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3758'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,088 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '27'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,089 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '15110'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,089 - cosmos_db - INFO - Retrieved 3 messages for conversation 46fb55c9-74d4-4efa-8d3a-867d9092bc93
2025-06-20 12:29:01,090 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,091 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:01,091 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:01,091 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,091 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:01,091 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:01,091 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:01,091 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:01,091 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:01,091 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:01,092 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:01,092 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:01,092 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:01,092 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:01,092 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:01,092 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:01,092 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:01,093 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:01,398 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:01,398 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:01,399 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:01,402 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,403 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,403 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/teams/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,405 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,406 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,406 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:01,407 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:01,407 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,407 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:01,407 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:01,407 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:01,407 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:01,407 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:01,407 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:01,408 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:01,408 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:01,408 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:01,408 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:01,408 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:01,408 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:01,408 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:01,409 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:01,546 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:01,546 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:01,548 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:01,548 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,549 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:01,549 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:01,549 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,549 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:01,549 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:01,549 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:01,549 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:01,550 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:01,550 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:01,550 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:01,550 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:01,550 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:01,550 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:01,551 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:01,551 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:01,551 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:01,552 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:01,712 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:01,714 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:01,717 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:01,718 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '580'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,719 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,720 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1517'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,721 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:01,722 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3758'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,722 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '27'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,724 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,725 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,763 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,763 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,765 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '569'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,765 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,768 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '45448'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,769 - cosmos_db - INFO - Retrieved 6 messages for conversation a996c895-903c-43b8-8eb1-122c3fd925a2
2025-06-20 12:29:01,770 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '575'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,771 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,775 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,775 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:01,775 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:01,775 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,775 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:01,775 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:01,775 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:01,776 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:01,776 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:01,776 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:01,776 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:01,776 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:01,776 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:01,777 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:01,777 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:01,777 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:01,777 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:01,778 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:01,931 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:01,932 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:01,933 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:01,934 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1517'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,935 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:01,935 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '569'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,936 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,937 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,937 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '20778'
    'Date': 'Fri, 20 Jun 2025 12:29:00 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,938 - cosmos_db - INFO - Retrieved 3 messages for conversation 50fafb3f-8d90-4943-997b-80c9b8972b4d
2025-06-20 12:29:01,939 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '20751'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:01,939 - cosmos_db - INFO - Retrieved 3 messages for conversation eec09e8b-b513-4ba7-af36-b33b240cee38
2025-06-20 12:29:01,940 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:29:01,940 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:29:01,943 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:01,946 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:01,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:01,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:01,946 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:01,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:01,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:01,946 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:01,947 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:01,947 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:01,947 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:01,947 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:01,947 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:01,947 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:01,947 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:01,948 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:01,948 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:01,948 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:02,104 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:02,104 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:02,105 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:02,107 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,108 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,110 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,110 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,111 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '20816'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,112 - cosmos_db - INFO - Retrieved 3 messages for conversation cc77f746-b4ab-4674-b03b-f586a8408c31
2025-06-20 12:29:02,113 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,114 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:02,114 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:02,114 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,114 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:02,114 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:02,114 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:02,114 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:02,115 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:02,115 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:02,115 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:02,115 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:02,115 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:02,115 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:02,115 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:02,116 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:02,116 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:02,116 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:02,262 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:02,264 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:02,265 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:02,266 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,266 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:02,266 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:02,266 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,266 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:02,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:02,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:02,267 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:02,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:02,267 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:02,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:02,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:02,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:02,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:02,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:02,268 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:02,268 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:02,269 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:02,407 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:02,408 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:02,408 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:02,410 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,412 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,413 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,414 - root - INFO - Font requested: /fonts/fabric-icons-a13498cf.woff
2025-06-20 12:29:02,415 - root - INFO - Font served successfully: fabric-icons-a13498cf.woff
2025-06-20 12:29:02,418 - root - INFO - Font requested: /fonts/fabric-icons-1-4d521695.woff
2025-06-20 12:29:02,419 - root - INFO - Font served successfully: fabric-icons-1-4d521695.woff
2025-06-20 12:29:02,421 - root - INFO - Font requested: /fonts/fabric-icons-4-a656cc0a.woff
2025-06-20 12:29:02,422 - root - INFO - Font served successfully: fabric-icons-4-a656cc0a.woff
2025-06-20 12:29:02,424 - root - INFO - Font requested: /fonts/fabric-icons-0-467ee27f.woff
2025-06-20 12:29:02,424 - root - INFO - Font served successfully: fabric-icons-0-467ee27f.woff
2025-06-20 12:29:02,428 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,428 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:02,428 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:02,428 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,428 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:02,428 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:02,428 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:02,429 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:02,429 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:02,429 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:02,429 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:02,429 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:02,429 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:02,429 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:02,429 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:02,429 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:02,429 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:02,430 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:02,570 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:02,571 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:02,572 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:02,573 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,574 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '61318'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,574 - cosmos_db - INFO - Retrieved 6 messages for conversation c5795c81-b117-43c9-829f-047d9c2d0b8e
2025-06-20 12:29:02,575 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,576 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,577 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '575'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,578 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,578 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '573'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,579 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,582 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,584 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,584 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:02,585 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:02,585 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,585 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:02,585 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:02,585 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:02,585 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:02,585 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:02,586 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:02,586 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:02,586 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:02,586 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:02,586 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:02,586 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:02,586 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:02,587 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:02,587 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:02,728 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:02,728 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:02,729 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:02,730 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,730 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:02,730 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:02,731 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,731 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:02,731 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:02,731 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:02,731 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:02,731 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:02,731 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:02,731 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:02,731 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:02,732 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:02,732 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:02,732 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:02,732 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:02,732 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:02,733 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:02,897 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:02,897 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:02,898 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:02,899 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '18414'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,899 - cosmos_db - INFO - Retrieved 3 messages for conversation b8535ea7-ed7d-418f-9966-f93a2a91aa39
2025-06-20 12:29:02,900 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '31955'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,900 - cosmos_db - INFO - Retrieved 3 messages for conversation 65ec5ca5-31be-4c78-aad8-8d558b4c121d
2025-06-20 12:29:02,900 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '576'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,901 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,902 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '4237'
    'Date': 'Fri, 20 Jun 2025 12:29:01 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:02,902 - cosmos_db - INFO - Retrieved 7 messages for conversation 331ed6de-5dd8-47cb-b424-8e3257386be6
2025-06-20 12:29:02,903 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,904 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:02,906 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,906 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:02,906 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:02,906 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:02,906 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:02,906 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:02,906 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:02,906 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:02,907 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:02,907 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:02,907 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:02,907 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:02,907 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:02,907 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:02,907 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:02,907 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:02,907 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:02,908 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:03,045 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:03,046 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:03,046 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:03,047 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,047 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:03,048 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:03,048 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,048 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:03,048 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:03,048 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:03,048 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:03,048 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:03,048 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:03,049 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:03,049 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:03,049 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:03,049 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:03,049 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:03,049 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:03,049 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:03,050 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:03,190 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:03,191 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:03,191 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:03,192 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,192 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:03,192 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:03,192 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,192 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:03,192 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:03,192 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:03,192 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:03,193 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:03,193 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:03,193 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:03,193 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:03,193 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:03,193 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:03,193 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:03,193 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:03,193 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:03,194 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:03,327 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:03,327 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:03,328 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:03,329 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3815'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,329 - cosmos_db - INFO - Retrieved 6 messages for conversation 181a2685-3099-45da-830d-4fe9e3810259
2025-06-20 12:29:03,330 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '571'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,330 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,331 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '579'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,331 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,332 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,333 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,334 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,335 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,335 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:03,335 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:03,335 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,336 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:03,336 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:03,336 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:03,336 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:03,336 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:03,336 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:03,336 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:03,336 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:03,336 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:03,337 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:03,337 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:03,337 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:03,337 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:03,337 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:03,468 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:03,468 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:03,469 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:03,470 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2031'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,471 - cosmos_db - INFO - Retrieved 3 messages for conversation 697abc27-40b3-4973-a08f-322137a24b92
2025-06-20 12:29:03,472 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '11917'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,472 - cosmos_db - INFO - Retrieved 6 messages for conversation b42c9903-3a77-4266-895e-7d53ade471f5
2025-06-20 12:29:03,473 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '571'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,474 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,475 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '574'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,475 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,477 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,479 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,479 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:03,479 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:03,479 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,479 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:03,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:03,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:03,480 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:03,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:03,480 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:03,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:03,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:03,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:03,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:03,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:03,480 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:03,480 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:03,481 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:03,625 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:03,626 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:03,627 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:03,628 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,628 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:03,628 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:03,628 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,628 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:03,629 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:03,629 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:03,629 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:03,629 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:03,629 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:03,629 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:03,629 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:03,630 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:03,630 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:03,630 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:03,630 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:03,630 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:03,631 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:03,783 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:03,784 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:03,785 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:03,786 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2022'
    'Date': 'Fri, 20 Jun 2025 12:29:02 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,786 - cosmos_db - INFO - Retrieved 3 messages for conversation b202879d-be64-485d-84cf-2c271e3efeab
2025-06-20 12:29:03,787 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '553'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,788 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,789 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3996'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:03,789 - cosmos_db - INFO - Retrieved 6 messages for conversation 4ea2fe3b-e848-4b9b-b18a-b1d35e68cd2b
2025-06-20 12:29:03,791 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,792 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:03,794 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,794 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:03,795 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:03,795 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,795 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:03,795 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:03,795 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:03,795 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:03,795 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:03,796 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:03,796 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:03,796 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:03,796 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:03,796 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:03,796 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:03,796 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:03,796 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:03,797 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:03,942 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:03,943 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:03,943 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:03,944 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,944 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:03,944 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:03,945 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:03,945 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:03,945 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:03,945 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:03,945 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:03,945 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:03,945 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:03,945 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:03,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:03,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:03,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:03,946 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:03,946 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:03,946 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:03,947 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:04,093 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:04,094 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:04,095 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:04,097 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '30178'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,098 - cosmos_db - INFO - Retrieved 4 messages for conversation 356083e2-06f2-4c9d-a588-5b8331d67631
2025-06-20 12:29:04,099 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '553'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,100 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,100 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '559'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,101 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,102 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,103 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,106 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:04,106 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:04,106 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:04,106 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:29:04,106 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:04,106 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:04,106 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:04,107 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:04,107 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:04,107 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:04,107 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:04,107 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:04,107 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:04,107 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:04,108 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:04,108 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:04,108 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:04,108 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:04,273 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:04,274 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:04,275 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:04,278 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '16692'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,279 - cosmos_db - INFO - Retrieved 3 messages for conversation 16a0af4f-8e50-445c-bd70-6618e14c3d9c
2025-06-20 12:29:04,280 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '551'
    'Date': 'Fri, 20 Jun 2025 12:29:04 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,280 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,281 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '16851'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,282 - cosmos_db - INFO - Retrieved 3 messages for conversation 9aaa4999-3e02-43b6-ad40-10ed1086df1d
2025-06-20 12:29:04,282 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '547'
    'Date': 'Fri, 20 Jun 2025 12:29:04 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,283 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,284 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,326 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/rbac/users
2025-06-20 12:29:04,326 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:04,327 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/rbac/users
2025-06-20 12:29:04,327 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'x-current-user-id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=0'}
2025-06-20 12:29:04,327 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:04,327 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:04,327 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:04,327 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:04,327 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:04,327 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:04,327 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:04,328 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:04,328 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:04,328 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:04,328 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:04,328 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:29:04,329 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:04,330 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '14888'
    'Date': 'Fri, 20 Jun 2025 12:29:04 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,330 - cosmos_db - INFO - Retrieved 3 messages for conversation 53c56c18-5302-4565-b849-6354c99fb031
2025-06-20 12:29:04,331 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '30008'
    'Date': 'Fri, 20 Jun 2025 12:29:04 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,331 - cosmos_db - INFO - Retrieved 6 messages for conversation ae5b3bf8-f3ba-447d-a09b-d3904c9179cc
2025-06-20 12:29:04,332 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '556'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,333 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,335 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '579'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,336 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,379 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '18476'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,380 - cosmos_db - INFO - Retrieved 6 messages for conversation bbd8213a-7255-4482-95f3-a34245ea07b8
2025-06-20 12:29:04,389 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6001'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,390 - cosmos_db - INFO - Retrieved 9 messages for conversation bc76d819-3965-44e1-b214-b430a24f752a
2025-06-20 12:29:04,495 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:29:04,496 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:29:04,497 - rbac - INFO - RBAC client initialized successfully.
2025-06-20 12:29:04,498 - rbac - INFO - Found current user ID in header: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-20 12:29:04,499 - rbac - INFO - Using mock authentication with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-20 12:29:04,500 - root - INFO - get_mock_user called with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-20 12:29:04,501 - root - INFO - User 7e43ed9b-4998-468e-bcd1-9b78f7c82977 not found in mock_users, creating temporary user
2025-06-20 12:29:04,501 - root - INFO - Created temporary user: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150'}
2025-06-20 12:29:04,501 - rbac - INFO - get_mock_user returned: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150'}
2025-06-20 12:29:04,502 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:04,558 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,559 - rbac - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:29:04,559 - rbac - INFO - Set user role from database to: SUPER_ADMIN
2025-06-20 12:29:04,559 - rbac - INFO - Updated user with database info: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:29:04,560 - rbac - INFO - Returning authenticated user with role SUPER_ADMIN: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:29:04,561 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:04,608 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:04,612 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:04,658 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:29:03 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:10,568 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Connection': 'keep-alive', 'Referer': 'http://localhost:50505/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=0'}
2025-06-20 12:29:10,568 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:10,568 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:10,568 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Connection': 'keep-alive', 'Referer': 'http://localhost:50505/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=0'}
2025-06-20 12:29:10,568 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:10,569 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:10,569 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:10,569 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:10,569 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:10,569 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:10,569 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:10,569 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:10,569 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:10,570 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:10,570 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:10,570 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:10,570 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:10,570 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:10,729 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:10,730 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:10,730 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:10,731 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:10,733 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Connection': 'keep-alive', 'Referer': 'http://localhost:50505/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=0'}
2025-06-20 12:29:10,733 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:29:10,733 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:29:10,733 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Connection': 'keep-alive', 'Referer': 'http://localhost:50505/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=0'}
2025-06-20 12:29:10,734 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:29:10,734 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:29:10,734 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:29:10,734 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:29:10,734 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:29:10,734 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:29:10,735 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:29:10,735 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:29:10,735 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:29:10,735 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:29:10,735 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:29:10,735 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:29:10,735 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:29:10,736 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:29:10,896 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:29:10,898 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:29:10,901 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:29:10,903 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:10,905 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:10 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:10,905 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-20 12:29:10,905 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-20 12:29:10,905 - backend.cost_management.cost_management_service - DEBUG - Executing resource cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, resource_group: None, resource_type: None
2025-06-20 12:29:10,906 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff7a572770>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff7a573ee0>}
2025-06-20 12:29:10,906 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:29:10,906 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:29:10,907 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533
2025-06-20 12:29:10,907 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:10,909 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0xffff79b36230>
2025-06-20 12:29:10,909 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0xffff7a5a4d60>, 12904.8)]']
connector: <aiohttp.connector.TCPConnector object at 0xffff79b364a0>
2025-06-20 12:29:10,910 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-20 12:29:11,091 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2025-06-20 12:29:11,092 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'max-age=86400, private'
    'Content-Type': 'application/json; charset=utf-8'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Access-Control-Allow-Origin': 'REDACTED'
    'Access-Control-Allow-Methods': 'REDACTED'
    'P3P': 'REDACTED'
    'x-ms-request-id': '72d707fe-1ecc-488a-a970-270736462000'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Fri, 20 Jun 2025 12:29:10 GMT'
    'Content-Length': '1753'
2025-06-20 12:29:11,094 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/kerberos', 'tenant_region_scope': 'EU', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-20 12:29:11,094 - msal.application - DEBUG - Broker enabled? None
2025-06-20 12:29:11,095 - msal.application - DEBUG - Region to be used: None
2025-06-20 12:29:11,098 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 37b49317-2c0d-4126-8cdc-25b1c848a4d5
2025-06-20 12:29:11,098 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token'
Request method: 'POST'
Request headers:
    'Accept': 'application/json'
    'x-client-sku': 'REDACTED'
    'x-client-ver': 'REDACTED'
    'x-client-os': 'REDACTED'
    'x-ms-lib-capability': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-client-current-telemetry': 'REDACTED'
    'x-client-last-telemetry': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:11,208 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token HTTP/1.1" 200 1655
2025-06-20 12:29:11,210 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'P3P': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-ms-request-id': 'b3c3c91a-6262-44bc-84b1-48d002e90d00'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-clitelem': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Fri, 20 Jun 2025 12:29:10 GMT'
    'Content-Length': '1655'
2025-06-20 12:29:11,211 - msal.token_cache - DEBUG - event={
    "client_id": "bb1ebfc1-47d8-4273-9206-3acc107c1e35",
    "data": {
        "claims": null,
        "scope": [
            "https://management.azure.com/.default"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "client_credentials",
    "params": null,
    "response": {
        "access_token": "********",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "token_type": "Bearer"
    },
    "scope": [
        "https://management.azure.com/.default"
    ],
    "token_endpoint": "https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token"
}
2025-06-20 12:29:11,212 - azure.identity._internal.get_token_mixin - DEBUG - ClientSecretCredential.get_token succeeded
2025-06-20 12:29:11,213 - azure.identity._internal.decorators - DEBUG - EnvironmentCredential.get_token succeeded
2025-06-20 12:29:11,214 - azure.identity._internal.decorators - DEBUG - [Authenticated account] Client ID: bb1ebfc1-47d8-4273-9206-3acc107c1e35. Tenant ID: ee78877a-c63a-405d-85d6-8914358aa533. User Principal Name: unavailableUpn. Object ID (user): 4b27bf23-ad30-46f2-a2f7-c5998d42367a
2025-06-20 12:29:11,214 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-06-20 12:29:11,215 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '293'
    'Accept': 'application/json'
    'x-ms-client-request-id': '2b0aa252-4dd2-11f0-9f93-8205a49bb958'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-20 12:29:11,217 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): management.azure.com:443
2025-06-20 12:29:13,166 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 148009
2025-06-20 12:29:13,227 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '148009'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'b2a4b205-2ba0-4132-ad3e-84f931791859'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '2b0aa252-4dd2-11f0-9f93-8205a49bb958'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: D57C4D6EC22342358B43740E13AA1328 Ref B: AMS231020615039 Ref C: 2025-06-20T12:29:11Z'
    'Date': 'Fri, 20 Jun 2025 12:29:12 GMT'
2025-06-20 12:29:13,242 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:29:13,242 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK resource query successful with 851 rows
2025-06-20 12:29:13,247 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:10 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:13,249 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:13,250 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:29:13,250 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:29:13,302 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:29:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:13,303 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:29:13,347 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '66789'
    'Date': 'Fri, 20 Jun 2025 12:29:12 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:13,348 - backend.cost_management.cost_api_quart - WARNING - Cosmos DB configuration missing
2025-06-20 12:29:13,348 - backend.cost_management.cost_api_quart - INFO - Querying Azure Cost Management for tag 'project-id' with project_id: 8e1f2205-d6f3-433f-808d-ae980d45d90d
2025-06-20 12:29:13,348 - backend.cost_management.cost_api_quart - INFO - Time range: month
2025-06-20 12:29:13,348 - backend.cost_management.cost_management_service - DEBUG - Creating tag filter for project-id = 8e1f2205-d6f3-433f-808d-ae980d45d90d
2025-06-20 12:29:13,348 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-20 12:29:13,348 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-20 12:29:13,348 - backend.cost_management.cost_management_service - DEBUG - Executing cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, tag_name: project-id, tag_value: 8e1f2205-d6f3-433f-808d-ae980d45d90d
2025-06-20 12:29:13,348 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff7a572710>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff7a572f20>}
2025-06-20 12:29:13,349 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:29:13,349 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:29:13,349 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '407'
    'Accept': 'application/json'
    'x-ms-client-request-id': '2c7f4ade-4dd2-11f0-9f93-8205a49bb958'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-20 12:29:14,198 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 451
2025-06-20 12:29:14,201 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '451'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'fc227201-c8af-440d-b8a8-1dfebe6290dc'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '2c7f4ade-4dd2-11f0-9f93-8205a49bb958'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: AD0B43AE2A99476581CF5FE216B33F72 Ref B: AMS231020615039 Ref C: 2025-06-20T12:29:13Z'
    'Date': 'Fri, 20 Jun 2025 12:29:13 GMT'
2025-06-20 12:29:14,202 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:29:14,202 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK cost query successful with 0 rows
2025-06-20 12:29:14,203 - backend.cost_management.cost_management_service - INFO - Columns: ['Cost', 'ResourceId', 'Currency']
2025-06-20 12:29:14,203 - backend.cost_management.cost_api_quart - INFO - Azure Cost Management query completed
2025-06-20 12:29:14,204 - backend.cost_management.cost_api_quart - INFO - Result type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:29:14,204 - backend.cost_management.cost_api_quart - INFO - Result has rows: True
2025-06-20 12:29:14,204 - backend.cost_management.cost_api_quart - INFO - Number of rows: 0
2025-06-20 12:29:14,205 - backend.cost_management.cost_api_quart - INFO - Fetching names for 0 projects: []
2025-06-20 12:29:14,205 - backend.cost_management.cost_api_quart - INFO - Retrieved project names: {}
2025-06-20 12:29:14,205 - backend.cost_management.cost_api_quart - DEBUG - project_costs length: 0, result: {'additional_properties': {}, 'id': 'subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourcegroups/rg-internal-ai/providers/Microsoft.CostManagement/query/2611fb60-6214-40f8-9cde-a431dece2405', 'name': '2611fb60-6214-40f8-9cde-a431dece2405', 'type': 'Microsoft.CostManagement/query', 'location': None, 'sku': None, 'e_tag': None, 'tags': None, 'next_link': None, 'columns': [<azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff7a5725c0>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff7a5714e0>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff7a572a10>], 'rows': []}, use_cosmos_data: False
2025-06-20 12:29:14,206 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/westeurope/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:14,262 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '487'
    'Date': 'Fri, 20 Jun 2025 12:29:13 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0c00aa8f-0000-0d00-0000-68404c030000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:14,264 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/westus2/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:14,312 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '476'
    'Date': 'Fri, 20 Jun 2025 12:29:13 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0300fb04-0000-0d00-0000-684c2f550000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:14,315 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/chilenorthcentral/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:29:14,359 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '503'
    'Date': 'Fri, 20 Jun 2025 12:29:13 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0c00ab8f-0000-0d00-0000-68404c090000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:29:23,254 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:29:23,263 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:29:33,268 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:29:33,269 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:29:43,271 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:29:43,272 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:29:53,279 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:29:53,280 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:30:03,282 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:30:03,282 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:30:05,378 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Connection': 'keep-alive', 'Referer': 'http://localhost:50505/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=0'}
2025-06-20 12:30:05,378 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:30:05,378 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:30:05,379 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Connection': 'keep-alive', 'Referer': 'http://localhost:50505/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=0'}
2025-06-20 12:30:05,379 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:30:05,379 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:30:05,379 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:30:05,379 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:30:05,379 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:30:05,379 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:30:05,380 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:30:05,380 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:30:05,380 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:30:05,380 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:30:05,380 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:30:05,380 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:30:05,380 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:30:05,381 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:30:05,535 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:30:05,536 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:30:05,537 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:30:05,538 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:30:05,539 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Connection': 'keep-alive', 'Referer': 'http://localhost:50505/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=0'}
2025-06-20 12:30:05,539 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:30:05,539 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:30:05,540 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': 'application/json, text/plain, */*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Connection': 'keep-alive', 'Referer': 'http://localhost:50505/', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=0'}
2025-06-20 12:30:05,540 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:30:05,540 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...XUqV5IDECA (truncated, length: 2473)
2025-06-20 12:30:05,540 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:30:05,540 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:30:05,540 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...XUqV5IDECA (length: 2466)
2025-06-20 12:30:05,541 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:30:05,541 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'ppjR1MFYyYRPBEh8ManwsvcXIHb0hm-i3NDHRIg0MYg', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:30:05,541 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:30:05,541 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750425420
2025-06-20 12:30:05,541 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:30:05,541 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:30:05,541 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:30:05,542 - root - DEBUG - Token: eyJ0eXAiOi...XUqV5IDECA (truncated)
2025-06-20 12:30:05,542 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:30:05,688 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:30:05,690 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:30:05,694 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:30:05,695 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:30:05,816 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:30:05 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:30:05,818 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:30:05,832 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:30:05 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:30:05,833 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-20 12:30:05,833 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-20 12:30:05,833 - backend.cost_management.cost_management_service - DEBUG - Executing resource cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, resource_group: None, resource_type: None
2025-06-20 12:30:05,833 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff7a5711b0>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff7a570220>}
2025-06-20 12:30:05,834 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:30:05,834 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:30:05,834 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '293'
    'Accept': 'application/json'
    'x-ms-client-request-id': '4bc7e568-4dd2-11f0-9f93-8205a49bb958'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-20 12:30:06,961 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 148009
2025-06-20 12:30:06,979 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '148009'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'e6b93c69-bf77-4b68-83dc-cb4c3e40bb83'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '4bc7e568-4dd2-11f0-9f93-8205a49bb958'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: B4EB29F04D5940729A0A2F6873ABE98D Ref B: AMS231020615039 Ref C: 2025-06-20T12:30:05Z'
    'Date': 'Fri, 20 Jun 2025 12:30:06 GMT'
2025-06-20 12:30:06,991 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:30:06,991 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK resource query successful with 851 rows
2025-06-20 12:30:06,996 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:30:05 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:30:06,999 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:30:07,085 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '66789'
    'Date': 'Fri, 20 Jun 2025 12:30:06 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:30:07,087 - backend.cost_management.cost_api_quart - WARNING - Cosmos DB configuration missing
2025-06-20 12:30:07,087 - backend.cost_management.cost_api_quart - INFO - Querying Azure Cost Management for tag 'project-id' with project_id: 51fc8503-9c4d-4907-a6d8-8eecb9aaff66
2025-06-20 12:30:07,088 - backend.cost_management.cost_api_quart - INFO - Time range: month
2025-06-20 12:30:07,088 - backend.cost_management.cost_management_service - DEBUG - Creating tag filter for project-id = 51fc8503-9c4d-4907-a6d8-8eecb9aaff66
2025-06-20 12:30:07,089 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-20 12:30:07,089 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-20 12:30:07,090 - backend.cost_management.cost_management_service - DEBUG - Executing cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, tag_name: project-id, tag_value: 51fc8503-9c4d-4907-a6d8-8eecb9aaff66
2025-06-20 12:30:07,090 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff7a572620>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff7a572440>}
2025-06-20 12:30:07,091 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:30:07,091 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:30:07,092 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '407'
    'Accept': 'application/json'
    'x-ms-client-request-id': '4c87c860-4dd2-11f0-9f93-8205a49bb958'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-20 12:30:08,144 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 451
2025-06-20 12:30:08,146 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '451'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': '099acc49-5a14-4e51-aa5d-b4e57ec0b96e'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '4c87c860-4dd2-11f0-9f93-8205a49bb958'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 3C1019E11527401A806AD31AABD1CB16 Ref B: AMS231020615039 Ref C: 2025-06-20T12:30:07Z'
    'Date': 'Fri, 20 Jun 2025 12:30:07 GMT'
2025-06-20 12:30:08,148 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:30:08,148 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK cost query successful with 0 rows
2025-06-20 12:30:08,149 - backend.cost_management.cost_management_service - INFO - Columns: ['Cost', 'ResourceId', 'Currency']
2025-06-20 12:30:08,149 - backend.cost_management.cost_api_quart - INFO - Azure Cost Management query completed
2025-06-20 12:30:08,150 - backend.cost_management.cost_api_quart - INFO - Result type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:30:08,150 - backend.cost_management.cost_api_quart - INFO - Result has rows: True
2025-06-20 12:30:08,151 - backend.cost_management.cost_api_quart - INFO - Number of rows: 0
2025-06-20 12:30:08,151 - backend.cost_management.cost_api_quart - INFO - Fetching names for 0 projects: []
2025-06-20 12:30:08,152 - backend.cost_management.cost_api_quart - INFO - Retrieved project names: {}
2025-06-20 12:30:08,152 - backend.cost_management.cost_api_quart - DEBUG - project_costs length: 0, result: {'additional_properties': {}, 'id': 'subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourcegroups/rg-internal-ai/providers/Microsoft.CostManagement/query/8f2ab597-33b9-42cc-a694-ded4245922e4', 'name': '8f2ab597-33b9-42cc-a694-ded4245922e4', 'type': 'Microsoft.CostManagement/query', 'location': None, 'sku': None, 'e_tag': None, 'tags': None, 'next_link': None, 'columns': [<azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff7a572680>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff7a572740>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff7a5724d0>], 'rows': []}, use_cosmos_data: False
2025-06-20 12:30:08,153 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/westeurope/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:30:08,199 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '487'
    'Date': 'Fri, 20 Jun 2025 12:30:07 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0c00aa8f-0000-0d00-0000-68404c030000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:30:08,202 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/westus2/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:30:08,246 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '476'
    'Date': 'Fri, 20 Jun 2025 12:30:07 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0300fb04-0000-0d00-0000-684c2f550000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:30:08,249 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/chilenorthcentral/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:30:08,304 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '503'
    'Date': 'Fri, 20 Jun 2025 12:30:07 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0c00ab8f-0000-0d00-0000-68404c090000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:30:13,288 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:30:13,289 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:30:23,290 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:30:23,291 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:30:33,292 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:30:33,294 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:30:43,297 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:30:43,298 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:30:53,300 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:30:53,300 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:31:03,306 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:31:03,307 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:31:13,309 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:31:13,310 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:31:23,311 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:31:23,312 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:31:32,029 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/user-context/me
2025-06-20 12:31:32,029 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:31:32,029 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:31:32,029 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:31:32,030 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:31:32,030 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:31:32,030 - rbac - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-20 12:31:32,030 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:31:32,031 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:31:32,241 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:31:32 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:31:32,241 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:31:32,242 - rbac - INFO - RBAC client initialized successfully in development mode.
2025-06-20 12:31:32,242 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:31:32,242 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:31:32,243 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:31:32,243 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:31:32,243 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:31:32,243 - root - ERROR - No valid user found in request or database after authentication checks
2025-06-20 12:31:32,244 - root - ERROR - Error getting user context: 
2025-06-20 12:31:32,244 - root - ERROR - Traceback: Traceback (most recent call last):
  File "/workspaces/branch1/backend/rbac/user_context.py", line 240, in get_user_context
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
fastapi.exceptions.HTTPException

2025-06-20 12:31:32,244 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:31:32,244 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:31:32,245 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:31:32,245 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:31:32,245 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:31:32,245 - root - INFO - get_mock_user called with user_id: 3
2025-06-20 12:31:32,245 - root - INFO - Returning existing user: {'id': '3', 'name': 'Regular User', 'email': '<EMAIL>', 'password': 'password123', 'role': 'REGULAR_USER', 'region': 'North America'}
2025-06-20 12:31:32,245 - root - INFO - Returning mock user due to error: {'id': '3', 'name': 'Regular User', 'email': '<EMAIL>', 'password': 'password123', 'role': 'REGULAR_USER', 'region': 'North America'}
2025-06-20 12:31:33,315 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:31:33,316 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:31:40,623 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/user-context/me
2025-06-20 12:31:40,624 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:31:40,624 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:31:40,624 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:31:40,625 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:31:40,625 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:31:40,625 - rbac - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-20 12:31:40,625 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:31:40,626 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:31:40,792 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:31:39 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:31:40,792 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:31:40,793 - rbac - INFO - RBAC client initialized successfully in development mode.
2025-06-20 12:31:40,793 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:31:40,794 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:31:40,794 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:31:40,794 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:31:40,794 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:31:40,794 - root - ERROR - No valid user found in request or database after authentication checks
2025-06-20 12:31:40,795 - root - ERROR - Error getting user context: 
2025-06-20 12:31:40,795 - root - ERROR - Traceback: Traceback (most recent call last):
  File "/workspaces/branch1/backend/rbac/user_context.py", line 240, in get_user_context
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
fastapi.exceptions.HTTPException

2025-06-20 12:31:40,795 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:31:40,795 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:31:40,795 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:31:40,795 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:31:40,796 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:31:40,796 - root - INFO - get_mock_user called with user_id: 3
2025-06-20 12:31:40,796 - root - INFO - Returning existing user: {'id': '3', 'name': 'Regular User', 'email': '<EMAIL>', 'password': 'password123', 'role': 'REGULAR_USER', 'region': 'North America'}
2025-06-20 12:31:40,796 - root - INFO - Returning mock user due to error: {'id': '3', 'name': 'Regular User', 'email': '<EMAIL>', 'password': 'password123', 'role': 'REGULAR_USER', 'region': 'North America'}
2025-06-20 12:31:43,320 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:31:43,321 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:31:53,323 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:31:53,323 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:32:03,325 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:32:03,325 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:32:13,326 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:32:13,326 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:32:23,326 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:32:23,328 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:32:33,328 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:32:33,329 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:32:43,331 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:32:43,331 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:32:53,334 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:32:53,334 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:33:03,340 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:33:03,340 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:33:13,341 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:33:13,341 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:33:23,344 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:33:23,345 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:33:31,300 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/user-context/me
2025-06-20 12:33:31,300 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:33:31,300 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:33:31,301 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:33:31,301 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:33:31,301 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:33:31,301 - rbac - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-20 12:33:31,302 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:33:31,303 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:31,471 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:33:30 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:33:31,471 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:33:31,471 - rbac - INFO - RBAC client initialized successfully in development mode.
2025-06-20 12:33:31,472 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:33:31,472 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:33:31,472 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:33:31,472 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:33:31,472 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:33:31,472 - root - ERROR - No valid user found in request or database after authentication checks
2025-06-20 12:33:31,472 - root - ERROR - Error getting user context: 
2025-06-20 12:33:31,472 - root - ERROR - Traceback: Traceback (most recent call last):
  File "/workspaces/branch1/backend/rbac/user_context.py", line 240, in get_user_context
    raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED)
fastapi.exceptions.HTTPException

2025-06-20 12:33:31,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:33:31,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:33:31,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json'}
2025-06-20 12:33:31,473 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:33:31,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type']
2025-06-20 12:33:31,473 - root - INFO - get_mock_user called with user_id: 3
2025-06-20 12:33:31,473 - root - INFO - Returning existing user: {'id': '3', 'name': 'Regular User', 'email': '<EMAIL>', 'password': 'password123', 'role': 'REGULAR_USER', 'region': 'North America'}
2025-06-20 12:33:31,473 - root - INFO - Returning mock user due to error: {'id': '3', 'name': 'Regular User', 'email': '<EMAIL>', 'password': 'password123', 'role': 'REGULAR_USER', 'region': 'North America'}
2025-06-20 12:33:33,347 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:33:33,347 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:33:43,351 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:33:43,351 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:33:47,195 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/rbac/projects
2025-06-20 12:33:47,195 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:33:47,195 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/rbac/projects
2025-06-20 12:33:47,195 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json', 'x-current-user-id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977'}
2025-06-20 12:33:47,196 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:33:47,196 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type', 'x-current-user-id']
2025-06-20 12:33:47,196 - rbac - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-20 12:33:47,197 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:33:47,197 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:47,361 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:33:47 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:33:47,361 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:33:47,361 - rbac - INFO - RBAC client initialized successfully in development mode.
2025-06-20 12:33:47,362 - rbac - INFO - Found current user ID in header: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-20 12:33:47,362 - rbac - INFO - Using mock authentication with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-20 12:33:47,362 - root - INFO - get_mock_user called with user_id: 7e43ed9b-4998-468e-bcd1-9b78f7c82977
2025-06-20 12:33:47,362 - root - INFO - Returning existing user: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:33:47,362 - rbac - INFO - get_mock_user returned: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:33:47,363 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:47,425 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:47 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:47,425 - rbac - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:33:47,425 - rbac - INFO - Set user role from database to: SUPER_ADMIN
2025-06-20 12:33:47,426 - rbac - INFO - Updated user with database info: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:33:47,426 - rbac - INFO - Returning authenticated user with role SUPER_ADMIN: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'name': 'Temporary User 7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'avatar': 'https://via.placeholder.com/150', 'type': 'user', 'region': None, 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:33:47,426 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:47,486 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:47 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:47,488 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:33:47,582 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '66789'
    'Date': 'Fri, 20 Jun 2025 12:33:47 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:53,353 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:33:53,353 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:33:54,843 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/user-context/me
2025-06-20 12:33:54,843 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:33:54,844 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:33:54,844 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json', 'x-current-user-id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977'}
2025-06-20 12:33:54,844 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:33:54,844 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type', 'x-current-user-id']
2025-06-20 12:33:54,844 - rbac - WARNING - No token found in request, but DEVELOPMENT_MODE is enabled. Creating RBAC client without token validation.
2025-06-20 12:33:54,845 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:33:54,845 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:55,039 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:33:55,040 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:33:55,040 - rbac - INFO - RBAC client initialized successfully in development mode.
2025-06-20 12:33:55,040 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:33:55,041 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:33:55,041 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'curl/7.88.1', 'accept': '*/*', 'content-type': 'application/json', 'x-current-user-id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977'}
2025-06-20 12:33:55,041 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:33:55,041 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['host', 'user-agent', 'accept', 'content-type', 'x-current-user-id']
2025-06-20 12:33:55,041 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:55,099 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,099 - root - INFO - Using user from database with ID from headers: Jean De Mevius (<EMAIL>)
2025-06-20 12:33:55,099 - root - INFO - Authenticated user: Jean De Mevius (<EMAIL>)
2025-06-20 12:33:55,100 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:55,101 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0xffff7a570490>
2025-06-20 12:33:55,101 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0xffff7a589060>, 13187.724)]']
connector: <aiohttp.connector.TCPConnector object at 0xffff7a570c10>
2025-06-20 12:33:55,162 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,165 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T12:29:00.426435+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00003297-0000-0d00-0000-6855540c0000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750422540}
2025-06-20 12:33:55,165 - root - INFO - Removing region from Super <NAME_EMAIL>
2025-06-20 12:33:55,166 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:55,210 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00003297-0000-0d00-0000-6855540c0000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,211 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:55,288 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '673'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00006303-0000-0d00-0000-67fe64ae0000"'
    'x-ms-schemaversion': 'REDACTED'
    'collection-partition-index': 'REDACTED'
    'collection-service-index': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-max-content-length': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:33:55,289 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'PUT'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Content-Type': 'application/json'
    'Accept': 'application/json'
    'Content-Length': '577'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:33:55,339 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00005697-0000-0d00-0000-685555330000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,342 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:55,407 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00005697-0000-0d00-0000-685555330000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,408 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:33:55,557 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '66789'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,558 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:55,604 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00005697-0000-0d00-0000-685555330000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,607 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/teams/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:33:55,765 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3758'
    'Date': 'Fri, 20 Jun 2025 12:33:55 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,769 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '27'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:33:55,896 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1517'
    'Date': 'Fri, 20 Jun 2025 12:33:55 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,899 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:33:55,947 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:33:55 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00005697-0000-0d00-0000-685555330000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:33:55,950 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:33:56,001 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:33:55 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:34:03,354 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:34:03,355 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:34:13,363 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:34:13,363 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:34:23,366 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:34:23,367 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:34:33,370 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:34:33,371 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:34:43,373 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:34:43,373 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:34:53,374 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:34:53,375 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:35:03,378 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:35:03,379 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:35:04,315 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:35:04,315 - root - INFO - Shutting down WebSocket service
2025-06-20 12:35:04,315 - root - INFO - Closing Cosmos DB client
2025-06-20 12:35:04,315 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:35:04,315 - root - INFO - Closing Storage Management client
2025-06-20 12:35:04,316 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:35:04,316 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:35:04,320 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:35:04,320 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:35:04,572 - root - INFO - Cleanup finished.

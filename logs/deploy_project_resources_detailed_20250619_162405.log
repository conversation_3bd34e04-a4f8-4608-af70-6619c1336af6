2025-06-19 16:24:05,887 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250619_162405.log
2025-06-19 16:24:05,887 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250619_162405.log
2025-06-19 16:24:05,890 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250619_162405.log
2025-06-19 16:24:05,890 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250619_162405.log
2025-06-19 16:24:05,965 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-19 16:24:05,965 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-19 16:24:05,970 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-19 16:24:05,973 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-19 16:24:05,989 - root - INFO - Running in development mode; using null session backend
2025-06-19 16:24:05,990 - root - INFO - Running in development mode; using null session backend
2025-06-19 16:24:05,994 - root - INFO - Running in development mode; using null session backend
2025-06-19 16:24:05,997 - root - INFO - Running in development mode; using null session backend
2025-06-19 16:24:06,030 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-19 16:24:06,030 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-19 16:24:06,031 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-19 16:24:06,031 - cosmos_db - INFO - Found existing deployments container
2025-06-19 16:24:06,031 - cosmos_db - INFO - Found existing deployments container
2025-06-19 16:24:06,031 - cosmos_db - INFO - Found existing deployments container
2025-06-19 16:24:06,031 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-19 16:24:06,032 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 16:24:06,031 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 16:24:06,032 - cosmos_db - INFO - Found existing deployments container
2025-06-19 16:24:06,031 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 16:24:06,035 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 16:24:06,130 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Thu, 19 Jun 2025 16:24:05 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-19 16:24:06,130 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-19 16:24:06,130 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-19 16:24:06,131 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-19 16:24:06,144 - root - INFO - Azure OpenAI client initialized
2025-06-19 16:24:06,144 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-19 16:24:06,144 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-19 16:24:06,144 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-19 16:24:06,144 - root - INFO - Using DummyCredential for local development
2025-06-19 16:24:06,144 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-19 16:24:06,145 - root - INFO - Storage Management Client initialized successfully.
2025-06-19 16:24:06,145 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-19 16:24:06,145 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-19 16:24:06,145 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-19 16:24:06,145 - root - INFO - Blob and index WebSocket service initialized
2025-06-19 16:24:06,146 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:06,146 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:06,158 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Thu, 19 Jun 2025 16:24:05 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-19 16:24:06,158 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-19 16:24:06,159 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-19 16:24:06,159 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-19 16:24:06,168 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Thu, 19 Jun 2025 16:24:06 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-19 16:24:06,169 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-19 16:24:06,171 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-19 16:24:06,172 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-19 16:24:06,176 - root - INFO - Azure OpenAI client initialized
2025-06-19 16:24:06,176 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-19 16:24:06,176 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-19 16:24:06,176 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-19 16:24:06,176 - root - INFO - Using DummyCredential for local development
2025-06-19 16:24:06,176 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-19 16:24:06,177 - root - INFO - Storage Management Client initialized successfully.
2025-06-19 16:24:06,177 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-19 16:24:06,177 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-19 16:24:06,177 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-19 16:24:06,177 - root - INFO - Blob and index WebSocket service initialized
2025-06-19 16:24:06,178 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:06,178 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:06,184 - root - INFO - Azure OpenAI client initialized
2025-06-19 16:24:06,184 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-19 16:24:06,184 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-19 16:24:06,184 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-19 16:24:06,184 - root - INFO - Using DummyCredential for local development
2025-06-19 16:24:06,184 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-19 16:24:06,185 - root - INFO - Storage Management Client initialized successfully.
2025-06-19 16:24:06,185 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-19 16:24:06,185 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-19 16:24:06,185 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-19 16:24:06,185 - root - INFO - Blob and index WebSocket service initialized
2025-06-19 16:24:06,185 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:06,185 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:06,188 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Thu, 19 Jun 2025 16:24:05 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-19 16:24:06,188 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-19 16:24:06,188 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-19 16:24:06,189 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-19 16:24:06,201 - root - INFO - Azure OpenAI client initialized
2025-06-19 16:24:06,201 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-19 16:24:06,201 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-19 16:24:06,201 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-19 16:24:06,201 - root - INFO - Using DummyCredential for local development
2025-06-19 16:24:06,201 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-19 16:24:06,202 - root - INFO - Storage Management Client initialized successfully.
2025-06-19 16:24:06,202 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-19 16:24:06,202 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-19 16:24:06,202 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-19 16:24:06,202 - root - INFO - Blob and index WebSocket service initialized
2025-06-19 16:24:06,203 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:06,203 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:16,146 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:16,147 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:16,178 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:16,179 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:16,187 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:16,187 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:16,204 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:16,205 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:26,151 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:26,152 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:26,181 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:26,182 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:26,189 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:26,189 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:26,208 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:26,209 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:36,157 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:36,158 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:36,190 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:36,191 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:36,191 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:36,192 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:36,213 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:36,214 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:46,168 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:46,169 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:46,193 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:46,193 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:46,194 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:46,194 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:46,219 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:46,219 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:56,171 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:56,172 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:56,198 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:56,197 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:56,198 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:56,199 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:24:56,226 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:24:56,227 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:06,180 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:06,181 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:06,200 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:06,200 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:06,201 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:06,201 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:06,235 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:06,236 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:16,184 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:16,185 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:16,205 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:16,206 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:16,211 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:16,211 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:16,240 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:16,241 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:26,195 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:26,196 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:26,206 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:26,207 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:26,213 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:26,214 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:26,245 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:26,246 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:36,207 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:36,208 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:36,208 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:36,210 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:36,216 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:36,217 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:36,248 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:36,249 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:46,211 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:46,211 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:46,212 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:46,212 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:46,221 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:46,222 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:46,250 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:46,251 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:56,214 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:56,215 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:56,216 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:56,217 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:56,224 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:56,225 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:25:56,253 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:25:56,253 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:06,217 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:06,218 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:06,218 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:06,219 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:06,228 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:06,229 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:06,256 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:06,257 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:16,221 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:16,222 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:16,222 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:16,223 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:16,231 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:16,231 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:16,266 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:16,267 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:26,229 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:26,229 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:26,230 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:26,230 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:26,234 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:26,235 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:26,270 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:26,270 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:36,231 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:36,232 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:36,232 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:36,233 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:36,237 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:36,238 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:36,277 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:36,279 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:46,235 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:46,236 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:46,236 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:46,236 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:46,240 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:46,240 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:46,284 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:46,284 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:56,238 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:56,237 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:56,238 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:56,239 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:56,246 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:56,246 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:26:56,291 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:26:56,291 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:06,245 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:06,246 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:06,247 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:06,248 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:06,248 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:06,249 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:06,299 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:06,300 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:16,251 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:16,251 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:16,253 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:16,254 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:16,258 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:16,258 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:16,311 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:16,313 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:26,253 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:26,254 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:26,256 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:26,256 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:26,261 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:26,262 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:26,317 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:26,318 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:36,257 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:36,258 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:36,258 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:36,259 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:36,266 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:36,267 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:36,322 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:36,322 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:46,265 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:46,265 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:46,269 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:46,269 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:46,276 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:46,276 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:46,324 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:46,325 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:56,270 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:56,271 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:56,275 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:56,276 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:56,280 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:56,281 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:27:56,331 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:27:56,331 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:06,271 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:06,271 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:06,280 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:06,280 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:06,283 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:06,283 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:06,333 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:06,334 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:16,278 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:16,278 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:16,282 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:16,283 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:16,286 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:16,287 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:16,336 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:16,337 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:26,281 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:26,282 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:26,285 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:26,286 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:26,289 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:26,290 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:26,338 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:26,339 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:36,288 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:36,289 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:36,290 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:36,291 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:36,299 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:36,299 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:36,345 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:36,345 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:46,290 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:46,291 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:46,292 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:46,293 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:46,302 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:46,303 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:46,354 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:46,355 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:56,292 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:56,293 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:56,295 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:56,295 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:56,305 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:56,305 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:28:56,357 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:28:56,358 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:06,301 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:06,302 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:06,302 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:06,303 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:06,309 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:06,309 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:06,362 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:06,363 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:16,306 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:16,306 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:16,307 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:16,307 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:16,312 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:16,313 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:16,368 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:16,368 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:26,314 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:26,315 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:26,317 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:26,317 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:26,323 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:26,324 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:26,371 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:26,372 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:36,317 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:36,318 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:36,318 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:36,319 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:36,330 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:36,331 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:36,380 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:36,381 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:46,320 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:46,321 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:46,322 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:46,323 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:46,333 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:46,334 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:46,383 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:46,384 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:56,326 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:56,326 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:56,327 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:56,328 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:56,336 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:56,337 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:29:56,391 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:29:56,392 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:06,330 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:06,331 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:06,339 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:06,340 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:06,341 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:06,342 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:06,400 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:06,400 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:16,332 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:16,333 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:16,343 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:16,344 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:16,344 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:16,345 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:16,402 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:16,403 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:26,345 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:26,345 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:26,349 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:26,350 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:26,354 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:26,355 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:26,406 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:26,407 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:36,350 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:36,351 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:36,355 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:36,355 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:36,359 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:36,360 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:36,413 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:36,414 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:46,363 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:46,363 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:46,364 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:46,363 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:46,364 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:46,365 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:46,416 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:46,417 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:56,368 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:56,368 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:56,369 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:56,369 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:56,369 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:56,370 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:30:56,424 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:30:56,425 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:06,370 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:06,371 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:06,372 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:06,373 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:06,376 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:06,377 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:06,435 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:06,436 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:16,373 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:16,374 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:16,374 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:16,375 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:16,379 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:16,380 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:16,439 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:16,440 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:26,375 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:26,376 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:26,376 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:26,377 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:26,382 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:26,382 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:26,441 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:26,442 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:36,381 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:36,381 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:36,382 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:36,382 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:36,383 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:36,383 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:36,443 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:36,444 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:46,383 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:46,384 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:46,384 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:46,384 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:46,385 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:46,386 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:46,447 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:46,448 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:56,387 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:56,388 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:56,391 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:56,392 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:56,394 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:56,395 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:31:56,459 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:31:56,460 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:06,391 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:06,392 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:06,393 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:06,394 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:06,402 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:06,403 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:06,461 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:06,462 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:16,394 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:16,394 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:16,395 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:16,395 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:16,405 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:16,405 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:16,463 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:16,464 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:26,397 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:26,397 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:26,398 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:26,398 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:26,413 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:26,414 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:26,466 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:26,466 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:36,405 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:36,405 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:36,406 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:36,406 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:36,416 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:36,417 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:36,471 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:36,472 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:46,407 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:46,409 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:46,409 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:46,410 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:46,420 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:46,421 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:46,476 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:46,477 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:56,411 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:56,412 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:56,413 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:56,413 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:56,424 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:56,425 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:32:56,480 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:32:56,481 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:06,417 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:06,417 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:06,418 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:06,418 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:06,427 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:06,428 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:06,482 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:06,483 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:16,420 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:16,420 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:16,421 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:16,422 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:16,433 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:16,434 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:16,487 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:16,488 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:26,428 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:26,428 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:26,433 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:26,433 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:26,436 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:26,436 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:26,492 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:26,493 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:36,433 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:36,434 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:36,434 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:36,435 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:36,437 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:36,437 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:36,498 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:36,499 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:46,437 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:46,438 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:46,439 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:46,439 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:46,440 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:46,440 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:46,500 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:46,501 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:56,443 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:56,444 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:56,445 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:56,445 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:56,446 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:56,445 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:33:56,503 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:33:56,504 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:06,447 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:06,448 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:06,450 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:06,450 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:06,451 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:06,451 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:06,505 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:06,506 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:16,450 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:16,451 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:16,453 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:16,453 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:16,454 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:16,454 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:16,509 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:16,510 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:26,455 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:26,455 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:26,456 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:26,456 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:26,463 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:26,463 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:26,511 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:26,512 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:36,460 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:36,461 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:36,465 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:36,466 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:36,467 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:36,468 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:36,516 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:36,517 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:46,472 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:46,472 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:46,473 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:46,473 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:46,473 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:46,473 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:46,520 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:46,520 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:56,473 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:56,474 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:56,478 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:56,478 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:56,479 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:56,479 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:34:56,524 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:34:56,525 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:06,476 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:06,477 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:06,482 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:06,482 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:06,482 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:06,483 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:06,529 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:06,529 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:16,479 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:16,480 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:16,488 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:16,489 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:16,490 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:16,490 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:16,530 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:16,531 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:26,487 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:26,488 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:26,495 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:26,496 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:26,496 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:26,497 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:26,538 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:26,539 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:36,498 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:36,499 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:36,500 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:36,500 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:36,508 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:36,509 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:36,541 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:36,541 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:46,500 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:46,501 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:46,505 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:46,506 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:46,510 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:46,511 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:46,543 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:46,544 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:56,505 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:56,506 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:56,515 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:56,516 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:56,516 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:56,517 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:35:56,546 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:35:56,547 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:06,513 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:06,513 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:06,523 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:06,523 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:06,523 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:06,523 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:06,555 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:06,556 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:16,524 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:16,525 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:16,527 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:16,529 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:16,529 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:16,529 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:16,559 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:16,560 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:26,532 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:26,532 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:26,533 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:26,534 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:26,535 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:26,535 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:26,563 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:26,564 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:36,538 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:36,539 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:36,540 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:36,540 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:36,542 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:36,542 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:36,566 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:36,567 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:46,540 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:46,541 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:46,543 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:46,543 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:46,550 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:46,551 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:46,567 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:46,568 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:56,544 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:56,545 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:56,548 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:56,548 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:56,552 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:56,553 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:36:56,570 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:36:56,571 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:06,551 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:06,552 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:06,560 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:06,560 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:06,562 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:06,563 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:06,572 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:06,573 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:16,554 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:16,555 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:16,562 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:16,563 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:16,563 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:16,564 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:16,575 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:16,576 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:26,558 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:26,559 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:26,571 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:26,571 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:26,572 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:26,572 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:26,577 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:26,577 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:36,564 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:36,565 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:36,575 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:36,575 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:36,576 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:36,576 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:36,579 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:36,579 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:46,570 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:46,570 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:46,578 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:46,579 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:46,580 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:46,580 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:46,581 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:46,581 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:56,575 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:56,576 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:56,583 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:56,583 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:56,585 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:56,586 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:37:56,591 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:37:56,592 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:06,578 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:06,578 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:06,586 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:06,586 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:06,588 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:06,589 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:06,599 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:06,599 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:16,583 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:16,584 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:16,587 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:16,588 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:16,593 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:16,593 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:16,601 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:16,602 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:26,586 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:26,586 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:26,589 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:26,590 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:26,596 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:26,597 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:26,609 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:26,610 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:36,588 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:36,589 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:36,591 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:36,592 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:36,600 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:36,601 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:36,613 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:36,614 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:46,594 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:46,595 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:46,594 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:46,595 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:46,604 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:46,605 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:46,617 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:46,617 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:56,600 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:56,601 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:56,600 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:56,602 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:56,605 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:56,606 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:38:56,618 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:38:56,618 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:06,604 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:06,605 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:06,604 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:06,606 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:06,611 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:06,611 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:06,620 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:06,621 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:16,607 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:16,608 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:16,607 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:16,609 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:16,615 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:16,616 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:16,624 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:16,624 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:26,608 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:26,609 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:26,610 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:26,610 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:26,620 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:26,621 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:26,628 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:26,629 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:36,620 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:36,621 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:36,620 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:36,622 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:36,630 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:36,630 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:36,631 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:36,632 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:46,625 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:46,626 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:46,625 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:46,627 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:46,633 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:46,634 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:46,636 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:46,637 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:56,631 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:56,632 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:56,631 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:56,633 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:56,636 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:56,637 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:39:56,638 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:39:56,639 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:06,638 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:06,638 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:06,639 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:06,640 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:06,642 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:06,642 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:06,648 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:06,649 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:16,641 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:16,641 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:16,642 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:16,643 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:16,642 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:16,644 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:16,651 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:16,651 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:26,647 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:26,647 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:26,648 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:26,647 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:26,648 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:26,649 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:26,655 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:26,655 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:36,649 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:36,650 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:36,651 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:36,650 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:36,652 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:36,651 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:36,658 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:36,658 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:46,654 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:46,654 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:46,654 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:46,656 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:46,655 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:46,656 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:46,660 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:46,661 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:56,659 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:56,658 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:56,659 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:56,658 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:56,660 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:56,661 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:40:56,663 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:40:56,664 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:06,661 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:06,661 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:06,662 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:06,663 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:06,663 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:06,664 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:06,668 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:06,669 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:16,663 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:16,664 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:16,665 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:16,665 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:16,666 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:16,665 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:16,673 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:16,674 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:26,665 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:26,666 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:26,667 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:26,667 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:26,668 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:26,667 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:26,675 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:26,676 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:36,670 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:36,670 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:36,671 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:36,670 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:36,672 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:36,672 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:36,681 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:36,682 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:46,675 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:46,676 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:46,675 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:46,677 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:46,681 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:46,681 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:46,688 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:46,689 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:56,679 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:56,680 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:56,683 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:56,683 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:56,688 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:56,689 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:41:56,700 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:41:56,701 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:06,682 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:06,683 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:06,684 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:06,685 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:06,692 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:06,692 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:06,703 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:06,703 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:16,684 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:16,685 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:16,685 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:16,686 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:16,695 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:16,696 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:16,707 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:16,708 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:26,686 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:26,687 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:26,687 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:26,688 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:26,699 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:26,699 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:26,713 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:26,714 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:36,690 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:36,691 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:36,692 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:36,693 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:36,702 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:36,702 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:36,716 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:36,717 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:46,703 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:46,703 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:46,704 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:46,704 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:46,711 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:46,712 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:46,719 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:46,720 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:56,707 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:56,708 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:56,708 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:56,710 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:56,722 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:56,722 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:42:56,723 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:42:56,723 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:06,710 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:06,711 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:06,711 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:06,712 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:06,728 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:06,729 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:06,735 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:06,735 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:16,713 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:16,713 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:16,714 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:16,713 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:16,733 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:16,734 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:16,738 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:16,739 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:26,718 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:26,719 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:26,719 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:26,720 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:26,735 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:26,736 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:26,740 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:26,741 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:36,727 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:36,728 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:36,727 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:36,729 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:36,739 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:36,740 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:36,743 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:36,743 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:46,737 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:46,737 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:46,738 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:46,738 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:46,744 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:46,745 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:46,746 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:46,747 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:56,740 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:56,741 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:56,740 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:56,742 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:56,748 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:56,749 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:43:56,750 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:43:56,751 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:06,750 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:06,750 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:06,750 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:06,751 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:06,751 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:06,751 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:06,751 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:06,751 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:16,752 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:16,753 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:16,753 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:16,753 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:16,753 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:16,753 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:16,753 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:16,754 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:26,753 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:26,754 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:26,753 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:26,755 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:26,755 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:26,756 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:26,757 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:26,758 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:36,758 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:36,758 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:36,758 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:36,759 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:36,759 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:36,759 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:36,760 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:36,760 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:46,764 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:46,764 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:46,765 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:46,765 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:46,771 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:46,771 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:46,771 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:46,772 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:56,767 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:56,768 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:56,775 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:56,776 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:56,776 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:56,777 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:44:56,780 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:44:56,781 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:06,769 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:06,770 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:06,777 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:06,778 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:06,779 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:06,780 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:06,783 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:06,784 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:16,771 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:16,772 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:16,780 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:16,781 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:16,782 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:16,783 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:16,788 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:16,788 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:26,780 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:26,781 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:26,782 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:26,782 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:26,790 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:26,791 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:26,792 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:26,793 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:36,783 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:36,784 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:36,784 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:36,788 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:36,793 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:36,794 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:36,796 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:36,797 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:46,790 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:46,791 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:46,792 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:46,791 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:46,798 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:46,798 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:46,801 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:46,802 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:56,798 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:56,799 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:56,799 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:56,801 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:56,801 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:56,801 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:45:56,808 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:45:56,808 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:06,801 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:06,803 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:06,802 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:06,804 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:06,809 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:06,810 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:06,814 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:06,815 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:16,805 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:16,805 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:16,806 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:16,806 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:16,810 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:16,811 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:16,819 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:16,819 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:26,809 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:26,810 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:26,812 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:26,813 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:26,814 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:26,815 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:26,819 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:26,819 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:36,814 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:36,814 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:36,814 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:36,815 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:36,816 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:36,816 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:36,820 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:36,821 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:46,817 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:46,818 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:46,817 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:46,819 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:46,823 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:46,824 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:46,825 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:46,826 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:56,823 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:56,823 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:56,824 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:56,824 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:56,826 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:56,827 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:46:56,829 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:46:56,829 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:06,827 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:06,828 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:06,830 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:06,831 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:06,833 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:06,833 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:06,834 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:06,834 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:16,830 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:16,834 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:16,834 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:16,835 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:16,835 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:16,835 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:16,837 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:16,837 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:26,836 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:26,837 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:26,836 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:26,837 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:26,838 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:26,838 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:26,838 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:26,839 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:36,839 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:36,840 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:36,839 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:36,840 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:36,841 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:36,841 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:36,842 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:36,842 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:46,843 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:46,843 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:46,844 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:46,844 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:46,845 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:46,846 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:46,847 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:46,848 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:56,851 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:56,851 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:56,852 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:56,853 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:56,853 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:56,854 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:47:56,856 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:47:56,856 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:06,855 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:06,855 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:06,856 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:06,855 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:06,856 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:06,857 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:06,858 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:06,858 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:16,865 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:16,865 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:16,866 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:16,866 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:16,865 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:16,866 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:16,868 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:16,868 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:26,875 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:26,875 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:26,876 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:26,875 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:26,876 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:26,877 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:26,877 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:26,878 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:36,883 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:36,883 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:36,884 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:36,884 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:36,884 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:36,885 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:36,886 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:36,886 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:46,891 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:46,892 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:46,891 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:46,892 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:46,893 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:46,893 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:46,895 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:46,896 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:56,897 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:56,898 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:56,897 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:56,899 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:56,898 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:48:56,900 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:56,898 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:48:56,899 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:06,905 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:06,906 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:06,905 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:06,905 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:06,907 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:06,907 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:06,908 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:06,909 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:16,908 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:16,909 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:16,916 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:16,916 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:16,917 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:16,916 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:16,917 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:16,918 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:26,910 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:26,911 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:26,918 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:26,918 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:26,919 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:26,919 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:26,919 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:26,920 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:36,912 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:36,913 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:36,921 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:36,921 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:36,921 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:36,922 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:36,922 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:36,922 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:46,919 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:46,920 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:46,926 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:46,930 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:46,927 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:46,927 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:46,935 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:46,935 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:56,923 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:56,924 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:56,934 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:56,935 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:56,937 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:56,938 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:49:56,939 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:49:56,939 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:06,930 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:06,931 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:06,938 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:06,939 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:06,942 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:06,942 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:06,943 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:06,943 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:16,936 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:16,937 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:16,941 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:16,941 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:16,945 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:16,946 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:16,947 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:16,948 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:26,947 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:26,947 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:26,947 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:26,948 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:26,948 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:26,949 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:26,949 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:26,950 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:36,952 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:36,952 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:36,952 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:36,954 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:36,953 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:36,955 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:36,954 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:36,954 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:46,960 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:46,960 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:46,961 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:46,960 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:46,960 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:46,961 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:46,962 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:46,963 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:56,962 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:56,962 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:56,963 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:56,963 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:56,964 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:56,964 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:50:56,965 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:50:56,965 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:06,965 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:06,965 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:06,965 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:06,965 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:06,967 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:06,966 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:06,966 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:06,967 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:16,969 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:16,969 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:16,970 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:16,969 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:16,970 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:16,971 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:16,972 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:16,972 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:26,974 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:26,974 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:26,975 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:26,975 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:26,979 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:26,979 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:26,980 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:26,980 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:36,982 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:36,984 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:36,983 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:36,983 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:36,985 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:36,983 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:36,985 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:36,986 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:46,987 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:46,987 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:46,989 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:46,989 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:46,991 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:46,991 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:46,992 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:46,993 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:56,990 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:56,991 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:56,992 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:56,992 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:56,993 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:56,994 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:51:56,997 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:51:56,998 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:06,994 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:06,995 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:06,994 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:06,996 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:07,000 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:07,001 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:07,001 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:07,001 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:16,997 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:16,997 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:16,997 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:16,998 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:17,006 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:17,006 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:17,008 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:17,008 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:26,999 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:27,001 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:27,000 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:27,002 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:27,008 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:27,009 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:27,010 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:27,011 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:37,011 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:37,012 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:37,011 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:37,013 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:37,015 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:37,015 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:37,019 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:37,020 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:47,016 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:47,017 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:47,016 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:47,016 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:47,018 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:47,018 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:47,022 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:47,023 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:57,022 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:57,022 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:57,022 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:57,023 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:57,024 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:57,024 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:52:57,025 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:52:57,025 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:07,027 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:07,028 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:07,028 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:07,028 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:07,028 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:07,029 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:07,029 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:07,029 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:17,035 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:17,035 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:17,036 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:17,036 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:17,036 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:17,037 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:17,038 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:17,038 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:27,040 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:27,041 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:27,040 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:27,042 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:27,041 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:27,041 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:27,042 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:27,043 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:37,050 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:37,051 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:37,050 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:37,051 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:37,052 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:37,052 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:37,052 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:37,052 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:47,052 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:47,053 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:47,053 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:47,054 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:47,057 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:47,057 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:47,058 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:47,059 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:57,065 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:57,066 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:57,065 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:57,066 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:57,066 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:53:57,067 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:57,067 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:53:57,068 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:07,067 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:07,068 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:07,069 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:07,069 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:07,070 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:07,070 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:07,075 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:07,076 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:17,071 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:17,071 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:17,072 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:17,072 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:17,071 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:17,073 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:17,079 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:17,080 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:27,079 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:27,080 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:27,080 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:27,080 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:27,081 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:27,081 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:27,085 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:27,086 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:37,082 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:37,083 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:37,083 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:37,084 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:37,086 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:37,087 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:37,087 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:37,088 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:47,087 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:47,088 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:47,089 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:47,088 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:47,088 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:47,090 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:47,089 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:47,090 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:57,093 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:57,094 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:57,093 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:57,095 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:57,095 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:57,094 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:54:57,101 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:54:57,102 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:07,098 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:07,100 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:07,099 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:07,101 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:07,104 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:07,105 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:07,106 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:07,107 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:17,103 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:17,103 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:17,104 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:17,103 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:17,107 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:17,108 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:17,111 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:17,112 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:27,107 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:27,108 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:27,107 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:27,109 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:27,118 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:27,118 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:27,120 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:27,119 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:37,109 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:37,110 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:37,111 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:37,111 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:37,121 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:37,121 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:37,123 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:37,123 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:47,114 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:47,115 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:47,114 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:47,116 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:47,128 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:47,129 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:47,129 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:47,130 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:57,118 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:57,119 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:57,118 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:57,120 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:57,134 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:57,134 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:55:57,135 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:55:57,136 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:07,123 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:07,124 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:07,124 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:07,125 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:07,137 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:07,138 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:07,139 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:07,140 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:17,126 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:17,127 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:17,126 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:17,128 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:17,142 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:17,142 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:17,143 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:17,143 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:27,130 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:27,131 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:27,130 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:27,132 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:27,144 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:27,145 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:27,145 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:27,146 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:37,134 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:37,134 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:37,135 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:37,135 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:37,148 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:37,149 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:37,149 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:37,151 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:47,136 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:47,137 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:47,137 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:47,138 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:47,152 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:47,152 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:47,154 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:47,155 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:57,139 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:57,140 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:57,141 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:57,142 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:57,155 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:57,156 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:56:57,156 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:56:57,157 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:07,142 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:07,143 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:07,142 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:07,144 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:07,163 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:07,164 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:07,165 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:07,166 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:17,147 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:17,148 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:17,147 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:17,149 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:17,165 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:17,166 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:17,168 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:17,169 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:27,150 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:27,151 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:27,150 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:27,152 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:27,169 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:27,170 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:27,175 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:27,176 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:37,153 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:37,154 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:37,153 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:37,155 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:37,175 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:37,176 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:37,180 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:37,181 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:47,164 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:47,164 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:47,165 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:47,166 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:47,178 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:47,178 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:47,187 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:47,187 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:57,167 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:57,167 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:57,168 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:57,168 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:57,178 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:57,179 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:57:57,189 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:57:57,190 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:07,170 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:07,172 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:07,171 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:07,174 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:07,189 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:07,190 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:07,192 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:07,192 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:17,176 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:17,177 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:17,179 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:17,179 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:17,191 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:17,192 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:17,198 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:17,198 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:27,178 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:27,179 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:27,180 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:27,181 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:27,193 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:27,194 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:27,201 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:27,202 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:37,183 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:37,183 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:37,184 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:37,185 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:37,199 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:37,200 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:37,205 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:37,205 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:47,192 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:47,193 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:47,192 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:47,193 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:47,202 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:47,204 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:47,211 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:47,211 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:57,196 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:57,197 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:57,204 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:57,205 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:57,206 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:57,207 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:58:57,214 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:58:57,215 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:07,199 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:07,200 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:07,209 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:07,209 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:07,212 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:07,213 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:07,216 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:07,216 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:17,207 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:17,208 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:17,211 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:17,211 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:17,218 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:17,219 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:17,225 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:17,226 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:27,214 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:27,215 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:27,214 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:27,216 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:27,220 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:27,221 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:27,229 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:27,230 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:37,215 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:37,216 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:37,217 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:37,218 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:37,225 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:37,225 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:37,234 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:37,235 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:47,217 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:47,218 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:47,224 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:47,225 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:47,226 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:47,226 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:47,244 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:47,245 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:57,220 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:57,221 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:57,228 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:57,228 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:57,228 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:57,229 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 16:59:57,250 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 16:59:57,250 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:07,228 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:07,228 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:07,238 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:07,240 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:07,238 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:07,242 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:07,252 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:07,253 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:17,233 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:17,233 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:17,244 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:17,244 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:17,245 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:17,245 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:17,257 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:17,258 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:27,238 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:27,239 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:27,246 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:27,247 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:27,247 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:27,247 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:27,265 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:27,266 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:37,241 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:37,242 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:37,248 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:37,248 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:37,249 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:37,250 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:37,268 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:37,269 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:47,247 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:47,248 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:47,251 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:47,251 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:47,252 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:47,252 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:47,270 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:47,271 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:57,251 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:57,252 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:57,254 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:57,255 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:57,262 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:57,263 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:00:57,275 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:00:57,276 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:07,255 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:07,256 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:07,256 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:07,257 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:07,265 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:07,266 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:07,284 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:07,285 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:17,262 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:17,263 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:17,262 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:17,263 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:17,268 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:17,269 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:17,287 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:17,288 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:27,265 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:27,266 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:27,267 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:27,266 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:27,271 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:27,272 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:27,293 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:27,294 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:37,269 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:37,270 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:37,270 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:37,270 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:37,275 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:37,276 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:37,305 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:37,306 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:47,271 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:47,272 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:47,271 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:47,273 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:47,278 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:47,279 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:47,314 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:47,315 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:57,276 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:57,276 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:57,277 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:57,277 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:57,282 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:57,283 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:01:57,324 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:01:57,325 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:07,281 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:07,281 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:07,283 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:07,282 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:07,284 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:07,285 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:07,332 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:07,333 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:17,292 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:17,293 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:17,293 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:17,292 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:17,294 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:17,294 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:17,336 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:17,336 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:27,297 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:27,297 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:27,298 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:27,297 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:27,299 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:27,299 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:27,347 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:27,348 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:37,303 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:37,303 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:37,304 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:37,304 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:37,305 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:37,306 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:37,351 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:37,352 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:47,307 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:47,307 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:47,308 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:47,309 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:47,313 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:47,314 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:47,356 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:47,357 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:57,311 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:57,311 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:57,312 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:57,312 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:57,315 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:57,316 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:02:57,361 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:02:57,362 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:07,313 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:07,314 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:07,315 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:07,316 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:07,319 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:07,320 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:07,367 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:07,368 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:17,315 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:17,316 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:17,317 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:17,317 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:17,322 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:17,322 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:17,377 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:17,377 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:27,320 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:27,321 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:27,322 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:27,323 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:27,323 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:27,324 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:27,379 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:27,380 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:37,327 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:37,327 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:37,327 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:37,328 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:37,329 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:37,330 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:37,391 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:37,392 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:47,330 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:47,330 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:47,331 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:47,331 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:47,331 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:47,332 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:47,393 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:47,394 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:57,334 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:57,334 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:57,335 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:57,336 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:57,335 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:57,336 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:03:57,394 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:03:57,395 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:07,346 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:07,346 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:07,347 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:07,347 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:07,348 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:07,348 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:07,401 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:07,402 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:17,349 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:17,349 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:17,349 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:17,349 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:17,349 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:17,349 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:17,407 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:17,408 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:27,353 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:27,354 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:27,355 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:27,355 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:27,359 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:27,359 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:27,411 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:27,412 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:37,355 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:37,356 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:37,357 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:37,358 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:37,361 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:37,362 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:37,415 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:37,416 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:47,359 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:47,360 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:47,362 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:47,362 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:47,363 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:47,364 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:47,426 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:47,427 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:57,366 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:57,367 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:57,369 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:57,369 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:57,369 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:57,370 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:04:57,439 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:04:57,440 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:07,372 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:07,372 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:07,373 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:07,372 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:07,373 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:07,374 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:07,442 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:07,443 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:17,382 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:17,382 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:17,382 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:17,382 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:17,384 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:17,385 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:17,455 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:17,456 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:27,387 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:27,387 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:27,388 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:27,387 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:27,389 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:27,388 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:27,458 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:27,459 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:37,391 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:37,391 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:37,392 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:37,391 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:37,393 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:37,392 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:37,463 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:37,464 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:47,394 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:47,394 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:47,395 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:47,394 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:47,396 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:47,396 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:47,465 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:47,465 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:57,398 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:57,399 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:57,402 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:57,403 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:57,404 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:57,405 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:05:57,469 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:05:57,469 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:07,400 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:07,401 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:07,406 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:07,406 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:07,412 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:07,413 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:07,474 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:07,475 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:17,404 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:17,405 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:17,407 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:17,407 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:17,414 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:17,414 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:17,475 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:17,476 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:27,415 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:27,415 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:27,414 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:27,416 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:27,417 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:27,417 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:27,475 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:27,477 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:37,421 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:37,421 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:37,421 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:37,422 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:37,423 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:37,423 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:37,485 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:37,486 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:47,425 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:47,426 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:47,427 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:47,428 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:47,427 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:47,428 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:47,489 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:47,491 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:57,431 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:57,432 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:57,432 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:57,432 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:57,433 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:57,434 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:06:57,495 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:06:57,496 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:07,434 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:07,435 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:07,435 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:07,436 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:07,438 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:07,439 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:07,499 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:07,499 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:17,446 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:17,446 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:17,447 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:17,446 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:17,447 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:17,447 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:17,501 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:17,502 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:27,449 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:27,449 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:27,449 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:27,450 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:27,450 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:27,451 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:27,503 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:27,504 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:37,453 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:37,453 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:37,454 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:37,454 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:37,457 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:37,457 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:37,512 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:37,513 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:47,459 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:47,460 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:47,459 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:47,461 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:47,460 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:47,462 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:47,524 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:47,525 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:57,463 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:57,463 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:57,463 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:57,464 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:57,465 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:57,464 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:07:57,526 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:07:57,527 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:07,467 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:07,468 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:07,469 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:07,469 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:07,468 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:07,470 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:07,530 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:07,530 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:17,476 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:17,476 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:17,477 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:17,477 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:17,477 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:17,477 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:17,532 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:17,533 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:27,485 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:27,485 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:27,486 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:27,485 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:27,486 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:27,487 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:27,537 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:27,538 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:37,497 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:37,497 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:37,498 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:37,497 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:37,498 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:37,499 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:37,544 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:37,544 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:47,500 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:47,501 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:47,500 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:47,502 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:47,509 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:47,510 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:47,550 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:47,551 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:57,507 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:57,508 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:57,507 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:57,509 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:57,511 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:57,512 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:08:57,558 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:08:57,559 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:07,510 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:07,511 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:07,517 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:07,517 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:07,518 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:07,518 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:07,560 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:07,561 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:17,517 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:17,518 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:17,519 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:17,519 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:17,520 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:17,520 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:17,568 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:17,569 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:27,518 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:27,519 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:27,520 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:27,521 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:27,521 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:27,522 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:27,571 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:27,571 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:37,523 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:37,523 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:37,524 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:37,524 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:37,525 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:37,526 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:37,576 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:37,578 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:47,530 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:47,531 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:47,530 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:47,532 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:47,530 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:47,532 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:47,578 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:47,579 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:57,533 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:57,534 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:57,536 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:57,537 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:57,537 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:57,538 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:09:57,586 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:09:57,587 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:07,539 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:07,539 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:07,539 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:07,540 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:07,546 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:07,547 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:07,594 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:07,595 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:17,544 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:17,545 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:17,544 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:17,546 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:17,547 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:17,548 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:17,600 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:17,600 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:27,547 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:27,548 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:27,549 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:27,550 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:27,552 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:27,552 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:27,608 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:27,611 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:37,555 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:37,556 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:37,556 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:37,558 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:37,559 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:37,559 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:37,615 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:37,617 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:47,559 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:47,560 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:47,559 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:47,561 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:47,566 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:47,566 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:47,619 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:47,619 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:57,563 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:57,564 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:57,563 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:57,565 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:57,571 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:57,571 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:10:57,622 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:10:57,623 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:07,570 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:07,571 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:07,570 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:07,572 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:07,575 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:07,575 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:07,624 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:07,626 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:17,577 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:17,578 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:17,577 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:17,579 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:17,579 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:17,581 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:17,628 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:17,628 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:27,582 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:27,582 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:27,583 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:27,582 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:27,584 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:27,583 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:27,634 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:27,635 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:37,585 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:37,585 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:37,586 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:37,586 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:37,595 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:37,596 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:37,645 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:37,646 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:47,590 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:47,590 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:47,591 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:47,591 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:47,598 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:47,598 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:47,648 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:47,649 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:57,596 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:57,596 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:57,597 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:57,598 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:57,599 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:57,599 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:11:57,655 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:11:57,656 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:07,602 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:07,602 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:07,603 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:07,603 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:07,606 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:07,607 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:07,659 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:07,660 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:17,608 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:17,608 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:17,609 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:17,609 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:17,615 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:17,615 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:17,666 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:17,666 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:27,620 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:27,620 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:27,621 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:27,621 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:27,622 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:27,623 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:27,673 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:27,673 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:37,624 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:37,625 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:37,624 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:37,626 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:37,633 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:37,634 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:37,678 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:37,680 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:47,636 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:47,636 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:47,636 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:47,637 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:47,638 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:47,638 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:47,681 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:47,681 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:57,640 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:57,641 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:57,640 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:57,642 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:57,642 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:57,643 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:12:57,682 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:12:57,683 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:07,648 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:07,648 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:07,648 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:07,649 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:07,650 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:07,650 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:07,685 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:07,686 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:17,653 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:17,653 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:17,653 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:17,654 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:17,654 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:17,654 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:17,697 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:17,698 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:27,656 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:27,656 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:27,655 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:27,657 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:27,658 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:27,659 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:27,707 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:27,709 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:37,662 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:37,662 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:37,663 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:37,663 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:37,664 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:37,665 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:37,714 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:37,714 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:47,667 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:47,668 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:47,668 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:47,669 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:47,675 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:47,675 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:47,715 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:47,716 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:57,672 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:57,672 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:57,673 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:57,674 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:57,677 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:57,677 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:13:57,717 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:13:57,718 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:07,680 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:07,680 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:07,681 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:07,680 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:07,682 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:07,682 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:07,720 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:07,722 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:17,691 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:17,691 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:17,691 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:17,692 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:17,692 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:17,692 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:17,729 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:17,730 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:27,694 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:27,694 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:27,694 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:27,695 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:27,695 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:27,696 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:27,734 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:27,735 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:37,700 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:37,700 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:37,700 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:37,701 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:37,701 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:37,701 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:37,740 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:37,741 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:47,705 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:47,705 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:47,706 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:47,705 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:47,706 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:47,707 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:47,745 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:47,746 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:57,710 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:57,710 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:57,711 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:57,711 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:57,712 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:57,713 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:14:57,750 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:14:57,750 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:07,716 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:07,717 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:07,716 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:07,718 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:07,721 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:07,721 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:07,750 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:07,751 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:17,722 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:17,723 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:17,722 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:17,724 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:17,732 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:17,732 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:17,753 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:17,754 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:27,733 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:27,734 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:27,733 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:27,735 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:27,737 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:27,737 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:27,757 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:27,758 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:37,737 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:37,738 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:37,737 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:37,739 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:37,747 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:37,747 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:37,760 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:37,761 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:47,748 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:47,749 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:47,748 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:47,750 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:47,750 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:47,751 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:47,763 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:47,764 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:57,752 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:57,753 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:57,753 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:57,752 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:57,754 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:57,755 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:15:57,764 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:15:57,765 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:07,755 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:07,756 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:07,755 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:07,757 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:07,763 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:07,764 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:07,775 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:07,775 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:17,761 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:17,762 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:17,761 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:17,762 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:17,772 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:17,772 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:17,780 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:17,780 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:27,764 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:27,764 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:27,765 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:27,764 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:27,775 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:27,775 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:27,783 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:27,783 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:37,771 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:37,772 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:37,771 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:37,773 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:37,777 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:37,778 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:37,784 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:37,785 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:47,772 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:47,773 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:47,773 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:47,774 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:47,781 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:47,781 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:47,786 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:47,787 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:57,775 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:57,775 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:57,776 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:57,776 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:57,784 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:57,785 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:16:57,793 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:16:57,793 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:07,779 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:07,780 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:07,780 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:07,781 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:07,786 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:07,787 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:07,795 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:07,796 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:17,782 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:17,783 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:17,790 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:17,790 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:17,794 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:17,795 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:17,796 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:17,797 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:27,783 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:27,784 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:27,793 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:27,794 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:27,798 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:27,798 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:27,799 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:27,799 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:37,787 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:37,788 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:37,800 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:37,800 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:37,801 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:37,802 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:37,810 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:37,811 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:47,790 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:47,791 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:47,802 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:47,803 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:47,803 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:47,804 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:47,812 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:47,813 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:57,794 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:57,795 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:57,804 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:57,805 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:57,814 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:57,814 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:17:57,814 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:17:57,815 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:07,800 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:07,801 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:07,807 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:07,808 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:07,819 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:07,819 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:07,820 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:07,821 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:17,807 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:17,808 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:17,809 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:17,810 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:17,825 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:17,825 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:17,825 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:17,826 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:27,812 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:27,812 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:27,813 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:27,814 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:27,832 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:27,832 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:27,834 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:27,834 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:37,817 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:37,818 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:37,819 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:37,818 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:37,834 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:37,835 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:37,836 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:37,837 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:47,822 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:47,823 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:47,830 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:47,831 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:47,839 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:47,840 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:47,840 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:47,841 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:57,825 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:57,826 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:57,833 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:57,834 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:57,842 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:57,842 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:18:57,842 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:18:57,844 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:07,833 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:07,834 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:07,843 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:07,844 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:07,845 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:07,846 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:07,846 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:07,847 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:17,844 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:17,844 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:17,845 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:17,846 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:17,846 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:17,847 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:17,847 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:17,848 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:27,846 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:27,847 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:27,848 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:27,849 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:27,850 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:27,850 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:27,852 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:27,852 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:37,852 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:37,852 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:37,853 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:37,852 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:37,854 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:37,854 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:37,858 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:37,858 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:47,858 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:47,858 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:47,858 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:47,858 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:47,859 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:47,860 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:47,860 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:47,861 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:57,860 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:57,861 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:57,860 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:57,862 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:57,861 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:19:57,863 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:57,863 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:19:57,864 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:07,870 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:07,870 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:07,870 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:07,871 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:07,871 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:07,872 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:07,872 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:07,873 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:17,873 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:17,873 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:17,874 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:17,874 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:17,874 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:17,874 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:17,875 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:17,875 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:27,875 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:27,875 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:27,876 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:27,875 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:27,875 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:27,876 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:27,877 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:27,877 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:37,884 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:37,885 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:37,884 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:37,885 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:37,886 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:37,886 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:37,887 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:37,888 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:47,888 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:47,888 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:47,889 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:47,888 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:47,888 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:47,890 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:47,890 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:47,891 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:57,891 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:57,892 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:57,892 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:57,893 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:57,895 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:57,895 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:20:57,898 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:20:57,899 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:07,895 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:07,895 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:07,896 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:07,896 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:07,897 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:07,898 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:07,898 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:07,899 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:17,898 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:17,898 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:17,899 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:17,899 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:17,898 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:17,899 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:17,902 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:17,902 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:27,903 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:27,903 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:27,904 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:27,903 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:27,904 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:27,905 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:27,905 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:27,907 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:37,905 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:37,906 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:37,907 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:37,908 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:37,907 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:37,908 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:37,910 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:37,911 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:47,916 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:47,917 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:47,916 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:47,918 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:47,917 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:47,918 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:47,918 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:47,919 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:57,922 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:57,922 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:57,923 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:57,922 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:57,923 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:57,924 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:21:57,924 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:21:57,925 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:07,926 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:07,926 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:07,927 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:07,927 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:07,928 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:07,929 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:07,930 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:07,930 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:17,928 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:17,929 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:17,930 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:17,930 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:17,930 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:17,930 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:17,938 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:17,938 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:27,934 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:27,934 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:27,934 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:27,935 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:27,936 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:27,936 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:27,946 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:27,947 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:37,940 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:37,940 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:37,941 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:37,942 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:37,941 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:37,942 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:37,951 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:37,952 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:47,946 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:47,946 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:47,946 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:47,947 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:47,946 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:47,947 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:47,956 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:47,957 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:57,949 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:57,950 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:57,949 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:57,950 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:57,951 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:57,951 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:22:57,957 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:22:57,958 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:07,951 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:07,951 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:07,952 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:07,952 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:07,952 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:07,952 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:07,961 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:07,962 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:17,954 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:17,954 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:17,954 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:17,955 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:17,955 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:17,955 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:17,962 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:17,963 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:27,960 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:27,961 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:27,960 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:27,962 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:27,967 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:27,967 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:27,968 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:27,968 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:37,962 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:37,964 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:37,963 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:37,964 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:37,969 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:37,970 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:37,970 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:37,970 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:47,965 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:47,966 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:47,966 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:47,967 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:47,971 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:47,972 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:47,972 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:47,973 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:57,968 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:57,969 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:57,977 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:57,977 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:57,977 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:23:57,978 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:57,978 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:23:57,980 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:24:06,413 - schedule - INFO - Running job Every 1 hour do <lambda>() (last run: [never], next run: 2025-06-19 17:24:06)
2025-06-19 17:24:06,414 - backend.cost_management.cost_management_service - INFO - 🔧 Initializing CostManagementService...
2025-06-19 17:24:06,415 - backend.cost_management.cost_management_service - INFO - 🔧 Subscription ID: 4c1c14a3...
2025-06-19 17:24:06,415 - backend.cost_management.cost_management_service - INFO - 🔧 Resource Group: rg-internal-ai
2025-06-19 17:24:06,416 - backend.cost_management.cost_management_service - INFO - 🔧 CostManagementClient available: True
2025-06-19 17:24:06,416 - backend.cost_management.cost_management_service - INFO - 🔧 DefaultAzureCredential available: True
2025-06-19 17:24:06,416 - backend.cost_management.cost_management_service - INFO - 🔧 Attempting to initialize Azure Cost Management client...
2025-06-19 17:24:06,417 - backend.cost_management.cost_management_service - INFO - 🔧 Creating DefaultAzureCredential...
2025-06-19 17:24:06,418 - azure.identity._credentials.environment - INFO - Environment is configured for ClientSecretCredential
2025-06-19 17:24:06,418 - azure.identity._credentials.managed_identity - INFO - ManagedIdentityCredential will use IMDS
2025-06-19 17:24:06,419 - backend.cost_management.cost_management_service - INFO - 🔧 Creating CostManagementClient...
2025-06-19 17:24:06,420 - backend.cost_management.cost_management_service - INFO - ✅ CostManagementClient initialized successfully for subscription: 4c1c14a3...
2025-06-19 17:24:06,425 - backend.cost_management.cost_management_service - INFO - 🔧 Testing client connectivity...
2025-06-19 17:24:06,420 - schedule - INFO - Running job Every 1 hour do <lambda>() (last run: [never], next run: 2025-06-19 17:24:06)
2025-06-19 17:24:06,426 - backend.cost_management.cost_collection_task - INFO - Starting cost data collection task
2025-06-19 17:24:06,426 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:06,426 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:06,427 - backend.cost_management.cost_management_service - DEBUG - Executing cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, tag_name: project-id, tag_value: None
2025-06-19 17:24:06,426 - backend.cost_management.cost_management_service - INFO - 🔧 Initializing CostManagementService...
2025-06-19 17:24:06,429 - backend.cost_management.cost_management_service - INFO - 🔧 Subscription ID: 4c1c14a3...
2025-06-19 17:24:06,431 - backend.cost_management.cost_management_service - INFO - 🔧 Resource Group: rg-internal-ai
2025-06-19 17:24:06,431 - backend.cost_management.cost_management_service - INFO - 🔧 CostManagementClient available: True
2025-06-19 17:24:06,431 - backend.cost_management.cost_management_service - INFO - 🔧 DefaultAzureCredential available: True
2025-06-19 17:24:06,431 - backend.cost_management.cost_management_service - INFO - 🔧 Attempting to initialize Azure Cost Management client...
2025-06-19 17:24:06,433 - backend.cost_management.cost_management_service - INFO - 🔧 Creating DefaultAzureCredential...
2025-06-19 17:24:06,429 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7eb0>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5be1c040>}
2025-06-19 17:24:06,434 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:06,433 - azure.identity._credentials.environment - INFO - Environment is configured for ClientSecretCredential
2025-06-19 17:24:06,435 - azure.identity._credentials.managed_identity - INFO - ManagedIdentityCredential will use IMDS
2025-06-19 17:24:06,435 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:06,436 - backend.cost_management.cost_management_service - INFO - 🔧 Creating CostManagementClient...
2025-06-19 17:24:06,437 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533
2025-06-19 17:24:06,438 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 17:24:06,438 - backend.cost_management.cost_management_service - INFO - ✅ CostManagementClient initialized successfully for subscription: 4c1c14a3...
2025-06-19 17:24:06,440 - backend.cost_management.cost_management_service - INFO - 🔧 Testing client connectivity...
2025-06-19 17:24:06,441 - backend.cost_management.cost_collection_task - INFO - Starting cost data collection task
2025-06-19 17:24:06,443 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:06,445 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:06,446 - backend.cost_management.cost_management_service - DEBUG - Executing cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, tag_name: project-id, tag_value: None
2025-06-19 17:24:06,446 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7f70>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5be1c100>}
2025-06-19 17:24:06,447 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:06,447 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:06,447 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-19 17:24:06,448 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533
2025-06-19 17:24:06,449 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 17:24:06,450 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-19 17:24:06,462 - schedule - INFO - Running job Every 1 hour do <lambda>() (last run: [never], next run: 2025-06-19 17:24:06)
2025-06-19 17:24:06,462 - backend.cost_management.cost_management_service - INFO - 🔧 Initializing CostManagementService...
2025-06-19 17:24:06,462 - backend.cost_management.cost_management_service - INFO - 🔧 Subscription ID: 4c1c14a3...
2025-06-19 17:24:06,462 - backend.cost_management.cost_management_service - INFO - 🔧 Resource Group: rg-internal-ai
2025-06-19 17:24:06,463 - backend.cost_management.cost_management_service - INFO - 🔧 CostManagementClient available: True
2025-06-19 17:24:06,463 - backend.cost_management.cost_management_service - INFO - 🔧 DefaultAzureCredential available: True
2025-06-19 17:24:06,463 - backend.cost_management.cost_management_service - INFO - 🔧 Attempting to initialize Azure Cost Management client...
2025-06-19 17:24:06,463 - backend.cost_management.cost_management_service - INFO - 🔧 Creating DefaultAzureCredential...
2025-06-19 17:24:06,463 - azure.identity._credentials.environment - INFO - Environment is configured for ClientSecretCredential
2025-06-19 17:24:06,464 - azure.identity._credentials.managed_identity - INFO - ManagedIdentityCredential will use IMDS
2025-06-19 17:24:06,464 - backend.cost_management.cost_management_service - INFO - 🔧 Creating CostManagementClient...
2025-06-19 17:24:06,464 - backend.cost_management.cost_management_service - INFO - ✅ CostManagementClient initialized successfully for subscription: 4c1c14a3...
2025-06-19 17:24:06,465 - backend.cost_management.cost_management_service - INFO - 🔧 Testing client connectivity...
2025-06-19 17:24:06,465 - backend.cost_management.cost_collection_task - INFO - Starting cost data collection task
2025-06-19 17:24:06,465 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:06,465 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:06,465 - backend.cost_management.cost_management_service - DEBUG - Executing cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, tag_name: project-id, tag_value: None
2025-06-19 17:24:06,465 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7f70>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5be1c100>}
2025-06-19 17:24:06,466 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:06,466 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:06,467 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533
2025-06-19 17:24:06,468 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 17:24:06,469 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-19 17:24:06,498 - schedule - INFO - Running job Every 1 hour do <lambda>() (last run: [never], next run: 2025-06-19 17:24:06)
2025-06-19 17:24:06,499 - backend.cost_management.cost_management_service - INFO - 🔧 Initializing CostManagementService...
2025-06-19 17:24:06,499 - backend.cost_management.cost_management_service - INFO - 🔧 Subscription ID: 4c1c14a3...
2025-06-19 17:24:06,499 - backend.cost_management.cost_management_service - INFO - 🔧 Resource Group: rg-internal-ai
2025-06-19 17:24:06,499 - backend.cost_management.cost_management_service - INFO - 🔧 CostManagementClient available: True
2025-06-19 17:24:06,499 - backend.cost_management.cost_management_service - INFO - 🔧 DefaultAzureCredential available: True
2025-06-19 17:24:06,499 - backend.cost_management.cost_management_service - INFO - 🔧 Attempting to initialize Azure Cost Management client...
2025-06-19 17:24:06,499 - backend.cost_management.cost_management_service - INFO - 🔧 Creating DefaultAzureCredential...
2025-06-19 17:24:06,500 - azure.identity._credentials.environment - INFO - Environment is configured for ClientSecretCredential
2025-06-19 17:24:06,500 - azure.identity._credentials.managed_identity - INFO - ManagedIdentityCredential will use IMDS
2025-06-19 17:24:06,500 - backend.cost_management.cost_management_service - INFO - 🔧 Creating CostManagementClient...
2025-06-19 17:24:06,500 - backend.cost_management.cost_management_service - INFO - ✅ CostManagementClient initialized successfully for subscription: 4c1c14a3...
2025-06-19 17:24:06,500 - backend.cost_management.cost_management_service - INFO - 🔧 Testing client connectivity...
2025-06-19 17:24:06,500 - backend.cost_management.cost_collection_task - INFO - Starting cost data collection task
2025-06-19 17:24:06,501 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:06,501 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:06,501 - backend.cost_management.cost_management_service - DEBUG - Executing cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, tag_name: project-id, tag_value: None
2025-06-19 17:24:06,501 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5be18070>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5be181c0>}
2025-06-19 17:24:06,501 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:06,501 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:06,502 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533
2025-06-19 17:24:06,502 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 17:24:06,503 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-19 17:24:06,560 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2025-06-19 17:24:06,561 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'max-age=86400, private'
    'Content-Type': 'application/json; charset=utf-8'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Access-Control-Allow-Origin': 'REDACTED'
    'Access-Control-Allow-Methods': 'REDACTED'
    'P3P': 'REDACTED'
    'x-ms-request-id': 'f5818281-2766-41cf-bdc6-fd50f9b90000'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 19 Jun 2025 17:24:06 GMT'
    'Content-Length': '1753'
2025-06-19 17:24:06,562 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/kerberos', 'tenant_region_scope': 'EU', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-19 17:24:06,562 - msal.application - DEBUG - Broker enabled? None
2025-06-19 17:24:06,562 - msal.application - DEBUG - Region to be used: None
2025-06-19 17:24:06,564 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2025-06-19 17:24:06,564 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'max-age=86400, private'
    'Content-Type': 'application/json; charset=utf-8'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Access-Control-Allow-Origin': 'REDACTED'
    'Access-Control-Allow-Methods': 'REDACTED'
    'P3P': 'REDACTED'
    'x-ms-request-id': '1f023d08-6bae-4c64-a9ba-108144bb0000'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 19 Jun 2025 17:24:05 GMT'
    'Content-Length': '1753'
2025-06-19 17:24:06,565 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/kerberos', 'tenant_region_scope': 'EU', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-19 17:24:06,565 - msal.application - DEBUG - Broker enabled? None
2025-06-19 17:24:06,566 - msal.application - DEBUG - Region to be used: None
2025-06-19 17:24:06,567 - msal.telemetry - DEBUG - Generate or reuse correlation_id: f6006ecb-631a-4e42-857e-49a17f6a2a82
2025-06-19 17:24:06,567 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token'
Request method: 'POST'
Request headers:
    'Accept': 'application/json'
    'x-client-sku': 'REDACTED'
    'x-client-ver': 'REDACTED'
    'x-client-os': 'REDACTED'
    'x-ms-lib-capability': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-client-current-telemetry': 'REDACTED'
    'x-client-last-telemetry': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-19 17:24:06,569 - msal.telemetry - DEBUG - Generate or reuse correlation_id: bdcfb8f4-6037-46ff-918e-4cc5e7d5d60e
2025-06-19 17:24:06,569 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token'
Request method: 'POST'
Request headers:
    'Accept': 'application/json'
    'x-client-sku': 'REDACTED'
    'x-client-ver': 'REDACTED'
    'x-client-os': 'REDACTED'
    'x-ms-lib-capability': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-client-current-telemetry': 'REDACTED'
    'x-client-last-telemetry': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-19 17:24:06,600 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2025-06-19 17:24:06,600 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'max-age=86400, private'
    'Content-Type': 'application/json; charset=utf-8'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Access-Control-Allow-Origin': 'REDACTED'
    'Access-Control-Allow-Methods': 'REDACTED'
    'P3P': 'REDACTED'
    'x-ms-request-id': '710279f4-ecdc-452f-ac77-08dee1bf0200'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 19 Jun 2025 17:24:06 GMT'
    'Content-Length': '1753'
2025-06-19 17:24:06,601 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/kerberos', 'tenant_region_scope': 'EU', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-19 17:24:06,602 - msal.application - DEBUG - Broker enabled? None
2025-06-19 17:24:06,602 - msal.application - DEBUG - Region to be used: None
2025-06-19 17:24:06,604 - msal.telemetry - DEBUG - Generate or reuse correlation_id: dad10243-ec9b-4f15-8535-c85f48947c09
2025-06-19 17:24:06,604 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token'
Request method: 'POST'
Request headers:
    'Accept': 'application/json'
    'x-client-sku': 'REDACTED'
    'x-client-ver': 'REDACTED'
    'x-client-os': 'REDACTED'
    'x-ms-lib-capability': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-client-current-telemetry': 'REDACTED'
    'x-client-last-telemetry': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-19 17:24:06,639 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2025-06-19 17:24:06,639 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'max-age=86400, private'
    'Content-Type': 'application/json; charset=utf-8'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Access-Control-Allow-Origin': 'REDACTED'
    'Access-Control-Allow-Methods': 'REDACTED'
    'P3P': 'REDACTED'
    'x-ms-request-id': '41b9d579-be2f-4be5-b590-31c472ac0200'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 19 Jun 2025 17:24:06 GMT'
    'Content-Length': '1753'
2025-06-19 17:24:06,640 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/kerberos', 'tenant_region_scope': 'EU', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-19 17:24:06,641 - msal.application - DEBUG - Broker enabled? None
2025-06-19 17:24:06,641 - msal.application - DEBUG - Region to be used: None
2025-06-19 17:24:06,643 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 18a9fbaf-277b-4709-addf-bff32dcf7fd6
2025-06-19 17:24:06,643 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token'
Request method: 'POST'
Request headers:
    'Accept': 'application/json'
    'x-client-sku': 'REDACTED'
    'x-client-ver': 'REDACTED'
    'x-client-os': 'REDACTED'
    'x-ms-lib-capability': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-client-current-telemetry': 'REDACTED'
    'x-client-last-telemetry': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-19 17:24:06,692 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token HTTP/1.1" 200 1645
2025-06-19 17:24:06,693 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'P3P': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-ms-request-id': '9dee5b22-7482-45c3-89f3-1e8d22b60000'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-clitelem': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 19 Jun 2025 17:24:06 GMT'
    'Content-Length': '1645'
2025-06-19 17:24:06,693 - msal.token_cache - DEBUG - event={
    "client_id": "bb1ebfc1-47d8-4273-9206-3acc107c1e35",
    "data": {
        "claims": null,
        "scope": [
            "https://management.azure.com/.default"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "client_credentials",
    "params": null,
    "response": {
        "access_token": "********",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "token_type": "Bearer"
    },
    "scope": [
        "https://management.azure.com/.default"
    ],
    "token_endpoint": "https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token"
}
2025-06-19 17:24:06,693 - azure.identity._internal.get_token_mixin - DEBUG - ClientSecretCredential.get_token succeeded
2025-06-19 17:24:06,693 - azure.identity._internal.decorators - DEBUG - EnvironmentCredential.get_token succeeded
2025-06-19 17:24:06,693 - azure.identity._internal.decorators - DEBUG - [Authenticated account] Client ID: bb1ebfc1-47d8-4273-9206-3acc107c1e35. Tenant ID: ee78877a-c63a-405d-85d6-8914358aa533. User Principal Name: unavailableUpn. Object ID (user): 4b27bf23-ad30-46f2-a2f7-c5998d42367a
2025-06-19 17:24:06,693 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-06-19 17:24:06,694 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '290'
    'Accept': 'application/json'
    'x-ms-client-request-id': '34011cf6-4d32-11f0-9a5e-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:06,695 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): management.azure.com:443
2025-06-19 17:24:06,724 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token HTTP/1.1" 200 1647
2025-06-19 17:24:06,725 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'P3P': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-ms-request-id': '2cc30934-9e25-422e-ad7c-ef8ea2de0200'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-clitelem': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 19 Jun 2025 17:24:06 GMT'
    'Content-Length': '1647'
2025-06-19 17:24:06,725 - msal.token_cache - DEBUG - event={
    "client_id": "bb1ebfc1-47d8-4273-9206-3acc107c1e35",
    "data": {
        "claims": null,
        "scope": [
            "https://management.azure.com/.default"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "client_credentials",
    "params": null,
    "response": {
        "access_token": "********",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "token_type": "Bearer"
    },
    "scope": [
        "https://management.azure.com/.default"
    ],
    "token_endpoint": "https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token"
}
2025-06-19 17:24:06,725 - azure.identity._internal.get_token_mixin - DEBUG - ClientSecretCredential.get_token succeeded
2025-06-19 17:24:06,725 - azure.identity._internal.decorators - DEBUG - EnvironmentCredential.get_token succeeded
2025-06-19 17:24:06,726 - azure.identity._internal.decorators - DEBUG - [Authenticated account] Client ID: bb1ebfc1-47d8-4273-9206-3acc107c1e35. Tenant ID: ee78877a-c63a-405d-85d6-8914358aa533. User Principal Name: unavailableUpn. Object ID (user): 4b27bf23-ad30-46f2-a2f7-c5998d42367a
2025-06-19 17:24:06,726 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-06-19 17:24:06,726 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '290'
    'Accept': 'application/json'
    'x-ms-client-request-id': '33fe357c-4d32-11f0-88e9-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:06,727 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): management.azure.com:443
2025-06-19 17:24:06,753 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token HTTP/1.1" 200 1645
2025-06-19 17:24:06,757 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'P3P': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-ms-request-id': '18fa46d0-00a3-4d8d-ac14-51342cab0000'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-clitelem': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 19 Jun 2025 17:24:06 GMT'
    'Content-Length': '1645'
2025-06-19 17:24:06,757 - msal.token_cache - DEBUG - event={
    "client_id": "bb1ebfc1-47d8-4273-9206-3acc107c1e35",
    "data": {
        "claims": null,
        "scope": [
            "https://management.azure.com/.default"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "client_credentials",
    "params": null,
    "response": {
        "access_token": "********",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "token_type": "Bearer"
    },
    "scope": [
        "https://management.azure.com/.default"
    ],
    "token_endpoint": "https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token"
}
2025-06-19 17:24:06,757 - azure.identity._internal.get_token_mixin - DEBUG - ClientSecretCredential.get_token succeeded
2025-06-19 17:24:06,757 - azure.identity._internal.decorators - DEBUG - EnvironmentCredential.get_token succeeded
2025-06-19 17:24:06,758 - azure.identity._internal.decorators - DEBUG - [Authenticated account] Client ID: bb1ebfc1-47d8-4273-9206-3acc107c1e35. Tenant ID: ee78877a-c63a-405d-85d6-8914358aa533. User Principal Name: unavailableUpn. Object ID (user): 4b27bf23-ad30-46f2-a2f7-c5998d42367a
2025-06-19 17:24:06,758 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-06-19 17:24:06,758 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '290'
    'Accept': 'application/json'
    'x-ms-client-request-id': '33fc7ab6-4d32-11f0-a113-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:06,758 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): management.azure.com:443
2025-06-19 17:24:06,762 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token HTTP/1.1" 200 1645
2025-06-19 17:24:06,762 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'P3P': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-ms-request-id': '32e200d1-546e-40db-85f5-5eb6de290300'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-clitelem': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Thu, 19 Jun 2025 17:24:06 GMT'
    'Content-Length': '1645'
2025-06-19 17:24:06,763 - msal.token_cache - DEBUG - event={
    "client_id": "bb1ebfc1-47d8-4273-9206-3acc107c1e35",
    "data": {
        "claims": null,
        "scope": [
            "https://management.azure.com/.default"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "client_credentials",
    "params": null,
    "response": {
        "access_token": "********",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "token_type": "Bearer"
    },
    "scope": [
        "https://management.azure.com/.default"
    ],
    "token_endpoint": "https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token"
}
2025-06-19 17:24:06,763 - azure.identity._internal.get_token_mixin - DEBUG - ClientSecretCredential.get_token succeeded
2025-06-19 17:24:06,763 - azure.identity._internal.decorators - DEBUG - EnvironmentCredential.get_token succeeded
2025-06-19 17:24:06,763 - azure.identity._internal.decorators - DEBUG - [Authenticated account] Client ID: bb1ebfc1-47d8-4273-9206-3acc107c1e35. Tenant ID: ee78877a-c63a-405d-85d6-8914358aa533. User Principal Name: unavailableUpn. Object ID (user): 4b27bf23-ad30-46f2-a2f7-c5998d42367a
2025-06-19 17:24:06,763 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-06-19 17:24:06,763 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '290'
    'Accept': 'application/json'
    'x-ms-client-request-id': '34065f0e-4d32-11f0-b88c-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:06,764 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): management.azure.com:443
2025-06-19 17:24:08,466 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 12993
2025-06-19 17:24:08,467 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '12993'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'a9ce3758-e9f6-4319-876c-16968a8584bd'
    'x-ms-correlation-id': 'REDACTED'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '34011cf6-4d32-11f0-9a5e-ae6aa769d194'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: FFD1251B94974AAAAB022C7A75541620 Ref B: AMS231022012031 Ref C: 2025-06-19T17:24:06Z'
    'Date': 'Thu, 19 Jun 2025 17:24:08 GMT'
2025-06-19 17:24:08,470 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-19 17:24:08,470 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK cost query successful with 192 rows
2025-06-19 17:24:08,470 - backend.cost_management.cost_management_service - INFO - First row: [3.632860833e-06, 'project-id', '051307-test', 'EUR']
2025-06-19 17:24:08,471 - backend.cost_management.cost_management_service - INFO - Second row: [6.862070461e-06, 'project-id', '497e2a90-20db-4396-b658-9e74fb9e0882', 'EUR']
2025-06-19 17:24:08,471 - backend.cost_management.cost_management_service - INFO - Third row: [1.2916838516e-05, 'project-id', '051209-test', 'EUR']
2025-06-19 17:24:08,471 - backend.cost_management.cost_management_service - INFO - Columns: ['Cost', 'TagKey', 'TagValue', 'Currency']
2025-06-19 17:24:08,471 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:08,471 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:08,471 - backend.cost_management.cost_management_service - DEBUG - Executing dimension cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, dimension_name: ResourceLocation, dimension_value: None
2025-06-19 17:24:08,472 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7fd0>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5bc324a0>}
2025-06-19 17:24:08,472 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:08,472 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:08,473 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '35331a84-4d32-11f0-9a5e-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:08,481 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 12993
2025-06-19 17:24:08,482 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '12993'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': '4213ee8e-2fbd-4bec-9d99-94a59a7d1b14'
    'x-ms-correlation-id': 'REDACTED'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '34065f0e-4d32-11f0-b88c-ae6aa769d194'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: B6FB4B6F5279456BB19F799021C3BB20 Ref B: AMS231022012011 Ref C: 2025-06-19T17:24:06Z'
    'Date': 'Thu, 19 Jun 2025 17:24:08 GMT'
2025-06-19 17:24:08,484 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-19 17:24:08,484 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK cost query successful with 192 rows
2025-06-19 17:24:08,485 - backend.cost_management.cost_management_service - INFO - First row: [3.632860833e-06, 'project-id', '051307-test', 'EUR']
2025-06-19 17:24:08,485 - backend.cost_management.cost_management_service - INFO - Second row: [6.862070461e-06, 'project-id', '497e2a90-20db-4396-b658-9e74fb9e0882', 'EUR']
2025-06-19 17:24:08,485 - backend.cost_management.cost_management_service - INFO - Third row: [1.2916838516e-05, 'project-id', '051209-test', 'EUR']
2025-06-19 17:24:08,485 - backend.cost_management.cost_management_service - INFO - Columns: ['Cost', 'TagKey', 'TagValue', 'Currency']
2025-06-19 17:24:08,485 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:08,485 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:08,485 - backend.cost_management.cost_management_service - DEBUG - Executing dimension cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, dimension_name: ResourceLocation, dimension_value: None
2025-06-19 17:24:08,485 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bc31900>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5bc31f30>}
2025-06-19 17:24:08,486 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:08,486 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:08,486 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '35352db0-4d32-11f0-b88c-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:08,518 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 12993
2025-06-19 17:24:08,519 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '12993'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': '44b10fdb-8585-4d47-858e-b86c0a18d41d'
    'x-ms-correlation-id': 'REDACTED'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '33fe357c-4d32-11f0-88e9-ae6aa769d194'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 120C1A17AE0D4EDDA40FB6ECFE49EFB2 Ref B: AMS231032607021 Ref C: 2025-06-19T17:24:06Z'
    'Date': 'Thu, 19 Jun 2025 17:24:08 GMT'
2025-06-19 17:24:08,522 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-19 17:24:08,522 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK cost query successful with 192 rows
2025-06-19 17:24:08,522 - backend.cost_management.cost_management_service - INFO - First row: [3.632860833e-06, 'project-id', '051307-test', 'EUR']
2025-06-19 17:24:08,522 - backend.cost_management.cost_management_service - INFO - Second row: [6.862070461e-06, 'project-id', '497e2a90-20db-4396-b658-9e74fb9e0882', 'EUR']
2025-06-19 17:24:08,522 - backend.cost_management.cost_management_service - INFO - Third row: [1.2916838516e-05, 'project-id', '051209-test', 'EUR']
2025-06-19 17:24:08,522 - backend.cost_management.cost_management_service - INFO - Columns: ['Cost', 'TagKey', 'TagValue', 'Currency']
2025-06-19 17:24:08,523 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:08,523 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:08,523 - backend.cost_management.cost_management_service - DEBUG - Executing dimension cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, dimension_name: ResourceLocation, dimension_value: None
2025-06-19 17:24:08,523 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7fd0>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5bc324a0>}
2025-06-19 17:24:08,523 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:08,523 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:08,524 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '353aea16-4d32-11f0-88e9-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:08,691 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 12993
2025-06-19 17:24:08,693 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '12993'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'e179cf06-6893-4b3c-ba6b-5446b1db3983'
    'x-ms-correlation-id': 'REDACTED'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '33fc7ab6-4d32-11f0-a113-ae6aa769d194'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 6FE09BFEBDFC457EBF70A9FDFE275544 Ref B: AMS231020614017 Ref C: 2025-06-19T17:24:06Z'
    'Date': 'Thu, 19 Jun 2025 17:24:08 GMT'
2025-06-19 17:24:08,697 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-19 17:24:08,698 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK cost query successful with 192 rows
2025-06-19 17:24:08,698 - backend.cost_management.cost_management_service - INFO - First row: [3.632860833e-06, 'project-id', '051307-test', 'EUR']
2025-06-19 17:24:08,698 - backend.cost_management.cost_management_service - INFO - Second row: [6.862070461e-06, 'project-id', '497e2a90-20db-4396-b658-9e74fb9e0882', 'EUR']
2025-06-19 17:24:08,698 - backend.cost_management.cost_management_service - INFO - Third row: [1.2916838516e-05, 'project-id', '051209-test', 'EUR']
2025-06-19 17:24:08,699 - backend.cost_management.cost_management_service - INFO - Columns: ['Cost', 'TagKey', 'TagValue', 'Currency']
2025-06-19 17:24:08,699 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:08,699 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:08,699 - backend.cost_management.cost_management_service - DEBUG - Executing dimension cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, dimension_name: ResourceLocation, dimension_value: None
2025-06-19 17:24:08,699 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7f70>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5bc31ea0>}
2025-06-19 17:24:08,700 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:08,700 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:08,701 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '3555dcb8-4d32-11f0-a113-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:09,071 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:09,073 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '26009f7e-c96c-4a23-947f-e6fec4a83ed2'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 97597C85211E45E98C0C920987620EE5 Ref B: AMS231032607021 Ref C: 2025-06-19T17:24:08Z'
    'Date': 'Thu, 19 Jun 2025 17:24:08 GMT'
2025-06-19 17:24:09,075 - backend.cost_management.cost_management_service - WARNING - Attempt 1/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 1 seconds...
2025-06-19 17:24:09,076 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:24:09,077 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:24:09,712 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:09,714 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'b03fe8c8-753c-4cc4-97aa-bfb9c3539ca8'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 622C0A8DF143442A9A18EB289E84FE0E Ref B: AMS231022012011 Ref C: 2025-06-19T17:24:08Z'
    'Date': 'Thu, 19 Jun 2025 17:24:09 GMT'
2025-06-19 17:24:09,715 - backend.cost_management.cost_management_service - WARNING - Attempt 1/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 1 seconds...
2025-06-19 17:24:09,716 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:24:09,716 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:24:10,009 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:10,011 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '4d76c452-1b02-4bf8-a159-6d69d733e991'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: A1383442BB794E75A8CE84E142075515 Ref B: AMS231020614017 Ref C: 2025-06-19T17:24:08Z'
    'Date': 'Thu, 19 Jun 2025 17:24:09 GMT'
2025-06-19 17:24:10,012 - backend.cost_management.cost_management_service - WARNING - Attempt 1/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 1 seconds...
2025-06-19 17:24:10,013 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:24:10,013 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:24:10,085 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:10,086 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:10,088 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '362991c0-4d32-11f0-88e9-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:10,717 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:10,718 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:10,720 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '3689e976-4d32-11f0-b88c-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:10,724 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:10,725 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '8aafebd4-61ec-4427-8ca7-e1196488b4ea'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: A6414011822A45BEBBF1897D6E90DE7E Ref B: AMS231022012031 Ref C: 2025-06-19T17:24:08Z'
    'Date': 'Thu, 19 Jun 2025 17:24:10 GMT'
2025-06-19 17:24:10,726 - backend.cost_management.cost_management_service - WARNING - Attempt 1/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 1 seconds...
2025-06-19 17:24:10,726 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:24:10,726 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-19 17:24:10,846 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:10,849 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '5a72ead8-9713-4a22-af2f-764251420ef8'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: BAA1E14E69F0403CA1E13F3EE0B11A39 Ref B: AMS231032607021 Ref C: 2025-06-19T17:24:10Z'
    'Date': 'Thu, 19 Jun 2025 17:24:10 GMT'
2025-06-19 17:24:10,850 - backend.cost_management.cost_management_service - WARNING - Attempt 2/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 2 seconds...
2025-06-19 17:24:11,018 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:11,020 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:11,021 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '36b7f30c-4d32-11f0-a113-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:11,442 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:11,444 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '655d1a1f-aabb-49a6-8446-72294de5f48b'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 8FED0DD042A041268D994C8D32A669AD Ref B: AMS231022012011 Ref C: 2025-06-19T17:24:10Z'
    'Date': 'Thu, 19 Jun 2025 17:24:11 GMT'
2025-06-19 17:24:11,445 - backend.cost_management.cost_management_service - WARNING - Attempt 2/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 2 seconds...
2025-06-19 17:24:11,728 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:11,729 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:11,731 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '37242a68-4d32-11f0-9a5e-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:12,852 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:12,853 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:12,855 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '37cfba40-4d32-11f0-88e9-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:12,876 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:12,878 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'd32c14a6-42f2-4eac-92dd-dac0f89f213c'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 45C88536064043B394A99B15CE9704B7 Ref B: AMS231020614017 Ref C: 2025-06-19T17:24:11Z'
    'Date': 'Thu, 19 Jun 2025 17:24:12 GMT'
2025-06-19 17:24:12,879 - backend.cost_management.cost_management_service - WARNING - Attempt 2/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 2 seconds...
2025-06-19 17:24:12,940 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:12,942 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '3fdde6a6-c38e-47ca-8f34-ca9b086cafae'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 1511B7DF580F43388A36954DF9E2D68F Ref B: AMS231022012031 Ref C: 2025-06-19T17:24:11Z'
    'Date': 'Thu, 19 Jun 2025 17:24:12 GMT'
2025-06-19 17:24:12,943 - backend.cost_management.cost_management_service - WARNING - Attempt 2/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 2 seconds...
2025-06-19 17:24:13,454 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:13,455 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:13,457 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '382ba3a0-4d32-11f0-b88c-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:13,673 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:13,674 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'f597d6c8-8e82-4477-9b87-56c2fd08a99f'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: AB9CABC348C242C09535751D0256A959 Ref B: AMS231032607021 Ref C: 2025-06-19T17:24:12Z'
    'Date': 'Thu, 19 Jun 2025 17:24:13 GMT'
2025-06-19 17:24:13,674 - backend.cost_management.cost_management_service - ERROR - Azure Cost Management query failed: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.
2025-06-19 17:24:13,674 - backend.cost_management.cost_management_service - ERROR - Query details - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, time_range: month, dimension_name: ResourceLocation, dimension_value: None
2025-06-19 17:24:13,674 - backend.cost_management.cost_management_service - DEBUG - Exception type: <class 'azure.core.exceptions.HttpResponseError'>
2025-06-19 17:24:13,675 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:13,675 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:13,675 - backend.cost_management.cost_management_service - DEBUG - Executing dimension cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, dimension_name: ResourceGroupName, dimension_value: None
2025-06-19 17:24:13,675 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7fa0>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5bc31ea0>}
2025-06-19 17:24:13,675 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:13,676 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:13,676 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '384d14b8-4d32-11f0-88e9-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:14,129 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:14,133 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '960e9cda-5cca-406e-973c-80e43524a1c1'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '248'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 7DF596032061469AA38D630E0AD3AAC7 Ref B: AMS231022012011 Ref C: 2025-06-19T17:24:13Z'
    'Date': 'Thu, 19 Jun 2025 17:24:13 GMT'
2025-06-19 17:24:14,134 - backend.cost_management.cost_management_service - ERROR - Azure Cost Management query failed: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.
2025-06-19 17:24:14,135 - backend.cost_management.cost_management_service - ERROR - Query details - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, time_range: month, dimension_name: ResourceLocation, dimension_value: None
2025-06-19 17:24:14,135 - backend.cost_management.cost_management_service - DEBUG - Exception type: <class 'azure.core.exceptions.HttpResponseError'>
2025-06-19 17:24:14,136 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:14,139 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:14,139 - backend.cost_management.cost_management_service - DEBUG - Executing dimension cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, dimension_name: ResourceGroupName, dimension_value: None
2025-06-19 17:24:14,139 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bc31db0>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5bc31900>}
2025-06-19 17:24:14,140 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:14,140 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:14,142 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '38942736-4d32-11f0-b88c-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:14,206 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:14,208 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '3c162f38-e707-40a2-99de-a311b9b2b0bd'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 872C09730FBE44F394DE741A8405D099 Ref B: AMS231032607021 Ref C: 2025-06-19T17:24:13Z'
    'Date': 'Thu, 19 Jun 2025 17:24:14 GMT'
2025-06-19 17:24:14,209 - backend.cost_management.cost_management_service - WARNING - Attempt 1/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 1 seconds...
2025-06-19 17:24:14,890 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:14,891 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:14,894 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '3906c912-4d32-11f0-a113-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:14,946 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:14,947 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:14,949 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '299'
    'Accept': 'application/json'
    'x-ms-client-request-id': '390f4308-4d32-11f0-9a5e-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:15,216 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:15,216 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:15,218 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '393859c8-4d32-11f0-88e9-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:15,700 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:15,701 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '97977b04-d436-47ba-9950-4053b0d6269c'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-retry-after': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: A32F65E34409406D91B98CF9BA7A3BDB Ref B: AMS231020614017 Ref C: 2025-06-19T17:24:14Z'
    'Date': 'Thu, 19 Jun 2025 17:24:15 GMT'
2025-06-19 17:24:15,701 - backend.cost_management.cost_management_service - ERROR - Azure Cost Management query failed: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.
2025-06-19 17:24:15,701 - backend.cost_management.cost_management_service - ERROR - Query details - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, time_range: month, dimension_name: ResourceLocation, dimension_value: None
2025-06-19 17:24:15,701 - backend.cost_management.cost_management_service - DEBUG - Exception type: <class 'azure.core.exceptions.HttpResponseError'>
2025-06-19 17:24:15,702 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:15,702 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:15,702 - backend.cost_management.cost_management_service - DEBUG - Executing dimension cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, dimension_name: ResourceGroupName, dimension_value: None
2025-06-19 17:24:15,702 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7f10>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5bd9e740>}
2025-06-19 17:24:15,702 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:15,702 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:15,703 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '39825b5e-4d32-11f0-a113-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:15,903 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:15,906 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '421b697f-7a21-4822-9a9a-1977dcd42542'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-retry-after': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 824CD8363AF04DEAB7E9E0C4260465AD Ref B: AMS231032607021 Ref C: 2025-06-19T17:24:15Z'
    'Date': 'Thu, 19 Jun 2025 17:24:15 GMT'
2025-06-19 17:24:15,907 - backend.cost_management.cost_management_service - WARNING - Attempt 2/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 2 seconds...
2025-06-19 17:24:16,106 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:16,108 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '395d1845-6fdb-4dfe-a2d4-bdffd0992b55'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 0F37AA1CC7834B1BA6790086293808F1 Ref B: AMS231022012031 Ref C: 2025-06-19T17:24:14Z'
    'Date': 'Thu, 19 Jun 2025 17:24:16 GMT'
2025-06-19 17:24:16,109 - backend.cost_management.cost_management_service - ERROR - Azure Cost Management query failed: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.
2025-06-19 17:24:16,109 - backend.cost_management.cost_management_service - ERROR - Query details - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, time_range: month, dimension_name: ResourceLocation, dimension_value: None
2025-06-19 17:24:16,110 - backend.cost_management.cost_management_service - DEBUG - Exception type: <class 'azure.core.exceptions.HttpResponseError'>
2025-06-19 17:24:16,110 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-19 17:24:16,110 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-19 17:24:16,111 - backend.cost_management.cost_management_service - DEBUG - Executing dimension cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, dimension_name: ResourceGroupName, dimension_value: None
2025-06-19 17:24:16,111 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5bdf7fa0>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5bc31ea0>}
2025-06-19 17:24:16,112 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:16,112 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:16,113 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '39c0eacc-4d32-11f0-9a5e-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:16,164 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:16,165 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'f4b4b2d7-9b73-45a7-a488-3262bb60a4e6'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: DD9ECEEC4E71487AA861BCD90D40AB77 Ref B: AMS231020614017 Ref C: 2025-06-19T17:24:15Z'
    'Date': 'Thu, 19 Jun 2025 17:24:15 GMT'
2025-06-19 17:24:16,166 - backend.cost_management.cost_management_service - WARNING - Attempt 1/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 1 seconds...
2025-06-19 17:24:16,214 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:16,216 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'cca71a00-9d58-4615-b94f-ffd1ee12fd0d'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-retry-after': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: F71578F5D51E42D2A53EDC75638247C9 Ref B: AMS231022012011 Ref C: 2025-06-19T17:24:14Z'
    'Date': 'Thu, 19 Jun 2025 17:24:16 GMT'
2025-06-19 17:24:16,217 - backend.cost_management.cost_management_service - WARNING - Attempt 1/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 1 seconds...
2025-06-19 17:24:16,864 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:16,866 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '62c618ca-5aa9-4464-992a-4fd42a3f19a9'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-tenant-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 45C36BF554874087999FC9829DC0A188 Ref B: AMS231022012031 Ref C: 2025-06-19T17:24:16Z'
    'Date': 'Thu, 19 Jun 2025 17:24:16 GMT'
2025-06-19 17:24:16,867 - backend.cost_management.cost_management_service - WARNING - Attempt 1/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 1 seconds...
2025-06-19 17:24:17,170 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:17,171 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:17,173 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '3a62a5ce-4d32-11f0-a113-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:17,224 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:17,225 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:17,226 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '3a6ab930-4d32-11f0-b88c-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:17,872 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:17,873 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:17,875 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '3acdc2c8-4d32-11f0-9a5e-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:17,911 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:17,911 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:17,912 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '300'
    'Accept': 'application/json'
    'x-ms-client-request-id': '3ad35ea4-4d32-11f0-88e9-ae6aa769d194'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-19 17:24:17,987 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:17,987 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'c0cf79f8-696b-44b5-921f-3ac73291af59'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-tenant-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 9BA25CD949A84D259E1D0ABF2B104CC4 Ref B: AMS231022012011 Ref C: 2025-06-19T17:24:17Z'
    'Date': 'Thu, 19 Jun 2025 17:24:17 GMT'
2025-06-19 17:24:17,988 - backend.cost_management.cost_management_service - WARNING - Attempt 2/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 2 seconds...
2025-06-19 17:24:18,186 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:18,186 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '2c41111a-71d8-4f66-b5a0-546398984506'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-tenant-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 6D0518B1DCD54460B368803C24753BA5 Ref B: AMS231022012031 Ref C: 2025-06-19T17:24:17Z'
    'Date': 'Thu, 19 Jun 2025 17:24:18 GMT'
2025-06-19 17:24:18,186 - backend.cost_management.cost_management_service - WARNING - Attempt 2/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 2 seconds...
2025-06-19 17:24:18,714 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:18,714 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 429 69
2025-06-19 17:24:18,716 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'b27cf506-7dc5-41dd-aaaa-e3d9c60dbf40'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-tenant-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 22F8DD4216664BF7B771A41E03BF1EFE Ref B: AMS231020614017 Ref C: 2025-06-19T17:24:17Z'
    'Date': 'Thu, 19 Jun 2025 17:24:18 GMT'
2025-06-19 17:24:18,716 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 429
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '69'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'session-id': 'REDACTED'
    'x-ms-request-id': '5f7dbe4a-d05b-4fb2-aabe-0ae544039427'
    'x-ms-ratelimit-microsoft.costmanagement-entity-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-entity-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-tenant-retry-after': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-tenant-requests': 'REDACTED'
    'x-ms-ratelimit-remaining-microsoft.costmanagement-clienttype-requests': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-consumed': 'REDACTED'
    'x-ms-ratelimit-microsoft.costmanagement-qpu-remaining': 'REDACTED'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 0BFD4E5F5FE84D9BA953F7ACF74AC0C0 Ref B: AMS231032607021 Ref C: 2025-06-19T17:24:17Z'
    'Date': 'Thu, 19 Jun 2025 17:24:18 GMT'
2025-06-19 17:24:18,717 - backend.cost_management.cost_management_service - WARNING - Attempt 2/3 failed due to rate limit: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.. Retrying in 2 seconds...
2025-06-19 17:24:18,718 - backend.cost_management.cost_management_service - ERROR - Azure Cost Management query failed: (429) Too many requests. Please retry.
Code: 429
Message: Too many requests. Please retry.
2025-06-19 17:24:18,718 - backend.cost_management.cost_management_service - ERROR - Query details - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, time_range: month, dimension_name: ResourceGroupName, dimension_value: None
2025-06-19 17:24:18,718 - backend.cost_management.cost_management_service - DEBUG - Exception type: <class 'azure.core.exceptions.HttpResponseError'>
2025-06-19 17:24:18,725 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/051307-test/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-19 17:24:18,811 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3206'
    'Date': 'Thu, 19 Jun 2025 17:24:18 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-19 17:24:18,813 - backend.cost_management.cost_collection_task - DEBUG - Could not fetch project 051307-test details: (NotFound) Entity with the specified id does not exist in the system. More info: https://aka.ms/cosmosdb-tsg-not-found, {"Summary":{"DirectCalls":{"(404, 0)":1}},"name":"HandleDocumentRequest","start datetime":"2025-06-19T17:24:18.799Z","duration in milliseconds":22.0012,"data":{"Client Side Request Stats":{"Id":"AggregatedClientSideRequestStatistics","ContactedReplicas":[{"Count":1,"Uri":"rntbd://cdb-ms-prod-westeurope1-be26.documents.azure.com:14414/apps/1ea7ed80-b17c-47b6-a4ed-7d7a744e4747/services/2ce41c22-593e-4a62-9874-c041202ff51a/partitions/76001287-7d11-43b6-bbeb-342d798e8bc1/replicas/133931202112972640s/"}],"RegionsContacted":[],"FailedReplicas":[],"AddressResolutionStatistics":[],"StoreResponseStatistics":[{"ResponseTimeUTC":"2025-06-19T17:24:18.8209605Z","DurationInMs":21.8144,"ResourceType":"Document","OperationType":"Read","LocationEndpoint":"https://internal-ai-conversation-history-db-westeurope.sql.cosmos.azure.com/","StoreResult":{"ActivityId":"ba543db7-9146-4d1a-b1c8-a1405e13ccaf","StatusCode":"NotFound","SubStatusCode":"Unknown","LSN":2140,"PartitionKeyRangeId":"0","GlobalCommittedLSN":2140,"ItemLSN":-1,"UsingLocalLSN":true,"QuorumAckedLSN":-1,"SessionToken":"-1#2140","CurrentWriteQuorum":-1,"CurrentReplicaSetSize":-1,"NumberOfReadRegions":0,"IsValid":true,"StorePhysicalAddress":"rntbd://cdb-ms-prod-westeurope1-be26.documents.azure.com:14414/apps/1ea7ed80-b17c-47b6-a4ed-7d7a744e4747/services/2ce41c22-593e-4a62-9874-c041202ff51a/partitions/76001287-7d11-43b6-bbeb-342d798e8bc1/replicas/133931202112972640s/","RequestCharge":1,"RetryAfterInMs":null,"BELatencyInMs":"20.343","ReplicaHealthStatuses":["(port: 14407 | status: Unknown | lkt: 6/19/2025 4:29:04 PM)","(port: 14069 | status: Unknown | lkt: 6/19/2025 4:29:04 PM)","(port: 14303 | status: Unknown | lkt: 6/19/2025 4:29:04 PM)","(port: 14414 | status: Unknown | lkt: 6/19/2025 4:29:04 PM)"],"transportRequestTimeline":{"requestTimeline":[{"event": "Created", "startTimeUtc": "2025-06-19T17:24:18.7990707Z", "durationInMs": 0.0114},{"event": "ChannelAcquisitionStarted", "startTimeUtc": "2025-06-19T17:24:18.7990821Z", "durationInMs": 0.0089},{"event": "Pipelined", "startTimeUtc": "2025-06-19T17:24:18.7990910Z", "durationInMs": 0.0501},{"event": "Transit Time", "startTimeUtc": "2025-06-19T17:24:18.7991411Z", "durationInMs": 21.7735},{"event": "Received", "startTimeUtc": "2025-06-19T17:24:18.8209146Z", "durationInMs": 0.0274},{"event": "Completed", "startTimeUtc": "2025-06-19T17:24:18.8209420Z", "durationInMs": 0}],"serviceEndpointStats":{"inflightRequests":1,"openConnections":1},"connectionStats":{"waitforConnectionInit":"False","callsPendingReceive":0,"lastSendAttempt":"2025-06-19T17:15:21.6594936Z","lastSend":"2025-06-19T17:15:21.6595284Z","lastReceive":"2025-06-19T17:15:21.6620559Z"},"requestSizeInBytes":535,"responseMetadataSizeInBytes":149,"responseBodySizeInBytes":84},"TransportException":null}}]}}}, Windows/10.0.20348 cosmos-netstandard-sdk/3.18.0
Code: NotFound
Message: Entity with the specified id does not exist in the system. More info: https://aka.ms/cosmosdb-tsg-not-found, {"Summary":{"DirectCalls":{"(404, 0)":1}},"name":"HandleDocumentRequest","start datetime":"2025-06-19T17:24:18.799Z","duration in milliseconds":22.0012,"data":{"Client Side Request Stats":{"Id":"AggregatedClientSideRequestStatistics","ContactedReplicas":[{"Count":1,"Uri":"rntbd://cdb-ms-prod-westeurope1-be26.documents.azure.com:14414/apps/1ea7ed80-b17c-47b6-a4ed-7d7a744e4747/services/2ce41c22-593e-4a62-9874-c041202ff51a/partitions/76001287-7d11-43b6-bbeb-342d798e8bc1/replicas/133931202112972640s/"}],"RegionsContacted":[],"FailedReplicas":[],"AddressResolutionStatistics":[],"StoreResponseStatistics":[{"ResponseTimeUTC":"2025-06-19T17:24:18.8209605Z","DurationInMs":21.8144,"ResourceType":"Document","OperationType":"Read","LocationEndpoint":"https://internal-ai-conversation-history-db-westeurope.sql.cosmos.azure.com/","StoreResult":{"ActivityId":"ba543db7-9146-4d1a-b1c8-a1405e13ccaf","StatusCode":"NotFound","SubStatusCode":"Unknown","LSN":2140,"PartitionKeyRangeId":"0","GlobalCommittedLSN":2140,"ItemLSN":-1,"UsingLocalLSN":true,"QuorumAckedLSN":-1,"SessionToken":"-1#2140","CurrentWriteQuorum":-1,"CurrentReplicaSetSize":-1,"NumberOfReadRegions":0,"IsValid":true,"StorePhysicalAddress":"rntbd://cdb-ms-prod-westeurope1-be26.documents.azure.com:14414/apps/1ea7ed80-b17c-47b6-a4ed-7d7a744e4747/services/2ce41c22-593e-4a62-9874-c041202ff51a/partitions/76001287-7d11-43b6-bbeb-342d798e8bc1/replicas/133931202112972640s/","RequestCharge":1,"RetryAfterInMs":null,"BELatencyInMs":"20.343","ReplicaHealthStatuses":["(port: 14407 | status: Unknown | lkt: 6/19/2025 4:29:04 PM)","(port: 14069 | status: Unknown | lkt: 6/19/2025 4:29:04 PM)","(port: 14303 | status: Unknown | lkt: 6/19/2025 4:29:04 PM)","(port: 14414 | status: Unknown | lkt: 6/19/2025 4:29:04 PM)"],"transportRequestTimeline":{"requestTimeline":[{"event": "Created", "startTimeUtc": "2025-06-19T17:24:18.7990707Z", "durationInMs": 0.0114},{"event": "ChannelAcquisitionStarted", "startTimeUtc": "2025-06-19T17:24:18.7990821Z", "durationInMs": 0.0089},{"event": "Pipelined", "startTimeUtc": "2025-06-19T17:24:18.7990910Z", "durationInMs": 0.0501},{"event": "Transit Time", "startTimeUtc": "2025-06-19T17:24:18.7991411Z", "durationInMs": 21.7735},{"event": "Received", "startTimeUtc": "2025-06-19T17:24:18.8209146Z", "durationInMs": 0.0274},{"event": "Completed", "startTimeUtc": "2025-06-19T17:24:18.8209420Z", "durationInMs": 0}],"serviceEndpointStats":{"inflightRequests":1,"openConnections":1},"connectionStats":{"waitforConnectionInit":"False","callsPendingReceive":0,"lastSendAttempt":"2025-06-19T17:15:21.6594936Z","lastSend":"2025-06-19T17:15:21.6595284Z","lastReceive":"2025-06-19T17:15:21.6620559Z"},"requestSizeInBytes":535,"responseMetadataSizeInBytes":149,"responseBodySizeInBytes":84},"TransportException":null}}]}}}, Windows/10.0.20348 cosmos-netstandard-sdk/3.18.0
2025-06-19 17:24:20,192 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-19 17:24:19,718 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-19 17:24:20,020 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()

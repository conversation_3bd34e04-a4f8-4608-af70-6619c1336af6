2025-06-20 12:15:52,900 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_121552.log
2025-06-20 12:15:52,947 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:15:52,967 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:15:52,994 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:15:52,994 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:15:52,994 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:15:53,188 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:15:52 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:15:53,188 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:15:53,189 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:15:53,190 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:15:53,209 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:15:53,209 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:15:53,209 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:15:53,209 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:15:53,209 - root - INFO - Using DummyCredential for local development
2025-06-20 12:15:53,210 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:15:53,210 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:15:53,210 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:15:53,210 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:15:53,210 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:15:53,210 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:15:53,211 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:15:53,212 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:16:03,213 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:16:03,213 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:16:13,216 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:16:13,217 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:16:23,223 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:16:23,223 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:16:28,874 - backend.deployments.service - INFO - Deployment worker started
2025-06-20 12:16:28,875 - backend.deployments.routes - INFO - Deployment service initialized
2025-06-20 12:16:28,875 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json'}
2025-06-20 12:16:28,876 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:16:28,877 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:16:28,877 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json'}
2025-06-20 12:16:28,878 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:16:28,878 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['Remote-Addr', 'Host', 'User-Agent', 'Accept', 'Content-Type']
2025-06-20 12:16:28,878 - root - WARNING - No authentication token found in request (checked Authorization and Easy Auth headers)
2025-06-20 12:16:28,879 - root - INFO - Development mode: Using mock user due to missing token
2025-06-20 12:16:28,879 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:28,880 - backend.deployments.service - INFO - Deployment queue processor started
2025-06-20 12:16:29,031 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3208'
    'Date': 'Fri, 20 Jun 2025 12:16:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:29,034 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:29,084 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3209'
    'Date': 'Fri, 20 Jun 2025 12:16:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:29,088 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:29,234 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1762'
    'Date': 'Fri, 20 Jun 2025 12:16:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'x-ms-max-media-storage-usage-mb': 'REDACTED'
    'x-ms-media-storage-usage-mb': 'REDACTED'
    'x-ms-databaseaccount-consumed-mb': 'REDACTED'
    'x-ms-databaseaccount-reserved-mb': 'REDACTED'
    'x-ms-databaseaccount-provisioned-mb': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:16:29,237 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '386'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:29,464 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 400
Response headers:
    'Date': 'Fri, 20 Jun 2025 12:16:28 GMT'
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/json'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-substatus': 'REDACTED'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
2025-06-20 12:16:29,468 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/json'
    'x-ms-session-token': 'REDACTED'
    'x-ms-cosmos-is-query-plan-request': 'REDACTED'
    'x-ms-cosmos-supported-query-features': 'REDACTED'
    'x-ms-cosmos-query-version': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '386'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:29,490 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '730'
    'Date': 'Fri, 20 Jun 2025 12:16:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
2025-06-20 12:16:29,491 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/pkranges/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:29,518 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '391'
    'Date': 'Fri, 20 Jun 2025 12:16:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:16:29,520 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:29,882 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2620346'
    'Date': 'Fri, 20 Jun 2025 12:16:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:29,899 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:30,090 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2586284'
    'Date': 'Fri, 20 Jun 2025 12:16:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:30,113 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:30,332 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2554576'
    'Date': 'Fri, 20 Jun 2025 12:16:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:30,415 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:30,615 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2534066'
    'Date': 'Fri, 20 Jun 2025 12:16:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:30,632 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:30,772 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1801428'
    'Date': 'Fri, 20 Jun 2025 12:16:30 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:30,787 - backend.cost_management.cost_api_quart - INFO - Retrieved 474 cost documents from Cosmos DB for time range: month
2025-06-20 12:16:30,787 - backend.cost_management.cost_api_quart - INFO - Using 474 cost documents from Cosmos DB
2025-06-20 12:16:30,805 - backend.cost_management.cost_api_quart - DEBUG - About to call get_project_names_map with 191 project IDs
2025-06-20 12:16:30,806 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '14172'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:30,852 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '113'
    'Date': 'Fri, 20 Jun 2025 12:16:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:30,853 - backend.cost_management.cost_management_service - DEBUG - Retrieved names for 1 out of 191 projects
2025-06-20 12:16:30,853 - backend.cost_management.cost_api_quart - DEBUG - get_project_names_map completed successfully
2025-06-20 12:16:30,853 - backend.cost_management.cost_api_quart - DEBUG - project_costs length: 0, result: None, use_cosmos_data: True
2025-06-20 12:16:30,853 - backend.cost_management.cost_api_quart - INFO - Using mock cost data for development/fallback
2025-06-20 12:16:30,853 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/West%20US%202/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:30,911 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3204'
    'Date': 'Fri, 20 Jun 2025 12:16:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:30,912 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/East%20US/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:30,955 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3202'
    'Date': 'Fri, 20 Jun 2025 12:16:30 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:30,956 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/West%20Europe/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:30,999 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3200'
    'Date': 'Fri, 20 Jun 2025 12:16:30 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:33,224 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:16:33,225 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:16:38,560 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json', 'X-User-Id': 'test-user', 'X-User-Role': 'super_admin'}
2025-06-20 12:16:38,561 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:16:38,561 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:16:38,561 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json', 'X-User-Id': 'test-user', 'X-User-Role': 'super_admin'}
2025-06-20 12:16:38,561 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:16:38,561 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['Remote-Addr', 'Host', 'User-Agent', 'Accept', 'Content-Type', 'X-User-Id', 'X-User-Role']
2025-06-20 12:16:38,561 - root - WARNING - No authentication token found in request (checked Authorization and Easy Auth headers)
2025-06-20 12:16:38,561 - root - INFO - Development mode: Using mock user due to missing token
2025-06-20 12:16:38,562 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:38,612 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3209'
    'Date': 'Fri, 20 Jun 2025 12:16:38 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:38,612 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:38,657 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3208'
    'Date': 'Fri, 20 Jun 2025 12:16:38 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:38,658 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:38,806 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1762'
    'Date': 'Fri, 20 Jun 2025 12:16:38 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'x-ms-max-media-storage-usage-mb': 'REDACTED'
    'x-ms-media-storage-usage-mb': 'REDACTED'
    'x-ms-databaseaccount-consumed-mb': 'REDACTED'
    'x-ms-databaseaccount-reserved-mb': 'REDACTED'
    'x-ms-databaseaccount-provisioned-mb': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:16:38,808 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '386'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:38,906 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 400
Response headers:
    'Date': 'Fri, 20 Jun 2025 12:16:38 GMT'
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/json'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-substatus': 'REDACTED'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
2025-06-20 12:16:38,907 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/json'
    'x-ms-session-token': 'REDACTED'
    'x-ms-cosmos-is-query-plan-request': 'REDACTED'
    'x-ms-cosmos-supported-query-features': 'REDACTED'
    'x-ms-cosmos-query-version': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '386'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:38,927 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '730'
    'Date': 'Fri, 20 Jun 2025 12:16:38 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
2025-06-20 12:16:38,928 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/pkranges/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:38,960 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '391'
    'Date': 'Fri, 20 Jun 2025 12:16:38 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:16:38,962 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:39,329 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2620346'
    'Date': 'Fri, 20 Jun 2025 12:16:38 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:39,349 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:39,572 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2586284'
    'Date': 'Fri, 20 Jun 2025 12:16:39 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:39,589 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:39,828 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2554576'
    'Date': 'Fri, 20 Jun 2025 12:16:39 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:39,917 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:40,164 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2534066'
    'Date': 'Fri, 20 Jun 2025 12:16:39 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:40,183 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:40,359 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1801428'
    'Date': 'Fri, 20 Jun 2025 12:16:40 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:40,368 - backend.cost_management.cost_api_quart - INFO - Retrieved 474 cost documents from Cosmos DB for time range: month
2025-06-20 12:16:40,369 - backend.cost_management.cost_api_quart - INFO - Using 474 cost documents from Cosmos DB
2025-06-20 12:16:40,387 - backend.cost_management.cost_api_quart - DEBUG - About to call get_project_names_map with 191 project IDs
2025-06-20 12:16:40,388 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '14172'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:16:40,447 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '113'
    'Date': 'Fri, 20 Jun 2025 12:16:40 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:40,448 - backend.cost_management.cost_management_service - DEBUG - Retrieved names for 1 out of 191 projects
2025-06-20 12:16:40,448 - backend.cost_management.cost_api_quart - DEBUG - get_project_names_map completed successfully
2025-06-20 12:16:40,448 - backend.cost_management.cost_api_quart - DEBUG - project_costs length: 0, result: None, use_cosmos_data: True
2025-06-20 12:16:40,448 - backend.cost_management.cost_api_quart - INFO - Using mock cost data for development/fallback
2025-06-20 12:16:40,448 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/West%20US%202/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:40,512 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3204'
    'Date': 'Fri, 20 Jun 2025 12:16:40 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:40,513 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/East%20US/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:40,568 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3203'
    'Date': 'Fri, 20 Jun 2025 12:16:40 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:40,571 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/West%20Europe/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:16:40,619 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3203'
    'Date': 'Fri, 20 Jun 2025 12:16:40 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:16:43,230 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:16:43,232 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:16:53,238 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:16:53,239 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:17:03,240 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:17:03,241 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:17:13,244 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:17:13,245 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:17:23,247 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:17:23,247 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:17:33,254 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:17:33,254 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:17:43,256 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:17:43,256 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:17:53,260 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:17:53,262 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:18:03,265 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:18:03,267 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:18:13,269 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:18:13,269 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:18:23,275 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:18:23,276 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:18:33,277 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:18:33,278 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:18:43,283 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:18:43,284 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:18:53,289 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:18:53,290 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:19:03,295 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:19:03,297 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:19:13,300 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:19:13,301 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:19:23,305 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:19:23,306 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:19:33,308 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:19:33,313 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:19:43,314 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:19:43,315 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:19:53,316 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:19:53,317 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:19:59,233 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:19:59,233 - root - INFO - Shutting down WebSocket service
2025-06-20 12:19:59,234 - root - INFO - Closing Cosmos DB client
2025-06-20 12:19:59,234 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:19:59,234 - root - INFO - Closing Storage Management client
2025-06-20 12:19:59,234 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:19:59,234 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:19:59,237 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:19:59,237 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:19:59,489 - root - INFO - Cleanup finished.

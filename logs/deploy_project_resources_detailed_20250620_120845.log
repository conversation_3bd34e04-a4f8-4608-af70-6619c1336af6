2025-06-20 12:08:45,378 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_120845.log
2025-06-20 12:08:45,463 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:08:45,485 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_120845.log
2025-06-20 12:08:45,485 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:08:45,522 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:08:45,522 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:08:45,523 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:08:45,523 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_120845.log
2025-06-20 12:08:45,558 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:08:45,576 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_120845.log
2025-06-20 12:08:45,586 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:08:45,590 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:08:45,613 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:08:45,615 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:08:45,616 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:08:45,616 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:08:45,640 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:08:45,640 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:08:45,640 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:08:45,644 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:08:45,666 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:08:45,690 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:08:45,690 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:08:45,691 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:08:45,699 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:08:45 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:08:45,699 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:08:45,699 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:08:45,700 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:08:45,711 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:08:45,711 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:08:45,711 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:08:45,711 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:08:45,711 - root - INFO - Using DummyCredential for local development
2025-06-20 12:08:45,711 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:08:45,712 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:08:45,712 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:08:45,712 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:08:45,712 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:08:45,712 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:08:45,713 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:45,713 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:45,745 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:08:45 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:08:45,745 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:08:45,746 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:08:45,746 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:08:45,757 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:08:45,758 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:08:45,758 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:08:45,758 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:08:45,758 - root - INFO - Using DummyCredential for local development
2025-06-20 12:08:45,758 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:08:45,758 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:08:45,758 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:08:45,758 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:08:45,758 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:08:45,758 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:08:45,759 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:45,759 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:45,787 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:08:45 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:08:45,787 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:08:45,787 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:08:45,788 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:08:45,800 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:08:45,800 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:08:45,800 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:08:45,800 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:08:45,800 - root - INFO - Using DummyCredential for local development
2025-06-20 12:08:45,800 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:08:45,801 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:08:45,801 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:08:45,801 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:08:45,801 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:08:45,801 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:08:45,802 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:45,802 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:45,816 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:08:45 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:08:45,816 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:08:45,817 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:08:45,817 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:08:45,829 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:08:45,829 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:08:45,829 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:08:45,829 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:08:45,829 - root - INFO - Using DummyCredential for local development
2025-06-20 12:08:45,829 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:08:45,830 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:08:45,830 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:08:45,830 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:08:45,830 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:08:45,830 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:08:45,831 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:45,831 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:55,714 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:55,715 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:55,761 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:55,762 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:55,804 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:55,804 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:08:55,833 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:08:55,834 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:05,722 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:05,722 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:05,763 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:05,764 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:05,805 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:05,806 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:05,835 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:05,836 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:10,384 - backend.deployments.service - INFO - Deployment worker started
2025-06-20 12:09:10,384 - backend.deployments.routes - INFO - Deployment service initialized
2025-06-20 12:09:10,384 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json'}
2025-06-20 12:09:10,384 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:09:10,384 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:09:10,385 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json'}
2025-06-20 12:09:10,385 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:09:10,385 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['Remote-Addr', 'Host', 'User-Agent', 'Accept', 'Content-Type']
2025-06-20 12:09:10,385 - root - WARNING - No authentication token found in request (checked Authorization and Easy Auth headers)
2025-06-20 12:09:10,385 - root - INFO - Development mode: Using mock user due to missing token
2025-06-20 12:09:10,386 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:09:10,386 - backend.deployments.service - INFO - Deployment queue processor started
2025-06-20 12:09:10,594 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3209'
    'Date': 'Fri, 20 Jun 2025 12:09:10 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:09:10,595 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:09:10,641 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3208'
    'Date': 'Fri, 20 Jun 2025 12:09:10 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:09:10,642 - backend.cost_management.cost_api_quart - INFO - TEMPORARY: Skipping Cosmos DB and using mock data for debugging
2025-06-20 12:09:10,642 - backend.cost_management.cost_api_quart - INFO - Querying Azure Cost Management for tag 'project-id' with project_id: None
2025-06-20 12:09:10,642 - backend.cost_management.cost_api_quart - INFO - Time range: month
2025-06-20 12:09:10,642 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-20 12:09:10,642 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-20 12:09:10,642 - backend.cost_management.cost_management_service - DEBUG - Executing cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, tag_name: project-id, tag_value: None
2025-06-20 12:09:10,642 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5b99efb0>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5ba1d600>}
2025-06-20 12:09:10,643 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:09:10,643 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:09:10,643 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533
2025-06-20 12:09:10,644 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:09:10,644 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-20 12:09:10,873 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2025-06-20 12:09:10,874 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'max-age=86400, private'
    'Content-Type': 'application/json; charset=utf-8'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Access-Control-Allow-Origin': 'REDACTED'
    'Access-Control-Allow-Methods': 'REDACTED'
    'P3P': 'REDACTED'
    'x-ms-request-id': '06c28e59-6f51-47e5-90fc-255aaed00c00'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Fri, 20 Jun 2025 12:09:10 GMT'
    'Content-Length': '1753'
2025-06-20 12:09:10,874 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/kerberos', 'tenant_region_scope': 'EU', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-20 12:09:10,874 - msal.application - DEBUG - Broker enabled? None
2025-06-20 12:09:10,874 - msal.application - DEBUG - Region to be used: None
2025-06-20 12:09:10,877 - msal.telemetry - DEBUG - Generate or reuse correlation_id: 7c035449-efc8-48cf-b900-dc041ac2371a
2025-06-20 12:09:10,878 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token'
Request method: 'POST'
Request headers:
    'Accept': 'application/json'
    'x-client-sku': 'REDACTED'
    'x-client-ver': 'REDACTED'
    'x-client-os': 'REDACTED'
    'x-ms-lib-capability': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-client-current-telemetry': 'REDACTED'
    'x-client-last-telemetry': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:09:10,994 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token HTTP/1.1" 200 1645
2025-06-20 12:09:10,995 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'P3P': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-ms-request-id': '687c6a16-a917-42aa-b26d-eaee3ac01300'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-clitelem': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Fri, 20 Jun 2025 12:09:10 GMT'
    'Content-Length': '1645'
2025-06-20 12:09:10,995 - msal.token_cache - DEBUG - event={
    "client_id": "bb1ebfc1-47d8-4273-9206-3acc107c1e35",
    "data": {
        "claims": null,
        "scope": [
            "https://management.azure.com/.default"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "client_credentials",
    "params": null,
    "response": {
        "access_token": "********",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "token_type": "Bearer"
    },
    "scope": [
        "https://management.azure.com/.default"
    ],
    "token_endpoint": "https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token"
}
2025-06-20 12:09:10,995 - azure.identity._internal.get_token_mixin - DEBUG - ClientSecretCredential.get_token succeeded
2025-06-20 12:09:10,996 - azure.identity._internal.decorators - DEBUG - EnvironmentCredential.get_token succeeded
2025-06-20 12:09:10,996 - azure.identity._internal.decorators - DEBUG - [Authenticated account] Client ID: bb1ebfc1-47d8-4273-9206-3acc107c1e35. Tenant ID: ee78877a-c63a-405d-85d6-8914358aa533. User Principal Name: unavailableUpn. Object ID (user): 4b27bf23-ad30-46f2-a2f7-c5998d42367a
2025-06-20 12:09:10,996 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-06-20 12:09:10,996 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '290'
    'Accept': 'application/json'
    'x-ms-client-request-id': '5fa0eea2-4dcf-11f0-b3f3-8205a49bb958'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-20 12:09:10,997 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): management.azure.com:443
2025-06-20 12:09:13,435 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 12993
2025-06-20 12:09:13,436 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '12993'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': 'a58ae9a7-5ff4-4e5a-ae1a-6337dc8823fa'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '5fa0eea2-4dcf-11f0-b3f3-8205a49bb958'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 932443310221422B9F29EBC2EA2D46C5 Ref B: AMS231020614051 Ref C: 2025-06-20T12:09:11Z'
    'Date': 'Fri, 20 Jun 2025 12:09:13 GMT'
2025-06-20 12:09:13,438 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:09:13,438 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK cost query successful with 192 rows
2025-06-20 12:09:13,439 - backend.cost_management.cost_management_service - INFO - First row: [3.632860833e-06, 'project-id', '051307-test', 'EUR']
2025-06-20 12:09:13,439 - backend.cost_management.cost_management_service - INFO - Second row: [6.862070461e-06, 'project-id', '497e2a90-20db-4396-b658-9e74fb9e0882', 'EUR']
2025-06-20 12:09:13,439 - backend.cost_management.cost_management_service - INFO - Third row: [1.2916838516e-05, 'project-id', '051209-test', 'EUR']
2025-06-20 12:09:13,439 - backend.cost_management.cost_management_service - INFO - Columns: ['Cost', 'TagKey', 'TagValue', 'Currency']
2025-06-20 12:09:13,439 - backend.cost_management.cost_api_quart - INFO - Azure Cost Management query completed
2025-06-20 12:09:13,439 - backend.cost_management.cost_api_quart - INFO - Result type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:09:13,439 - backend.cost_management.cost_api_quart - INFO - Result has rows: True
2025-06-20 12:09:13,439 - backend.cost_management.cost_api_quart - INFO - Number of rows: 192
2025-06-20 12:09:13,439 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051307-test with cost 3.632860833e-06 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project 497e2a90-20db-4396-b658-9e74fb9e0882 with cost 6.862070461e-06 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051209-test with cost 1.2916838516e-05 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project 5c4360dc-c3ca-4890-8341-38f360442020 with cost 1.8567955367e-05 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project d1ad36d7-06cb-4b8f-a09f-22ba75412f09 with cost 1.99941e-05 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project 61139a2c-01e1-4247-adad-9b9827de6a2e with cost 2.7044630642e-05 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project 95378200-af9a-42f6-a5c3-dbbf17425a71 with cost 3.2653152961e-05 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project a550b8a4-b650-4328-8cf3-fabf68a3ae3b with cost 3.5478711386e-05 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project dc900a18-d3cc-49c2-ad9c-92323d80ac2f with cost 3.6242203978e-05 EUR
2025-06-20 12:09:13,440 - backend.cost_management.cost_api_quart - DEBUG - Processing project 1e513bc6-f53d-4c22-90c9-0b41b488bbc3 with cost 3.6242203978e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project af148c1c-ea3c-4268-a721-ced1208b5d06 with cost 3.6286013793e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project 7c6594ef-fdc9-404b-8555-c8c2555f0297 with cost 3.6286013793e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project 21eb638f-2ad3-4657-a7ed-30a97b464319 with cost 3.6286013793e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project 2004f25c-7842-45d1-a6ee-71c145feb565 with cost 3.6286013793e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project test-project-dced4203 with cost 4.2340781848e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project e0ca5aad-cfb4-4bd4-9879-73f3c4c63f63 with cost 4.5973642681e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project c0db99cc-5532-46dd-93d5-b5c29f169597 with cost 4.6377293884e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project cbbd46af-406d-4aec-849c-da1c373736e5 with cost 5.4450317957e-05 EUR
2025-06-20 12:09:13,441 - backend.cost_management.cost_api_quart - DEBUG - Processing project 024ad4cc-6e07-4efa-b0de-b313fb46d23e with cost 6.9789063697e-05 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050708-test-v with cost 6.9789063697e-05 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project a6c76055-cbe1-408f-8d59-b78b70e4cf30 with cost 7.01927149e-05 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050628-test with cost 7.6651134159e-05 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051420-test with cost 0.094541677834361 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050714-test with cost 0.094541677834361 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051401-test with cost 0.094545310695194 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050812-test with cost 0.094981688825737 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051308-test with cost 0.095018940381952 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project ada5fcdb-a482-46f8-bf31-c2815837e167 with cost 0.095032429452082 EUR
2025-06-20 12:09:13,442 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051302-test with cost 0.095066831713815 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050707-test with cost 0.095100116062362 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050903-test with cost 0.095110629749634 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project test-deployment-123 with cost 0.095211134979624 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050814-test with cost 0.095224075847237 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050905-test with cost 0.095301336911964 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project a9080f29-a5cf-4f78-acdb-9dc8b96ba56e with cost 0.095456274185813 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051315-test with cost 0.096538978024518 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050906-test with cost 0.097339003844104 EUR
2025-06-20 12:09:13,443 - backend.cost_management.cost_api_quart - DEBUG - Processing project 624998cb-05cb-4c55-bf0e-161310012054 with cost 0.097393010550614 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051417-test with cost 0.189086988529555 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051402-test with cost 0.189086988529555 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050712-test with cost 0.189090621390387 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050809-test with cost 0.189537864212496 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051312-test with cost 0.189634414682227 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project c4531f63-b3fd-4663-b766-93961d3d6ec2 with cost 0.189638762422417 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050705-test with cost 0.189665303114381 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project da4b1905-0adf-4262-bc71-e64b97b2574e with cost 0.189702836366324 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project 283bc414-e86a-435b-be46-eb8e548574ad with cost 0.18971637387206 EUR
2025-06-20 12:09:13,444 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051302-test-fix with cost 0.189719862025027 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050706-test with cost 0.189811409972435 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project 6683bca1-4bc6-4b78-a6b4-28e009478c70 with cost 0.18984401650167 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project ec9e1002-f490-490a-89f3-c9311845ba44 with cost 0.18986100472439 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050711-test with cost 0.189922304717632 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project 29edc487-1c86-44aa-b183-c26c84eab470 with cost 0.190018102156659 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050901-test with cost 0.190028005179321 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project 4b923af3-1bc5-407d-85ed-f6159ffd724c with cost 0.190134729994913 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051212-test with cost 0.190464121638883 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project bf425892-3591-4ab2-a6b8-ca03b5710b4c with cost 0.19077950682205 EUR
2025-06-20 12:09:13,445 - backend.cost_management.cost_api_quart - DEBUG - Processing project a421ffcd-1692-4bc5-bd41-af3966571f8e with cost 0.19080741979343 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051314-test with cost 0.191166926280954 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project 7d5c2604-3770-48d7-a984-9298d85791ec with cost 0.284038044976678 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051211-test with cost 0.284045172979147 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050811-test with cost 0.284131257351464 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project bae2df92-e9ae-4e68-89f9-30139c79c685 with cost 0.284172519901634 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050709-test with cost 0.284189447472426 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project 910537a3-a201-45fb-a6c0-34ef8ce1cb7f with cost 0.28425168345516 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051301-test-fix with cost 0.284316287159968 EUR
2025-06-20 12:09:13,446 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051311-test with cost 0.28435960036981 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050806-test with cost 0.28446766665339 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050704-test with cost 0.284633609593372 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050710-test with cost 0.284726165595058 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051310-test with cost 0.285258360625258 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050808-test with cost 0.28549764197637 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051419-test with cost 0.378181242780773 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050805-test with cost 0.378238922308361 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050708-test with cost 0.378591268122477 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 92f3bea8-f426-42c0-8b24-4ae42bad4a76 with cost 0.37871499314971 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050703-test with cost 0.37872999538769 EUR
2025-06-20 12:09:13,447 - backend.cost_management.cost_api_quart - DEBUG - Processing project a67ef190-933c-4659-ace8-7d2a47729ddf with cost 0.378844455937041 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 77d970b7-81e7-49cd-8348-4c6ccb48fa5e with cost 0.378927480287321 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051301-test with cost 0.379015796919665 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project cb50ff0f-a19a-4d24-a9c1-e736a4450b3c with cost 0.379160955397267 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 795d29bb-fe07-4cc7-8d3a-eb43f1098643 with cost 0.379482774128057 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 8c6b33bf-5e43-4717-bdc1-432bc28726e7 with cost 0.380023808102955 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project cfe84b0b-f916-4b23-b670-6f5844d2e5a7 with cost 0.380947607242618 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051418-test with cost 0.472727360778375 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 52f2db54-4cd1-448d-96e8-5ea383b3e7bc with cost 0.472730589988004 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050701-test with cost 0.472780877535233 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 230629ee-8f4c-4d39-8114-b598b9e3c568 with cost 0.47348847522005 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 7e2f5d4f-cf7b-4869-868f-45e887c7e192 with cost 0.473506636681007 EUR
2025-06-20 12:09:13,448 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050702-test with cost 0.473530791492509 EUR
2025-06-20 12:09:13,449 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050804-test with cost 0.473559227507043 EUR
2025-06-20 12:09:13,449 - backend.cost_management.cost_api_quart - DEBUG - Processing project 232a00e6-1051-4da0-9e35-b93ae6c4407d with cost 0.473917995274125 EUR
2025-06-20 12:09:13,449 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051208-test with cost 0.475239837298787 EUR
2025-06-20 12:09:13,449 - backend.cost_management.cost_api_quart - DEBUG - Processing project a189f24c-6efa-43be-8f7e-3e2a6e2164fd with cost 0.475786885858849 EUR
2025-06-20 12:09:13,449 - backend.cost_management.cost_api_quart - DEBUG - Processing project a1d64f7f-6904-411e-b5d5-480a7089f313 with cost 0.567790681359448 EUR
2025-06-20 12:09:13,449 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050801-test with cost 0.56796994747688 EUR
2025-06-20 12:09:13,449 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050713-test with cost 0.568021441387802 EUR
2025-06-20 12:09:13,449 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050802-test with cost 0.568674608066689 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050627-test with cost 0.629115571464815 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 51429d7d-327c-40d2-aa2b-006a4b846b3c with cost 0.664486450402576 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050624-test with cost 0.943637251711764 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050625-test with cost 0.943637251711764 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050626-test with cost 0.943645587525075 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051405-test with cost 0.945420411204441 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 4fe3c1a3-0ce7-4f21-b64f-b90d42099d3a with cost 0.946099229669264 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051421-test with cost 1.22904705931234 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050715-test with cost 1.41819495657911 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050716-test with cost 1.41884961494116 EUR
2025-06-20 12:09:13,450 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050717-test with cost 1.41896350810573 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project 8433e493-1376-4935-848f-4b984fe1397f with cost 1.51408881699831 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050621-test2 with cost 1.57267980490325 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050622-test with cost 1.57268814071656 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050623-test with cost 1.57268814071656 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050621-test with cost 1.57270320206979 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project e683261b-bfbd-4b4e-8838-3dfd77b3e397 with cost 1.58550194977009 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project 665b6522-2fb0-42a3-8e8e-edcd86ee440c with cost 1.60820237071455 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project eacdb4d9-51ab-4c7b-b653-d95032d94a39 with cost 1.7024949334423 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050813-fix2-test with cost 1.79711179618539 EUR
2025-06-20 12:09:13,451 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050813-fix-test with cost 1.7971225840098 EUR
2025-06-20 12:09:13,452 - backend.cost_management.cost_api_quart - DEBUG - Processing project 253e7429-03b5-49ba-957e-5859aec7aa46 with cost 1.88713613624974 EUR
2025-06-20 12:09:13,452 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050813-test with cost 1.89133333034763 EUR
2025-06-20 12:09:13,452 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050629-test with cost 1.89140778886213 EUR
2025-06-20 12:09:13,452 - backend.cost_management.cost_api_quart - DEBUG - Processing project 21b08f43-5306-49e5-85ba-bad78af72c41 with cost 1.9067787250346 EUR
2025-06-20 12:09:13,453 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051605-test with cost 1.98577046534494 EUR
2025-06-20 12:09:13,453 - backend.cost_management.cost_api_quart - DEBUG - Processing project c0ec1fa8-7b9f-4be5-a733-abd0b398d46b with cost 1.98589317545841 EUR
2025-06-20 12:09:13,453 - backend.cost_management.cost_api_quart - DEBUG - Processing project 81f788f7-bf7a-4d28-9f94-fd880dc45861 with cost 1.98594255235311 EUR
2025-06-20 12:09:13,453 - backend.cost_management.cost_api_quart - DEBUG - Processing project 4458258f-46ad-42f8-9dd6-6f351d4a336c with cost 1.98603726359959 EUR
2025-06-20 12:09:13,453 - backend.cost_management.cost_api_quart - DEBUG - Processing project f4e2fd3a-ce33-4965-87aa-343ac2d77da7 with cost 2.17516218856494 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 0508-test-project-0805 with cost 2.17527681895962 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051601-test with cost 2.17538502512914 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050810-test with cost 2.175887539942 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051316-test with cost 2.17645186266212 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 17485152-ea1c-4385-bfcd-37f78b1146f2 with cost 2.53518990599579 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050612 with cost 2.84105272115707 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 8840f20f-2213-4dc9-a9c4-4062358f8186 with cost 2.8520116689281 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project test-project with cost 3.17002473531209 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 0e4f8dc2-6d71-4931-a4c9-4b5b83b3eb39 with cost 3.87699522832852 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051206-test with cost 4.25543346954284 EUR
2025-06-20 12:09:13,454 - backend.cost_management.cost_api_quart - DEBUG - Processing project 9e4f7374-6872-4507-98b1-c7890103039c with cost 4.2554350721545 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project fecb2f28-06f5-4e86-a084-12f10ea9bb87 with cost 4.42906503029853 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project 6b53514a-1cde-4292-ad48-3e6c5a5cb0c0 with cost 4.43492345026626 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051520-test with cost 4.44454769541689 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project ab55657c-eeca-4e85-b0c9-8722674bdfc8 with cost 4.53859876213635 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051514-test with cost 4.63351404121295 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051521-test with cost 5.01148431690418 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project b70a3ff5-ef8c-4df7-a549-8f307a888c08 with cost 5.99505613943272 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project 5ec6d026-66bb-4f2e-ba37-813dc8677b28 with cost 6.0000679564187 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project a7d2d11e-a66e-4392-9c07-e1646e299c20 with cost 6.00014635712406 EUR
2025-06-20 12:09:13,455 - backend.cost_management.cost_api_quart - DEBUG - Processing project e5747327-8bcb-4029-8900-990205c91fbc with cost 6.05109619031858 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project f21c91e4-6e5c-4731-a982-6e17033af4ba with cost 6.31190938211924 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project bdb36311-c696-4eb3-a25e-f68ec3b8863b with cost 6.31953907992516 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 2e69bae9-d0a8-4de2-89c9-7caf8bdbea0f with cost 6.61246603270672 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project bcc3cc0e-b161-40f6-9127-af6ac14eb148 with cost 6.63053033848656 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 1b726f9e-866f-4bb1-b1a2-23c568368449 with cost 6.63444184079434 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 9c0f944b-ca13-4d70-a0f9-be74a515b44b with cost 6.93966295084879 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 4b981f00-0fbd-4d52-90c0-ef08811724e3 with cost 6.94345069732123 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 296882d9-e444-4c81-ae23-78441de09ddd with cost 6.94654801840838 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 47917ed5-f83f-4bb3-b5a0-028b78717eff with cost 7.24867834300848 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 16cc9b6c-f166-49ee-9568-d4155571bf1a with cost 7.56961044588782 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 84ab5bb3-1cd3-4ac6-b9ef-a59b2a231d53 with cost 7.94249979171117 EUR
2025-06-20 12:09:13,456 - backend.cost_management.cost_api_quart - DEBUG - Processing project 5c4a937a-50b1-4509-8f2e-ed2c0ba57d60 with cost 8.70005983186492 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project e8898ebe-3e81-48a0-8f3f-49f7005af836 with cost 9.46310143633803 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050910-test with cost 10.5890889915101 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050907-test with cost 11.1574300872842 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project projectb with cost 11.3463460459369 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project projecta with cost 11.4406295790336 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050904-test with cost 11.5346433568754 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project 87547c7e-011b-4366-83a2-f09ce43840ac with cost 12.3855557720684 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project 343c7ea8-0ca5-473b-960d-4c3d74614bed with cost 13.0907775531743 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project 8afc7f8f-2aa5-4d9d-ae13-8d307c64b2b4 with cost 14.0873943745648 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project d10589b5-9de3-488d-9bb0-77d08ed94474 with cost 14.2764594277897 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project bb4c80f7-aebd-4a25-94f6-a5dbe7913819 with cost 14.2775316004166 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project ee67a747-b953-44c7-bbb7-cf3c1db37dc2 with cost 14.8438976335764 EUR
2025-06-20 12:09:13,457 - backend.cost_management.cost_api_quart - DEBUG - Processing project fce0facf-1b4b-4973-a5c4-552a26b36f37 with cost 14.8447448906724 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 585837de-b731-42b5-a8b3-8cc855fff198 with cost 14.9431648470284 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 0336582f-072a-46c0-a44b-d177ac2e66fc with cost 15.0763844441692 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050610 with cost 16.8290421225854 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051515-test with cost 27.2366087456432 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051512-test with cost 27.4254167525638 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051510-test with cost 27.5116786636049 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051505-test1 with cost 27.5207988660845 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051505-test with cost 27.6143631421493 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project 2baae374-b419-4c28-bdc6-04879e40b452 with cost 28.1819469975799 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project a70782ac-8957-4e59-b778-b74f86549cad with cost 28.2740613473066 EUR
2025-06-20 12:09:13,458 - backend.cost_management.cost_api_quart - DEBUG - Processing project f529b9df-c309-40be-926d-07212d006bfd with cost 28.5575861396006 EUR
2025-06-20 12:09:13,459 - backend.cost_management.cost_api_quart - DEBUG - Processing project d98606bd-cac7-4920-a9b8-4283bec5df11 with cost 29.4036804591471 EUR
2025-06-20 12:09:13,459 - backend.cost_management.cost_api_quart - DEBUG - Processing project 788a5827-a7ef-4f0a-a8b9-d5e17249ca00 with cost 29.4976735794732 EUR
2025-06-20 12:09:13,459 - backend.cost_management.cost_api_quart - DEBUG - Processing project 64ebae70-2b01-42d0-bb69-bc3efd936bc5 with cost 34.2245965820849 EUR
2025-06-20 12:09:13,459 - backend.cost_management.cost_api_quart - DEBUG - Processing project 35a7db33-b409-4873-b3bc-54e42931bd3d with cost 48.6674302098406 EUR
2025-06-20 12:09:13,459 - backend.cost_management.cost_api_quart - DEBUG - Processing project uk001 with cost 49.2266034028918 EUR
2025-06-20 12:09:13,459 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050813-fix3-test with cost 57.5822573804444 EUR
2025-06-20 12:09:13,459 - backend.cost_management.cost_api_quart - DEBUG - Skipping untagged resource with cost 488.150746417799
2025-06-20 12:09:13,459 - backend.cost_management.cost_api_quart - INFO - Fetching names for 191 projects: ['051307-test', '497e2a90-20db-4396-b658-9e74fb9e0882', '051209-test', '5c4360dc-c3ca-4890-8341-38f360442020', 'd1ad36d7-06cb-4b8f-a09f-22ba75412f09', '61139a2c-01e1-4247-adad-9b9827de6a2e', '95378200-af9a-42f6-a5c3-dbbf17425a71', 'a550b8a4-b650-4328-8cf3-fabf68a3ae3b', 'dc900a18-d3cc-49c2-ad9c-92323d80ac2f', '1e513bc6-f53d-4c22-90c9-0b41b488bbc3', 'af148c1c-ea3c-4268-a721-ced1208b5d06', '7c6594ef-fdc9-404b-8555-c8c2555f0297', '21eb638f-2ad3-4657-a7ed-30a97b464319', '2004f25c-7842-45d1-a6ee-71c145feb565', 'test-project-dced4203', 'e0ca5aad-cfb4-4bd4-9879-73f3c4c63f63', 'c0db99cc-5532-46dd-93d5-b5c29f169597', 'cbbd46af-406d-4aec-849c-da1c373736e5', '024ad4cc-6e07-4efa-b0de-b313fb46d23e', '050708-test-v', 'a6c76055-cbe1-408f-8d59-b78b70e4cf30', '050628-test', '051420-test', '050714-test', '051401-test', '050812-test', '051308-test', 'ada5fcdb-a482-46f8-bf31-c2815837e167', '051302-test', '050707-test', '050903-test', 'test-deployment-123', '050814-test', '050905-test', 'a9080f29-a5cf-4f78-acdb-9dc8b96ba56e', '051315-test', '050906-test', '624998cb-05cb-4c55-bf0e-161310012054', '051417-test', '051402-test', '050712-test', '050809-test', '051312-test', 'c4531f63-b3fd-4663-b766-93961d3d6ec2', '050705-test', 'da4b1905-0adf-4262-bc71-e64b97b2574e', '283bc414-e86a-435b-be46-eb8e548574ad', '051302-test-fix', '050706-test', '6683bca1-4bc6-4b78-a6b4-28e009478c70', 'ec9e1002-f490-490a-89f3-c9311845ba44', '050711-test', '29edc487-1c86-44aa-b183-c26c84eab470', '050901-test', '4b923af3-1bc5-407d-85ed-f6159ffd724c', '051212-test', 'bf425892-3591-4ab2-a6b8-ca03b5710b4c', 'a421ffcd-1692-4bc5-bd41-af3966571f8e', '051314-test', '7d5c2604-3770-48d7-a984-9298d85791ec', '051211-test', '050811-test', 'bae2df92-e9ae-4e68-89f9-30139c79c685', '050709-test', '910537a3-a201-45fb-a6c0-34ef8ce1cb7f', '051301-test-fix', '051311-test', '050806-test', '050704-test', '050710-test', '051310-test', '050808-test', '051419-test', '050805-test', '050708-test', '92f3bea8-f426-42c0-8b24-4ae42bad4a76', '050703-test', 'a67ef190-933c-4659-ace8-7d2a47729ddf', '77d970b7-81e7-49cd-8348-4c6ccb48fa5e', '051301-test', 'cb50ff0f-a19a-4d24-a9c1-e736a4450b3c', '795d29bb-fe07-4cc7-8d3a-eb43f1098643', '8c6b33bf-5e43-4717-bdc1-432bc28726e7', 'cfe84b0b-f916-4b23-b670-6f5844d2e5a7', '051418-test', '52f2db54-4cd1-448d-96e8-5ea383b3e7bc', '050701-test', '230629ee-8f4c-4d39-8114-b598b9e3c568', '7e2f5d4f-cf7b-4869-868f-45e887c7e192', '050702-test', '050804-test', '232a00e6-1051-4da0-9e35-b93ae6c4407d', '051208-test', 'a189f24c-6efa-43be-8f7e-3e2a6e2164fd', 'a1d64f7f-6904-411e-b5d5-480a7089f313', '050801-test', '050713-test', '050802-test', '050627-test', '51429d7d-327c-40d2-aa2b-006a4b846b3c', '050624-test', '050625-test', '050626-test', '051405-test', '4fe3c1a3-0ce7-4f21-b64f-b90d42099d3a', '051421-test', '050715-test', '050716-test', '050717-test', '8433e493-1376-4935-848f-4b984fe1397f', '050621-test2', '050622-test', '050623-test', '050621-test', 'e683261b-bfbd-4b4e-8838-3dfd77b3e397', '665b6522-2fb0-42a3-8e8e-edcd86ee440c', 'eacdb4d9-51ab-4c7b-b653-d95032d94a39', '050813-fix2-test', '050813-fix-test', '253e7429-03b5-49ba-957e-5859aec7aa46', '050813-test', '050629-test', '21b08f43-5306-49e5-85ba-bad78af72c41', '051605-test', 'c0ec1fa8-7b9f-4be5-a733-abd0b398d46b', '81f788f7-bf7a-4d28-9f94-fd880dc45861', '4458258f-46ad-42f8-9dd6-6f351d4a336c', 'f4e2fd3a-ce33-4965-87aa-343ac2d77da7', '0508-test-project-0805', '051601-test', '050810-test', '051316-test', '17485152-ea1c-4385-bfcd-37f78b1146f2', '050612', '8840f20f-2213-4dc9-a9c4-4062358f8186', 'test-project', '0e4f8dc2-6d71-4931-a4c9-4b5b83b3eb39', '051206-test', '9e4f7374-6872-4507-98b1-c7890103039c', 'fecb2f28-06f5-4e86-a084-12f10ea9bb87', '6b53514a-1cde-4292-ad48-3e6c5a5cb0c0', '051520-test', 'ab55657c-eeca-4e85-b0c9-8722674bdfc8', '051514-test', '051521-test', 'b70a3ff5-ef8c-4df7-a549-8f307a888c08', '5ec6d026-66bb-4f2e-ba37-813dc8677b28', 'a7d2d11e-a66e-4392-9c07-e1646e299c20', 'e5747327-8bcb-4029-8900-990205c91fbc', 'f21c91e4-6e5c-4731-a982-6e17033af4ba', 'bdb36311-c696-4eb3-a25e-f68ec3b8863b', '2e69bae9-d0a8-4de2-89c9-7caf8bdbea0f', 'bcc3cc0e-b161-40f6-9127-af6ac14eb148', '1b726f9e-866f-4bb1-b1a2-23c568368449', '9c0f944b-ca13-4d70-a0f9-be74a515b44b', '4b981f00-0fbd-4d52-90c0-ef08811724e3', '296882d9-e444-4c81-ae23-78441de09ddd', '47917ed5-f83f-4bb3-b5a0-028b78717eff', '16cc9b6c-f166-49ee-9568-d4155571bf1a', '84ab5bb3-1cd3-4ac6-b9ef-a59b2a231d53', '5c4a937a-50b1-4509-8f2e-ed2c0ba57d60', 'e8898ebe-3e81-48a0-8f3f-49f7005af836', '050910-test', '050907-test', 'projectb', 'projecta', '050904-test', '87547c7e-011b-4366-83a2-f09ce43840ac', '343c7ea8-0ca5-473b-960d-4c3d74614bed', '8afc7f8f-2aa5-4d9d-ae13-8d307c64b2b4', 'd10589b5-9de3-488d-9bb0-77d08ed94474', 'bb4c80f7-aebd-4a25-94f6-a5dbe7913819', 'ee67a747-b953-44c7-bbb7-cf3c1db37dc2', 'fce0facf-1b4b-4973-a5c4-552a26b36f37', '585837de-b731-42b5-a8b3-8cc855fff198', '0336582f-072a-46c0-a44b-d177ac2e66fc', '050610', '051515-test', '051512-test', '051510-test', '051505-test1', '051505-test', '2baae374-b419-4c28-bdc6-04879e40b452', 'a70782ac-8957-4e59-b778-b74f86549cad', 'f529b9df-c309-40be-926d-07212d006bfd', 'd98606bd-cac7-4920-a9b8-4283bec5df11', '788a5827-a7ef-4f0a-a8b9-d5e17249ca00', '64ebae70-2b01-42d0-bb69-bc3efd936bc5', '35a7db33-b409-4873-b3bc-54e42931bd3d', 'uk001', '050813-fix3-test']
2025-06-20 12:09:13,460 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '14172'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:09:13,507 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '113'
    'Date': 'Fri, 20 Jun 2025 12:09:13 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:09:13,508 - backend.cost_management.cost_management_service - DEBUG - Retrieved names for 1 out of 191 projects
2025-06-20 12:09:13,508 - backend.cost_management.cost_api_quart - INFO - Retrieved project names: {'050813-fix3-test': 'Priority_Plot_testing_Project'}
2025-06-20 12:09:13,508 - backend.cost_management.cost_api_quart - DEBUG - project_costs length: 191, result: {'additional_properties': {}, 'id': 'subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourcegroups/rg-internal-ai/providers/Microsoft.CostManagement/query/f79ead8f-233f-4a77-8e7d-453fec146f50', 'name': 'f79ead8f-233f-4a77-8e7d-453fec146f50', 'type': 'Microsoft.CostManagement/query', 'location': None, 'sku': None, 'e_tag': None, 'tags': None, 'next_link': None, 'columns': [<azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff5ba1e5f0>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff5ba1d0c0>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff5ba1cac0>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff5ba1cf10>], 'rows': [[3.632860833e-06, 'project-id', '051307-test', 'EUR'], [6.862070461e-06, 'project-id', '497e2a90-20db-4396-b658-9e74fb9e0882', 'EUR'], [1.2916838516e-05, 'project-id', '051209-test', 'EUR'], [1.8567955367e-05, 'project-id', '5c4360dc-c3ca-4890-8341-38f360442020', 'EUR'], [1.99941e-05, 'project-id', 'd1ad36d7-06cb-4b8f-a09f-22ba75412f09', 'EUR'], [2.7044630642e-05, 'project-id', '61139a2c-01e1-4247-adad-9b9827de6a2e', 'EUR'], [3.2653152961e-05, 'project-id', '95378200-af9a-42f6-a5c3-dbbf17425a71', 'EUR'], [3.5478711386e-05, 'project-id', 'a550b8a4-b650-4328-8cf3-fabf68a3ae3b', 'EUR'], [3.6242203978e-05, 'project-id', 'dc900a18-d3cc-49c2-ad9c-92323d80ac2f', 'EUR'], [3.6242203978e-05, 'project-id', '1e513bc6-f53d-4c22-90c9-0b41b488bbc3', 'EUR'], [3.6286013793e-05, 'project-id', 'af148c1c-ea3c-4268-a721-ced1208b5d06', 'EUR'], [3.6286013793e-05, 'project-id', '7c6594ef-fdc9-404b-8555-c8c2555f0297', 'EUR'], [3.6286013793e-05, 'project-id', '21eb638f-2ad3-4657-a7ed-30a97b464319', 'EUR'], [3.6286013793e-05, 'project-id', '2004f25c-7842-45d1-a6ee-71c145feb565', 'EUR'], [4.2340781848e-05, 'project-id', 'test-project-dced4203', 'EUR'], [4.5973642681e-05, 'project-id', 'e0ca5aad-cfb4-4bd4-9879-73f3c4c63f63', 'EUR'], [4.6377293884e-05, 'project-id', 'c0db99cc-5532-46dd-93d5-b5c29f169597', 'EUR'], [5.4450317957e-05, 'project-id', 'cbbd46af-406d-4aec-849c-da1c373736e5', 'EUR'], [6.9789063697e-05, 'project-id', '024ad4cc-6e07-4efa-b0de-b313fb46d23e', 'EUR'], [6.9789063697e-05, 'project-id', '050708-test-v', 'EUR'], [7.01927149e-05, 'project-id', 'a6c76055-cbe1-408f-8d59-b78b70e4cf30', 'EUR'], [7.6651134159e-05, 'project-id', '050628-test', 'EUR'], [0.094541677834361, 'project-id', '051420-test', 'EUR'], [0.094541677834361, 'project-id', '050714-test', 'EUR'], [0.094545310695194, 'project-id', '051401-test', 'EUR'], [0.094981688825737, 'project-id', '050812-test', 'EUR'], [0.095018940381952, 'project-id', '051308-test', 'EUR'], [0.095032429452082, 'project-id', 'ada5fcdb-a482-46f8-bf31-c2815837e167', 'EUR'], [0.095066831713815, 'project-id', '051302-test', 'EUR'], [0.095100116062362, 'project-id', '050707-test', 'EUR'], [0.095110629749634, 'project-id', '050903-test', 'EUR'], [0.095211134979624, 'project-id', 'test-deployment-123', 'EUR'], [0.095224075847237, 'project-id', '050814-test', 'EUR'], [0.095301336911964, 'project-id', '050905-test', 'EUR'], [0.095456274185813, 'project-id', 'a9080f29-a5cf-4f78-acdb-9dc8b96ba56e', 'EUR'], [0.096538978024518, 'project-id', '051315-test', 'EUR'], [0.097339003844104, 'project-id', '050906-test', 'EUR'], [0.097393010550614, 'project-id', '624998cb-05cb-4c55-bf0e-161310012054', 'EUR'], [0.189086988529555, 'project-id', '051417-test', 'EUR'], [0.189086988529555, 'project-id', '051402-test', 'EUR'], [0.189090621390387, 'project-id', '050712-test', 'EUR'], [0.189537864212496, 'project-id', '050809-test', 'EUR'], [0.189634414682227, 'project-id', '051312-test', 'EUR'], [0.189638762422417, 'project-id', 'c4531f63-b3fd-4663-b766-93961d3d6ec2', 'EUR'], [0.189665303114381, 'project-id', '050705-test', 'EUR'], [0.189702836366324, 'project-id', 'da4b1905-0adf-4262-bc71-e64b97b2574e', 'EUR'], [0.18971637387206, 'project-id', '283bc414-e86a-435b-be46-eb8e548574ad', 'EUR'], [0.189719862025027, 'project-id', '051302-test-fix', 'EUR'], [0.189811409972435, 'project-id', '050706-test', 'EUR'], [0.18984401650167, 'project-id', '6683bca1-4bc6-4b78-a6b4-28e009478c70', 'EUR'], [0.18986100472439, 'project-id', 'ec9e1002-f490-490a-89f3-c9311845ba44', 'EUR'], [0.189922304717632, 'project-id', '050711-test', 'EUR'], [0.190018102156659, 'project-id', '29edc487-1c86-44aa-b183-c26c84eab470', 'EUR'], [0.190028005179321, 'project-id', '050901-test', 'EUR'], [0.190134729994913, 'project-id', '4b923af3-1bc5-407d-85ed-f6159ffd724c', 'EUR'], [0.190464121638883, 'project-id', '051212-test', 'EUR'], [0.19077950682205, 'project-id', 'bf425892-3591-4ab2-a6b8-ca03b5710b4c', 'EUR'], [0.19080741979343, 'project-id', 'a421ffcd-1692-4bc5-bd41-af3966571f8e', 'EUR'], [0.191166926280954, 'project-id', '051314-test', 'EUR'], [0.284038044976678, 'project-id', '7d5c2604-3770-48d7-a984-9298d85791ec', 'EUR'], [0.284045172979147, 'project-id', '051211-test', 'EUR'], [0.284131257351464, 'project-id', '050811-test', 'EUR'], [0.284172519901634, 'project-id', 'bae2df92-e9ae-4e68-89f9-30139c79c685', 'EUR'], [0.284189447472426, 'project-id', '050709-test', 'EUR'], [0.28425168345516, 'project-id', '910537a3-a201-45fb-a6c0-34ef8ce1cb7f', 'EUR'], [0.284316287159968, 'project-id', '051301-test-fix', 'EUR'], [0.28435960036981, 'project-id', '051311-test', 'EUR'], [0.28446766665339, 'project-id', '050806-test', 'EUR'], [0.284633609593372, 'project-id', '050704-test', 'EUR'], [0.284726165595058, 'project-id', '050710-test', 'EUR'], [0.285258360625258, 'project-id', '051310-test', 'EUR'], [0.28549764197637, 'project-id', '050808-test', 'EUR'], [0.378181242780773, 'project-id', '051419-test', 'EUR'], [0.378238922308361, 'project-id', '050805-test', 'EUR'], [0.378591268122477, 'project-id', '050708-test', 'EUR'], [0.37871499314971, 'project-id', '92f3bea8-f426-42c0-8b24-4ae42bad4a76', 'EUR'], [0.37872999538769, 'project-id', '050703-test', 'EUR'], [0.378844455937041, 'project-id', 'a67ef190-933c-4659-ace8-7d2a47729ddf', 'EUR'], [0.378927480287321, 'project-id', '77d970b7-81e7-49cd-8348-4c6ccb48fa5e', 'EUR'], [0.379015796919665, 'project-id', '051301-test', 'EUR'], [0.379160955397267, 'project-id', 'cb50ff0f-a19a-4d24-a9c1-e736a4450b3c', 'EUR'], [0.379482774128057, 'project-id', '795d29bb-fe07-4cc7-8d3a-eb43f1098643', 'EUR'], [0.380023808102955, 'project-id', '8c6b33bf-5e43-4717-bdc1-432bc28726e7', 'EUR'], [0.380947607242618, 'project-id', 'cfe84b0b-f916-4b23-b670-6f5844d2e5a7', 'EUR'], [0.472727360778375, 'project-id', '051418-test', 'EUR'], [0.472730589988004, 'project-id', '52f2db54-4cd1-448d-96e8-5ea383b3e7bc', 'EUR'], [0.472780877535233, 'project-id', '050701-test', 'EUR'], [0.47348847522005, 'project-id', '230629ee-8f4c-4d39-8114-b598b9e3c568', 'EUR'], [0.473506636681007, 'project-id', '7e2f5d4f-cf7b-4869-868f-45e887c7e192', 'EUR'], [0.473530791492509, 'project-id', '050702-test', 'EUR'], [0.473559227507043, 'project-id', '050804-test', 'EUR'], [0.473917995274125, 'project-id', '232a00e6-1051-4da0-9e35-b93ae6c4407d', 'EUR'], [0.475239837298787, 'project-id', '051208-test', 'EUR'], [0.475786885858849, 'project-id', 'a189f24c-6efa-43be-8f7e-3e2a6e2164fd', 'EUR'], [0.567790681359448, 'project-id', 'a1d64f7f-6904-411e-b5d5-480a7089f313', 'EUR'], [0.56796994747688, 'project-id', '050801-test', 'EUR'], [0.568021441387802, 'project-id', '050713-test', 'EUR'], [0.568674608066689, 'project-id', '050802-test', 'EUR'], [0.629115571464815, 'project-id', '050627-test', 'EUR'], [0.664486450402576, 'project-id', '51429d7d-327c-40d2-aa2b-006a4b846b3c', 'EUR'], [0.943637251711764, 'project-id', '050624-test', 'EUR'], [0.943637251711764, 'project-id', '050625-test', 'EUR'], [0.943645587525075, 'project-id', '050626-test', 'EUR'], [0.945420411204441, 'project-id', '051405-test', 'EUR'], [0.946099229669264, 'project-id', '4fe3c1a3-0ce7-4f21-b64f-b90d42099d3a', 'EUR'], [1.22904705931234, 'project-id', '051421-test', 'EUR'], [1.41819495657911, 'project-id', '050715-test', 'EUR'], [1.41884961494116, 'project-id', '050716-test', 'EUR'], [1.41896350810573, 'project-id', '050717-test', 'EUR'], [1.51408881699831, 'project-id', '8433e493-1376-4935-848f-4b984fe1397f', 'EUR'], [1.57267980490325, 'project-id', '050621-test2', 'EUR'], [1.57268814071656, 'project-id', '050622-test', 'EUR'], [1.57268814071656, 'project-id', '050623-test', 'EUR'], [1.57270320206979, 'project-id', '050621-test', 'EUR'], [1.58550194977009, 'project-id', 'e683261b-bfbd-4b4e-8838-3dfd77b3e397', 'EUR'], [1.60820237071455, 'project-id', '665b6522-2fb0-42a3-8e8e-edcd86ee440c', 'EUR'], [1.7024949334423, 'project-id', 'eacdb4d9-51ab-4c7b-b653-d95032d94a39', 'EUR'], [1.79711179618539, 'project-id', '050813-fix2-test', 'EUR'], [1.7971225840098, 'project-id', '050813-fix-test', 'EUR'], [1.88713613624974, 'project-id', '253e7429-03b5-49ba-957e-5859aec7aa46', 'EUR'], [1.89133333034763, 'project-id', '050813-test', 'EUR'], [1.89140778886213, 'project-id', '050629-test', 'EUR'], [1.9067787250346, 'project-id', '21b08f43-5306-49e5-85ba-bad78af72c41', 'EUR'], [1.98577046534494, 'project-id', '051605-test', 'EUR'], [1.98589317545841, 'project-id', 'c0ec1fa8-7b9f-4be5-a733-abd0b398d46b', 'EUR'], [1.98594255235311, 'project-id', '81f788f7-bf7a-4d28-9f94-fd880dc45861', 'EUR'], [1.98603726359959, 'project-id', '4458258f-46ad-42f8-9dd6-6f351d4a336c', 'EUR'], [2.17516218856494, 'project-id', 'f4e2fd3a-ce33-4965-87aa-343ac2d77da7', 'EUR'], [2.17527681895962, 'project-id', '0508-test-project-0805', 'EUR'], [2.17538502512914, 'project-id', '051601-test', 'EUR'], [2.175887539942, 'project-id', '050810-test', 'EUR'], [2.17645186266212, 'project-id', '051316-test', 'EUR'], [2.53518990599579, 'project-id', '17485152-ea1c-4385-bfcd-37f78b1146f2', 'EUR'], [2.84105272115707, 'project-id', '050612', 'EUR'], [2.8520116689281, 'project-id', '8840f20f-2213-4dc9-a9c4-4062358f8186', 'EUR'], [3.17002473531209, 'project-id', 'test-project', 'EUR'], [3.87699522832852, 'project-id', '0e4f8dc2-6d71-4931-a4c9-4b5b83b3eb39', 'EUR'], [4.25543346954284, 'project-id', '051206-test', 'EUR'], [4.2554350721545, 'project-id', '9e4f7374-6872-4507-98b1-c7890103039c', 'EUR'], [4.42906503029853, 'project-id', 'fecb2f28-06f5-4e86-a084-12f10ea9bb87', 'EUR'], [4.43492345026626, 'project-id', '6b53514a-1cde-4292-ad48-3e6c5a5cb0c0', 'EUR'], [4.44454769541689, 'project-id', '051520-test', 'EUR'], [4.53859876213635, 'project-id', 'ab55657c-eeca-4e85-b0c9-8722674bdfc8', 'EUR'], [4.63351404121295, 'project-id', '051514-test', 'EUR'], [5.01148431690418, 'project-id', '051521-test', 'EUR'], [5.99505613943272, 'project-id', 'b70a3ff5-ef8c-4df7-a549-8f307a888c08', 'EUR'], [6.0000679564187, 'project-id', '5ec6d026-66bb-4f2e-ba37-813dc8677b28', 'EUR'], [6.00014635712406, 'project-id', 'a7d2d11e-a66e-4392-9c07-e1646e299c20', 'EUR'], [6.05109619031858, 'project-id', 'e5747327-8bcb-4029-8900-990205c91fbc', 'EUR'], [6.31190938211924, 'project-id', 'f21c91e4-6e5c-4731-a982-6e17033af4ba', 'EUR'], [6.31953907992516, 'project-id', 'bdb36311-c696-4eb3-a25e-f68ec3b8863b', 'EUR'], [6.61246603270672, 'project-id', '2e69bae9-d0a8-4de2-89c9-7caf8bdbea0f', 'EUR'], [6.63053033848656, 'project-id', 'bcc3cc0e-b161-40f6-9127-af6ac14eb148', 'EUR'], [6.63444184079434, 'project-id', '1b726f9e-866f-4bb1-b1a2-23c568368449', 'EUR'], [6.93966295084879, 'project-id', '9c0f944b-ca13-4d70-a0f9-be74a515b44b', 'EUR'], [6.94345069732123, 'project-id', '4b981f00-0fbd-4d52-90c0-ef08811724e3', 'EUR'], [6.94654801840838, 'project-id', '296882d9-e444-4c81-ae23-78441de09ddd', 'EUR'], [7.24867834300848, 'project-id', '47917ed5-f83f-4bb3-b5a0-028b78717eff', 'EUR'], [7.56961044588782, 'project-id', '16cc9b6c-f166-49ee-9568-d4155571bf1a', 'EUR'], [7.94249979171117, 'project-id', '84ab5bb3-1cd3-4ac6-b9ef-a59b2a231d53', 'EUR'], [8.70005983186492, 'project-id', '5c4a937a-50b1-4509-8f2e-ed2c0ba57d60', 'EUR'], [9.46310143633803, 'project-id', 'e8898ebe-3e81-48a0-8f3f-49f7005af836', 'EUR'], [10.5890889915101, 'project-id', '050910-test', 'EUR'], [11.1574300872842, 'project-id', '050907-test', 'EUR'], [11.3463460459369, 'project-id', 'projectb', 'EUR'], [11.4406295790336, 'project-id', 'projecta', 'EUR'], [11.5346433568754, 'project-id', '050904-test', 'EUR'], [12.3855557720684, 'project-id', '87547c7e-011b-4366-83a2-f09ce43840ac', 'EUR'], [13.0907775531743, 'project-id', '343c7ea8-0ca5-473b-960d-4c3d74614bed', 'EUR'], [14.0873943745648, 'project-id', '8afc7f8f-2aa5-4d9d-ae13-8d307c64b2b4', 'EUR'], [14.2764594277897, 'project-id', 'd10589b5-9de3-488d-9bb0-77d08ed94474', 'EUR'], [14.2775316004166, 'project-id', 'bb4c80f7-aebd-4a25-94f6-a5dbe7913819', 'EUR'], [14.8438976335764, 'project-id', 'ee67a747-b953-44c7-bbb7-cf3c1db37dc2', 'EUR'], [14.8447448906724, 'project-id', 'fce0facf-1b4b-4973-a5c4-552a26b36f37', 'EUR'], [14.9431648470284, 'project-id', '585837de-b731-42b5-a8b3-8cc855fff198', 'EUR'], [15.0763844441692, 'project-id', '0336582f-072a-46c0-a44b-d177ac2e66fc', 'EUR'], [16.8290421225854, 'project-id', '050610', 'EUR'], [27.2366087456432, 'project-id', '051515-test', 'EUR'], [27.4254167525638, 'project-id', '051512-test', 'EUR'], [27.5116786636049, 'project-id', '051510-test', 'EUR'], [27.5207988660845, 'project-id', '051505-test1', 'EUR'], [27.6143631421493, 'project-id', '051505-test', 'EUR'], [28.1819469975799, 'project-id', '2baae374-b419-4c28-bdc6-04879e40b452', 'EUR'], [28.2740613473066, 'project-id', 'a70782ac-8957-4e59-b778-b74f86549cad', 'EUR'], [28.5575861396006, 'project-id', 'f529b9df-c309-40be-926d-07212d006bfd', 'EUR'], [29.4036804591471, 'project-id', 'd98606bd-cac7-4920-a9b8-4283bec5df11', 'EUR'], [29.4976735794732, 'project-id', '788a5827-a7ef-4f0a-a8b9-d5e17249ca00', 'EUR'], [34.2245965820849, 'project-id', '64ebae70-2b01-42d0-bb69-bc3efd936bc5', 'EUR'], [48.6674302098406, 'project-id', '35a7db33-b409-4873-b3bc-54e42931bd3d', 'EUR'], [49.2266034028918, 'project-id', 'uk001', 'EUR'], [57.5822573804444, 'project-id', '050813-fix3-test', 'EUR'], [488.150746417799, 'project-id', None, 'EUR']]}, use_cosmos_data: False
2025-06-20 12:09:15,723 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:15,723 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:15,768 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:15,768 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:15,809 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:15,809 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:15,837 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:15,837 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:25,727 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:25,727 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:25,771 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:25,771 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:25,814 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:25,814 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:25,839 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:25,840 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:35,731 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:35,732 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:35,775 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:35,776 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:35,819 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:35,819 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:35,841 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:35,841 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:45,734 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:45,735 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:45,781 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:45,782 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:45,825 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:45,826 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:45,843 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:45,844 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:55,417 - backend.deployments.service - INFO - Deployment worker started
2025-06-20 12:09:55,418 - backend.deployments.routes - INFO - Deployment service initialized
2025-06-20 12:09:55,418 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json'}
2025-06-20 12:09:55,418 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:09:55,419 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:09:55,419 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json'}
2025-06-20 12:09:55,419 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:09:55,420 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['Remote-Addr', 'Host', 'User-Agent', 'Accept', 'Content-Type']
2025-06-20 12:09:55,420 - root - WARNING - No authentication token found in request (checked Authorization and Easy Auth headers)
2025-06-20 12:09:55,420 - root - INFO - Development mode: Using mock user due to missing token
2025-06-20 12:09:55,421 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:09:55,422 - backend.deployments.service - INFO - Deployment queue processor started
2025-06-20 12:09:55,586 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3210'
    'Date': 'Fri, 20 Jun 2025 12:09:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:09:55,588 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:09:55,633 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3209'
    'Date': 'Fri, 20 Jun 2025 12:09:54 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:09:55,634 - backend.cost_management.cost_api_quart - INFO - TEMPORARY: Skipping Cosmos DB and using mock data for debugging
2025-06-20 12:09:55,634 - backend.cost_management.cost_api_quart - INFO - Querying Azure Cost Management for tag 'project-id' with project_id: None
2025-06-20 12:09:55,634 - backend.cost_management.cost_api_quart - INFO - Time range: month
2025-06-20 12:09:55,634 - backend.cost_management.cost_management_service - INFO - 🔧 Using time period: 2025-05-03 to 2025-06-02
2025-06-20 12:09:55,634 - backend.cost_management.cost_management_service - INFO - 🔧 Using resource group scope: rg-internal-ai
2025-06-20 12:09:55,635 - backend.cost_management.cost_management_service - DEBUG - Executing cost query - scope: /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai, tag_name: project-id, tag_value: None
2025-06-20 12:09:55,635 - backend.cost_management.cost_management_service - DEBUG - Query parameters: {'additional_properties': {}, 'type': 'ActualCost', 'timeframe': 'Custom', 'time_period': <azure.mgmt.costmanagement.models._models_py3.QueryTimePeriod object at 0xffff5b99ee30>, 'dataset': <azure.mgmt.costmanagement.models._models_py3.QueryDataset object at 0xffff5ba1d4b0>}
2025-06-20 12:09:55,635 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:09:55,635 - azure.mgmt.costmanagement._serialization - WARNING - Datetime with no tzinfo will be considered UTC.
2025-06-20 12:09:55,636 - msal.authority - DEBUG - Initializing with Entra authority: https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533
2025-06-20 12:09:55,637 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration'
Request method: 'GET'
Request headers:
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:09:55,638 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): login.microsoftonline.com:443
2025-06-20 12:09:55,735 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:55,735 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:55,784 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:55,784 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:55,846 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:55,847 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:55,966 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "GET /ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration HTTP/1.1" 200 1753
2025-06-20 12:09:55,969 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'max-age=86400, private'
    'Content-Type': 'application/json; charset=utf-8'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'Access-Control-Allow-Origin': 'REDACTED'
    'Access-Control-Allow-Methods': 'REDACTED'
    'P3P': 'REDACTED'
    'x-ms-request-id': '19ad9f86-0c59-4070-9b9f-9607665b1400'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Fri, 20 Jun 2025 12:09:55 GMT'
    'Content-Length': '1753'
2025-06-20 12:09:55,971 - msal.authority - DEBUG - openid_config("https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0/.well-known/openid-configuration") = {'token_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token', 'token_endpoint_auth_methods_supported': ['client_secret_post', 'private_key_jwt', 'client_secret_basic'], 'jwks_uri': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/discovery/v2.0/keys', 'response_modes_supported': ['query', 'fragment', 'form_post'], 'subject_types_supported': ['pairwise'], 'id_token_signing_alg_values_supported': ['RS256'], 'response_types_supported': ['code', 'id_token', 'code id_token', 'id_token token'], 'scopes_supported': ['openid', 'profile', 'email', 'offline_access'], 'issuer': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/v2.0', 'request_uri_parameter_supported': False, 'userinfo_endpoint': 'https://graph.microsoft.com/oidc/userinfo', 'authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/authorize', 'device_authorization_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/devicecode', 'http_logout_supported': True, 'frontchannel_logout_supported': True, 'end_session_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/logout', 'claims_supported': ['sub', 'iss', 'cloud_instance_name', 'cloud_instance_host_name', 'cloud_graph_host_name', 'msgraph_host', 'aud', 'exp', 'iat', 'auth_time', 'acr', 'nonce', 'preferred_username', 'name', 'tid', 'ver', 'at_hash', 'c_hash', 'email'], 'kerberos_endpoint': 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/kerberos', 'tenant_region_scope': 'EU', 'cloud_instance_name': 'microsoftonline.com', 'cloud_graph_host_name': 'graph.windows.net', 'msgraph_host': 'graph.microsoft.com', 'rbac_url': 'https://pas.windows.net'}
2025-06-20 12:09:55,971 - msal.application - DEBUG - Broker enabled? None
2025-06-20 12:09:55,971 - msal.application - DEBUG - Region to be used: None
2025-06-20 12:09:55,976 - msal.telemetry - DEBUG - Generate or reuse correlation_id: be8a7986-5b30-464c-93fd-4184a2d88263
2025-06-20 12:09:55,977 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token'
Request method: 'POST'
Request headers:
    'Accept': 'application/json'
    'x-client-sku': 'REDACTED'
    'x-client-ver': 'REDACTED'
    'x-client-os': 'REDACTED'
    'x-ms-lib-capability': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-client-current-telemetry': 'REDACTED'
    'x-client-last-telemetry': 'REDACTED'
    'User-Agent': 'azsdk-python-identity/1.15.0 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:09:56,131 - urllib3.connectionpool - DEBUG - https://login.microsoftonline.com:443 "POST /ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token HTTP/1.1" 200 1647
2025-06-20 12:09:56,132 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'P3P': 'REDACTED'
    'client-request-id': 'REDACTED'
    'x-ms-request-id': '4c70791d-bae0-4155-a624-9644f2071200'
    'x-ms-ests-server': 'REDACTED'
    'x-ms-clitelem': 'REDACTED'
    'x-ms-srs': 'REDACTED'
    'Content-Security-Policy-Report-Only': 'REDACTED'
    'X-XSS-Protection': 'REDACTED'
    'Set-Cookie': 'REDACTED'
    'Date': 'Fri, 20 Jun 2025 12:09:56 GMT'
    'Content-Length': '1647'
2025-06-20 12:09:56,132 - msal.token_cache - DEBUG - event={
    "client_id": "bb1ebfc1-47d8-4273-9206-3acc107c1e35",
    "data": {
        "claims": null,
        "scope": [
            "https://management.azure.com/.default"
        ]
    },
    "environment": "login.microsoftonline.com",
    "grant_type": "client_credentials",
    "params": null,
    "response": {
        "access_token": "********",
        "expires_in": 3599,
        "ext_expires_in": 3599,
        "token_type": "Bearer"
    },
    "scope": [
        "https://management.azure.com/.default"
    ],
    "token_endpoint": "https://login.microsoftonline.com/ee78877a-c63a-405d-85d6-8914358aa533/oauth2/v2.0/token"
}
2025-06-20 12:09:56,132 - azure.identity._internal.get_token_mixin - DEBUG - ClientSecretCredential.get_token succeeded
2025-06-20 12:09:56,132 - azure.identity._internal.decorators - DEBUG - EnvironmentCredential.get_token succeeded
2025-06-20 12:09:56,132 - azure.identity._internal.decorators - DEBUG - [Authenticated account] Client ID: bb1ebfc1-47d8-4273-9206-3acc107c1e35. Tenant ID: ee78877a-c63a-405d-85d6-8914358aa533. User Principal Name: unavailableUpn. Object ID (user): 4b27bf23-ad30-46f2-a2f7-c5998d42367a
2025-06-20 12:09:56,132 - azure.identity._credentials.chained - INFO - DefaultAzureCredential acquired a token from EnvironmentCredential
2025-06-20 12:09:56,133 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://management.azure.com/subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=REDACTED'
Request method: 'POST'
Request headers:
    'Content-Type': 'application/json'
    'Content-Length': '290'
    'Accept': 'application/json'
    'x-ms-client-request-id': '7a724b90-4dcf-11f0-942e-8205a49bb958'
    'User-Agent': 'azsdk-python-mgmt-costmanagement/4.0.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
    'Authorization': 'REDACTED'
A body is sent with the request
2025-06-20 12:09:56,133 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): management.azure.com:443
2025-06-20 12:09:56,985 - urllib3.connectionpool - DEBUG - https://management.azure.com:443 "POST /subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourceGroups/rg-internal-ai/providers/Microsoft.CostManagement/query?api-version=2022-10-01 HTTP/1.1" 200 12993
2025-06-20 12:09:56,988 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Cache-Control': 'no-cache'
    'Pragma': 'no-cache'
    'Content-Length': '12993'
    'Content-Type': 'application/json; charset=utf-8'
    'Expires': '-1'
    'Vary': 'REDACTED'
    'session-id': 'REDACTED'
    'x-ms-request-id': '537b621f-e2db-4bbe-97fb-2162324a5cde'
    'x-ms-correlation-id': 'REDACTED'
    'x-ms-correlation-request-id': 'REDACTED'
    'x-ms-client-request-id': '7a724b90-4dcf-11f0-942e-8205a49bb958'
    'X-Powered-By': 'REDACTED'
    'x-ms-operation-identifier': 'REDACTED'
    'x-ms-ratelimit-remaining-subscription-resource-requests': '249'
    'x-ms-routing-request-id': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'X-Content-Type-Options': 'REDACTED'
    'X-Cache': 'REDACTED'
    'X-MSEdge-Ref': 'Ref A: 66F97E6116D34D3F924708D3F8596F3D Ref B: AMS231020615047 Ref C: 2025-06-20T12:09:56Z'
    'Date': 'Fri, 20 Jun 2025 12:09:56 GMT'
2025-06-20 12:09:56,994 - backend.cost_management.cost_management_service - DEBUG - Raw response type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:09:56,995 - backend.cost_management.cost_management_service - INFO - ✅ Azure SDK cost query successful with 192 rows
2025-06-20 12:09:56,995 - backend.cost_management.cost_management_service - INFO - First row: [3.632860833e-06, 'project-id', '051307-test', 'EUR']
2025-06-20 12:09:56,995 - backend.cost_management.cost_management_service - INFO - Second row: [6.862070461e-06, 'project-id', '497e2a90-20db-4396-b658-9e74fb9e0882', 'EUR']
2025-06-20 12:09:56,996 - backend.cost_management.cost_management_service - INFO - Third row: [1.2916838516e-05, 'project-id', '051209-test', 'EUR']
2025-06-20 12:09:56,996 - backend.cost_management.cost_management_service - INFO - Columns: ['Cost', 'TagKey', 'TagValue', 'Currency']
2025-06-20 12:09:56,996 - backend.cost_management.cost_api_quart - INFO - Azure Cost Management query completed
2025-06-20 12:09:56,996 - backend.cost_management.cost_api_quart - INFO - Result type: <class 'azure.mgmt.costmanagement.models._models_py3.QueryResult'>
2025-06-20 12:09:56,996 - backend.cost_management.cost_api_quart - INFO - Result has rows: True
2025-06-20 12:09:56,997 - backend.cost_management.cost_api_quart - INFO - Number of rows: 192
2025-06-20 12:09:56,997 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051307-test with cost 3.632860833e-06 EUR
2025-06-20 12:09:56,997 - backend.cost_management.cost_api_quart - DEBUG - Processing project 497e2a90-20db-4396-b658-9e74fb9e0882 with cost 6.862070461e-06 EUR
2025-06-20 12:09:56,997 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051209-test with cost 1.2916838516e-05 EUR
2025-06-20 12:09:56,997 - backend.cost_management.cost_api_quart - DEBUG - Processing project 5c4360dc-c3ca-4890-8341-38f360442020 with cost 1.8567955367e-05 EUR
2025-06-20 12:09:56,997 - backend.cost_management.cost_api_quart - DEBUG - Processing project d1ad36d7-06cb-4b8f-a09f-22ba75412f09 with cost 1.99941e-05 EUR
2025-06-20 12:09:56,998 - backend.cost_management.cost_api_quart - DEBUG - Processing project 61139a2c-01e1-4247-adad-9b9827de6a2e with cost 2.7044630642e-05 EUR
2025-06-20 12:09:56,998 - backend.cost_management.cost_api_quart - DEBUG - Processing project 95378200-af9a-42f6-a5c3-dbbf17425a71 with cost 3.2653152961e-05 EUR
2025-06-20 12:09:56,998 - backend.cost_management.cost_api_quart - DEBUG - Processing project a550b8a4-b650-4328-8cf3-fabf68a3ae3b with cost 3.5478711386e-05 EUR
2025-06-20 12:09:56,998 - backend.cost_management.cost_api_quart - DEBUG - Processing project dc900a18-d3cc-49c2-ad9c-92323d80ac2f with cost 3.6242203978e-05 EUR
2025-06-20 12:09:56,998 - backend.cost_management.cost_api_quart - DEBUG - Processing project 1e513bc6-f53d-4c22-90c9-0b41b488bbc3 with cost 3.6242203978e-05 EUR
2025-06-20 12:09:56,998 - backend.cost_management.cost_api_quart - DEBUG - Processing project af148c1c-ea3c-4268-a721-ced1208b5d06 with cost 3.6286013793e-05 EUR
2025-06-20 12:09:56,998 - backend.cost_management.cost_api_quart - DEBUG - Processing project 7c6594ef-fdc9-404b-8555-c8c2555f0297 with cost 3.6286013793e-05 EUR
2025-06-20 12:09:56,998 - backend.cost_management.cost_api_quart - DEBUG - Processing project 21eb638f-2ad3-4657-a7ed-30a97b464319 with cost 3.6286013793e-05 EUR
2025-06-20 12:09:56,999 - backend.cost_management.cost_api_quart - DEBUG - Processing project 2004f25c-7842-45d1-a6ee-71c145feb565 with cost 3.6286013793e-05 EUR
2025-06-20 12:09:56,999 - backend.cost_management.cost_api_quart - DEBUG - Processing project test-project-dced4203 with cost 4.2340781848e-05 EUR
2025-06-20 12:09:56,999 - backend.cost_management.cost_api_quart - DEBUG - Processing project e0ca5aad-cfb4-4bd4-9879-73f3c4c63f63 with cost 4.5973642681e-05 EUR
2025-06-20 12:09:56,999 - backend.cost_management.cost_api_quart - DEBUG - Processing project c0db99cc-5532-46dd-93d5-b5c29f169597 with cost 4.6377293884e-05 EUR
2025-06-20 12:09:56,999 - backend.cost_management.cost_api_quart - DEBUG - Processing project cbbd46af-406d-4aec-849c-da1c373736e5 with cost 5.4450317957e-05 EUR
2025-06-20 12:09:56,999 - backend.cost_management.cost_api_quart - DEBUG - Processing project 024ad4cc-6e07-4efa-b0de-b313fb46d23e with cost 6.9789063697e-05 EUR
2025-06-20 12:09:56,999 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050708-test-v with cost 6.9789063697e-05 EUR
2025-06-20 12:09:56,999 - backend.cost_management.cost_api_quart - DEBUG - Processing project a6c76055-cbe1-408f-8d59-b78b70e4cf30 with cost 7.01927149e-05 EUR
2025-06-20 12:09:57,000 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050628-test with cost 7.6651134159e-05 EUR
2025-06-20 12:09:57,000 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051420-test with cost 0.094541677834361 EUR
2025-06-20 12:09:57,000 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050714-test with cost 0.094541677834361 EUR
2025-06-20 12:09:57,000 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051401-test with cost 0.094545310695194 EUR
2025-06-20 12:09:57,000 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050812-test with cost 0.094981688825737 EUR
2025-06-20 12:09:57,000 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051308-test with cost 0.095018940381952 EUR
2025-06-20 12:09:57,000 - backend.cost_management.cost_api_quart - DEBUG - Processing project ada5fcdb-a482-46f8-bf31-c2815837e167 with cost 0.095032429452082 EUR
2025-06-20 12:09:57,000 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051302-test with cost 0.095066831713815 EUR
2025-06-20 12:09:57,001 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050707-test with cost 0.095100116062362 EUR
2025-06-20 12:09:57,001 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050903-test with cost 0.095110629749634 EUR
2025-06-20 12:09:57,001 - backend.cost_management.cost_api_quart - DEBUG - Processing project test-deployment-123 with cost 0.095211134979624 EUR
2025-06-20 12:09:57,001 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050814-test with cost 0.095224075847237 EUR
2025-06-20 12:09:57,001 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050905-test with cost 0.095301336911964 EUR
2025-06-20 12:09:57,001 - backend.cost_management.cost_api_quart - DEBUG - Processing project a9080f29-a5cf-4f78-acdb-9dc8b96ba56e with cost 0.095456274185813 EUR
2025-06-20 12:09:57,001 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051315-test with cost 0.096538978024518 EUR
2025-06-20 12:09:57,001 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050906-test with cost 0.097339003844104 EUR
2025-06-20 12:09:57,002 - backend.cost_management.cost_api_quart - DEBUG - Processing project 624998cb-05cb-4c55-bf0e-161310012054 with cost 0.097393010550614 EUR
2025-06-20 12:09:57,002 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051417-test with cost 0.189086988529555 EUR
2025-06-20 12:09:57,002 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051402-test with cost 0.189086988529555 EUR
2025-06-20 12:09:57,002 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050712-test with cost 0.189090621390387 EUR
2025-06-20 12:09:57,002 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050809-test with cost 0.189537864212496 EUR
2025-06-20 12:09:57,002 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051312-test with cost 0.189634414682227 EUR
2025-06-20 12:09:57,002 - backend.cost_management.cost_api_quart - DEBUG - Processing project c4531f63-b3fd-4663-b766-93961d3d6ec2 with cost 0.189638762422417 EUR
2025-06-20 12:09:57,002 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050705-test with cost 0.189665303114381 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project da4b1905-0adf-4262-bc71-e64b97b2574e with cost 0.189702836366324 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project 283bc414-e86a-435b-be46-eb8e548574ad with cost 0.18971637387206 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051302-test-fix with cost 0.189719862025027 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050706-test with cost 0.189811409972435 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project 6683bca1-4bc6-4b78-a6b4-28e009478c70 with cost 0.18984401650167 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project ec9e1002-f490-490a-89f3-c9311845ba44 with cost 0.18986100472439 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050711-test with cost 0.189922304717632 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project 29edc487-1c86-44aa-b183-c26c84eab470 with cost 0.190018102156659 EUR
2025-06-20 12:09:57,003 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050901-test with cost 0.190028005179321 EUR
2025-06-20 12:09:57,004 - backend.cost_management.cost_api_quart - DEBUG - Processing project 4b923af3-1bc5-407d-85ed-f6159ffd724c with cost 0.190134729994913 EUR
2025-06-20 12:09:57,004 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051212-test with cost 0.190464121638883 EUR
2025-06-20 12:09:57,004 - backend.cost_management.cost_api_quart - DEBUG - Processing project bf425892-3591-4ab2-a6b8-ca03b5710b4c with cost 0.19077950682205 EUR
2025-06-20 12:09:57,004 - backend.cost_management.cost_api_quart - DEBUG - Processing project a421ffcd-1692-4bc5-bd41-af3966571f8e with cost 0.19080741979343 EUR
2025-06-20 12:09:57,004 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051314-test with cost 0.191166926280954 EUR
2025-06-20 12:09:57,004 - backend.cost_management.cost_api_quart - DEBUG - Processing project 7d5c2604-3770-48d7-a984-9298d85791ec with cost 0.284038044976678 EUR
2025-06-20 12:09:57,005 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051211-test with cost 0.284045172979147 EUR
2025-06-20 12:09:57,005 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050811-test with cost 0.284131257351464 EUR
2025-06-20 12:09:57,005 - backend.cost_management.cost_api_quart - DEBUG - Processing project bae2df92-e9ae-4e68-89f9-30139c79c685 with cost 0.284172519901634 EUR
2025-06-20 12:09:57,005 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050709-test with cost 0.284189447472426 EUR
2025-06-20 12:09:57,006 - backend.cost_management.cost_api_quart - DEBUG - Processing project 910537a3-a201-45fb-a6c0-34ef8ce1cb7f with cost 0.28425168345516 EUR
2025-06-20 12:09:57,006 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051301-test-fix with cost 0.284316287159968 EUR
2025-06-20 12:09:57,006 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051311-test with cost 0.28435960036981 EUR
2025-06-20 12:09:57,006 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050806-test with cost 0.28446766665339 EUR
2025-06-20 12:09:57,006 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050704-test with cost 0.284633609593372 EUR
2025-06-20 12:09:57,006 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050710-test with cost 0.284726165595058 EUR
2025-06-20 12:09:57,006 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051310-test with cost 0.285258360625258 EUR
2025-06-20 12:09:57,007 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050808-test with cost 0.28549764197637 EUR
2025-06-20 12:09:57,007 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051419-test with cost 0.378181242780773 EUR
2025-06-20 12:09:57,007 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050805-test with cost 0.378238922308361 EUR
2025-06-20 12:09:57,007 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050708-test with cost 0.378591268122477 EUR
2025-06-20 12:09:57,007 - backend.cost_management.cost_api_quart - DEBUG - Processing project 92f3bea8-f426-42c0-8b24-4ae42bad4a76 with cost 0.37871499314971 EUR
2025-06-20 12:09:57,007 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050703-test with cost 0.37872999538769 EUR
2025-06-20 12:09:57,007 - backend.cost_management.cost_api_quart - DEBUG - Processing project a67ef190-933c-4659-ace8-7d2a47729ddf with cost 0.378844455937041 EUR
2025-06-20 12:09:57,007 - backend.cost_management.cost_api_quart - DEBUG - Processing project 77d970b7-81e7-49cd-8348-4c6ccb48fa5e with cost 0.378927480287321 EUR
2025-06-20 12:09:57,008 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051301-test with cost 0.379015796919665 EUR
2025-06-20 12:09:57,008 - backend.cost_management.cost_api_quart - DEBUG - Processing project cb50ff0f-a19a-4d24-a9c1-e736a4450b3c with cost 0.379160955397267 EUR
2025-06-20 12:09:57,008 - backend.cost_management.cost_api_quart - DEBUG - Processing project 795d29bb-fe07-4cc7-8d3a-eb43f1098643 with cost 0.379482774128057 EUR
2025-06-20 12:09:57,008 - backend.cost_management.cost_api_quart - DEBUG - Processing project 8c6b33bf-5e43-4717-bdc1-432bc28726e7 with cost 0.380023808102955 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project cfe84b0b-f916-4b23-b670-6f5844d2e5a7 with cost 0.380947607242618 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051418-test with cost 0.472727360778375 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 52f2db54-4cd1-448d-96e8-5ea383b3e7bc with cost 0.472730589988004 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050701-test with cost 0.472780877535233 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 230629ee-8f4c-4d39-8114-b598b9e3c568 with cost 0.47348847522005 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 7e2f5d4f-cf7b-4869-868f-45e887c7e192 with cost 0.473506636681007 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050702-test with cost 0.473530791492509 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050804-test with cost 0.473559227507043 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 232a00e6-1051-4da0-9e35-b93ae6c4407d with cost 0.473917995274125 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051208-test with cost 0.475239837298787 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project a189f24c-6efa-43be-8f7e-3e2a6e2164fd with cost 0.475786885858849 EUR
2025-06-20 12:09:57,009 - backend.cost_management.cost_api_quart - DEBUG - Processing project a1d64f7f-6904-411e-b5d5-480a7089f313 with cost 0.567790681359448 EUR
2025-06-20 12:09:57,010 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050801-test with cost 0.56796994747688 EUR
2025-06-20 12:09:57,010 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050713-test with cost 0.568021441387802 EUR
2025-06-20 12:09:57,010 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050802-test with cost 0.568674608066689 EUR
2025-06-20 12:09:57,010 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050627-test with cost 0.629115571464815 EUR
2025-06-20 12:09:57,010 - backend.cost_management.cost_api_quart - DEBUG - Processing project 51429d7d-327c-40d2-aa2b-006a4b846b3c with cost 0.664486450402576 EUR
2025-06-20 12:09:57,010 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050624-test with cost 0.943637251711764 EUR
2025-06-20 12:09:57,010 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050625-test with cost 0.943637251711764 EUR
2025-06-20 12:09:57,011 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050626-test with cost 0.943645587525075 EUR
2025-06-20 12:09:57,011 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051405-test with cost 0.945420411204441 EUR
2025-06-20 12:09:57,011 - backend.cost_management.cost_api_quart - DEBUG - Processing project 4fe3c1a3-0ce7-4f21-b64f-b90d42099d3a with cost 0.946099229669264 EUR
2025-06-20 12:09:57,011 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051421-test with cost 1.22904705931234 EUR
2025-06-20 12:09:57,011 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050715-test with cost 1.41819495657911 EUR
2025-06-20 12:09:57,012 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050716-test with cost 1.41884961494116 EUR
2025-06-20 12:09:57,012 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050717-test with cost 1.41896350810573 EUR
2025-06-20 12:09:57,012 - backend.cost_management.cost_api_quart - DEBUG - Processing project 8433e493-1376-4935-848f-4b984fe1397f with cost 1.51408881699831 EUR
2025-06-20 12:09:57,012 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050621-test2 with cost 1.57267980490325 EUR
2025-06-20 12:09:57,013 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050622-test with cost 1.57268814071656 EUR
2025-06-20 12:09:57,013 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050623-test with cost 1.57268814071656 EUR
2025-06-20 12:09:57,013 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050621-test with cost 1.57270320206979 EUR
2025-06-20 12:09:57,013 - backend.cost_management.cost_api_quart - DEBUG - Processing project e683261b-bfbd-4b4e-8838-3dfd77b3e397 with cost 1.58550194977009 EUR
2025-06-20 12:09:57,013 - backend.cost_management.cost_api_quart - DEBUG - Processing project 665b6522-2fb0-42a3-8e8e-edcd86ee440c with cost 1.60820237071455 EUR
2025-06-20 12:09:57,013 - backend.cost_management.cost_api_quart - DEBUG - Processing project eacdb4d9-51ab-4c7b-b653-d95032d94a39 with cost 1.7024949334423 EUR
2025-06-20 12:09:57,013 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050813-fix2-test with cost 1.79711179618539 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050813-fix-test with cost 1.7971225840098 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project 253e7429-03b5-49ba-957e-5859aec7aa46 with cost 1.88713613624974 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050813-test with cost 1.89133333034763 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050629-test with cost 1.89140778886213 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project 21b08f43-5306-49e5-85ba-bad78af72c41 with cost 1.9067787250346 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051605-test with cost 1.98577046534494 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project c0ec1fa8-7b9f-4be5-a733-abd0b398d46b with cost 1.98589317545841 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project 81f788f7-bf7a-4d28-9f94-fd880dc45861 with cost 1.98594255235311 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project 4458258f-46ad-42f8-9dd6-6f351d4a336c with cost 1.98603726359959 EUR
2025-06-20 12:09:57,014 - backend.cost_management.cost_api_quart - DEBUG - Processing project f4e2fd3a-ce33-4965-87aa-343ac2d77da7 with cost 2.17516218856494 EUR
2025-06-20 12:09:57,015 - backend.cost_management.cost_api_quart - DEBUG - Processing project 0508-test-project-0805 with cost 2.17527681895962 EUR
2025-06-20 12:09:57,015 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051601-test with cost 2.17538502512914 EUR
2025-06-20 12:09:57,015 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050810-test with cost 2.175887539942 EUR
2025-06-20 12:09:57,015 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051316-test with cost 2.17645186266212 EUR
2025-06-20 12:09:57,015 - backend.cost_management.cost_api_quart - DEBUG - Processing project 17485152-ea1c-4385-bfcd-37f78b1146f2 with cost 2.53518990599579 EUR
2025-06-20 12:09:57,015 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050612 with cost 2.84105272115707 EUR
2025-06-20 12:09:57,015 - backend.cost_management.cost_api_quart - DEBUG - Processing project 8840f20f-2213-4dc9-a9c4-4062358f8186 with cost 2.8520116689281 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project test-project with cost 3.17002473531209 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project 0e4f8dc2-6d71-4931-a4c9-4b5b83b3eb39 with cost 3.87699522832852 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051206-test with cost 4.25543346954284 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project 9e4f7374-6872-4507-98b1-c7890103039c with cost 4.2554350721545 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project fecb2f28-06f5-4e86-a084-12f10ea9bb87 with cost 4.42906503029853 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project 6b53514a-1cde-4292-ad48-3e6c5a5cb0c0 with cost 4.43492345026626 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051520-test with cost 4.44454769541689 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project ab55657c-eeca-4e85-b0c9-8722674bdfc8 with cost 4.53859876213635 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051514-test with cost 4.63351404121295 EUR
2025-06-20 12:09:57,016 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051521-test with cost 5.01148431690418 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project b70a3ff5-ef8c-4df7-a549-8f307a888c08 with cost 5.99505613943272 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project 5ec6d026-66bb-4f2e-ba37-813dc8677b28 with cost 6.0000679564187 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project a7d2d11e-a66e-4392-9c07-e1646e299c20 with cost 6.00014635712406 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project e5747327-8bcb-4029-8900-990205c91fbc with cost 6.05109619031858 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project f21c91e4-6e5c-4731-a982-6e17033af4ba with cost 6.31190938211924 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project bdb36311-c696-4eb3-a25e-f68ec3b8863b with cost 6.31953907992516 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project 2e69bae9-d0a8-4de2-89c9-7caf8bdbea0f with cost 6.61246603270672 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project bcc3cc0e-b161-40f6-9127-af6ac14eb148 with cost 6.63053033848656 EUR
2025-06-20 12:09:57,017 - backend.cost_management.cost_api_quart - DEBUG - Processing project 1b726f9e-866f-4bb1-b1a2-23c568368449 with cost 6.63444184079434 EUR
2025-06-20 12:09:57,018 - backend.cost_management.cost_api_quart - DEBUG - Processing project 9c0f944b-ca13-4d70-a0f9-be74a515b44b with cost 6.93966295084879 EUR
2025-06-20 12:09:57,018 - backend.cost_management.cost_api_quart - DEBUG - Processing project 4b981f00-0fbd-4d52-90c0-ef08811724e3 with cost 6.94345069732123 EUR
2025-06-20 12:09:57,018 - backend.cost_management.cost_api_quart - DEBUG - Processing project 296882d9-e444-4c81-ae23-78441de09ddd with cost 6.94654801840838 EUR
2025-06-20 12:09:57,018 - backend.cost_management.cost_api_quart - DEBUG - Processing project 47917ed5-f83f-4bb3-b5a0-028b78717eff with cost 7.24867834300848 EUR
2025-06-20 12:09:57,018 - backend.cost_management.cost_api_quart - DEBUG - Processing project 16cc9b6c-f166-49ee-9568-d4155571bf1a with cost 7.56961044588782 EUR
2025-06-20 12:09:57,019 - backend.cost_management.cost_api_quart - DEBUG - Processing project 84ab5bb3-1cd3-4ac6-b9ef-a59b2a231d53 with cost 7.94249979171117 EUR
2025-06-20 12:09:57,019 - backend.cost_management.cost_api_quart - DEBUG - Processing project 5c4a937a-50b1-4509-8f2e-ed2c0ba57d60 with cost 8.70005983186492 EUR
2025-06-20 12:09:57,019 - backend.cost_management.cost_api_quart - DEBUG - Processing project e8898ebe-3e81-48a0-8f3f-49f7005af836 with cost 9.46310143633803 EUR
2025-06-20 12:09:57,019 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050910-test with cost 10.5890889915101 EUR
2025-06-20 12:09:57,019 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050907-test with cost 11.1574300872842 EUR
2025-06-20 12:09:57,019 - backend.cost_management.cost_api_quart - DEBUG - Processing project projectb with cost 11.3463460459369 EUR
2025-06-20 12:09:57,020 - backend.cost_management.cost_api_quart - DEBUG - Processing project projecta with cost 11.4406295790336 EUR
2025-06-20 12:09:57,020 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050904-test with cost 11.5346433568754 EUR
2025-06-20 12:09:57,020 - backend.cost_management.cost_api_quart - DEBUG - Processing project 87547c7e-011b-4366-83a2-f09ce43840ac with cost 12.3855557720684 EUR
2025-06-20 12:09:57,020 - backend.cost_management.cost_api_quart - DEBUG - Processing project 343c7ea8-0ca5-473b-960d-4c3d74614bed with cost 13.0907775531743 EUR
2025-06-20 12:09:57,020 - backend.cost_management.cost_api_quart - DEBUG - Processing project 8afc7f8f-2aa5-4d9d-ae13-8d307c64b2b4 with cost 14.0873943745648 EUR
2025-06-20 12:09:57,021 - backend.cost_management.cost_api_quart - DEBUG - Processing project d10589b5-9de3-488d-9bb0-77d08ed94474 with cost 14.2764594277897 EUR
2025-06-20 12:09:57,021 - backend.cost_management.cost_api_quart - DEBUG - Processing project bb4c80f7-aebd-4a25-94f6-a5dbe7913819 with cost 14.2775316004166 EUR
2025-06-20 12:09:57,021 - backend.cost_management.cost_api_quart - DEBUG - Processing project ee67a747-b953-44c7-bbb7-cf3c1db37dc2 with cost 14.8438976335764 EUR
2025-06-20 12:09:57,021 - backend.cost_management.cost_api_quart - DEBUG - Processing project fce0facf-1b4b-4973-a5c4-552a26b36f37 with cost 14.8447448906724 EUR
2025-06-20 12:09:57,021 - backend.cost_management.cost_api_quart - DEBUG - Processing project 585837de-b731-42b5-a8b3-8cc855fff198 with cost 14.9431648470284 EUR
2025-06-20 12:09:57,021 - backend.cost_management.cost_api_quart - DEBUG - Processing project 0336582f-072a-46c0-a44b-d177ac2e66fc with cost 15.0763844441692 EUR
2025-06-20 12:09:57,021 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050610 with cost 16.8290421225854 EUR
2025-06-20 12:09:57,021 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051515-test with cost 27.2366087456432 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051512-test with cost 27.4254167525638 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051510-test with cost 27.5116786636049 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051505-test1 with cost 27.5207988660845 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project 051505-test with cost 27.6143631421493 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project 2baae374-b419-4c28-bdc6-04879e40b452 with cost 28.1819469975799 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project a70782ac-8957-4e59-b778-b74f86549cad with cost 28.2740613473066 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project f529b9df-c309-40be-926d-07212d006bfd with cost 28.5575861396006 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project d98606bd-cac7-4920-a9b8-4283bec5df11 with cost 29.4036804591471 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project 788a5827-a7ef-4f0a-a8b9-d5e17249ca00 with cost 29.4976735794732 EUR
2025-06-20 12:09:57,022 - backend.cost_management.cost_api_quart - DEBUG - Processing project 64ebae70-2b01-42d0-bb69-bc3efd936bc5 with cost 34.2245965820849 EUR
2025-06-20 12:09:57,023 - backend.cost_management.cost_api_quart - DEBUG - Processing project 35a7db33-b409-4873-b3bc-54e42931bd3d with cost 48.6674302098406 EUR
2025-06-20 12:09:57,023 - backend.cost_management.cost_api_quart - DEBUG - Processing project uk001 with cost 49.2266034028918 EUR
2025-06-20 12:09:57,023 - backend.cost_management.cost_api_quart - DEBUG - Processing project 050813-fix3-test with cost 57.5822573804444 EUR
2025-06-20 12:09:57,023 - backend.cost_management.cost_api_quart - DEBUG - Skipping untagged resource with cost 488.150746417799
2025-06-20 12:09:57,023 - backend.cost_management.cost_api_quart - INFO - Fetching names for 191 projects: ['051307-test', '497e2a90-20db-4396-b658-9e74fb9e0882', '051209-test', '5c4360dc-c3ca-4890-8341-38f360442020', 'd1ad36d7-06cb-4b8f-a09f-22ba75412f09', '61139a2c-01e1-4247-adad-9b9827de6a2e', '95378200-af9a-42f6-a5c3-dbbf17425a71', 'a550b8a4-b650-4328-8cf3-fabf68a3ae3b', 'dc900a18-d3cc-49c2-ad9c-92323d80ac2f', '1e513bc6-f53d-4c22-90c9-0b41b488bbc3', 'af148c1c-ea3c-4268-a721-ced1208b5d06', '7c6594ef-fdc9-404b-8555-c8c2555f0297', '21eb638f-2ad3-4657-a7ed-30a97b464319', '2004f25c-7842-45d1-a6ee-71c145feb565', 'test-project-dced4203', 'e0ca5aad-cfb4-4bd4-9879-73f3c4c63f63', 'c0db99cc-5532-46dd-93d5-b5c29f169597', 'cbbd46af-406d-4aec-849c-da1c373736e5', '024ad4cc-6e07-4efa-b0de-b313fb46d23e', '050708-test-v', 'a6c76055-cbe1-408f-8d59-b78b70e4cf30', '050628-test', '051420-test', '050714-test', '051401-test', '050812-test', '051308-test', 'ada5fcdb-a482-46f8-bf31-c2815837e167', '051302-test', '050707-test', '050903-test', 'test-deployment-123', '050814-test', '050905-test', 'a9080f29-a5cf-4f78-acdb-9dc8b96ba56e', '051315-test', '050906-test', '624998cb-05cb-4c55-bf0e-161310012054', '051417-test', '051402-test', '050712-test', '050809-test', '051312-test', 'c4531f63-b3fd-4663-b766-93961d3d6ec2', '050705-test', 'da4b1905-0adf-4262-bc71-e64b97b2574e', '283bc414-e86a-435b-be46-eb8e548574ad', '051302-test-fix', '050706-test', '6683bca1-4bc6-4b78-a6b4-28e009478c70', 'ec9e1002-f490-490a-89f3-c9311845ba44', '050711-test', '29edc487-1c86-44aa-b183-c26c84eab470', '050901-test', '4b923af3-1bc5-407d-85ed-f6159ffd724c', '051212-test', 'bf425892-3591-4ab2-a6b8-ca03b5710b4c', 'a421ffcd-1692-4bc5-bd41-af3966571f8e', '051314-test', '7d5c2604-3770-48d7-a984-9298d85791ec', '051211-test', '050811-test', 'bae2df92-e9ae-4e68-89f9-30139c79c685', '050709-test', '910537a3-a201-45fb-a6c0-34ef8ce1cb7f', '051301-test-fix', '051311-test', '050806-test', '050704-test', '050710-test', '051310-test', '050808-test', '051419-test', '050805-test', '050708-test', '92f3bea8-f426-42c0-8b24-4ae42bad4a76', '050703-test', 'a67ef190-933c-4659-ace8-7d2a47729ddf', '77d970b7-81e7-49cd-8348-4c6ccb48fa5e', '051301-test', 'cb50ff0f-a19a-4d24-a9c1-e736a4450b3c', '795d29bb-fe07-4cc7-8d3a-eb43f1098643', '8c6b33bf-5e43-4717-bdc1-432bc28726e7', 'cfe84b0b-f916-4b23-b670-6f5844d2e5a7', '051418-test', '52f2db54-4cd1-448d-96e8-5ea383b3e7bc', '050701-test', '230629ee-8f4c-4d39-8114-b598b9e3c568', '7e2f5d4f-cf7b-4869-868f-45e887c7e192', '050702-test', '050804-test', '232a00e6-1051-4da0-9e35-b93ae6c4407d', '051208-test', 'a189f24c-6efa-43be-8f7e-3e2a6e2164fd', 'a1d64f7f-6904-411e-b5d5-480a7089f313', '050801-test', '050713-test', '050802-test', '050627-test', '51429d7d-327c-40d2-aa2b-006a4b846b3c', '050624-test', '050625-test', '050626-test', '051405-test', '4fe3c1a3-0ce7-4f21-b64f-b90d42099d3a', '051421-test', '050715-test', '050716-test', '050717-test', '8433e493-1376-4935-848f-4b984fe1397f', '050621-test2', '050622-test', '050623-test', '050621-test', 'e683261b-bfbd-4b4e-8838-3dfd77b3e397', '665b6522-2fb0-42a3-8e8e-edcd86ee440c', 'eacdb4d9-51ab-4c7b-b653-d95032d94a39', '050813-fix2-test', '050813-fix-test', '253e7429-03b5-49ba-957e-5859aec7aa46', '050813-test', '050629-test', '21b08f43-5306-49e5-85ba-bad78af72c41', '051605-test', 'c0ec1fa8-7b9f-4be5-a733-abd0b398d46b', '81f788f7-bf7a-4d28-9f94-fd880dc45861', '4458258f-46ad-42f8-9dd6-6f351d4a336c', 'f4e2fd3a-ce33-4965-87aa-343ac2d77da7', '0508-test-project-0805', '051601-test', '050810-test', '051316-test', '17485152-ea1c-4385-bfcd-37f78b1146f2', '050612', '8840f20f-2213-4dc9-a9c4-4062358f8186', 'test-project', '0e4f8dc2-6d71-4931-a4c9-4b5b83b3eb39', '051206-test', '9e4f7374-6872-4507-98b1-c7890103039c', 'fecb2f28-06f5-4e86-a084-12f10ea9bb87', '6b53514a-1cde-4292-ad48-3e6c5a5cb0c0', '051520-test', 'ab55657c-eeca-4e85-b0c9-8722674bdfc8', '051514-test', '051521-test', 'b70a3ff5-ef8c-4df7-a549-8f307a888c08', '5ec6d026-66bb-4f2e-ba37-813dc8677b28', 'a7d2d11e-a66e-4392-9c07-e1646e299c20', 'e5747327-8bcb-4029-8900-990205c91fbc', 'f21c91e4-6e5c-4731-a982-6e17033af4ba', 'bdb36311-c696-4eb3-a25e-f68ec3b8863b', '2e69bae9-d0a8-4de2-89c9-7caf8bdbea0f', 'bcc3cc0e-b161-40f6-9127-af6ac14eb148', '1b726f9e-866f-4bb1-b1a2-23c568368449', '9c0f944b-ca13-4d70-a0f9-be74a515b44b', '4b981f00-0fbd-4d52-90c0-ef08811724e3', '296882d9-e444-4c81-ae23-78441de09ddd', '47917ed5-f83f-4bb3-b5a0-028b78717eff', '16cc9b6c-f166-49ee-9568-d4155571bf1a', '84ab5bb3-1cd3-4ac6-b9ef-a59b2a231d53', '5c4a937a-50b1-4509-8f2e-ed2c0ba57d60', 'e8898ebe-3e81-48a0-8f3f-49f7005af836', '050910-test', '050907-test', 'projectb', 'projecta', '050904-test', '87547c7e-011b-4366-83a2-f09ce43840ac', '343c7ea8-0ca5-473b-960d-4c3d74614bed', '8afc7f8f-2aa5-4d9d-ae13-8d307c64b2b4', 'd10589b5-9de3-488d-9bb0-77d08ed94474', 'bb4c80f7-aebd-4a25-94f6-a5dbe7913819', 'ee67a747-b953-44c7-bbb7-cf3c1db37dc2', 'fce0facf-1b4b-4973-a5c4-552a26b36f37', '585837de-b731-42b5-a8b3-8cc855fff198', '0336582f-072a-46c0-a44b-d177ac2e66fc', '050610', '051515-test', '051512-test', '051510-test', '051505-test1', '051505-test', '2baae374-b419-4c28-bdc6-04879e40b452', 'a70782ac-8957-4e59-b778-b74f86549cad', 'f529b9df-c309-40be-926d-07212d006bfd', 'd98606bd-cac7-4920-a9b8-4283bec5df11', '788a5827-a7ef-4f0a-a8b9-d5e17249ca00', '64ebae70-2b01-42d0-bb69-bc3efd936bc5', '35a7db33-b409-4873-b3bc-54e42931bd3d', 'uk001', '050813-fix3-test']
2025-06-20 12:09:57,024 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '14172'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:09:57,025 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:09:57,026 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:09:57,088 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '113'
    'Date': 'Fri, 20 Jun 2025 12:09:56 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:09:57,089 - backend.cost_management.cost_management_service - DEBUG - Retrieved names for 1 out of 191 projects
2025-06-20 12:09:57,089 - backend.cost_management.cost_api_quart - INFO - Retrieved project names: {'050813-fix3-test': 'Priority_Plot_testing_Project'}
2025-06-20 12:09:57,089 - backend.cost_management.cost_api_quart - DEBUG - project_costs length: 191, result: {'additional_properties': {}, 'id': 'subscriptions/4c1c14a3-de17-4cda-af60-01610fb493f9/resourcegroups/rg-internal-ai/providers/Microsoft.CostManagement/query/f0d4e507-52bc-457a-95a7-fa9ddb421c68', 'name': 'f0d4e507-52bc-457a-95a7-fa9ddb421c68', 'type': 'Microsoft.CostManagement/query', 'location': None, 'sku': None, 'e_tag': None, 'tags': None, 'next_link': None, 'columns': [<azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff5ba1e530>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff5ba1d000>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff5ba1ca00>, <azure.mgmt.costmanagement.models._models_py3.QueryColumn object at 0xffff5ba1ce50>], 'rows': [[3.632860833e-06, 'project-id', '051307-test', 'EUR'], [6.862070461e-06, 'project-id', '497e2a90-20db-4396-b658-9e74fb9e0882', 'EUR'], [1.2916838516e-05, 'project-id', '051209-test', 'EUR'], [1.8567955367e-05, 'project-id', '5c4360dc-c3ca-4890-8341-38f360442020', 'EUR'], [1.99941e-05, 'project-id', 'd1ad36d7-06cb-4b8f-a09f-22ba75412f09', 'EUR'], [2.7044630642e-05, 'project-id', '61139a2c-01e1-4247-adad-9b9827de6a2e', 'EUR'], [3.2653152961e-05, 'project-id', '95378200-af9a-42f6-a5c3-dbbf17425a71', 'EUR'], [3.5478711386e-05, 'project-id', 'a550b8a4-b650-4328-8cf3-fabf68a3ae3b', 'EUR'], [3.6242203978e-05, 'project-id', 'dc900a18-d3cc-49c2-ad9c-92323d80ac2f', 'EUR'], [3.6242203978e-05, 'project-id', '1e513bc6-f53d-4c22-90c9-0b41b488bbc3', 'EUR'], [3.6286013793e-05, 'project-id', 'af148c1c-ea3c-4268-a721-ced1208b5d06', 'EUR'], [3.6286013793e-05, 'project-id', '7c6594ef-fdc9-404b-8555-c8c2555f0297', 'EUR'], [3.6286013793e-05, 'project-id', '21eb638f-2ad3-4657-a7ed-30a97b464319', 'EUR'], [3.6286013793e-05, 'project-id', '2004f25c-7842-45d1-a6ee-71c145feb565', 'EUR'], [4.2340781848e-05, 'project-id', 'test-project-dced4203', 'EUR'], [4.5973642681e-05, 'project-id', 'e0ca5aad-cfb4-4bd4-9879-73f3c4c63f63', 'EUR'], [4.6377293884e-05, 'project-id', 'c0db99cc-5532-46dd-93d5-b5c29f169597', 'EUR'], [5.4450317957e-05, 'project-id', 'cbbd46af-406d-4aec-849c-da1c373736e5', 'EUR'], [6.9789063697e-05, 'project-id', '024ad4cc-6e07-4efa-b0de-b313fb46d23e', 'EUR'], [6.9789063697e-05, 'project-id', '050708-test-v', 'EUR'], [7.01927149e-05, 'project-id', 'a6c76055-cbe1-408f-8d59-b78b70e4cf30', 'EUR'], [7.6651134159e-05, 'project-id', '050628-test', 'EUR'], [0.094541677834361, 'project-id', '051420-test', 'EUR'], [0.094541677834361, 'project-id', '050714-test', 'EUR'], [0.094545310695194, 'project-id', '051401-test', 'EUR'], [0.094981688825737, 'project-id', '050812-test', 'EUR'], [0.095018940381952, 'project-id', '051308-test', 'EUR'], [0.095032429452082, 'project-id', 'ada5fcdb-a482-46f8-bf31-c2815837e167', 'EUR'], [0.095066831713815, 'project-id', '051302-test', 'EUR'], [0.095100116062362, 'project-id', '050707-test', 'EUR'], [0.095110629749634, 'project-id', '050903-test', 'EUR'], [0.095211134979624, 'project-id', 'test-deployment-123', 'EUR'], [0.095224075847237, 'project-id', '050814-test', 'EUR'], [0.095301336911964, 'project-id', '050905-test', 'EUR'], [0.095456274185813, 'project-id', 'a9080f29-a5cf-4f78-acdb-9dc8b96ba56e', 'EUR'], [0.096538978024518, 'project-id', '051315-test', 'EUR'], [0.097339003844104, 'project-id', '050906-test', 'EUR'], [0.097393010550614, 'project-id', '624998cb-05cb-4c55-bf0e-161310012054', 'EUR'], [0.189086988529555, 'project-id', '051417-test', 'EUR'], [0.189086988529555, 'project-id', '051402-test', 'EUR'], [0.189090621390387, 'project-id', '050712-test', 'EUR'], [0.189537864212496, 'project-id', '050809-test', 'EUR'], [0.189634414682227, 'project-id', '051312-test', 'EUR'], [0.189638762422417, 'project-id', 'c4531f63-b3fd-4663-b766-93961d3d6ec2', 'EUR'], [0.189665303114381, 'project-id', '050705-test', 'EUR'], [0.189702836366324, 'project-id', 'da4b1905-0adf-4262-bc71-e64b97b2574e', 'EUR'], [0.18971637387206, 'project-id', '283bc414-e86a-435b-be46-eb8e548574ad', 'EUR'], [0.189719862025027, 'project-id', '051302-test-fix', 'EUR'], [0.189811409972435, 'project-id', '050706-test', 'EUR'], [0.18984401650167, 'project-id', '6683bca1-4bc6-4b78-a6b4-28e009478c70', 'EUR'], [0.18986100472439, 'project-id', 'ec9e1002-f490-490a-89f3-c9311845ba44', 'EUR'], [0.189922304717632, 'project-id', '050711-test', 'EUR'], [0.190018102156659, 'project-id', '29edc487-1c86-44aa-b183-c26c84eab470', 'EUR'], [0.190028005179321, 'project-id', '050901-test', 'EUR'], [0.190134729994913, 'project-id', '4b923af3-1bc5-407d-85ed-f6159ffd724c', 'EUR'], [0.190464121638883, 'project-id', '051212-test', 'EUR'], [0.19077950682205, 'project-id', 'bf425892-3591-4ab2-a6b8-ca03b5710b4c', 'EUR'], [0.19080741979343, 'project-id', 'a421ffcd-1692-4bc5-bd41-af3966571f8e', 'EUR'], [0.191166926280954, 'project-id', '051314-test', 'EUR'], [0.284038044976678, 'project-id', '7d5c2604-3770-48d7-a984-9298d85791ec', 'EUR'], [0.284045172979147, 'project-id', '051211-test', 'EUR'], [0.284131257351464, 'project-id', '050811-test', 'EUR'], [0.284172519901634, 'project-id', 'bae2df92-e9ae-4e68-89f9-30139c79c685', 'EUR'], [0.284189447472426, 'project-id', '050709-test', 'EUR'], [0.28425168345516, 'project-id', '910537a3-a201-45fb-a6c0-34ef8ce1cb7f', 'EUR'], [0.284316287159968, 'project-id', '051301-test-fix', 'EUR'], [0.28435960036981, 'project-id', '051311-test', 'EUR'], [0.28446766665339, 'project-id', '050806-test', 'EUR'], [0.284633609593372, 'project-id', '050704-test', 'EUR'], [0.284726165595058, 'project-id', '050710-test', 'EUR'], [0.285258360625258, 'project-id', '051310-test', 'EUR'], [0.28549764197637, 'project-id', '050808-test', 'EUR'], [0.378181242780773, 'project-id', '051419-test', 'EUR'], [0.378238922308361, 'project-id', '050805-test', 'EUR'], [0.378591268122477, 'project-id', '050708-test', 'EUR'], [0.37871499314971, 'project-id', '92f3bea8-f426-42c0-8b24-4ae42bad4a76', 'EUR'], [0.37872999538769, 'project-id', '050703-test', 'EUR'], [0.378844455937041, 'project-id', 'a67ef190-933c-4659-ace8-7d2a47729ddf', 'EUR'], [0.378927480287321, 'project-id', '77d970b7-81e7-49cd-8348-4c6ccb48fa5e', 'EUR'], [0.379015796919665, 'project-id', '051301-test', 'EUR'], [0.379160955397267, 'project-id', 'cb50ff0f-a19a-4d24-a9c1-e736a4450b3c', 'EUR'], [0.379482774128057, 'project-id', '795d29bb-fe07-4cc7-8d3a-eb43f1098643', 'EUR'], [0.380023808102955, 'project-id', '8c6b33bf-5e43-4717-bdc1-432bc28726e7', 'EUR'], [0.380947607242618, 'project-id', 'cfe84b0b-f916-4b23-b670-6f5844d2e5a7', 'EUR'], [0.472727360778375, 'project-id', '051418-test', 'EUR'], [0.472730589988004, 'project-id', '52f2db54-4cd1-448d-96e8-5ea383b3e7bc', 'EUR'], [0.472780877535233, 'project-id', '050701-test', 'EUR'], [0.47348847522005, 'project-id', '230629ee-8f4c-4d39-8114-b598b9e3c568', 'EUR'], [0.473506636681007, 'project-id', '7e2f5d4f-cf7b-4869-868f-45e887c7e192', 'EUR'], [0.473530791492509, 'project-id', '050702-test', 'EUR'], [0.473559227507043, 'project-id', '050804-test', 'EUR'], [0.473917995274125, 'project-id', '232a00e6-1051-4da0-9e35-b93ae6c4407d', 'EUR'], [0.475239837298787, 'project-id', '051208-test', 'EUR'], [0.475786885858849, 'project-id', 'a189f24c-6efa-43be-8f7e-3e2a6e2164fd', 'EUR'], [0.567790681359448, 'project-id', 'a1d64f7f-6904-411e-b5d5-480a7089f313', 'EUR'], [0.56796994747688, 'project-id', '050801-test', 'EUR'], [0.568021441387802, 'project-id', '050713-test', 'EUR'], [0.568674608066689, 'project-id', '050802-test', 'EUR'], [0.629115571464815, 'project-id', '050627-test', 'EUR'], [0.664486450402576, 'project-id', '51429d7d-327c-40d2-aa2b-006a4b846b3c', 'EUR'], [0.943637251711764, 'project-id', '050624-test', 'EUR'], [0.943637251711764, 'project-id', '050625-test', 'EUR'], [0.943645587525075, 'project-id', '050626-test', 'EUR'], [0.945420411204441, 'project-id', '051405-test', 'EUR'], [0.946099229669264, 'project-id', '4fe3c1a3-0ce7-4f21-b64f-b90d42099d3a', 'EUR'], [1.22904705931234, 'project-id', '051421-test', 'EUR'], [1.41819495657911, 'project-id', '050715-test', 'EUR'], [1.41884961494116, 'project-id', '050716-test', 'EUR'], [1.41896350810573, 'project-id', '050717-test', 'EUR'], [1.51408881699831, 'project-id', '8433e493-1376-4935-848f-4b984fe1397f', 'EUR'], [1.57267980490325, 'project-id', '050621-test2', 'EUR'], [1.57268814071656, 'project-id', '050622-test', 'EUR'], [1.57268814071656, 'project-id', '050623-test', 'EUR'], [1.57270320206979, 'project-id', '050621-test', 'EUR'], [1.58550194977009, 'project-id', 'e683261b-bfbd-4b4e-8838-3dfd77b3e397', 'EUR'], [1.60820237071455, 'project-id', '665b6522-2fb0-42a3-8e8e-edcd86ee440c', 'EUR'], [1.7024949334423, 'project-id', 'eacdb4d9-51ab-4c7b-b653-d95032d94a39', 'EUR'], [1.79711179618539, 'project-id', '050813-fix2-test', 'EUR'], [1.7971225840098, 'project-id', '050813-fix-test', 'EUR'], [1.88713613624974, 'project-id', '253e7429-03b5-49ba-957e-5859aec7aa46', 'EUR'], [1.89133333034763, 'project-id', '050813-test', 'EUR'], [1.89140778886213, 'project-id', '050629-test', 'EUR'], [1.9067787250346, 'project-id', '21b08f43-5306-49e5-85ba-bad78af72c41', 'EUR'], [1.98577046534494, 'project-id', '051605-test', 'EUR'], [1.98589317545841, 'project-id', 'c0ec1fa8-7b9f-4be5-a733-abd0b398d46b', 'EUR'], [1.98594255235311, 'project-id', '81f788f7-bf7a-4d28-9f94-fd880dc45861', 'EUR'], [1.98603726359959, 'project-id', '4458258f-46ad-42f8-9dd6-6f351d4a336c', 'EUR'], [2.17516218856494, 'project-id', 'f4e2fd3a-ce33-4965-87aa-343ac2d77da7', 'EUR'], [2.17527681895962, 'project-id', '0508-test-project-0805', 'EUR'], [2.17538502512914, 'project-id', '051601-test', 'EUR'], [2.175887539942, 'project-id', '050810-test', 'EUR'], [2.17645186266212, 'project-id', '051316-test', 'EUR'], [2.53518990599579, 'project-id', '17485152-ea1c-4385-bfcd-37f78b1146f2', 'EUR'], [2.84105272115707, 'project-id', '050612', 'EUR'], [2.8520116689281, 'project-id', '8840f20f-2213-4dc9-a9c4-4062358f8186', 'EUR'], [3.17002473531209, 'project-id', 'test-project', 'EUR'], [3.87699522832852, 'project-id', '0e4f8dc2-6d71-4931-a4c9-4b5b83b3eb39', 'EUR'], [4.25543346954284, 'project-id', '051206-test', 'EUR'], [4.2554350721545, 'project-id', '9e4f7374-6872-4507-98b1-c7890103039c', 'EUR'], [4.42906503029853, 'project-id', 'fecb2f28-06f5-4e86-a084-12f10ea9bb87', 'EUR'], [4.43492345026626, 'project-id', '6b53514a-1cde-4292-ad48-3e6c5a5cb0c0', 'EUR'], [4.44454769541689, 'project-id', '051520-test', 'EUR'], [4.53859876213635, 'project-id', 'ab55657c-eeca-4e85-b0c9-8722674bdfc8', 'EUR'], [4.63351404121295, 'project-id', '051514-test', 'EUR'], [5.01148431690418, 'project-id', '051521-test', 'EUR'], [5.99505613943272, 'project-id', 'b70a3ff5-ef8c-4df7-a549-8f307a888c08', 'EUR'], [6.0000679564187, 'project-id', '5ec6d026-66bb-4f2e-ba37-813dc8677b28', 'EUR'], [6.00014635712406, 'project-id', 'a7d2d11e-a66e-4392-9c07-e1646e299c20', 'EUR'], [6.05109619031858, 'project-id', 'e5747327-8bcb-4029-8900-990205c91fbc', 'EUR'], [6.31190938211924, 'project-id', 'f21c91e4-6e5c-4731-a982-6e17033af4ba', 'EUR'], [6.31953907992516, 'project-id', 'bdb36311-c696-4eb3-a25e-f68ec3b8863b', 'EUR'], [6.61246603270672, 'project-id', '2e69bae9-d0a8-4de2-89c9-7caf8bdbea0f', 'EUR'], [6.63053033848656, 'project-id', 'bcc3cc0e-b161-40f6-9127-af6ac14eb148', 'EUR'], [6.63444184079434, 'project-id', '1b726f9e-866f-4bb1-b1a2-23c568368449', 'EUR'], [6.93966295084879, 'project-id', '9c0f944b-ca13-4d70-a0f9-be74a515b44b', 'EUR'], [6.94345069732123, 'project-id', '4b981f00-0fbd-4d52-90c0-ef08811724e3', 'EUR'], [6.94654801840838, 'project-id', '296882d9-e444-4c81-ae23-78441de09ddd', 'EUR'], [7.24867834300848, 'project-id', '47917ed5-f83f-4bb3-b5a0-028b78717eff', 'EUR'], [7.56961044588782, 'project-id', '16cc9b6c-f166-49ee-9568-d4155571bf1a', 'EUR'], [7.94249979171117, 'project-id', '84ab5bb3-1cd3-4ac6-b9ef-a59b2a231d53', 'EUR'], [8.70005983186492, 'project-id', '5c4a937a-50b1-4509-8f2e-ed2c0ba57d60', 'EUR'], [9.46310143633803, 'project-id', 'e8898ebe-3e81-48a0-8f3f-49f7005af836', 'EUR'], [10.5890889915101, 'project-id', '050910-test', 'EUR'], [11.1574300872842, 'project-id', '050907-test', 'EUR'], [11.3463460459369, 'project-id', 'projectb', 'EUR'], [11.4406295790336, 'project-id', 'projecta', 'EUR'], [11.5346433568754, 'project-id', '050904-test', 'EUR'], [12.3855557720684, 'project-id', '87547c7e-011b-4366-83a2-f09ce43840ac', 'EUR'], [13.0907775531743, 'project-id', '343c7ea8-0ca5-473b-960d-4c3d74614bed', 'EUR'], [14.0873943745648, 'project-id', '8afc7f8f-2aa5-4d9d-ae13-8d307c64b2b4', 'EUR'], [14.2764594277897, 'project-id', 'd10589b5-9de3-488d-9bb0-77d08ed94474', 'EUR'], [14.2775316004166, 'project-id', 'bb4c80f7-aebd-4a25-94f6-a5dbe7913819', 'EUR'], [14.8438976335764, 'project-id', 'ee67a747-b953-44c7-bbb7-cf3c1db37dc2', 'EUR'], [14.8447448906724, 'project-id', 'fce0facf-1b4b-4973-a5c4-552a26b36f37', 'EUR'], [14.9431648470284, 'project-id', '585837de-b731-42b5-a8b3-8cc855fff198', 'EUR'], [15.0763844441692, 'project-id', '0336582f-072a-46c0-a44b-d177ac2e66fc', 'EUR'], [16.8290421225854, 'project-id', '050610', 'EUR'], [27.2366087456432, 'project-id', '051515-test', 'EUR'], [27.4254167525638, 'project-id', '051512-test', 'EUR'], [27.5116786636049, 'project-id', '051510-test', 'EUR'], [27.5207988660845, 'project-id', '051505-test1', 'EUR'], [27.6143631421493, 'project-id', '051505-test', 'EUR'], [28.1819469975799, 'project-id', '2baae374-b419-4c28-bdc6-04879e40b452', 'EUR'], [28.2740613473066, 'project-id', 'a70782ac-8957-4e59-b778-b74f86549cad', 'EUR'], [28.5575861396006, 'project-id', 'f529b9df-c309-40be-926d-07212d006bfd', 'EUR'], [29.4036804591471, 'project-id', 'd98606bd-cac7-4920-a9b8-4283bec5df11', 'EUR'], [29.4976735794732, 'project-id', '788a5827-a7ef-4f0a-a8b9-d5e17249ca00', 'EUR'], [34.2245965820849, 'project-id', '64ebae70-2b01-42d0-bb69-bc3efd936bc5', 'EUR'], [48.6674302098406, 'project-id', '35a7db33-b409-4873-b3bc-54e42931bd3d', 'EUR'], [49.2266034028918, 'project-id', 'uk001', 'EUR'], [57.5822573804444, 'project-id', '050813-fix3-test', 'EUR'], [488.150746417799, 'project-id', None, 'EUR']]}, use_cosmos_data: False
2025-06-20 12:10:05,737 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:05,737 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:05,785 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:05,786 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:05,847 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:05,847 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:07,026 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:07,026 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:15,742 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:15,742 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:15,788 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:15,789 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:15,851 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:15,852 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:17,032 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:17,032 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:25,747 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:25,748 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:25,790 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:25,790 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:25,854 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:25,855 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:27,037 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:10:27,038 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:10:27,360 - backend.deployments.service - INFO - Deployment worker started
2025-06-20 12:10:27,360 - backend.deployments.routes - INFO - Deployment service initialized
2025-06-20 12:10:27,360 - backend.deployments.service - INFO - Deployment queue processor started
2025-06-20 12:10:27,497 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/user-context/me
2025-06-20 12:10:27,497 - root - DEBUG - Routing HTTP request to RBAC FastAPI app: /api/user-context/me
2025-06-20 12:10:27,497 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:27,498 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:27,498 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:10:27,498 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:10:27,498 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=4'}
2025-06-20 12:10:27,498 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=4'}
2025-06-20 12:10:27,498 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:27,498 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:27,498 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:27,498 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:27,498 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:27,499 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:27,499 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:27,499 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:27,499 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:27,499 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:27,500 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:27,500 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:27,500 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:10:27,500 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:10:27,500 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:27,501 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:27,505 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:27,700 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:10:27,700 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:10:26 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:10:27,700 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:10:27,701 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:10:27,703 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:27,703 - rbac - INFO - RBAC client initialized successfully.
2025-06-20 12:10:27,702 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:10:27,705 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:27,706 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:10:27,705 - rbac - INFO - RBAC client initialized successfully.
2025-06-20 12:10:27,706 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=4'}
2025-06-20 12:10:27,706 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:27,706 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:27,706 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/user-context/me
2025-06-20 12:10:27,706 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:27,707 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'host': 'localhost:50505', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'accept': '*/*', 'accept-language': 'en-US,en;q=0.5', 'accept-encoding': 'gzip, deflate, br, zstd', 'referer': 'http://localhost:50505/', 'content-type': 'application/json', 'connection': 'keep-alive', 'sec-fetch-dest': 'empty', 'sec-fetch-mode': 'cors', 'sec-fetch-site': 'same-origin', 'priority': 'u=4'}
2025-06-20 12:10:27,707 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:27,707 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:27,707 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:27,707 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:27,707 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:27,707 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:27,708 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:27,708 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:27,708 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:27,708 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:27,709 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:27,709 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:27,709 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:27,709 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:27,709 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:27,709 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:27,709 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:27,709 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:27,709 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:27,709 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:27,710 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:27,710 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:27,710 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:27,710 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:27,711 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:27,711 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:27,803 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '693'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00002506-0000-0d00-0000-6810c43c0000"'
    'x-ms-schemaversion': 'REDACTED'
    'collection-partition-index': 'REDACTED'
    'collection-service-index': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-max-content-length': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:10:27,811 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:27,811 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:27,812 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:27,812 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:27,812 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:27,812 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:27,812 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:27,812 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:27,812 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:27,813 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:27,813 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:27,813 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:27,813 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:27,813 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:27,813 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:27,813 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:27,813 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:27,814 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:27,918 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:27,919 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:27,919 - root - INFO - Successfully authenticated user from token: Jean De Mevius (Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com)
2025-06-20 12:10:27,920 - root - INFO - Looking up user by email: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com (normalized: <EMAIL>)
2025-06-20 12:10:27,921 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:27,977 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:27,978 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:27,979 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:27,979 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:27,980 - root - INFO - Found user in database by normalized email: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T11:59:51.209694+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00001a96-0000-0d00-0000-68554d370000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750420791}
2025-06-20 12:10:27,980 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '205'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:27,980 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T11:59:51.209694+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00001a96-0000-0d00-0000-68554d370000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750420791}
2025-06-20 12:10:27,981 - root - INFO - Using role from database: SUPER_ADMIN
2025-06-20 12:10:27,981 - root - INFO - Authenticated user: Jean De Mevius (Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com)
2025-06-20 12:10:27,982 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:27,985 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:27,986 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:27,986 - root - INFO - Successfully authenticated user from token: Jean De Mevius (Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com)
2025-06-20 12:10:27,987 - root - INFO - Looking up user by email: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com (normalized: <EMAIL>)
2025-06-20 12:10:27,988 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,026 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00001a96-0000-0d00-0000-68554d370000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,026 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T11:59:51.209694+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00001a96-0000-0d00-0000-68554d370000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750420791}
2025-06-20 12:10:28,026 - root - INFO - Removing region from Super Admin user Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,027 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,036 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '13105'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,044 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,045 - root - INFO - Found user in database by normalized email: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T11:59:51.209694+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00001a96-0000-0d00-0000-68554d370000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750420791}
2025-06-20 12:10:28,046 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,046 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T11:59:51.209694+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00001a96-0000-0d00-0000-68554d370000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750420791}
2025-06-20 12:10:28,047 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,047 - root - INFO - Using role from database: SUPER_ADMIN
2025-06-20 12:10:28,047 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,047 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,047 - root - INFO - Authenticated user: Jean De Mevius (Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com)
2025-06-20 12:10:28,047 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,048 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,048 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,048 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,048 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,048 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,049 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,049 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,049 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,049 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,050 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,050 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,050 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,050 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,051 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,086 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00001a96-0000-0d00-0000-68554d370000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,087 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,092 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00001a96-0000-0d00-0000-68554d370000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,092 - root - INFO - Found user in database: {'id': '7e43ed9b-4998-468e-bcd1-9b78f7c82977', 'type': 'user', 'name': 'Jean De Mevius', 'email': '<EMAIL>', 'role': 'SUPER_ADMIN', 'region': None, 'avatar': 'https://ui-avatars.com/api/?name=Jean+De+Mevius&background=random', 'created_at': '2025-05-30T08:54:50.542205+00:00', 'updated_at': '2025-06-20T11:59:51.209694+00:00', '_rid': 'cVwbAKDuCuoxAAAAAAAAAA==', '_self': 'dbs/cVwbAA==/colls/cVwbAKDuCuo=/docs/cVwbAKDuCuoxAAAAAAAAAA==/', '_etag': '"00001a96-0000-0d00-0000-68554d370000"', '_attachments': 'attachments/', 'normalized_email': '<EMAIL>', '_ts': 1750420791}
2025-06-20 12:10:28,092 - root - INFO - Removing region from Super Admin user Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,092 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,120 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,120 - backend.deployments.service - INFO - Deployment worker started
2025-06-20 12:10:28,121 - backend.deployments.routes - INFO - Deployment service initialized
2025-06-20 12:10:28,121 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,121 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,121 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,121 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,121 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,121 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,121 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,121 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,121 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,122 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,122 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,122 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,122 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,122 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,122 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,122 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,122 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,123 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,120 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,123 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,123 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,123 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,123 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,123 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,123 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,123 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,123 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,124 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,124 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,124 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,124 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,124 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,124 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,124 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,124 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,161 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,161 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,161 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,161 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,161 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,161 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,161 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,161 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,161 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,161 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,162 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,162 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,162 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,162 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,162 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,162 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,162 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,163 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,203 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,204 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,204 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,205 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,257 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '575'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,258 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,268 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,268 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,269 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,269 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00001a96-0000-0d00-0000-68554d370000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,270 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,270 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,277 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,277 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,278 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,278 - backend.deployments.service - INFO - Deployment queue processor started
2025-06-20 12:10:28,278 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '673'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00006303-0000-0d00-0000-67fe64ae0000"'
    'x-ms-schemaversion': 'REDACTED'
    'collection-partition-index': 'REDACTED'
    'collection-service-index': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-max-content-length': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:10:28,279 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'PUT'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Content-Type': 'application/json'
    'Accept': 'application/json'
    'Content-Length': '577'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,280 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,301 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '646'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,301 - cosmos_db - INFO - Retrieved 1 messages for conversation b1fdd710-229c-463a-861c-2af8730fd920
2025-06-20 12:10:28,303 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,303 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,304 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,304 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,304 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,304 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,304 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,304 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,304 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,304 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,304 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,304 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,305 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,305 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,305 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,305 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,305 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,306 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,311 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,311 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,312 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,312 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,326 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00009896-0000-0d00-0000-68554fb40000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,327 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,368 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00009896-0000-0d00-0000-68554fb40000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,369 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,374 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '673'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00006303-0000-0d00-0000-67fe64ae0000"'
    'x-ms-schemaversion': 'REDACTED'
    'collection-partition-index': 'REDACTED'
    'collection-service-index': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-max-content-length': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:10:28,375 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'PUT'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Content-Type': 'application/json'
    'Accept': 'application/json'
    'Content-Length': '577'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,392 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '586'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,392 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '575'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,393 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,392 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,422 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00009996-0000-0d00-0000-68554fb40000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,423 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,431 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '582'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,431 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,437 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '66789'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,438 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,444 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,444 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '639'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,444 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,444 - cosmos_db - INFO - Retrieved 1 messages for conversation 729e39cb-2817-4178-8147-73c55b1312cd
2025-06-20 12:10:28,445 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,445 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,447 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,447 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,447 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,447 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,447 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,447 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,447 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,448 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,448 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,448 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,448 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,448 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,448 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,448 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,448 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,448 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,449 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,449 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,469 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '35478'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,470 - cosmos_db - INFO - Retrieved 6 messages for conversation a919b2c2-aa6f-44ce-948c-74fd3b5d03dc
2025-06-20 12:10:28,473 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,473 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,473 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,473 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,474 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,474 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,474 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,474 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,474 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,474 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,474 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,474 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,475 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,476 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '15767'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,476 - cosmos_db - INFO - Retrieved 3 messages for conversation e6a9f0ea-ea28-4e32-a606-c13b7b79fc4e
2025-06-20 12:10:28,479 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,479 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,479 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,479 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,479 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,479 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,479 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,480 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,480 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,480 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,483 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,483 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,483 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,484 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,484 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,484 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,484 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,485 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,487 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '574'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,488 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,533 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '15110'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,533 - cosmos_db - INFO - Retrieved 3 messages for conversation 46fb55c9-74d4-4efa-8d3a-867d9092bc93
2025-06-20 12:10:28,536 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,536 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,536 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,536 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,536 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,536 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,536 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,536 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,536 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,536 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,536 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,536 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,537 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,537 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,537 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,537 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,537 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,537 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,624 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,624 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,624 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,624 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,625 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,625 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,625 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,626 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,626 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00009996-0000-0d00-0000-68554fb40000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,626 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,626 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,627 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,628 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00009996-0000-0d00-0000-68554fb40000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,628 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,628 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/teams/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,629 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,673 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '580'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,673 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,675 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '569'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,675 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,682 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '569'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,683 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,694 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3758'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,694 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '27'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,721 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '20778'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,721 - cosmos_db - INFO - Retrieved 3 messages for conversation 50fafb3f-8d90-4943-997b-80c9b8972b4d
2025-06-20 12:10:28,725 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,725 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,725 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,725 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,726 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,726 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,727 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,727 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,727 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,736 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,736 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '20816'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,737 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,737 - cosmos_db - INFO - Retrieved 3 messages for conversation cc77f746-b4ab-4674-b03b-f586a8408c31
2025-06-20 12:10:28,737 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,738 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,739 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,740 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,740 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,740 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,740 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,740 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,740 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,740 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,740 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,740 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,741 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,741 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,741 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,741 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,741 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,741 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,741 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,742 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,747 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '45448'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,747 - cosmos_db - INFO - Retrieved 6 messages for conversation a996c895-903c-43b8-8eb1-122c3fd925a2
2025-06-20 12:10:28,751 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,751 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,751 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,752 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,752 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,752 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,752 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,752 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,752 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,752 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,752 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,753 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,753 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,753 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,754 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,782 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '575'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,782 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,837 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '20751'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,838 - cosmos_db - INFO - Retrieved 3 messages for conversation eec09e8b-b513-4ba7-af36-b33b240cee38
2025-06-20 12:10:28,841 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,841 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,841 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,841 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,841 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,841 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,841 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,841 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,841 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,842 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,842 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,842 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,842 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,842 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,842 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,842 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,842 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,843 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,890 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,890 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,890 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,890 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,891 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,892 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1517'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,892 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,893 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,891 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,893 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,893 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,893 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,893 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,894 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '66789'
    'Date': 'Fri, 20 Jun 2025 12:10:27 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,895 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:28,895 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,935 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00009996-0000-0d00-0000-68554fb40000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,935 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,937 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,938 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '573'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,938 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,938 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,938 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,938 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,952 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00009996-0000-0d00-0000-68554fb40000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,953 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/teams/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,979 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,982 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '4237'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,982 - cosmos_db - INFO - Retrieved 7 messages for conversation 331ed6de-5dd8-47cb-b424-8e3257386be6
2025-06-20 12:10:28,983 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '18414'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:28,984 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,984 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,984 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,984 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,984 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,985 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,985 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,985 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,985 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,985 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,985 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,986 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,986 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,986 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,986 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,986 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,986 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,986 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:28,986 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,984 - cosmos_db - INFO - Retrieved 3 messages for conversation b8535ea7-ed7d-418f-9966-f93a2a91aa39
2025-06-20 12:10:28,986 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,986 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:28,987 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,986 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,987 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,987 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,987 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,987 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,987 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,987 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:28,988 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,988 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:28,988 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:28,989 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,989 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:28,989 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:28,989 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:28,990 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:28,990 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:28,990 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:28,990 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:28,990 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:28,990 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:28,990 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:28,990 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:28,991 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:28,991 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:28,991 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:28,991 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:28,991 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:28,991 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,046 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '575'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,046 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,091 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '31955'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,091 - cosmos_db - INFO - Retrieved 3 messages for conversation 65ec5ca5-31be-4c78-aad8-8d558b4c121d
2025-06-20 12:10:29,094 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,094 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,095 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,095 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,095 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,095 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,095 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,095 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,095 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,095 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,095 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,095 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,095 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,096 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,096 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,096 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,150 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,150 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,151 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,151 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '61318'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,152 - cosmos_db - INFO - Retrieved 6 messages for conversation c5795c81-b117-43c9-829f-047d9c2d0b8e
2025-06-20 12:10:29,153 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,156 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,156 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,156 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,156 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,156 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,156 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,156 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,156 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,156 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,156 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,157 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,157 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,157 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,157 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,157 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,157 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,157 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,158 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,159 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,160 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,160 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,161 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,168 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,168 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,169 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,169 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3758'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,170 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '27'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,170 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,204 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '571'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,204 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,221 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '579'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,222 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,247 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,248 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,248 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,249 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,256 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '11917'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,256 - cosmos_db - INFO - Retrieved 6 messages for conversation b42c9903-3a77-4266-895e-7d53ade471f5
2025-06-20 12:10:29,259 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,259 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,259 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,260 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,260 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,260 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,260 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,260 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,260 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,260 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,260 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,260 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,260 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,260 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,261 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,261 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,261 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,261 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,264 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2031'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,265 - cosmos_db - INFO - Retrieved 3 messages for conversation 697abc27-40b3-4973-a08f-322137a24b92
2025-06-20 12:10:29,267 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,267 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,268 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,268 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,268 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,268 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,269 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,269 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,269 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,269 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,270 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,291 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '571'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,292 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,305 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,305 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,306 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,306 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '576'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,307 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,307 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,335 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2022'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,335 - cosmos_db - INFO - Retrieved 3 messages for conversation b202879d-be64-485d-84cf-2c271e3efeab
2025-06-20 12:10:29,337 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,337 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,338 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,338 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,338 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,338 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,338 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,338 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,338 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,338 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,338 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,338 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,339 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,339 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,339 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,339 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,339 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,339 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,358 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3815'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,358 - cosmos_db - INFO - Retrieved 6 messages for conversation 181a2685-3099-45da-830d-4fe9e3810259
2025-06-20 12:10:29,360 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,361 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,361 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,361 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,361 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,361 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,361 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,361 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,361 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,361 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,361 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,362 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,362 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,362 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,362 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,362 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,362 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,362 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,409 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,409 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,409 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,410 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1517'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,410 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/7e43ed9b-4998-468e-bcd1-9b78f7c82977/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:10:29,411 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,463 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '577'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"00009996-0000-0d00-0000-68554fb40000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,463 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '43'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,464 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '553'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,464 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,488 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,488 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,489 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,489 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,508 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6906'
    'Date': 'Fri, 20 Jun 2025 12:10:28 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,508 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,510 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '30178'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,511 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,511 - cosmos_db - INFO - Retrieved 4 messages for conversation 356083e2-06f2-4c9d-a588-5b8331d67631
2025-06-20 12:10:29,512 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,513 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,514 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,514 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,514 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,514 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,514 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,514 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,514 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,514 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,514 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,515 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,515 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,515 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,515 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,515 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,515 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,515 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,516 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,516 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,533 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '559'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,534 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,558 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '553'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,559 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,568 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '574'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,569 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,579 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '16692'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,579 - cosmos_db - INFO - Retrieved 3 messages for conversation 16a0af4f-8e50-445c-bd70-6618e14c3d9c
2025-06-20 12:10:29,582 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,582 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,582 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,582 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,583 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,583 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,583 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,583 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,583 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,583 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,583 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,583 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,584 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,584 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,584 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,584 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,584 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,584 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,603 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '16851'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,603 - cosmos_db - INFO - Retrieved 3 messages for conversation 9aaa4999-3e02-43b6-ad40-10ed1086df1d
2025-06-20 12:10:29,628 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '3996'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,628 - cosmos_db - INFO - Retrieved 6 messages for conversation 4ea2fe3b-e848-4b9b-b18a-b1d35e68cd2b
2025-06-20 12:10:29,656 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,657 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,658 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,659 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,659 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:10:29,659 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:10:29,659 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/******** Firefox/139.0', 'Accept': '*/*', 'Accept-Language': 'en-US,en;q=0.5', 'Accept-Encoding': 'gzip, deflate, br, zstd', 'Referer': 'http://localhost:50505/', 'Content-Type': 'application/json', 'Content-Length': '58', 'Origin': 'http://localhost:50505', 'Connection': 'keep-alive', 'Sec-Fetch-Dest': 'empty', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Site': 'same-origin', 'Priority': 'u=4'}
2025-06-20 12:10:29,659 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Authorization header found
2025-06-20 12:10:29,659 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: Bearer eyJ...pl0ETzHTGw (truncated, length: 2481)
2025-06-20 12:10:29,660 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Header split into 2 parts
2025-06-20 12:10:29,660 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token
2025-06-20 12:10:29,660 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token preview: eyJ0eXAiOi...pl0ETzHTGw (length: 2474)
2025-06-20 12:10:29,660 - root - INFO - ✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)
2025-06-20 12:10:29,660 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token header: {'typ': 'JWT', 'nonce': 'jB0P14KKiDVsUFKLC6ru5Bly0dIf1hGzsS6KveHXicI', 'alg': 'RS256', 'x5t': 'CNv0OI3RwqlHFEVnaoMAshCH2XE', 'kid': 'CNv0OI3RwqlHFEVnaoMAshCH2XE'}
2025-06-20 12:10:29,660 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: ['aud', 'iss', 'iat', 'nbf', 'exp', 'acct', 'acr', 'acrs', 'aio', 'altsecid', 'amr', 'app_displayname', 'appid', 'appidacr', 'email', 'idp', 'idtyp', 'ipaddr', 'name', 'oid', 'platf', 'puid', 'rh', 'scp', 'sid', 'sub', 'tenant_region_scope', 'tid', 'unique_name', 'uti', 'ver', 'wids', 'xms_ftd', 'xms_idrel', 'xms_st', 'xms_tcdt', 'xms_tdbr']
2025-06-20 12:10:29,660 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: 1750424608
2025-06-20 12:10:29,660 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token audience: 00000003-0000-0000-c000-000000000000
2025-06-20 12:10:29,660 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: https://sts.windows.net/ee78877a-c63a-405d-85d6-8914358aa533/
2025-06-20 12:10:29,660 - root - INFO - Calling Microsoft Graph API with delegated token
2025-06-20 12:10:29,660 - root - DEBUG - Token: eyJ0eXAiOi...pl0ETzHTGw (truncated)
2025-06-20 12:10:29,661 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): graph.microsoft.com:443
2025-06-20 12:10:29,732 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,733 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,733 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,734 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,776 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '556'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,777 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,816 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,817 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,818 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,819 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,820 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,822 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '18476'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,823 - cosmos_db - INFO - Retrieved 6 messages for conversation bbd8213a-7255-4482-95f3-a34245ea07b8
2025-06-20 12:10:29,863 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '547'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,863 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,909 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '14888'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,909 - cosmos_db - INFO - Retrieved 3 messages for conversation 53c56c18-5302-4565-b849-6354c99fb031
2025-06-20 12:10:29,948 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '551'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:29,949 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:29,963 - urllib3.connectionpool - DEBUG - https://graph.microsoft.com:443 "GET /v1.0/me HTTP/1.1" 200 None
2025-06-20 12:10:29,963 - root - INFO - Successfully retrieved user info from Graph API: Jean De Mevius
2025-06-20 12:10:29,965 - root - INFO - Successfully authenticated user: Jean.DeMevius_keyrus.com#EXT#@keyrusbelgium.onmicrosoft.com
2025-06-20 12:10:29,966 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '262'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:30,009 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '579'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:30,010 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/conversations_project/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '294'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:10:30,029 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '30008'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:30,029 - cosmos_db - INFO - Retrieved 6 messages for conversation ae5b3bf8-f3ba-447d-a09b-d3904c9179cc
2025-06-20 12:10:30,055 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '6001'
    'Date': 'Fri, 20 Jun 2025 12:10:29 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:10:30,056 - cosmos_db - INFO - Retrieved 9 messages for conversation bc76d819-3965-44e1-b214-b430a24f752a
2025-06-20 12:10:35,411 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:10:35,411 - root - INFO - Shutting down WebSocket service
2025-06-20 12:10:35,412 - root - INFO - Closing Cosmos DB client
2025-06-20 12:10:35,412 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:10:35,413 - root - INFO - Closing Storage Management client
2025-06-20 12:10:35,413 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:10:35,414 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:10:35,423 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:10:35,424 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:10:35,431 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:10:35,431 - root - INFO - Shutting down WebSocket service
2025-06-20 12:10:35,431 - root - INFO - Closing Cosmos DB client
2025-06-20 12:10:35,432 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:10:35,432 - root - INFO - Closing Storage Management client
2025-06-20 12:10:35,432 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:10:35,432 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:10:35,438 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:10:35,438 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:10:35,448 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:10:35,448 - root - INFO - Shutting down WebSocket service
2025-06-20 12:10:35,448 - root - INFO - Closing Cosmos DB client
2025-06-20 12:10:35,448 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:10:35,448 - root - INFO - Closing Storage Management client
2025-06-20 12:10:35,449 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:10:35,449 - root - INFO - Shutting down WebSocket service
2025-06-20 12:10:35,450 - root - INFO - Closing Cosmos DB client
2025-06-20 12:10:35,450 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:10:35,450 - root - INFO - Closing Storage Management client
2025-06-20 12:10:35,450 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:10:35,451 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:10:35,449 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:10:35,451 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:10:35,456 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:10:35,456 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:10:35,458 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:10:35,459 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:10:35,678 - root - INFO - Cleanup finished.
2025-06-20 12:10:35,690 - root - INFO - Cleanup finished.
2025-06-20 12:10:35,707 - root - INFO - Cleanup finished.
2025-06-20 12:10:35,709 - root - INFO - Cleanup finished.

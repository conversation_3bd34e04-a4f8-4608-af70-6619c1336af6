2025-06-20 12:20:40,173 - root - INFO - Detailed logs will be written to logs/deploy_project_resources_detailed_20250620_122040.log
2025-06-20 12:20:40,211 - root - WARNING - backend.deployments.deployment_status module not fully available. Background status checker will not be started.
2025-06-20 12:20:40,231 - root - INFO - Running in development mode; using null session backend
2025-06-20 12:20:40,255 - root - INFO - Running in development mode, using dummy credential for CosmosDB
2025-06-20 12:20:40,255 - cosmos_db - INFO - Found existing deployments container
2025-06-20 12:20:40,256 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:40,429 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '182'
    'Date': 'Fri, 20 Jun 2025 12:20:39 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'Etag': '"0000e805-0000-0d00-0000-66dc49840000"'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-item-lsn': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-item-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:20:40,429 - cosmos_db - INFO - Successfully connected to CosmosDB database db_conversation_history
2025-06-20 12:20:40,430 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-20 12:20:40,431 - httpx - DEBUG - load_verify_locations cafile='/workspaces/branch1/.venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-20 12:20:40,450 - root - INFO - Azure OpenAI client initialized
2025-06-20 12:20:40,450 - root - INFO - Found AZURE_SUBSCRIPTION_ID: 4c1c...93f9
2025-06-20 12:20:40,450 - root - INFO - Attempting to get DefaultAzureCredential for StorageManagementClient...
2025-06-20 12:20:40,450 - root - INFO - Running in development mode, skipping Azure credential check
2025-06-20 12:20:40,450 - root - INFO - Using DummyCredential for local development
2025-06-20 12:20:40,450 - root - INFO - DefaultAzureCredential obtained. Initializing StorageManagementClient...
2025-06-20 12:20:40,451 - root - INFO - Storage Management Client initialized successfully.
2025-06-20 12:20:40,451 - root - WARNING - AZURE_STORAGE_CONTAINER_SAS_TOKEN environment variable is not set. Blob operations requiring SAS may fail.
2025-06-20 12:20:40,451 - backend.web_sockets.index_blob_status - INFO - Initialized GLOBAL WebSocket config: Account=, Input=, Output=, Index=ai-scope-app3
2025-06-20 12:20:40,451 - backend.web_sockets.index_blob_status - INFO - Started WebSocket service update loop
2025-06-20 12:20:40,451 - root - INFO - Blob and index WebSocket service initialized
2025-06-20 12:20:40,452 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:20:40,452 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:20:50,453 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:20:50,454 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:20:56,308 - backend.deployments.service - INFO - Deployment worker started
2025-06-20 12:20:56,308 - backend.deployments.routes - INFO - Deployment service initialized
2025-06-20 12:20:56,308 - root - DEBUG - Auth headers (safe): {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json'}
2025-06-20 12:20:56,308 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request
2025-06-20 12:20:56,308 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Request method: GET, path: /api/cost
2025-06-20 12:20:56,309 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {'Remote-Addr': '127.0.0.1', 'Host': 'localhost:50505', 'User-Agent': 'curl/7.88.1', 'Accept': '*/*', 'Content-Type': 'application/json'}
2025-06-20 12:20:56,309 - root - WARNING - ❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request
2025-06-20 12:20:56,309 - root - INFO - 🔍 TOKEN_EXTRACTION_DEBUG: Available headers: ['Remote-Addr', 'Host', 'User-Agent', 'Accept', 'Content-Type']
2025-06-20 12:20:56,309 - root - WARNING - No authentication token found in request (checked Authorization and Easy Auth headers)
2025-06-20 12:20:56,309 - root - INFO - Development mode: Using mock user due to missing token
2025-06-20 12:20:56,310 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:56,310 - backend.deployments.service - INFO - Deployment queue processor started
2025-06-20 12:20:56,462 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3208'
    'Date': 'Fri, 20 Jun 2025 12:20:56 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:56,465 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/users/docs/11111111-1111-1111-1111-111111111111/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:56,513 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3210'
    'Date': 'Fri, 20 Jun 2025 12:20:56 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:56,514 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:56,609 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1762'
    'Date': 'Fri, 20 Jun 2025 12:20:56 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'x-ms-max-media-storage-usage-mb': 'REDACTED'
    'x-ms-media-storage-usage-mb': 'REDACTED'
    'x-ms-databaseaccount-consumed-mb': 'REDACTED'
    'x-ms-databaseaccount-reserved-mb': 'REDACTED'
    'x-ms-databaseaccount-provisioned-mb': 'REDACTED'
    'Strict-Transport-Security': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:20:56,610 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '386'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:20:56,844 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 400
Response headers:
    'Date': 'Fri, 20 Jun 2025 12:20:55 GMT'
    'Transfer-Encoding': 'chunked'
    'Content-Type': 'application/json'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-substatus': 'REDACTED'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
2025-06-20 12:20:56,851 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/json'
    'x-ms-session-token': 'REDACTED'
    'x-ms-cosmos-is-query-plan-request': 'REDACTED'
    'x-ms-cosmos-supported-query-features': 'REDACTED'
    'x-ms-cosmos-query-version': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '386'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:20:56,875 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '730'
    'Date': 'Fri, 20 Jun 2025 12:20:55 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
2025-06-20 12:20:56,878 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/pkranges/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:56,904 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '391'
    'Date': 'Fri, 20 Jun 2025 12:20:55 GMT'
    'Content-Type': 'application/json'
    'Server': 'Microsoft-HTTPAPI/2.0'
    'x-ms-gatewayversion': 'REDACTED'
    'Cache-Control': 'no-store, no-cache'
    'Pragma': 'no-cache'
    'Strict-Transport-Security': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
    'Content-Location': 'REDACTED'
2025-06-20 12:20:56,906 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:20:57,239 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2620346'
    'Date': 'Fri, 20 Jun 2025 12:20:56 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:57,256 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:20:57,450 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2586284'
    'Date': 'Fri, 20 Jun 2025 12:20:56 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:57,470 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:20:57,690 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2554576'
    'Date': 'Fri, 20 Jun 2025 12:20:56 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:57,791 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:20:58,040 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '2534066'
    'Date': 'Fri, 20 Jun 2025 12:20:56 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:58,063 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db-westeurope.documents.azure.com:443/dbs/db_conversation_history/colls/cost_data/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-consistency-level': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-max-item-count': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-continuation': 'REDACTED'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '373'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:20:58,239 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '1801428'
    'Date': 'Fri, 20 Jun 2025 12:20:57 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:58,256 - backend.cost_management.cost_api_quart - INFO - Retrieved 474 cost documents from Cosmos DB for time range: month
2025-06-20 12:20:58,257 - backend.cost_management.cost_api_quart - INFO - Using 474 cost documents from Cosmos DB
2025-06-20 12:20:58,276 - backend.cost_management.cost_api_quart - DEBUG - About to call get_project_names_map with 191 project IDs
2025-06-20 12:20:58,277 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/'
Request method: 'POST'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-isquery': 'REDACTED'
    'Content-Type': 'application/query+json'
    'x-ms-documentdb-query-enablecrosspartition': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'x-ms-cosmos-correlated-activityid': 'REDACTED'
    'Content-Length': '14172'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
A body is sent with the request
2025-06-20 12:20:58,325 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 200
Response headers:
    'Content-Length': '113'
    'Date': 'Fri, 20 Jun 2025 12:20:57 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-resource-quota': 'REDACTED'
    'x-ms-resource-usage': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-item-count': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-alt-content-path': 'REDACTED'
    'x-ms-content-path': 'REDACTED'
    'x-ms-quorum-acked-lsn': 'REDACTED'
    'x-ms-current-write-quorum': 'REDACTED'
    'x-ms-current-replica-set-size': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-cosmos-query-execution-info': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-cosmos-quorum-acked-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-is-partition-key-delete-pending': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:58,325 - backend.cost_management.cost_management_service - DEBUG - Retrieved names for 1 out of 191 projects
2025-06-20 12:20:58,325 - backend.cost_management.cost_api_quart - DEBUG - get_project_names_map completed successfully
2025-06-20 12:20:58,325 - backend.cost_management.cost_api_quart - DEBUG - project_costs length: 0, result: None, use_cosmos_data: True
2025-06-20 12:20:58,325 - backend.cost_management.cost_api_quart - INFO - Using mock cost data for development/fallback
2025-06-20 12:20:58,326 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/West%20US%202/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:58,385 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3202'
    'Date': 'Fri, 20 Jun 2025 12:20:58 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:58,386 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/East%20US/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:58,448 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3204'
    'Date': 'Fri, 20 Jun 2025 12:20:58 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:20:58,450 - azure.core.pipeline.policies.http_logging_policy - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/regions/docs/West%20Europe/'
Request method: 'GET'
Request headers:
    'Cache-Control': 'no-cache'
    'x-ms-version': 'REDACTED'
    'x-ms-documentdb-query-iscontinuationexpected': 'REDACTED'
    'x-ms-documentdb-partitionkey': 'REDACTED'
    'x-ms-date': 'REDACTED'
    'authorization': 'REDACTED'
    'Accept': 'application/json'
    'Content-Length': '0'
    'User-Agent': 'azsdk-python-cosmos-async/4.5.1 Python/3.10.17 (Linux-6.10.14-linuxkit-aarch64-with-glibc2.36)'
No body was attached to the request
2025-06-20 12:20:58,500 - azure.core.pipeline.policies.http_logging_policy - INFO - Response status: 404
Response headers:
    'Content-Length': '3203'
    'Date': 'Fri, 20 Jun 2025 12:20:58 GMT'
    'Content-Type': 'application/json'
    'Server': 'Compute'
    'x-ms-gatewayversion': 'REDACTED'
    'x-ms-activity-id': 'REDACTED'
    'x-ms-last-state-change-utc': 'REDACTED'
    'x-ms-schemaversion': 'REDACTED'
    'lsn': 'REDACTED'
    'x-ms-request-charge': 'REDACTED'
    'x-ms-documentdb-partitionkeyrangeid': 'REDACTED'
    'x-ms-xp-role': 'REDACTED'
    'x-ms-global-Committed-lsn': 'REDACTED'
    'x-ms-number-of-read-regions': 'REDACTED'
    'x-ms-transport-request-id': 'REDACTED'
    'x-ms-cosmos-llsn': 'REDACTED'
    'x-ms-session-token': 'REDACTED'
    'x-ms-request-duration-ms': 'REDACTED'
    'x-ms-serviceversion': 'REDACTED'
    'x-ms-cosmos-physical-partition-id': 'REDACTED'
    'x-ms-cosmos-internal-partition-id': 'REDACTED'
2025-06-20 12:21:00,458 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:21:00,459 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:21:10,460 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:21:10,461 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:21:20,463 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:21:20,463 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:21:30,468 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:21:30,468 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:21:40,478 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:21:40,485 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:21:50,491 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:21:50,491 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:22:00,494 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:22:00,495 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:22:10,498 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:22:10,499 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:22:20,500 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:22:20,501 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:22:30,502 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:22:30,503 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:22:40,504 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:22:40,504 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:22:50,507 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:22:50,508 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:23:00,509 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:23:00,509 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:23:10,511 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:23:10,512 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:23:20,516 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:23:20,517 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:23:30,517 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:23:30,517 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:23:40,517 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:23:40,518 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:23:50,526 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:23:50,527 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:24:00,532 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:24:00,533 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:24:10,533 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:24:10,534 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:24:20,535 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:24:20,536 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:24:30,538 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:24:30,539 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:24:40,542 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:24:40,543 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:24:50,543 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:24:50,545 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:25:00,547 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:25:00,548 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:25:10,551 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:25:10,552 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:25:20,557 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:25:20,558 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:25:30,563 - backend.web_sockets.index_blob_status - DEBUG - Active projects with WebSocket connections: set()
2025-06-20 12:25:30,564 - backend.web_sockets.index_blob_status - DEBUG - No active projects with connections found to update.
2025-06-20 12:25:34,100 - root - INFO - Initiating resource cleanup on shutdown
2025-06-20 12:25:34,100 - root - INFO - Shutting down WebSocket service
2025-06-20 12:25:34,100 - root - INFO - Closing Cosmos DB client
2025-06-20 12:25:34,100 - root - INFO - Closing Azure OpenAI client
2025-06-20 12:25:34,100 - root - INFO - Closing Storage Management client
2025-06-20 12:25:34,100 - root - INFO - Gathering 4 cleanup tasks.
2025-06-20 12:25:34,101 - backend.web_sockets.index_blob_status - INFO - Shutting down WebSocket service. Closing 0 connections.
2025-06-20 12:25:34,103 - backend.web_sockets.index_blob_status - INFO - WebSocket service update loop cancelled.
2025-06-20 12:25:34,104 - backend.web_sockets.index_blob_status - INFO - WebSocket service shutdown complete.
2025-06-20 12:25:34,354 - root - INFO - Cleanup finished.

# Virtual environments
.venv/
venv/

# Node modules
frontend/node_modules/


# Python cache files
__pycache__/
*.py[cod]

# IDE and editor files

*.swp
*.swo
.idea/

# OS generated files
.DS_Store
Thumbs.db

# Azure-related files
.azure/
# .devcontainer/ is now tracked
.devcontainer/devcontainer.json

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# Build outputs
static/assets/
*.map

# Temporary files
*.tmp
*.bak

# Zone.Identifier files (often created when downloading files on Windows)
*:Zone.Identifier

# Keep static directory, but ignore its contents
static/*
!static/.gitkeep

# Ignore pycache in all directories
**/__pycache__/

# Ignore dist directories (common for frontend builds)
**/dist/

# Ignore test coverage reports
coverage/
.coverage

# Ignore package-lock.json for npm
package-lock.json

# Ignore compiled Python files
*.pyc

# Ignore local development configuration files
*.local

# Ignore any secrets or sensitive files
*.pem
*.key
setup_ssh_agent.sh
~/.ssh/
~/.ssh/config
# SSH-related files
**/ssh-setup.sh
**/restore-ssh-keys.sh
**/known_hosts
**/ssh-backup/
setup-ssh.sh
.devcontainer/devcontainer.json



.github/
.git/
.vscode/
.idea/


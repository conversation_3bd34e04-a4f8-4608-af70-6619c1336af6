#!/usr/bin/env python3
"""
Fix deployment for project 06170713 that shows as completed but missing function app
"""

import asyncio
import os
import sys
import subprocess
import logging
from backend.services.project_service import ProjectDataService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_project_deployment():
    project_id = "7a712b78-2840-4ac4-b61d-52fe5544df6c"
    project_name = "06170713"
    
    # Initialize project service
    project_service = ProjectDataService()
    
    # Get current project data
    logger.info(f"Fetching project {project_id} from Cosmos DB...")
    project = await project_service.get_project(project_id)
    
    if not project:
        logger.error(f"Project {project_id} not found!")
        return
    
    logger.info(f"Current function app name: {project.get('function_app_name', 'Not set')}")
    
    # Check if function app actually exists in Azure
    function_app_name = f"func-06170713-5wmx"  # Expected name based on pattern
    resource_group = "rg-internal-ai"
    
    logger.info(f"Checking if function app {function_app_name} exists in Azure...")
    cmd = [
        "az", "functionapp", "show",
        "--name", function_app_name,
        "--resource-group", resource_group,
        "--query", "name",
        "-o", "tsv"
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0 and result.stdout.strip():
        logger.info(f"Function app {function_app_name} exists in Azure!")
        
        # Update Cosmos DB with function app details
        logger.info("Updating Cosmos DB with function app details...")
        
        update_data = {
            "function_app_name": function_app_name,
            "function_app_url": f"https://{function_app_name}.azurewebsites.net",
            "function_names": ["maturity_assessment", "executive_summary"]
        }
        
        # Update the project
        project.update(update_data)
        
        # Also update deployment status details
        if "deployment_status" in project and "details" in project["deployment_status"]:
            project["deployment_status"]["details"]["function"]["function_app"] = True
            project["deployment_status"]["details"]["function"]["maturity_assessment"] = True
            project["deployment_status"]["details"]["function"]["executive_summary"] = True
            project["deployment_status"]["details"]["function_complete"] = True
            project["deployment_status"]["details"]["overall_complete"] = True
            project["deployment_status"]["details"]["completion_percentage"] = 100
        
        success = await project_service.update_project(project_id, project)
        
        if success:
            logger.info("Successfully updated project in Cosmos DB!")
        else:
            logger.error("Failed to update project in Cosmos DB!")
    else:
        logger.warning(f"Function app {function_app_name} not found in Azure")
        logger.info("The function app needs to be deployed first")
        
        # You would need to run the deployment here
        # For now, just log what needs to be done
        logger.info("To deploy the function app, run:")
        logger.info(f"./scripts/ACR_deployment/deploy_function_app_from_acr.sh")

if __name__ == "__main__":
    asyncio.run(fix_project_deployment())
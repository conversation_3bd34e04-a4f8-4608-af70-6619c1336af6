#!/usr/bin/env python3
"""
Script to check if the API server is running on the expected port.
If not, it will start the server.
"""

import os
import sys
import time
import logging
import subprocess
import requests
import signal
import atexit

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Default API port
DEFAULT_API_PORT = 50505

def is_server_running(port=DEFAULT_API_PORT):
    """Check if the API server is running on the specified port."""
    try:
        response = requests.get(f"http://localhost:{port}", timeout=2)
        return True
    except requests.exceptions.ConnectionError:
        return False
    except Exception as e:
        logger.error(f"Error checking if server is running: {e}")
        return False

def start_api_server(port=DEFAULT_API_PORT):
    """Start the API server on the specified port."""
    try:
        # Get the current directory
        current_dir = os.getcwd()
        
        # Command to start the server
        cmd = [
            sys.executable,
            "-m",
            "quart",
            "run",
            "--port",
            str(port),
            "--host",
            "0.0.0.0"
        ]
        
        # Start the server as a subprocess
        logger.info(f"Starting API server on port {port}...")
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=current_dir
        )
        
        # Register a function to kill the process when the script exits
        def cleanup():
            if process.poll() is None:
                logger.info("Terminating API server...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("API server did not terminate gracefully, killing...")
                    process.kill()
        
        atexit.register(cleanup)
        
        # Wait for the server to start
        max_retries = 10
        retry_delay = 1
        for i in range(max_retries):
            if is_server_running(port):
                logger.info(f"API server is now running on port {port}")
                return True
            
            logger.info(f"Waiting for API server to start (attempt {i+1}/{max_retries})...")
            time.sleep(retry_delay)
        
        logger.error(f"API server failed to start after {max_retries} attempts")
        return False
    
    except Exception as e:
        logger.error(f"Error starting API server: {e}")
        return False

def main():
    """Main entry point for the script."""
    # Get the port from environment variable or use default
    port = int(os.environ.get("API_PORT", DEFAULT_API_PORT))
    
    # Check if the server is already running
    if is_server_running(port):
        logger.info(f"API server is already running on port {port}")
        return 0
    
    # Start the server
    if start_api_server(port):
        logger.info(f"API server started successfully on port {port}")
        return 0
    else:
        logger.error(f"Failed to start API server on port {port}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

import sys
import types
import json
import uuid
from datetime import datetime, timezone
import importlib
import asyncio
import pytest

pytest.skip("skipping integration style tests in limited environment", allow_module_level=True)


def test_generate_deployment_summary_continues_on_key_failure(monkeypatch):
    # Stub external modules required during import
    dummy_mod = types.SimpleNamespace()
    class DummyResponse:
        def __init__(self, status=200):
            self.status = status
        async def __aenter__(self):
            return self
        async def __aexit__(self, exc_type, exc, tb):
            pass

    class DummySession:
        async def __aenter__(self):
            return self
        async def __aexit__(self, exc_type, exc, tb):
            pass
        def get(self, *a, **k):
            return DummyResponse()

    monkeypatch.setitem(sys.modules, "aiohttp", types.SimpleNamespace(ClientSession=DummySession))
    monkeypatch.setitem(sys.modules, "requests", dummy_mod)
    monkeypatch.setitem(sys.modules, "dotenv", types.SimpleNamespace(load_dotenv=lambda *a, **k: None))

    azure_pkg = types.ModuleType("azure")
    identity_pkg = types.ModuleType("azure.identity")
    identity_pkg.DefaultAzureCredential = object
    mgmt_pkg = types.ModuleType("azure.mgmt")
    resource_pkg = types.ModuleType("azure.mgmt.resource")
    resource_pkg.ResourceManagementClient = object
    storage_pkg = types.ModuleType("azure.mgmt.storage")
    storage_pkg.StorageManagementClient = object
    web_pkg = types.ModuleType("azure.mgmt.web")
    web_pkg.WebSiteManagementClient = object
    storage_models_pkg = types.ModuleType("azure.mgmt.storage.models")
    storage_models_pkg.StorageAccountCreateParameters = object
    storage_models_pkg.Sku = object
    storage_models_pkg.SkuName = object
    storage_models_pkg.Kind = object
    storage_models_pkg.Identity = object
    storage_models_pkg.StorageAccountUpdateParameters = object
    resources_pkg = types.ModuleType("azure.mgmt.resource.resources")
    resources_models_pkg = types.ModuleType("azure.mgmt.resource.resources.models")
    resources_models_pkg.DeploymentMode = object
    resources_models_pkg.Deployment = object
    resources_models_pkg.DeploymentProperties = object
    resources_models_pkg.TemplateLink = object
    resources_models_pkg.DeploymentWhatIf = object
    resources_models_pkg.DeploymentWhatIfProperties = object

    azure_pkg.identity = identity_pkg
    azure_pkg.mgmt = mgmt_pkg
    mgmt_pkg.resource = resource_pkg
    mgmt_pkg.storage = storage_pkg
    mgmt_pkg.web = web_pkg
    resource_pkg.resources = resources_pkg
    storage_pkg.models = storage_models_pkg
    resources_pkg.models = resources_models_pkg

    monkeypatch.setitem(sys.modules, "azure", azure_pkg)
    monkeypatch.setitem(sys.modules, "azure.identity", identity_pkg)
    monkeypatch.setitem(sys.modules, "azure.mgmt", mgmt_pkg)
    monkeypatch.setitem(sys.modules, "azure.mgmt.resource", resource_pkg)
    monkeypatch.setitem(sys.modules, "azure.mgmt.storage", storage_pkg)
    monkeypatch.setitem(sys.modules, "azure.mgmt.web", web_pkg)
    monkeypatch.setitem(sys.modules, "azure.mgmt.storage.models", storage_models_pkg)
    monkeypatch.setitem(sys.modules, "azure.mgmt.resource.resources", resources_pkg)
    monkeypatch.setitem(sys.modules, "azure.mgmt.resource.resources.models", resources_models_pkg)
    monkeypatch.setitem(sys.modules, "azure.core.exceptions", types.SimpleNamespace(ResourceNotFoundError=Exception))

    # Stub backend modules used during import
    dep_status_mod = types.ModuleType("backend.deployments.deployment_status")
    dep_status_mod.DeploymentSummary = object
    dep_status_mod.update_project_deployment_status = lambda *a, **k: None
    rbac_mod = types.ModuleType("backend.rbac.rbac_routes")
    async def dummy_create_project(data):
        return True
    rbac_mod.rbac_client = types.SimpleNamespace(create_project=dummy_create_project)
    rbac_mod.create_project = dummy_create_project
    monkeypatch.setitem(sys.modules, "backend.deployments.deployment_status", dep_status_mod)
    monkeypatch.setitem(sys.modules, "backend.rbac.rbac_routes", rbac_mod)

    deploy_project_resources = importlib.import_module("deploy_project_resources")

    class DummyProcess:
        def __init__(self, returncode=0, stdout="", stderr=""):
            self.returncode = returncode
            self.stdout = stdout
            self.stderr = stderr

    def fake_run(cmd, *args, **kwargs):
        cmd_str = " ".join(cmd) if isinstance(cmd, list) else cmd
        if "functionapp" in cmd_str and "keys" in cmd_str:
            return DummyProcess(returncode=1, stdout="", stderr="error")
        elif "storage" in cmd_str and "generate-sas" in cmd_str:
            return DummyProcess(returncode=0, stdout="sas-token", stderr="")
        return DummyProcess(returncode=0, stdout="", stderr="")

    updated = {"called": False}

    async def fake_update_project_resources(project_id, resources, api_url=""):
        return True

    def fake_update_project_with_deployment(project_id, summary):
        updated["called"] = True
        return True

    def fake_create_project(data):
        return True

    monkeypatch.setattr(deploy_project_resources.subprocess, "run", fake_run)
    monkeypatch.setattr(deploy_project_resources, "update_project_resources", fake_update_project_resources)
    monkeypatch.setattr(deploy_project_resources, "update_project_with_deployment", fake_update_project_with_deployment)
    monkeypatch.setattr(deploy_project_resources, "create_project", fake_create_project)
    monkeypatch.setattr(deploy_project_resources, "update_project_available", True)
    monkeypatch.setattr(deploy_project_resources, "create_project_available", True)

    start = datetime.now(timezone.utc)

    summary_file = asyncio.run(
        deploy_project_resources.generate_deployment_summary(
        project_id=str(uuid.uuid4()),
        project_name="testproj",
        region_id="westeurope",
        resource_group="rg",
        start_time=start,
        resource_data={},
        main_bicep_outputs={"resources": {"function_app_name": "func-test"}},
        resource_durations={},
        status="success",
        auto_update_project=False,
        api_url="http://localhost",
        )
    )

    assert summary_file is not None
    with open(summary_file, "r") as f:
        summary = json.load(f)

    resources = summary.get("resources", {})
    assert resources.get("function_key_maturity") is None
    assert updated["called"]

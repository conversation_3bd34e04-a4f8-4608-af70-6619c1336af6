import os
import asyncio
import types
from datetime import datetime, timezone

import pytest

from backend.services.project_service import ProjectDataService

class DummyAsyncIterator:
    def __init__(self, items):
        self._items = list(items)
    def __aiter__(self):
        self._idx = 0
        return self
    async def __anext__(self):
        if self._idx < len(self._items):
            item = self._items[self._idx]
            self._idx += 1
            return item
        raise StopAsyncIteration

class MockContainer:
    def __init__(self, items=None):
        self.items = list(items or [])
        self.replaced = []
    def query_items(self, *args, **kwargs):
        return DummyAsyncIterator(self.items)
    async def replace_item(self, item, body, partition_key):
        self.replaced.append((item, body, partition_key))

class MockDatabase:
    def __init__(self, container):
        self.container = container
    def get_container_client(self, name):
        return self.container

class MockCosmosClient:
    def __init__(self, db):
        self.db = db
    def get_database_client(self, name):
        return self.db

@pytest.fixture
def service(monkeypatch):
    container = MockContainer([
        {"id": "proj1", "region": "west"}
    ])
    db = MockDatabase(container)
    client = MockCosmosClient(db)
    monkeypatch.setattr('backend.services.project_service.get_cosmos_client', lambda: client)
    os.environ['AZURE_COSMOSDB_DATABASE'] = 'db'
    os.environ['AZURE_COSMOSDB_PROJECTS_CONTAINER'] = 'projects'
    return ProjectDataService()

@pytest.mark.asyncio
async def test_get_project_found(service):
    project = await service.get_project('proj1')
    assert project == {"id": "proj1", "region": "west"}

@pytest.mark.asyncio
async def test_update_project_success(service):
    project_data = {"id": "proj1", "region": "west", "foo": "bar"}
    assert await service.update_project('proj1', project_data)
    container = service.container
    assert container.replaced[0][1]["foo"] == "bar"

@pytest.mark.asyncio
async def test_update_project_missing_region(service):
    project_data = {"id": "proj1"}
    assert not await service.update_project('proj1', project_data)

@pytest.mark.asyncio
async def test_update_deployment_status(service):
    ok = await service.update_deployment_status('proj1', 'running', 'msg')
    assert ok
    replaced = service.container.replaced[-1][1]
    assert replaced['deployment_status']['status'] == 'running'
    assert replaced['deployment_status']['message'] == 'msg'

@pytest.mark.asyncio
async def test_update_final_deployment_summary(service):
    summary = {
        'resources': {'storage': 'st1'},
        'environment': {'ENV_VAR': '1'},
        'status': 'success',
        'deployment_time': 1.2,
        'resource_durations': {'foo': 1}
    }
    ok = await service.update_final_deployment_summary('proj1', summary)
    assert ok
    replaced = service.container.replaced[-1][1]
    assert replaced['storage'] == 'st1'
    assert replaced['environment']['ENV_VAR'] == '1'
    assert replaced['deployment_status']['status'] == 'success'

import types
import pytest
from types import SimpleNamespace
from unittest.mock import AsyncMock, patch

import importlib.util
from pathlib import Path

ROOT = Path(__file__).resolve().parents[1]
COST_API_QUART_PATH = ROOT / "backend" / "cost_management" / "cost_api_quart.py"

spec = importlib.util.spec_from_file_location("cost_api_quart", COST_API_QUART_PATH)
cost_api = importlib.util.module_from_spec(spec)
spec.loader.exec_module(cost_api)


class MockRequest:
    def __init__(self, headers=None, args=None):
        self.headers = headers or {}
        self.args = args or {}

    async def get_json(self):
        return {}


@pytest.mark.asyncio
async def test_overview_with_minimal_token_user():
    user = {"user_principal_id": "u1", "roles": ["super_admin"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[[100, "project-id", "1", "EUR"]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
    )
    patched_cost_service = SimpleNamespace(
        query_cost_by_tag=AsyncMock(return_value=mock_rows),
        get_project_names_map=AsyncMock(return_value={}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(cost_api, "cost_service", patched_cost_service), patch.object(
        cost_api, "get_latest_cost_data_from_cosmos", AsyncMock(return_value=None)
    ):
        result = await cost_api.get_cost_overview()
        assert result["totalCost"] == 100.0


@pytest.mark.asyncio
async def test_resources_with_minimal_token_user():
    user = {"user_principal_id": "u1", "roles": ["super_admin"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[[42, "/subscriptions/sub/resourceGroups/rg/providers/Microsoft.Storage/storageAccounts/res1", "EUR"]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api,
        "cost_service",
        SimpleNamespace(query_cost_by_resource=AsyncMock(return_value=mock_rows)),
    ):
        result = await cost_api.get_resource_costs()
        assert result["totalCost"] == 42


@pytest.mark.asyncio
async def test_collect_now_endpoint():
    user = {"user_principal_id": "u1", "roles": ["SuperAdmin"]}
    request = MockRequest(headers={}, args={})
    rbac_client = SimpleNamespace(
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"})
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(cost_api, "run_cost_collection_task", AsyncMock()):
        result = await cost_api.collect_cost_now()
        assert result["message"] == "Cost data collection triggered"


@pytest.mark.asyncio
async def test_role_overridden_from_db():
    token_user = {"user_principal_id": "u1", "roles": ["RegularUser"]}
    request = MockRequest(headers={}, args={})
    mock_rows = SimpleNamespace(rows=[[100, "project-id", "1", "EUR"]])
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
    )
    patched_cost_service = SimpleNamespace(
        query_cost_by_tag=AsyncMock(return_value=mock_rows),
        get_project_names_map=AsyncMock(return_value={}),
    )
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=token_user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(cost_api, "cost_service", patched_cost_service), patch.object(
        cost_api, "get_latest_cost_data_from_cosmos", AsyncMock(return_value=None)
    ):
        result = await cost_api.get_cost_overview()
        assert result["totalCost"] == 100.0
        assert rbac_client.get_user.awaited


@pytest.mark.asyncio
async def test_get_cost_overview_7_days():
    """Test that 7-day time range aggregates costs from multiple documents."""
    user = {"user_principal_id": "u1", "roles": ["super_admin"]}
    request = MockRequest(headers={}, args={"timeRange": "7d"})
    
    # Mock multiple cost documents from the last 7 days
    mock_cosmos_data = [
        {
            "id": "cost-2025-06-20T12:00:00",
            "timestamp": "2025-06-20T12:00:00Z",
            "projectCosts": [
                {"projectId": "project1", "cost": 100.0},
                {"projectId": "project2", "cost": 200.0}
            ]
        },
        {
            "id": "cost-2025-06-19T12:00:00",
            "timestamp": "2025-06-19T12:00:00Z",
            "projectCosts": [
                {"projectId": "project1", "cost": 150.0},
                {"projectId": "project2", "cost": 250.0}
            ]
        },
        {
            "id": "cost-2025-06-18T12:00:00",
            "timestamp": "2025-06-18T12:00:00Z",
            "projectCosts": [
                {"projectId": "project1", "cost": 120.0},
                {"projectId": "project2", "cost": 180.0}
            ]
        }
    ]
    
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[
            {"id": "project1", "name": "Project 1", "region": "westus2", "cost_limit": 1000.0},
            {"id": "project2", "name": "Project 2", "region": "eastus", "cost_limit": 2000.0}
        ]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
        get_region=AsyncMock(return_value={"name": "West US 2", "cost_limit": 5000.0})
    )
    
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api, "get_latest_cost_data_from_cosmos", AsyncMock(return_value=(mock_cosmos_data, None))
    ):
        result = await cost_api.get_cost_overview()
        
        # Expected: costs should be aggregated across all documents
        # project1: 100 + 150 + 120 = 370
        # project2: 200 + 250 + 180 = 630
        # total: 370 + 630 = 1000
        assert result["totalCost"] == 1000.0
        assert len(result["projectCosts"]) == 2
        
        project1_cost = next(p for p in result["projectCosts"] if p["projectId"] == "project1")
        project2_cost = next(p for p in result["projectCosts"] if p["projectId"] == "project2")
        
        assert project1_cost["cost"] == 370.0
        assert project2_cost["cost"] == 630.0


@pytest.mark.asyncio
async def test_get_cost_overview_30_days():
    """Test that 30-day time range aggregates costs correctly."""
    user = {"user_principal_id": "u1", "roles": ["super_admin"]}
    request = MockRequest(headers={}, args={"timeRange": "30d"})
    
    # Mock cost documents from the last 30 days
    mock_cosmos_data = [
        {
            "id": f"cost-2025-06-{day:02d}T12:00:00",
            "timestamp": f"2025-06-{day:02d}T12:00:00Z",
            "projectCosts": [
                {"projectId": "project1", "cost": 50.0 * day},
                {"projectId": "project2", "cost": 75.0 * day}
            ]
        }
        for day in range(1, 11)  # 10 days of data
    ]
    
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[
            {"id": "project1", "name": "Project 1", "region": "westus2", "cost_limit": 10000.0},
            {"id": "project2", "name": "Project 2", "region": "eastus", "cost_limit": 15000.0}
        ]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
        get_region=AsyncMock(return_value={"name": "West US 2", "cost_limit": 50000.0})
    )
    
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api, "get_latest_cost_data_from_cosmos", AsyncMock(return_value=(mock_cosmos_data, None))
    ):
        result = await cost_api.get_cost_overview()
        
        # Expected: aggregate all daily costs
        # project1: sum(50 * day for day in 1-10) = 50 * 55 = 2750
        # project2: sum(75 * day for day in 1-10) = 75 * 55 = 4125
        # total: 2750 + 4125 = 6875
        assert result["totalCost"] == 6875.0


@pytest.mark.asyncio
async def test_get_cost_overview_single_project():
    """Test that project filtering returns only the selected project's costs."""
    user = {"user_principal_id": "u1", "roles": ["super_admin"]}
    request = MockRequest(headers={}, args={"timeRange": "7d", "projectId": "project1"})
    
    # Mock multiple cost documents
    mock_cosmos_data = [
        {
            "id": "cost-2025-06-20T12:00:00",
            "timestamp": "2025-06-20T12:00:00Z",
            "projectCosts": [
                {"projectId": "project1", "cost": 100.0},
                {"projectId": "project2", "cost": 200.0},
                {"projectId": "project3", "cost": 300.0}
            ]
        },
        {
            "id": "cost-2025-06-19T12:00:00",
            "timestamp": "2025-06-19T12:00:00Z",
            "projectCosts": [
                {"projectId": "project1", "cost": 150.0},
                {"projectId": "project2", "cost": 250.0},
                {"projectId": "project3", "cost": 350.0}
            ]
        }
    ]
    
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[
            {"id": "project1", "name": "Project 1", "region": "westus2", "cost_limit": 1000.0},
            {"id": "project2", "name": "Project 2", "region": "eastus", "cost_limit": 2000.0},
            {"id": "project3", "name": "Project 3", "region": "westus2", "cost_limit": 3000.0}
        ]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
        get_region=AsyncMock(return_value={"name": "West US 2", "cost_limit": 5000.0})
    )
    
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api, "get_latest_cost_data_from_cosmos", AsyncMock(return_value=(mock_cosmos_data, None))
    ), patch.object(
        cost_api.cost_service, "get_project_names_map", AsyncMock(return_value={"project1": "Project 1"})
    ):
        result = await cost_api.get_cost_overview()
        
        # Expected: only project1 costs should be included
        # project1: 100 + 150 = 250
        assert result["totalCost"] == 250.0
        assert len(result["projectCosts"]) == 1
        assert result["projectCosts"][0]["projectId"] == "project1"
        assert result["projectCosts"][0]["cost"] == 250.0


@pytest.mark.asyncio
async def test_get_cost_overview_time_range_with_project():
    """Test combined time range and project filtering."""
    user = {"user_principal_id": "u1", "roles": ["super_admin"]}
    request = MockRequest(headers={}, args={"timeRange": "7d", "projectId": "US_UAT"})
    
    # Mock the actual data structure from cosmos_cost_data.json
    mock_cosmos_data = [
        {
            "id": "cost-2025-06-16T01:19:28.584353",
            "timestamp": "2025-06-16T01:19:28.584353",
            "projectCosts": [
                {"projectId": "Priority_Plot_testing_Project", "cost": 115.5},
                {"projectId": "US_UAT", "cost": 245.8},
                {"projectId": "Other_Project", "cost": 89.2}
            ]
        }
    ]
    
    rbac_client = SimpleNamespace(
        get_accessible_projects=AsyncMock(return_value=[
            {"id": "Priority_Plot_testing_Project", "name": "Priority Plot Testing", "region": "westus2", "cost_limit": 5000.0},
            {"id": "US_UAT", "name": "US UAT", "region": "westus2", "cost_limit": 10000.0},
            {"id": "Other_Project", "name": "Other Project", "region": "eastus", "cost_limit": 5000.0}
        ]),
        get_user=AsyncMock(return_value={"id": "u1", "role": "super_admin"}),
        get_region=AsyncMock(return_value={"name": "West US 2", "cost_limit": 50000.0})
    )
    
    with patch.object(
        cost_api, "get_authenticated_user_details", AsyncMock(return_value=user)
    ), patch.object(cost_api, "request", request), patch.object(
        cost_api, "current_app", SimpleNamespace(cosmos_conversation_client=rbac_client)
    ), patch.object(
        cost_api, "jsonify", lambda x: x
    ), patch.object(
        cost_api, "get_latest_cost_data_from_cosmos", AsyncMock(return_value=(mock_cosmos_data, None))
    ), patch.object(
        cost_api.cost_service, "get_project_names_map", AsyncMock(return_value={"US_UAT": "US UAT"})
    ):
        result = await cost_api.get_cost_overview()
        
        # Expected: only US_UAT project costs
        assert result["totalCost"] == 245.8
        assert len(result["projectCosts"]) == 1
        assert result["projectCosts"][0]["projectId"] == "US_UAT"
        assert result["projectCosts"][0]["cost"] == 245.8

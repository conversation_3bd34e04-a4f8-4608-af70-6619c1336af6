// db_containers_only.bicep
// This modified Bicep file only creates new containers without modifying the existing Cosmos DB account

param accountName string
param databaseName string = 'db_conversation_history'

// Define the new containers to create
var newContainers = [
  {
    name: 'conversations_project'
    id: 'conversations_project'
    partitionKey: '/userId'
  }
  {
    name: 'users'
    id: 'users'
    partitionKey: '/id'
  }
  {
    name: 'regions'
    id: 'regions'
    partitionKey: '/id'
  }
  {
    name: 'teams'
    id: 'teams'
    partitionKey: '/region'
  }
  {
    name: 'projects'
    id: 'projects'
    partitionKey: '/region'
  }
  {
    name: 'roleAssignments'
    id: 'roleAssignments'
    partitionKey: '/userId'
  }
]

// Create each container individually to avoid modifying the Cosmos DB account
resource database 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases@2022-05-15' existing = {
  name: '${accountName}/${databaseName}'
}

// Deploy each container separately
resource conversationsProjectContainer 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2022-05-15' = {
  name: '${database.name}/${newContainers[0].name}'
  properties: {
    resource: {
      id: newContainers[0].id
      partitionKey: { paths: [ newContainers[0].partitionKey ] }
    }
    options: {}
  }
}

resource usersContainer 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2022-05-15' = {
  name: '${database.name}/${newContainers[1].name}'
  properties: {
    resource: {
      id: newContainers[1].id
      partitionKey: { paths: [ newContainers[1].partitionKey ] }
    }
    options: {}
  }
}

resource regionsContainer 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2022-05-15' = {
  name: '${database.name}/${newContainers[2].name}'
  properties: {
    resource: {
      id: newContainers[2].id
      partitionKey: { paths: [ newContainers[2].partitionKey ] }
    }
    options: {}
  }
}

resource teamsContainer 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2022-05-15' = {
  name: '${database.name}/${newContainers[3].name}'
  properties: {
    resource: {
      id: newContainers[3].id
      partitionKey: { paths: [ newContainers[3].partitionKey ] }
    }
    options: {}
  }
}

resource projectsContainer 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2022-05-15' = {
  name: '${database.name}/${newContainers[4].name}'
  properties: {
    resource: {
      id: newContainers[4].id
      partitionKey: { paths: [ newContainers[4].partitionKey ] }
    }
    options: {}
  }
}

resource roleAssignmentsContainer 'Microsoft.DocumentDB/databaseAccounts/sqlDatabases/containers@2022-05-15' = {
  name: '${database.name}/${newContainers[5].name}'
  properties: {
    resource: {
      id: newContainers[5].id
      partitionKey: { paths: [ newContainers[5].partitionKey ] }
    }
    options: {}
  }
}

// Outputs
output usersContainerName string = usersContainer.name
output regionsContainerName string = regionsContainer.name
output teamsContainerName string = teamsContainer.name
output projectsContainerName string = projectsContainer.name
output roleAssignmentsContainerName string = roleAssignmentsContainer.name
output conversationsProjectContainerName string = conversationsProjectContainer.name

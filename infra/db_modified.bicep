param accountName string
param location string = resourceGroup().location
param tags object = {}

param databaseName string = 'db_conversation_history'
param principalIds array = []

// Containers to create including conversations_project
param containers array = [
  {
    name: 'conversations_project'
    id: 'conversations_project'
    partitionKey: '/userId'
  }
  {
    name: 'users'
    id: 'users'
    partitionKey: '/id'
  }
  {
    name: 'regions'
    id: 'regions'
    partitionKey: '/id'
  }
  {
    name: 'teams'
    id: 'teams'
    partitionKey: '/region'
  }
  {
    name: 'projects'
    id: 'projects'
    partitionKey: '/region'
  }
  {
    name: 'roleAssignments'
    id: 'roleAssignments'
    partitionKey: '/userId'
  }
]

module cosmos 'core/database/cosmos/sql/cosmos-sql-db.bicep' = {
  name: 'cosmos-sql'
  params: {
    accountName: accountName
    databaseName: databaseName
    location: location
    containers: containers
    tags: tags
    principalIds: principalIds
  }
}

// Updated outputs to reflect the modified container array
output databaseName string = cosmos.outputs.databaseName
output conversationsProjectContainerName string = containers[0].name
output usersContainerName string = containers[1].name
output regionsContainerName string = containers[2].name
output teamsContainerName string = containers[3].name
output projectsContainerName string = containers[4].name
output roleAssignmentsContainerName string = containers[5].name
output accountName string = cosmos.outputs.accountName
output endpoint string = cosmos.outputs.endpoint

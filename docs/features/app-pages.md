# Application Pages and Navigation

This document provides a comprehensive overview of all pages in the AI Scope application, their functionality, and how users navigate between them.

## Page Structure Overview

The AI Scope application consists of the following main pages:

1. **Login Page** (`/login`)
   - Entry point for user authentication
   - Provides access to the application

2. **Projects Page** (`/projects`)
   - Main dashboard for viewing all accessible projects
   - Entry point for creating new projects

3. **New Project Page** (`/new-project`)
   - Form for creating a new project
   - Deployment status tracking

4. **Project Page** (`/project/:projectId`)
   - Project-specific workspace
   - Contains chat interface and file management

5. **Admin Panel** (`/admin/*`)
   - User management and application settings
   - Only accessible to users with admin roles

6. **Development Tools** (`/dev`)
   - Development utilities and quick navigation
   - For development and testing purposes

## Navigation Flow

### Initial Access

1. Users access the application at the root URL (`/`)
2. If not authenticated, they are redirected to the Login page
3. If authenticated, they are redirected to the Projects page

```
/ → /login (if not authenticated)
/ → /projects (if authenticated)
```

## Detailed Page Descriptions

### Login Page (`/login`)

**Purpose**: Authenticate users and provide access to the application.

**Components**:
- Email and password input fields
- Sign-in button
- Demo mode option (for development)

**User Experience**:
1. User enters their credentials
2. Upon successful authentication, user is redirected to the Projects page
3. If authentication fails, an error message is displayed

**Navigation Options**:
- Successful login → Projects page (`/projects`)

### Projects Page (`/projects`)

**Purpose**: Display all projects accessible to the user and provide project management options.

**Components**:
1. **Header**
   - Application title and logo
   - User profile and settings

2. **Project List**
   - Grid or list view of available projects
   - Each project card shows:
     - Project name
     - Project icon/color
     - Description (if available)
     - Deployment status indicator

3. **Action Buttons**
   - "Create new" button for initiating project creation
   - View toggle (grid/list)
   - Search/filter functionality

4. **Project Actions Menu**
   - Accessible via menu on each project card
   - Options include:
     - Open Project
    - Edit Project (change name or icon)
     - Delete Project

**User Experience**:
1. User sees a list of all projects they have access to
2. User can search or filter projects
3. User can toggle between grid and list views
4. User can click on a project to open it
5. User can access project actions via the menu

**Navigation Options**:
- Click "Create new" → New Project page (`/new-project`)
- Click on a project → Project page (`/project/:projectId`)
- Access admin functions → Admin Panel (`/admin`)

### New Project Page (`/new-project`)

**Purpose**: Create a new project and deploy associated Azure resources.

**Components**:
1. **Project Creation Form**
   - Project name input
   - Region selection dropdown
   - Create/Cancel buttons

2. **Deployment Status**
   - Progress indicators for resource deployment
   - Status of individual resources
   - Overall deployment progress

**User Experience**:
1. User enters project name and selects a region
2. User clicks "Create Project" to initiate deployment
3. User sees real-time deployment status updates
4. Upon completion, user can navigate to the new project or return to the projects list

**Navigation Options**:
- Click "Cancel" or "Return to Projects" → Projects page (`/projects`)
- Deployment completion → Project page (`/project/:projectId`)

### Project Page (`/project/:projectId`)

**Purpose**: Provide a workspace for interacting with a specific project.

**Components**:
1. **Header**
   - Project name
   - Navigation options
   - Back to projects button

2. **Chat Interface**
   - Message input
   - Conversation history
   - AI responses with citations

3. **File Management**
   - File upload interface
   - File listing
   - File status indicators

**User Experience**:
1. User can send messages to the AI assistant
2. User can upload files to the project
3. User can view and manage uploaded files
4. User can view conversation history

**Navigation Options**:
- Click "Back to Projects" → Projects page (`/projects`)

### Admin Panel (`/admin/*`)

**Purpose**: Provide administrative functions for managing the application.

**Components**:
1. **Navigation Tabs**
   - User Management
   - Global Settings
   - Project Settings
   - Team Management
   - User Tags
   - Regional Admins
   - Regions
   - Cost Analytics

2. **Admin Functions**
   - User creation and role assignment
   - Application settings configuration
   - Project management

**User Experience**:
1. Admin users can navigate between different administrative functions
2. Admin users can manage users, roles, and permissions
3. Admin users can configure application settings

**Navigation Options**:
- Various admin functions accessible via tabs
- Return to main application → Projects page (`/projects`)

### Development Tools (`/dev`)

**Purpose**: Provide utilities for development and testing.

**Components**:
1. **Role Switcher**
   - Tool for switching between different user roles

2. **Quick Navigation**
   - Links to various application pages

**User Experience**:
1. Developers can switch between different user roles for testing
2. Developers can quickly navigate to different parts of the application

**Navigation Options**:
- Quick links to all major application pages

## API Endpoints

The application uses the following key API endpoints for page functionality:

1. **Project Management**
   - `GET /api/projects`: List projects
   - `POST /api/projects`: Create a new project
   - `GET /api/projects/{project_id}`: Get project details
   - `PUT /api/projects/{project_id}`: Update project
   - `DELETE /api/projects/{project_id}`: Delete project

2. **Chat Functionality**
   - `POST /api/projects/{project_id}/conversation`: Send a message to the AI assistant

3. **File Management**
   - `POST /api/projects/{project_id}/upload`: Upload files to a project

4. **WebSocket Endpoints**
   - Real-time updates for deployment status
   - Real-time updates for file indexing status

## Technical Implementation

The application uses React Router for client-side routing, with the main routes defined in `frontend/src/index.tsx`. The backend routes are defined in `app.py` using Quart/FastAPI.

The application implements a role-based access control (RBAC) system to manage user permissions, with different pages and functionality available based on the user's role.

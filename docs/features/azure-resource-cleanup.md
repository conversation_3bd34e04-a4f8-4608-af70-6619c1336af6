# Azure Resource Cleanup

This document describes the automated Azure resource cleanup feature that removes Azure resources when a project is deleted.

## Overview

When a project is deleted from the system, the automated cleanup process will:

1. Remove the project from Cosmos DB (as before)
2. Delete all associated Azure resources:
   - Storage Accounts and containers
   - Azure Search Services, indexes, datasources, and indexers
   - Function Apps and App Service Plans
   - Event Grid topics and subscriptions
   - Any resources tagged with the project ID

## Configuration

The Azure resource cleanup feature is configured through environment variables:

### Required Settings

```bash
# Azure subscription and resource group
AZURE_SUBSCRIPTION_ID=your-subscription-id
AZURE_RESOURCE_GROUP=your-resource-group-name
```

### Optional Settings

```bash
# Enable/disable cleanup (default: true)
AZURE_CLEANUP_ENABLED=true

# Dry run mode - log what would be deleted without actually deleting (default: false)
AZURE_CLEANUP_DRY_RUN=false

# Resource-specific cleanup flags (all default to true)
AZURE_CLEANUP_STORAGE=true
AZURE_CLEANUP_SEARCH=true
AZURE_CLEANUP_FUNCTION_APPS=true
AZURE_CLEANUP_EVENT_GRID=true

# Cleanup timeout in seconds (default: 300)
AZURE_CLEANUP_TIMEOUT=300

# Tag-based cleanup settings
AZURE_CLEANUP_BY_TAGS=true
AZURE_PROJECT_ID_TAG=project_id
```

## How It Works

### 1. Project Deletion Flow

When a project is deleted via the API (`DELETE /api/rbac/projects/{project_id}`):

1. **Authorization Check**: Verify user has permission to delete the project
2. **Cosmos DB Deletion**: Remove project from database
3. **Azure Resource Cleanup**: If enabled and configured, delete Azure resources
4. **Logging**: Record all actions in the deletion log

### 2. Resource Deletion Order

Resources are deleted in a specific order to handle dependencies:

1. **Event Grid Resources** (first, as other services may depend on them)
   - Event subscriptions
   - System topics
   - Regular topics

2. **Function Apps**
   - Function app itself
   - Associated App Service Plan (if detected)

3. **Search Services**
   - Search service
   - Indexes, datasources, and indexers are deleted with the service

4. **Storage Accounts** (last, as other services may depend on storage)
   - Storage account and all containers

5. **Tagged Resources**
   - Any remaining resources tagged with the project ID

### 3. Safety Mechanisms

The cleanup service includes several safety features:

- **Non-blocking**: Cleanup failures don't prevent project deletion
- **Comprehensive Logging**: All actions are logged to `logs/azure_resource_cleanup.log`
- **Error Handling**: Each resource deletion is isolated; failure of one doesn't affect others
- **Verification**: Built-in methods to verify resources were actually deleted
- **Dry Run Mode**: Test cleanup without actually deleting resources

## Logging

### Deletion Logs

Project deletions are logged to `logs/project_card_deletion.log` with Azure cleanup status:

```
2025-06-06 10:15:23 - INFO - DELETION SUCCESS - Project: abc123 (Test Project) - User: user123 (SUPER_ADMIN) - Region: eastus - Azure Cleanup: SUCCESS
2025-06-06 10:15:23 - INFO - AZURE RESOURCES DELETED - Project: abc123 - Resources: {'storage_account': {...}, 'search_service': {...}, ...}
```

### Azure Cleanup Logs

Detailed Azure resource cleanup operations are logged to `logs/azure_resource_cleanup.log`:

```
2025-06-06 10:15:20 - INFO - Starting resource cleanup for project: abc123 (Test Project)
2025-06-06 10:15:21 - INFO - Successfully deleted storage account: testprojectstorage123
2025-06-06 10:15:22 - INFO - Successfully deleted search service: testprojectsearch123
...
```

## Testing

### Test Script

Use the provided test script to validate the cleanup configuration:

```bash
python test_azure_cleanup.py
```

This will:
- Verify configuration
- Perform a dry run with sample data
- Test resource verification

### Manual Testing

1. **Create a test project** with Azure resources
2. **Note the resource names** in the project data
3. **Delete the project** via the API
4. **Check the logs** for cleanup status
5. **Verify in Azure Portal** that resources were deleted

## Troubleshooting

### Common Issues

1. **Cleanup not running**
   - Check `AZURE_SUBSCRIPTION_ID` and `AZURE_RESOURCE_GROUP` are set
   - Verify `AZURE_CLEANUP_ENABLED` is not set to `false`
   - Check RBAC logs for configuration warnings

2. **Resources not deleted**
   - Check `logs/azure_resource_cleanup.log` for specific errors
   - Verify the service principal has necessary permissions
   - Ensure resources exist in the specified resource group

3. **Timeout errors**
   - Increase `AZURE_CLEANUP_TIMEOUT` if deletion is timing out
   - Some resources take longer to delete (especially storage accounts)

### Required Azure Permissions

The service principal or managed identity needs these Azure RBAC roles:

- `Storage Account Contributor` - for storage account deletion
- `Search Service Contributor` - for search service deletion  
- `Website Contributor` - for function app deletion
- `EventGrid Contributor` - for event grid resource deletion
- Or `Contributor` role on the resource group for all operations

## Best Practices

1. **Use Tags**: Ensure all project resources are tagged with the project ID
2. **Monitor Logs**: Regularly check cleanup logs for failures
3. **Test First**: Use dry run mode when testing in production
4. **Handle Failures**: Have a process for manual cleanup if automated cleanup fails
5. **Resource Naming**: Use consistent naming conventions for easier tracking

## API Response

The project deletion endpoint now returns cleanup status:

```json
{
  "message": "Project deleted successfully",
  "azure_cleanup": true
}
```

Where `azure_cleanup` indicates if Azure resource cleanup was successful.
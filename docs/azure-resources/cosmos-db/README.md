# Cosmos DB Documentation

This section provides detailed documentation on the Azure Cosmos DB implementation in the AI Scope application.

## Contents

- [Implementation](./implementation.md): Detailed implementation of Cosmos DB in the application

## Overview

Azure Cosmos DB is used as the primary database for the application, storing:

- Project configurations
- User data and permissions
- Conversation history
- Deployment status tracking
- System configuration

## Database Structure

```
CosmosDB Account
└── Database: ai-scope-db
    └── Container: conversations
        ├── Partition Key: /type
        └── Items:
            ├── type='project' (Project configurations)
            ├── type='conversation' (Chat conversations)
            ├── type='message' (Individual chat messages)
            ├── type='user' (User profiles)
            ├── type='deployment' (Deployment status)
            └── type='system' (System configuration)
```

## Key Features Used

- NoSQL API (formerly DocumentDB)
- Session consistency level
- Automatic indexing
- TTL (Time-To-Live) for temporary data
- Multi-region replication (optional)

## Performance Optimization

See [Implementation](./implementation.md) for details on:

- Indexing policy optimization
- Request Units (RU) optimization
- Query patterns
- Caching strategies

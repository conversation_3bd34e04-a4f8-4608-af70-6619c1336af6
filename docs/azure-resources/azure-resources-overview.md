# Azure Resources in AI Scope Application

This document details how Azure resources are created, configured, and used within the AI Scope application.

## Resource Architecture

The AI Scope application uses a multi-tenant architecture where each project has its own isolated set of resources:

```
Azure AD Tenant
  └── Role Definitions
Resource Group
  ├── Blob Storage Account
  │   └── Project Containers (per project)
  ├── AI Search Service
  │   └── Project Indexes (per project)
  ├── Cosmos DB Account
  │   └── Project Configs DB/Container
  └── Function Apps (per project)
      └── Deployed from ACR
```

## Blob Storage

### Configuration

Each project uses three containers within a shared Azure Storage account:

1. **Uploads Container**: Stores user-uploaded documents for processing
   - Naming convention: `project-{sanitized-name}-{unique-suffix}-uploads`
   - Used for storing documents that will be indexed by AI Search

2. **Input Container**: Stores template files
   - Naming convention: `project-{sanitized-name}-{unique-suffix}-input`
   - Used for storing template files like Excel spreadsheets or Word documents

3. **Output Container**: Stores processed outputs
   - Naming convention: `project-{sanitized-name}-{unique-suffix}-output`
   - Used for storing outputs generated by the Function App

### Creation Process

Storage containers are created during project creation:

1. The backend uses `StorageManagementClient` to create containers
2. Container names are generated based on the project name with a unique suffix
3. Container names are stored in the project configuration in Cosmos DB

### Access Control

Access to blob storage is controlled through Azure RBAC:

1. The application uses Managed Identity or Azure CLI credentials
2. For uploads to work, the identity needs "Storage Blob Data Contributor" role
3. For reading, the identity needs "Storage Blob Data Reader" role

### Usage in Application

The application interacts with blob storage in several ways:

1. **File Upload**: Users upload files to the uploads container
   - Endpoint: `/api/projects/{project_id}/upload`
   - Uses `BlobServiceClient` to upload files

2. **Template Upload**: Users upload template files to the input container
   - Endpoint: `/api/projects/{project_id}/upload-template`

3. **File Listing**: WebSocket service provides real-time updates on files
   - Uses `BlobServiceClient` to list blobs in containers
   - Updates are sent to clients via WebSocket

## Azure AI Search

### Configuration

Each project has its own search resources within a shared Azure AI Search service:

1. **Search Index**: Stores indexed document content and metadata
   - Naming convention: `project-{sanitized-name}-index`
   - Schema includes fields for content, metadata, and vector embeddings

2. **Search Datasource**: Connects to the project's uploads container
   - Naming convention: `project-{sanitized-name}-ds`
   - Uses connection string or Managed Identity for access

3. **Search Indexer**: Processes documents from the datasource
   - Naming convention: `project-{sanitized-name}-indexer`
   - Configured to extract content and metadata from various file types

### Creation Process

Search resources are created during project creation:

1. The backend uses `SearchIndexClient` and `SearchIndexerClient` to create resources
2. Index schema is defined with fields for content, metadata, and search capabilities
3. Datasource is connected to the project's uploads container
4. Indexer is configured to process documents and update the index
5. Resource names are stored in the project configuration in Cosmos DB

### Index Schema

The search index is created with the following schema:

```json
{
  "name": "project-name-index",
  "fields": [
    {
      "name": "id",
      "type": "Edm.String",
      "key": true,
      "searchable": true,
      "filterable": true,
      "sortable": true
    },
    {
      "name": "content",
      "type": "Edm.String",
      "searchable": true,
      "filterable": false,
      "sortable": false
    },
    {
      "name": "metadata_storage_name",
      "type": "Edm.String",
      "searchable": true,
      "filterable": true,
      "sortable": true
    },
    // Additional metadata fields...
  ]
}
```

### Usage in Application

The application uses Azure AI Search in several ways:

1. **Document Indexing**: Indexer processes documents uploaded to the blob container
   - Scheduled to run automatically or triggered manually

2. **Search Queries**: Chat functionality uses the search index for RAG
   - Endpoint: `/api/search/*` (proxied to AI Search)
   - Queries are enhanced with project context

3. **Index Status**: WebSocket service provides updates on indexing status
   - Queries the indexer status and sends updates to clients

## Azure Function App

### Configuration

Each project has its own Function App deployed from Azure Container Registry:

1. **Function App**: Runs project-specific processing logic
   - Naming convention: `func-{sanitized-name}-{unique-suffix}`
   - Deployed from ACR image: `functionappaiscope.azurecr.io/functionapp:latest`

2. **App Service Plan**: Hosts the Function App
   - Shared or dedicated based on configuration
   - Basic tier (B1) by default

### Deployment Process

Function Apps are deployed during project creation:

1. The backend verifies access to the ACR
2. Bicep template `function_app_acr.bicep` is used to deploy the Function App
3. Function App is configured with:
   - Connection to project storage containers
   - Search service configuration
   - OpenAI service configuration
   - Project-specific environment variables

### Function App Configuration

The Function App is configured with the following settings:

```
DOCKER_REGISTRY_SERVER_URL=https://functionappaiscope.azurecr.io
DOCKER_REGISTRY_SERVER_USERNAME=<acr-username>
DOCKER_REGISTRY_SERVER_PASSWORD=<acr-password>
AzureWebJobsStorage=<storage-connection-string>
FUNCTIONS_EXTENSION_VERSION=~4
FUNCTIONS_WORKER_RUNTIME=python
PROJECT_ID=<project-id>
PROJECT_NAME=<project-name>
STORAGE_CONTAINER_UPLOADS=<uploads-container-name>
STORAGE_CONTAINER_INPUT=<input-container-name>
STORAGE_CONTAINER_OUTPUT=<output-container-name>
SEARCH_SERVICE_NAME=<search-service-name>
SearchServiceName=<search-service-name>
SEARCH_INDEX_NAME=<search-index-name>
SEARCH_INDEXER_NAME=<search-indexer-name>
IndexerName=<search-indexer-name>
SEARCH_DATASOURCE_NAME=<search-datasource-name>
__SHARED_SEARCH_KEY__=<search-api-key>
AZURE_AI_SEARCH_API_KEY=<search-api-key>
AzureSearchApiKey=<search-api-key>
EVENT_GRID_TOPIC_ENDPOINT=
EVENT_GRID_TOPIC_KEY=
__SHARED_OPENAI_SERVICE__=<openai-service-name>
__SHARED_OPENAI_KEY__=<openai-api-key>
__SHARED_OPENAI_DEPLOYMENT__=<openai-model-deployment>
```

### Usage in Application

The Function App is used for various processing tasks:

1. **Document Processing**: Processes documents uploaded to the blob container
2. **Data Transformation**: Transforms data between different formats
3. **Integration**: Connects with other services and APIs

## Azure Cosmos DB

### Configuration

The application uses Azure Cosmos DB for configuration and data storage:

1. **Database**: Shared database for all projects
   - Contains project configurations, user data, and conversation history

2. **Container**: Stores items with different types
   - Items are distinguished by a `type` field ('conversation', 'message', 'project')
   - Project items contain all configuration for a project

### Project Configuration

Project configuration is stored in Cosmos DB with the following structure:

```json
{
  "id": "project-uuid",
  "type": "project",
  "name": "Project Name",
  "description": "Project description",
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "storage_container_uploads": "project-name-1234-uploads",
  "storage_container_input": "project-name-1234-input",
  "storage_container_output": "project-name-1234-output",
  "search_index_name": "project-name-index",
  "search_datasource_name": "project-name-ds",
  "search_indexer_name": "project-name-indexer",
  "function_app_name": "func-name-1234",
  "function_app_url": "https://func-name-1234.azurewebsites.net",
  "icon": "📊",
  "role": "owner",
  "environment": {}
}
```

### Usage in Application

Cosmos DB is used throughout the application:

1. **Project Management**: Stores project configurations and metadata
2. **User Management**: Stores user data and permissions
3. **Conversation History**: Stores chat conversations and messages
4. **Configuration Retrieval**: Backend services fetch project-specific configurations

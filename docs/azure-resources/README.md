# Azure Resources Documentation

This section provides detailed documentation on the Azure resources used by the AI Scope application.

## Contents

- **[Storage](./storage/)**: Azure Blob Storage implementation and usage
- **[Search](./search/)**: Azure AI Search implementation and usage
- **[Function Apps](./function-apps/)**: Azure Function Apps implementation and usage
- **[Cosmos DB](./cosmos-db/)**: Azure Cosmos DB implementation and usage

## Resource Architecture

The application uses a multi-region architecture where the application is deployed in a single location while project resources can be created in different Azure regions based on user selection.

```mermaid
graph TD
    A[Central App Resource Group] --> B[App Service/Container App]
    A --> C[Cosmos DB Account]
    
    B --> D[Multi-Region Resource Management]
    
    D --> E[Region 1 Resources]
    D --> F[Region 2 Resources]
    D --> G[Region 3 Resources]
    
    E --> E1[Storage Account]
    E --> E2[AI Search Service]
    
    F --> F1[Storage Account]
    F --> F2[AI Search Service]
    
    G --> G1[Storage Account]
    G --> G2[AI Search Service]
```

## Supported Regions

The application currently supports the following Azure regions:

- West US 2
- West Europe
- Chile North Central

## Resource Naming Conventions

Resources follow a consistent naming convention:

- Storage Containers: `project-{sanitized-name}-{unique-suffix}-{purpose}`
- Search Indexes: `project-{sanitized-name}-index`
- Function Apps: `func-{sanitized-name}-{unique-suffix}`

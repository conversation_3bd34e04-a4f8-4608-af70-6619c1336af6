# Custom Container Deployment Guide for AI Scope Application

This document describes how to deploy the AI Scope application to Azure App Service using a custom Docker container hosted in Azure Container Registry (ACR).

## Overview

The AI Scope application uses a custom container deployment strategy to ensure all dependencies (including Azure CLI) are available in the runtime environment. This approach provides full control over the execution environment and resolves compatibility issues with the standard Azure App Service platform images.

## Architecture

- **Container Registry**: Azure Container Registry (ACR) - `webappaiscope`
- **App Service**: `ai-scope-app3` in resource group `rg-internal-ai`
- **Authentication**: Managed Identity with AcrPull role
- **Port Configuration**: Application runs on port 8000

## Prerequisites

1. Azure CLI installed and authenticated
2. Access to the `rg-internal-ai` resource group
3. Docker image built and pushed to ACR
4. Local `.env` file with application configuration

## Initial Setup

### 1. Create Azure Container Registry

```bash
az acr create \
  --resource-group rg-internal-ai \
  --name webappaiscope \
  --sku Standard
```

### 2. Enable Managed Identity on App Service

```bash
az webapp identity assign \
  --resource-group rg-internal-ai \
  --name ai-scope-app3
```

### 3. Grant ACR Access to App Service

```bash
# Get the App Service Principal ID
PRINCIPAL_ID=$(az webapp identity show \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --query principalId --output tsv)

# Get the ACR ID
ACR_ID=$(az acr show \
  --resource-group rg-internal-ai \
  --name webappaiscope \
  --query id --output tsv)

# Assign the AcrPull role
az role assignment create \
  --assignee $PRINCIPAL_ID \
  --scope $ACR_ID \
  --role AcrPull
```

### 4. Enable Managed Identity Authentication for ACR

```bash
az webapp config set \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --generic-configurations '{"acrUseManagedIdentityCreds": true}'
```

## Building and Deploying

### Build Docker Image

The application uses `WebApp.Dockerfile` which:
- Builds the React frontend
- Installs Python dependencies including Azure CLI
- Configures Gunicorn with uvicorn workers

```bash
# Build with versioned tag and latest
az acr build \
  --registry webappaiscope \
  --image aiscope/webapp:v2-$(date +%Y%m%d-%H%M%S) \
  --image aiscope/webapp:latest \
  --file WebApp.Dockerfile \
  .
```

### Deploy to App Service

#### 1. Configure Container Image

```bash
az webapp config container set \
  --name ai-scope-app3 \
  --resource-group rg-internal-ai \
  --docker-custom-image-name webappaiscope.azurecr.io/aiscope/webapp:latest
```

Note: The `--docker-custom-image-name` flag is deprecated but still functional. In future versions, use `--container-image-name`.

#### 2. Set Environment Variables

```bash
# Convert .env file to JSON format
grep -E '^[A-Za-z_][A-Za-z0-9_]*=' .env | \
  jq -R -s 'split("\n") | map(select(length > 0)) | 
  map(if test("^[A-Za-z_][A-Za-z0-9_]*=") then 
    capture("^(?<name>[A-Za-z_][A-Za-z0-9_]*)=(?<value>.*)$") | 
    {name: .name, value: (.value | 
      if startswith("\"") and endswith("\"") then .[1:-1] else . end), 
      slotSetting: false} 
  else empty end)' > env.json

# Apply settings to App Service
az webapp config appsettings set \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --settings "@env.json"
```

#### 3. Configure Port

```bash
az webapp config appsettings set \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --settings WEBSITES_PORT=8000
```

#### 4. Clear Custom Startup Command

**Important**: Always clear any custom startup command. The Dockerfile's CMD provides the correct startup configuration.

```bash
az webapp config set \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --startup-file " "
```

Note: Use a single space `" "` instead of empty string `""` to effectively clear the startup command.

#### 5. Restart App Service

```bash
az webapp restart \
  --resource-group rg-internal-ai \
  --name ai-scope-app3
```

## Monitoring

### View Live Logs

```bash
az webapp log tail \
  --resource-group rg-internal-ai \
  --name ai-scope-app3
```

### Download Logs

```bash
az webapp log download \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --log-file aiscope-logs.zip
```

### Check Application Status

```bash
# Check if app is running
az webapp show \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --query state -o tsv

# Test health endpoint (returns 401 if authentication is required)
curl -I https://ai-scope-app3.azurewebsites.net/health
```

## Troubleshooting

### Common Issues

1. **Container fails to respond to HTTP pings**
   - Ensure `WEBSITES_PORT=8000` is set
   - Verify the Dockerfile EXPOSE and CMD use port 8000
   - Clear any custom startup commands

2. **Gunicorn startup errors**
   - Check for Python syntax errors in app.py
   - Ensure proper indentation in all Python files
   - Use `--bind` instead of `-b` in gunicorn command
   - Clear custom startup command using `" "` (space)

3. **ACR authentication failures**
   - Verify managed identity is enabled
   - Check AcrPull role assignment
   - Ensure `acrUseManagedIdentityCreds` is true

4. **Python import or syntax errors**
   - Review Docker build logs for errors
   - Check app.py for indentation issues
   - Ensure all try blocks have corresponding except/finally blocks

### Viewing Container Logs

```bash
# Download and extract logs
az webapp log download \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --log-file webapp-logs.zip

# View Docker container logs
unzip -p webapp-logs.zip LogFiles/2025_*_default_docker.log | tail -100
```

## Complete Deployment Script

For automated deployments, use the script in `scripts/docker_deployment/build_and_push.sh`:

```bash
#!/bin/bash
# Complete deployment script for AI Scope application

# Variables
RESOURCE_GROUP="rg-internal-ai"
ACR_NAME="webappaiscope"
APP_NAME="ai-scope-app3"
IMAGE_NAME="aiscope/webapp"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
IMAGE_TAG="v2-${TIMESTAMP}"

# Build and push Docker image
echo "Building and pushing Docker image..."
az acr build \
  --registry ${ACR_NAME} \
  --image ${IMAGE_NAME}:${IMAGE_TAG} \
  --image ${IMAGE_NAME}:latest \
  --file WebApp.Dockerfile \
  .

# Deploy to App Service
echo "Deploying to App Service..."
az webapp config container set \
  --name ${APP_NAME} \
  --resource-group ${RESOURCE_GROUP} \
  --docker-custom-image-name ${ACR_NAME}.azurecr.io/${IMAGE_NAME}:${IMAGE_TAG}

# Clear any custom startup command
echo "Clearing custom startup command..."
az webapp config set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --startup-file " "

# Ensure port is set correctly
echo "Setting WEBSITES_PORT..."
az webapp config appsettings set \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME} \
  --settings WEBSITES_PORT=8000

# Restart the App Service
echo "Restarting App Service..."
az webapp restart \
  --resource-group ${RESOURCE_GROUP} \
  --name ${APP_NAME}

echo "Deployment complete! Image tag: ${IMAGE_TAG}"
echo "Monitor logs with: az webapp log tail --resource-group ${RESOURCE_GROUP} --name ${APP_NAME}"
```

## Key Configuration Details

### Dockerfile Configuration

The `WebApp.Dockerfile` must include:

```dockerfile
# Expose port 8000
EXPOSE 8000

# Use --bind instead of -b for gunicorn
CMD ["gunicorn", "app:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

### Required Python Packages

Ensure `requirements.txt` includes:
- `gunicorn>=20.1.0`
- `uvicorn[standard]>=0.18.0`
- `quart` (async web framework)
- `azure-identity` (for managed identity authentication)

### Environment Variables

Critical environment variables:
- `WEBSITES_PORT=8000` - Required for App Service to know which port to probe
- `API_PORT=8000` - Application configuration
- All other app-specific settings from `.env` file

## Quick Recovery Script

If the application stops working after a deployment:

```bash
#!/bin/bash
# Quick recovery script

# Reset to latest image
az webapp config container set \
  --name ai-scope-app3 \
  --resource-group rg-internal-ai \
  --docker-custom-image-name webappaiscope.azurecr.io/aiscope/webapp:latest

# Clear startup command
az webapp config set \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --startup-file " "

# Ensure port setting
az webapp config appsettings set \
  --resource-group rg-internal-ai \
  --name ai-scope-app3 \
  --settings WEBSITES_PORT=8000

# Restart
az webapp restart \
  --resource-group rg-internal-ai \
  --name ai-scope-app3
```

## Migration Notes

This deployment strategy replaces the previous `az webapp up` approach, providing:
- Full control over the runtime environment
- Consistent dependencies across environments
- Better support for async Python applications
- Integrated Azure CLI for deployment scripts
- Proper handling of gunicorn startup commands
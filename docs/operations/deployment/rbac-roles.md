# rbac-roles.md (New)

Goal: Summarize all Azure RBAC roles required for the web app's managed identity.

The web app uses a managed identity to access Azure resources. Assign the following roles to this identity:

- **Contributor** on the target resource group
- **Search Service Contributor**
- **Search Index Data Reader**
- **Storage Blob Data Contributor**
- **Cognitive Services OpenAI User**
- **Cognitive Services OpenAI Contributor** (referenced in `scripts/role_assignment.sh`)

These permissions allow the web app to deploy resources, read from Azure AI Search, access Blob Storage, and interact with Azure OpenAI services.

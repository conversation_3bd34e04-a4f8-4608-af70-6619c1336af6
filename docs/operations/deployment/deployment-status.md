# deployment-status.md (Enhanced)

Goal: Detail the status object, API, and monitoring.
Content:
Status Object: Define the `deployment_status` JSON structure stored in the project document (status, message, updated_at, details object).
API: Document `GET` (incl. `force_update` param, ETag) and `POST /api/projects/{project_id}/deployment-status`. Explain the POST logic (merge details, update status, recalc progress, broadcast).
WebSocket: Document `/ws/deployment-status/{project_id}` endpoint and `deployment_status_update` message payload.
Status Definitions: Define pending, in_progress, completed, failed, partial_success.
Details Fields: Briefly explain the meaning of `storage_complete`, `search_complete`, `function_complete`, `overall_complete`, `completion_percentage`.
Monitoring Role: Explain AzureResourceMonitor checks Azure resources and updates status via the POST API as a verification step.

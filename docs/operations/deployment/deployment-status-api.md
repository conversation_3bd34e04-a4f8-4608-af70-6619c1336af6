# Deployment Status API

This document provides detailed information about the deployment status API endpoints and WebSocket implementation in the AI Scope application.

## API Endpoints

### Get Deployment Status

**Endpoint:** `GET /api/projects/{project_id}/deployment-status`

**Description:** Retrieves the current deployment status for a project.

**Authentication:** Requires user authentication. The user must have access to the project.

**Query Parameters:**
- `force_update` (boolean, optional): If set to `true`, the API will check the actual Azure resources and update the status in CosmosDB before returning the result. Default is `false`.

**Response:**
```json
{
  "status": "in_progress",  // "pending", "in_progress", "completed", "failed"
  "message": "Deploying storage resources",
  "updated_at": "2023-06-15T10:35:00Z",
  "details": {
    "storage": {
      "storage_account": true,
      "containers": {
        "uploads": true,
        "input": true,
        "output": false
      }
    },
    "storage_complete": false,
    "search": {
      "search_service": true,
      "index": false,
      "indexer": false,
      "datasource": false
    },
    "search_complete": false,
    "function": {
      "function_app": false,
      "event_grid_topic": false,
      "event_grid_system_topic": false,
      "event_grid": false,
      "maturity_assessment": false,
      "executive_summary": false
    },
    "function_complete": false,
    "overall_complete": false,
    "completion_percentage": 30
  },
  "_meta": {
    "resource_summary": {
      "total": 15,
      "completed": 4,
      "in_progress": 0,
      "failed": 0,
      "pending": 11
    }
  }
}
```

**Caching:**
- The API uses ETags for caching
- Cache-Control headers are set based on the status:
  - `in_progress`: 10 seconds
  - `completed`: 1 hour
  - `failed`: 5 minutes
  - `pending`: 30 seconds

### Update Deployment Status

**Endpoint:** `POST /api/projects/{project_id}/deployment-status`

**Description:** Updates the deployment status for a project. This endpoint is primarily used by the deployment script and resource monitor.

**Authentication:** Requires user authentication or Azure CLI credentials.

**Request Body:**
```json
{
  "status": "in_progress",  // "pending", "in_progress", "completed", "failed"
  "message": "Deploying storage resources",
  "details": {
    "storage": {
      "containers": {
        "uploads": true,
        "input": true
      }
    }
  }
}
```

**Response:**
```json
{
  "message": "Deployment status updated successfully",
  "status": {
    "status": "in_progress",
    "message": "Deploying storage resources",
    "updated_at": "2023-06-15T10:35:00Z",
    "details": {
      // Full merged details object
    }
  }
}
```

**Implementation Details:**
1. The API reads the current project document from CosmosDB
2. It gets the existing `deployment_status` object or creates a default one
3. It deep merges the incoming `details` with the existing `details`
4. It updates the `status`, `message`, and `updated_at` fields
5. It recalculates `completion_percentage` and `overall_complete` based on the merged `details`
6. It writes the updated `deployment_status` object back to CosmosDB
7. It broadcasts the updated status via WebSocket

### Create Support Ticket

**Endpoint:** `POST /api/projects/{project_id}/support-ticket`

**Description:** Creates a support ticket for deployment issues.

**Authentication:** Requires user authentication. The user must have access to the project.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "error_details": "Detailed error description",
  "resource_statuses": {
    "storage": "failed",
    "search": "pending",
    "function": "pending"
  }
}
```

**Response:**
```json
{
  "ticket_id": "ticket-uuid",
  "message": "Support ticket created successfully"
}
```

## WebSocket Implementation

### Connection Endpoint

**Endpoint:** `/ws/deployment-status/{project_id}`

**Description:** Establishes a WebSocket connection for real-time deployment status updates.

**Authentication:** Requires user authentication or Azure CLI credentials.

**Query Parameters:**
- `user_id` (string, optional): The user ID. Default is "anonymous".

### Message Types

#### Connection Established

Sent when the WebSocket connection is established.

```json
{
  "type": "connection_established",
  "message": "Connected to deployment status WebSocket",
  "timestamp": "2023-06-15T10:35:00Z"
}
```

#### Deployment Status Update

Sent when the deployment status is updated.

```json
{
  "type": "deployment_status_update",
  "data": {
    "status": "in_progress",
    "message": "Deploying storage resources",
    "details": {
      // Full details object
    },
    "updated_at": "2023-06-15T10:35:00Z"
  },
  "timestamp": "2023-06-15T10:35:00Z"
}
```

#### Ping/Pong

The client can send a ping message to keep the connection alive.

**Ping:**
```json
{
  "type": "ping"
}
```

**Pong:**
```json
{
  "type": "pong",
  "timestamp": "2023-06-15T10:35:00Z"
}
```

### Implementation Details

1. **Connection Management:**
   - The WebSocket connection is managed by the `handle_deployment_websocket` function in `backend/web_sockets/deployment_status.py`
   - Connections are stored in a dictionary keyed by project ID
   - The connection is kept alive with a 120-second timeout

2. **Status Caching:**
   - The deployment status is cached in memory for quick access
   - When a new connection is established, the cached status is sent if available
   - If not in cache, the status is retrieved from CosmosDB

3. **Broadcasting:**
   - The `broadcast_deployment_status` function sends status updates to all connected clients for a project
   - It updates the cache and prepares a message with the updated status
   - It sends the message to each connected client
   - It removes stale connections

4. **Error Handling:**
   - If a connection is lost, it is removed from the connections dictionary
   - If a client disconnects, the connection is closed gracefully
   - If an error occurs during broadcasting, it is logged but does not affect other connections

## Status Document Structure

The deployment status is stored in the `deployment_status` field of the project document in CosmosDB. The `status` field only uses the values `pending`, `in_progress`, `completed`, and `failed`; stage details like "Function App deployment" are placed in the `message` field. The structure is as follows:

```json
{
  "status": "in_progress",  // "pending", "in_progress", "completed", "failed"
  "message": "Deploying storage resources",
  "updated_at": "2023-06-15T10:35:00Z",
  "details": {
    "storage": {
      "storage_account": true,
      "containers": {
        "uploads": true,
        "input": true,
        "output": true
      }
    },
    "storage_complete": true,
    "search": {
      "search_service": true,
      "index": true,
      "indexer": true,
      "datasource": true
    },
    "search_complete": true,
    "function": {
      "function_app": true,
      "event_grid_topic": true,
      "event_grid_system_topic": true,
      "event_grid": true,
      "maturity_assessment": true,
      "executive_summary": true
    },
    "function_complete": true,
    "overall_complete": true,
    "completion_percentage": 100
  }
}
```

**Status Values:**
- `pending`: The deployment has not started yet
- `in_progress`: The deployment is in progress
- `completed`: The deployment has completed successfully
- `failed`: The deployment has failed

**Resource Status Values:**
- `true`: The resource has been created successfully
- `false`: The resource has not been created yet or creation failed

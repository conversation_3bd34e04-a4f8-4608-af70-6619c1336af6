# deployment-process.md (Enhanced)

Goal: Explain the overall workflow and how status is tracked.
Content:
Diagram: Sequence diagram (UI -> API -> Background Task -> Scripts -> Azure -> Status API -> CosmosDB -> WebSocket -> UI).
Phases:
1.  **Initiation (Synchronous API Call):** Project doc creation in CosmosDB (status: pending), initial resource check/creation (e.g., containers), background task trigger.
2.  **Main Infrastructure Deployment (Async Background):** `deploy_project_resources.py` calls `deploy_project_resources.sh` -> Bicep deploys Storage, Search, etc. -> Status updated to `in_progress` (stage noted in the `message` field).
3.  **Function App Deployment (Async Background):** `deploy_project_resources.py` calls `scripts/ACR_deployment/deploy_function_app_from_acr.sh` -> ACR Bicep deploys Function App -> Status updated to `in_progress` (stage noted in the `message` field).
4.  **Event Grid Linkage (Async Background):** `deploy_project_resources.py` calls `deploy_project_resources.sh` (with `event_grid.bicep`) -> Event Grid Subscription created.
5.  **Completion:** Final status (completed or failed/partial_success) updated.
Status Tracking: Briefly mention status is stored in CosmosDB (`deployment_status` field), updated via API, and broadcast via WebSocket. Link to [deployment-status.md](./deployment-status.md).
Note: The `status` field only uses the values `pending`, `in_progress`, `completed`, and `failed`. Messages such as "Deploying Function App" are stored in the `message` field.

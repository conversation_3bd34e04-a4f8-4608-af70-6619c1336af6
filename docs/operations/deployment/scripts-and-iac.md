# scripts-and-iac.md (New)

Goal: Central reference for key deployment automation assets.
Content:
Python Orchestrator: `deploy_project_resources.py` (Role: Manages phases, calls scripts, updates status).
Shell Scripts:
- `deploy_project_resources.sh` (Role: Runs main Bicep, Event Grid Bicep; Key Inputs: Project details, template path, specific resource IDs).
- `scripts/ACR_deployment/deploy_function_app_from_acr.sh` (Role: Runs ACR Bicep, configures Function App; Key Inputs: Dynamically generated parameters.json).
Bicep Templates/Modules: List main files (`project_resources*.bicep`, `function_app_deployment.bicep`) and key modules (storage, search, event_grid) with one-line descriptions.
Links: Provide relative links to the source files for easy access.

# Project Configuration Management

This document outlines the approach for dynamically linking project-specific configuration stored in Cosmos DB to the frontend components when a project page is opened.

## Overview

The core idea is to fetch project-specific configuration from the backend when the user navigates to a project page and make this configuration available to the relevant frontend components. This ensures that each project uses its own dedicated Azure resources (storage containers, search index, function app, etc.) rather than relying on global environment variables.

## Current Implementation

### Backend API Endpoints

1. **Project Configuration Endpoint:**
   - **Endpoint:** `GET /api/projects/{project_id}/config`
   - **Description:** Retrieves the full configuration for a specific project
   - **Implementation:** Handled by `get_project_config` function in `app.py`
   - **Response:** Includes all necessary configuration details for the project:
     - Project metadata (ID, name, description)
     - Storage resources (account name, container names)
     - Search resources (service name, index name)
     - Function app resources (app name, URL)
     - SAS token for storage access
     - Project-specific environment variables

2. **Project Resources Update Endpoint:**
   - **Endpoint:** `POST /api/projects/{project_id}/resources`
   - **Description:** Updates the project document with actual resource names from Azure deployment
   - **Implementation:** Called by `deploy_project_resources.py` after successful deployment
   - **Request Body:** Contains the actual resource names created during deployment

### Response Structure

The project configuration endpoint returns a JSON object with the following structure:

```json
{
  "id": "project-uuid",
  "name": "Project Name",
  "storageAccountName": "stprojecta1234",
  "uploadsContainerName": "project-name-1234-uploads",
  "inputContainerName": "project-name-1234-input",
  "outputContainerName": "project-name-1234-output",
  "searchIndexName": "project-name-index",
  "searchServiceName": "search-projecta",
  "functionAppName": "func-projecta-123",
  "sasToken": "?sv=2022-11-02&ss=...",
  "environment": {
    "CUSTOM_API_KEY": "some_value"
  }
}
```

### Security Considerations

- The endpoint is properly authenticated and authorized
- Users can only fetch configuration for projects they have access to
- Sensitive information like SAS tokens is transmitted securely
- The SAS token has limited permissions and expiration

## Frontend Implementation

### Project Configuration Context

The frontend uses a React Context to manage and access the project configuration:

1. **Context Definition:**
   - **File:** `frontend/src/contexts/ProjectConfigContext.tsx`
   - **Purpose:** Provides a centralized way to manage and access project configuration
   - **Interface:**
     ```typescript
     export interface ProjectConfig {
       id: string;
       name: string;
       storageAccountName: string | null;
       uploadsContainerName: string | null;
       inputContainerName: string | null;
       outputContainerName: string | null;
       searchIndexName: string | null;
       searchServiceName: string | null;
       functionAppName: string | null;
       sasToken: string | null;
       environment?: Record<string, string>;
     }
     ```
   - **Context API:**
     - `projectConfig`: The current project configuration
     - `setProjectConfig`: Function to update the configuration
     - `isLoading`: Boolean indicating if the configuration is being loaded
     - `error`: Error message if the configuration failed to load
     - `fetchConfig`: Function to fetch the configuration for a project

2. **Context Provider:**
   - Wraps the project page components
   - Manages the state of the project configuration
   - Provides functions to fetch and update the configuration
   - Handles loading and error states

### Integration with Project Page

1. **Router Configuration:**
   - The project page route is wrapped with the `ProjectConfigProvider`
   - This makes the context available to all components within the project page
   - Example:
     ```typescript
     <Route
       path="/project/:projectId/*"
       element={
         <ProjectConfigProvider>
           <ProjectPage />
         </ProjectConfigProvider>
       }
     />
     ```

2. **Project Layout Component:**
   - Fetches the project configuration when the page loads
   - Uses the project ID from the URL parameters
   - Handles loading and error states
   - Renders the project components once the configuration is loaded
   - Example:
     ```typescript
     const ProjectLayout: React.FC = () => {
       const { projectId } = useParams<{ projectId: string }>();
       const { projectConfig, isLoading, error, fetchConfig } = useProjectConfig();

       useEffect(() => {
         if (projectId) {
           fetchConfig(projectId);
         }
       }, [projectId]);

       // Handle loading and error states...

       return (
         <div>
           <h1>Project: {projectConfig.name}</h1>
           <Chat />
           {/* Other project components */}
         </div>
       );
     };
     ```

### Component Integration

1. **File Management Component:**
   - Uses the project configuration from the context
   - Dynamically initializes storage connections based on the project configuration
   - Handles file operations (list, upload, delete) for project-specific containers
   - Key features:
     - Container selection based on props (`uploads`, `input`, or `output`)
     - Dynamic initialization of Azure Storage SDK clients
     - Error handling and loading states
     - File operations using project-specific container names

2. **Chat Component:**
   - Uses the project ID from the context for API calls
   - Ensures all conversation data is associated with the correct project
   - Passes project-specific configuration to child components

3. **Search Integration:**
   - Uses the project-specific search index name from the context
   - Ensures search queries are performed against the correct index
   - Handles search results in the context of the current project

## Benefits of This Approach

1. **Isolation:** Each project uses its own dedicated Azure resources
2. **Security:** Access control is enforced at the project level
3. **Flexibility:** Projects can have different configurations and resource types
4. **Maintainability:** Configuration is centralized and consistent
5. **Scalability:** New projects can be created without modifying global configuration

## Implementation Status

This approach has been implemented in the application, with the following components using project-specific configuration:

- File Management component for uploads, input, and output containers
- Chat component for project-specific conversations
- Search component for project-specific document search

The frontend now dynamically fetches and uses project-specific configuration from the backend, eliminating the need for global environment variables for resource names.


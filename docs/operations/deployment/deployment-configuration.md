# deployment-configuration.md (New)

Goal: Explain how deployment is configured.
Content:
Sources: List main sources: `.env` (global defaults/secrets), `config.yaml` (if used), dynamic secrets (az cli calls in scripts), project doc from CosmosDB (for existing resource names).
Flow: Summarize how Python (`deploy_project_resources.py`) reads config and passes necessary values (as env vars or temp files like `parameters_PROJECT_ID.json`) to shell scripts (`*.sh`), which then use them for `az deployment create --parameters` or Function App settings.
Key Settings: List critical settings required (e.g., `AZURE_SUBSCRIPTION_ID`, `AZURE_RESOURCE_GROUP`, `ACR_NAME`, `AZURE_SEARCH_KEY`, `AZURE_OPENAI_KEY`, `AZURE_WEBJOBS_STORAGE`). Note which are typically in `.env`.
Function App: Note that project/shared settings are injected as app settings during ACR deployment.

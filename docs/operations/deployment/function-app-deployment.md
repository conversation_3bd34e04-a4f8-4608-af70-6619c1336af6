# function-app-deployment.md (New)

Goal: Explain the ACR-based Function App part of the deployment.
Content:
Approach: Deploying via container image from ACR (functionappaiscope/functionapp:latest).
Trigger: Called by `deploy_project_resources.py` after main infrastructure.
Script: `scripts/ACR_deployment/deploy_function_app_from_acr.sh` is executed.
IaC: Uses `scripts/ACR_deployment/function_app_deployment.bicep` to create App Service Plan and Function App resource configured for ACR pull.
Parameters: Uses `scripts/ACR_deployment/parameters.json` (dynamically populated by the Python script) to pass project ID, ACR details, service names, etc.
Functions Included: Briefly list the key functions included in the container image (e.g., Event Grid trigger, HTTP triggers).

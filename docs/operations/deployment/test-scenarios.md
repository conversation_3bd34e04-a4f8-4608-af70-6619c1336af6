# Deployment Test Scenarios

This document outlines the test scenarios for project deployment and deletion functionalities in the AI Scope application.

## 1. Project Creation Flow

### 1.1 Basic Project Creation
**Objective**: Verify successful creation of a new project with minimal configuration
**Steps**:
1. Navigate to New Project page
2. Enter project name
3. Select region
4. Submit project creation
**Expected Results**:
- Project document created in CosmosDB
- Azure resources deployed successfully:
  - Storage account created
  - Storage containers created (uploads, input, output)
  - AI Search service created
  - Search resources created (index, indexer, data source)
  - Event Grid System Topic created
  - App Service Plan created (or reused)
  - Function App created (Linux, Python runtime)
  - Function App configured (settings, connection strings)
  - Function code deployed (e.g., from ACR or package)
  - Event Grid Subscription created (linking system topic to function trigger - **Verify this step**)
- Deployment status updates correctly in CosmosDB (`deployment_status` field in project doc).
- Deployment status updates correctly via WebSocket (`/ws/deployment-status/{project_id}`).
- UI reflects status updates accurately.
- User redirected to Projects page upon initiation (deployment happens in background).
- New project appears in project list with 'pending' or 'in_progress' status initially.
- Project status eventually updates to 'completed' or 'failed'.

### 1.2 Project Creation with Invalid Inputs
**Objective**: Verify proper error handling for invalid inputs
**Test Cases**:
1. Empty project name
2. Special characters in project name
3. Invalid region selection
4. Duplicate project name
**Expected Results**:
- Appropriate error messages displayed
- No Azure resources created
- No CosmosDB entries created
- User remains on New Project page

### 1.3 Project Creation API Call Failures
**Objective**: Verify system behavior during failures in the initial synchronous API call (`POST /api/projects`).
**Test Cases**:
1. Failure during initial CosmosDB project document creation.
2. Failure during initial Azure resource creation (e.g., container, search resource, Event Grid System Topic creation within the API call).
3. Network failure during API call.
4. Permission errors during initial resource creation.
**Expected Results**:
- Appropriate error message returned to the user (e.g., 500, 403).
- No project document created in CosmosDB (or it's cleaned up if creation failed after initial save).
- Any partially created Azure resources during the API call are rolled back (e.g., containers deleted).
- No background deployment process is initiated.
- User remains on the New Project page.

### 1.4 Background Deployment Failures (Post-API Call)
**Objective**: Verify system behavior when the background deployment process (`deploy_project_resources.py` triggered via thread/task queue) fails.
**Test Cases**:
1. Failure during Bicep deployment (e.g., invalid template, resource conflict, quota limit).
2. Failure during Function App code deployment (e.g., ACR pull error, package deployment error).
3. Failure configuring Function App settings or connections.
4. Failure creating Event Grid Subscription.
5. Timeout during a long-running deployment step.
6. Network interruption during background deployment.
7. Permission errors encountered by the background process.
**Expected Results**:
- Deployment status in CosmosDB updated to 'failed'.
- Specific error message captured in `deployment_status.message` or `deployment_status.error`.
- WebSocket pushes 'failed' status update.
- UI reflects 'failed' status.
- Rollback mechanism within the deployment script attempts to clean up resources created *during that background process*. (Verify rollback effectiveness).
- Support ticket creation option available in the UI based on 'failed' status.
- Logs capture detailed error information (Verify logging).

## 2. Resource Deployment

### 2.1 Storage Resources
**Objective**: Verify storage resource creation and configuration
**Steps**:
1. Create new project
2. Monitor storage resource creation
**Expected Results**:
- Storage account exists (or is reused).
- Three containers created in the correct storage account:
  - `project-{sanitized-name}-{suffix}-uploads`
  - `project-{sanitized-name}-{suffix}-input`
  - `project-{sanitized-name}-{suffix}-output`
- Container creation status updated in `deployment_status.details.storage.containers`.
- `deployment_status.details.storage_complete` updated to true.
- WebSocket status updates reflect progress.

### 2.2 Search Resources (Index, Indexer, Datasource)
**Objective**: Verify search resource creation and configuration.
**Steps**:
1. Create new project.
2. Monitor search resource creation status via API/WebSocket/CosmosDB.
**Expected Results**:
- Search service exists (or is reused).
- Search index created (`project-{sanitized-name}-index`).
- Search datasource created (`project-{sanitized-name}-ds`), connected to the uploads container using appropriate authentication (e.g., connection string, managed identity).
- Search indexer created (`project-{sanitized-name}-indexer`), linked to datasource and index.
- Status updated in `deployment_status.details.search` (index, indexer, datasource).
- `deployment_status.details.search_complete` updated to true.
- WebSocket status updates reflect progress.

### 2.3 Event Grid System Topic
**Objective**: Verify Event Grid System Topic creation.
**Steps**:
1. Create new project.
2. Monitor Event Grid System Topic creation status.
**Expected Results**:
- Event Grid System Topic created (`evgt-{sanitized-name}-{suffix}`).
- Status updated in `deployment_status.details.function.event_grid_system_topic`.
- WebSocket status updates reflect progress.

### 2.4 Function App and Related Resources
**Objective**: Verify Function App deployment and configuration.
**Steps**:
1. Create new project.
2. Monitor Function App deployment status via API/WebSocket/CosmosDB.
**Expected Results**:
- App Service Plan created or reused (Linux, correct SKU e.g., B1).
- Function App created (`func-{sanitized-name}-{suffix}`, Linux, Python runtime).
- Function App configured with necessary app settings (AzureWebJobsStorage, runtime, etc.).
- Function code for `maturity_assessment` and `executive_summary` deployed successfully (e.g., from ACR or package).
  - Event Grid *Subscription* created, linking the system topic to the appropriate function trigger (e.g., maturity_assessment). **[Critical Verification]**
- Status updated in `deployment_status.details.function` (function_app, event_grid_subscription, function deployments).
- `deployment_status.details.function_complete` updated to true.
- WebSocket status updates reflect progress.

## 3. Deployment Status Tracking

### 3.1 Status Update Mechanisms
**Objective**: Verify status updates via WebSocket and API polling.
**Steps**:
1. Create new project.
2. Establish WebSocket connection to `/ws/deployment-status/{project_id}`.
3. Monitor WebSocket messages for status updates (`deployment_status_update` type).
4. Periodically poll `GET /api/projects/{project_id}/deployment-status`.
5. Check the `deployment_status` field in the project document in CosmosDB directly.
**Expected Results**:
- WebSocket connection established successfully.
- Status updates (pending, in_progress, completed, failed) received via WebSocket in near real-time as deployment progresses.
- WebSocket messages contain the correct nested `details` structure reflecting resource completion.
- Polling the API endpoint returns the current status consistent with CosmosDB.
- The `deployment_status` field in the CosmosDB project document is the source of truth and is updated correctly by the deployment process.
- UI accurately reflects the latest status received from WebSocket or API polling.

### 3.2 Status Update Consistency
**Objective**: Verify consistency between different status update sources and monitor recovery.
**Test Cases**:
1. Simulate failure of WebSocket connection during deployment.
2. Simulate failure of the deployment script to post a status update callback.
3. Simulate delay or failure in `AzureResourceMonitor` checks.
**Expected Results**:
1. UI eventually updates status correctly via API polling fallback.
2. `AzureResourceMonitor` eventually detects actual resource state and updates the status in CosmosDB if the script callback failed. Status corrects on next API poll or WebSocket broadcast (if monitor triggers one).
3. Status remains consistent based on the primary update mechanism (script callbacks/task queue events), with `AzureResourceMonitor` acting as a eventual consistency check.

### 3.2 Progress Calculation
**Objective**: Verify progress calculation accuracy
**Steps**:
1. Create new project
2. Monitor progress percentage
**Expected Results**:
- Progress increases as resources complete
- Final progress reaches 100%
- Individual resource statuses accurate

## 4. Project Deletion

### 4.1 Complete Project Deletion (`DELETE /api/projects/{project_id}`)
**Objective**: Verify complete project cleanup (Azure resources and CosmosDB document).
**Steps**:
1. Create a new project and wait for deployment completion.
2. Send `DELETE /api/projects/{project_id}` request.
3. The API calls the `cleanup_project_resources` helper to remove Azure resources using information stored in Cosmos DB.
4. Monitor Azure portal and CosmosDB.
**Expected Results**:
- API returns 200 OK with deletion results summary.
- Azure resources deleted successfully:
    - Storage containers (uploads, input, output)
    - Search index, indexer, datasource
    - Function App
    - Event Grid System Topic
    - App Service Plan (if not shared/configured for deletion)
- CosmosDB project document deleted.
- Project removed from UI list (after refresh/cache invalidation).
- No orphaned Azure resources related to the project remain.
- Cache for project list invalidated.

### 4.2 Partial Deletion Handling
**Objective**: Verify system behavior when deletion of some Azure resources fails.
**Test Cases**:
1. Simulate permission issue preventing deletion of one resource type (e.g., Function App).
2. Simulate a resource lock preventing deletion.
3. Simulate network failure during deletion API calls.
**Expected Results**:
- API attempts to delete all specified resources.
- API returns 200 OK (or potentially 500 if the error is critical) but the `results` field in the response indicates which deletions failed.
- Errors during deletion are logged server-side.
- CosmosDB project document *is still deleted* (current implementation). **[Consider if this is desired behavior - maybe leave the doc if Azure cleanup fails?]**
- User needs to manually clean up any remaining Azure resources based on logs or API response.

## 5. Error Handling and Recovery

### 5.1 Deployment Failure Rollback (Background Process)
**Objective**: Verify rollback mechanism within the background deployment process.
**Steps**:
1. Introduce a failure condition within `deploy_project_resources.py` (e.g., force Bicep deployment to fail after creating some resources).
2. Create a new project to trigger the faulty deployment.
3. Monitor resource creation and deletion in Azure.
4. Check final deployment status.
**Expected Results**:
- Deployment status eventually becomes 'failed'.
- Resources created *by the background script* before the failure point are deleted by the script's rollback logic (e.g., Function App deleted if Bicep failed after creating it).
- Resources created *before* the background script started (e.g., initial containers/search resources created in `app.py`'s API call) are *not* deleted by this rollback (they should have been rolled back if the initial API call failed).
- Final state in Azure should ideally be clean, or only contain resources created before the background process started if the initial API call succeeded but background failed.

### 5.2 Support Ticket Creation (`POST /api/projects/{project_id}/support-ticket`)
**Objective**: Verify support ticket creation process via API.
**Steps**:
1. Create a project and let it fail (or manually set status to failed).
2. Send POST request to `/api/projects/{project_id}/support-ticket` with required JSON body (email, error details, resource statuses).
3. Check CosmosDB for the new support ticket document (`type='support_ticket'`).
4. Check the project document for the added ticket reference in `support_tickets` array.
**Expected Results**:
- API returns 201 Created with the new ticket ID.
- Support ticket document created in CosmosDB containing project ID, user ID, email, error details, resource statuses, deployment status snapshot, timestamp, and 'open' status.
- Project document updated with a reference to the created ticket.
- (Optional: Verify email notification if implemented).

## 6. Performance Testing

### 6.1 Concurrent Project Creations
**Objective**: Verify system behavior when multiple projects are created concurrently.
**Steps**:
1. Initiate creation of multiple (e.g., 5-10) projects simultaneously or in rapid succession via API calls.
2. Monitor the status of all deployments.
3. Check Azure for resource naming conflicts.
**Expected Results**:
- All project creation API calls succeed and return 201.
- Background deployment processes are initiated for all projects (handled sequentially if using threads, potentially parallel if using a proper task queue).
- All deployments eventually complete successfully (or fail gracefully with correct status).
- No Azure resource naming conflicts occur due to unique suffix generation.
- Status updates via WebSocket and API remain accurate for each project.
- System remains responsive during concurrent operations.

### 6.2 Concurrent Project Deletions
**Objective**: Verify system behavior under load during deletion.
**Steps**:
1. Create multiple projects.
2. Initiate deletion of multiple projects simultaneously or in rapid succession via API calls.
3. Monitor Azure resource deletion and CosmosDB.
**Expected Results**:
- All deletion API calls return successfully (e.g., 200 OK).
- All specified Azure resources for the deleted projects are successfully removed.
- All corresponding project documents are removed from CosmosDB.
- System remains responsive.

### 6.3 Deployment Performance
**Objective**: Measure the time taken for project deployment.
**Steps**:
1. Create a new project.
2. Record the start time (API call submission).
3. Monitor the deployment status until it reaches 'completed'.
4. Record the end time.
5. Calculate the total deployment duration.
**Expected Results**:
- Deployment completes within an acceptable timeframe (e.g., < 5-10 minutes, depending on complexity).
- (Optional: Track timing for individual resource groups - storage, search, function app - if status details provide timestamps).

### 6.4 Resource Cleanup Performance
**Objective**: Measure the time taken for project deletion.
**Steps**:
1. Create a project and wait for completion.
2. Record the start time (API call submission for DELETE).
3. Monitor Azure resources until they are removed.
4. Record the end time.
5. Calculate the total deletion duration.
**Expected Results**:
- Deletion completes within an acceptable timeframe (e.g., < 5 minutes).

## 7. Security Testing

### 7.1 Access Control (RBAC)
**Objective**: Verify Role-Based Access Control for project operations.
**Test Cases**:
1. Attempt project creation (`POST /api/projects`) without valid authentication headers (`X-MS-CLIENT-PRINCIPAL`).
2. Attempt project creation with valid headers but insufficient roles.
3. Attempt to get project list (`GET /api/projects`) as User A, verify only User A's projects are returned.
4. Attempt to get specific project config (`GET /api/projects/{project_id}/config`) for a project owned by User B, logged in as User A.
5. Attempt to delete (`DELETE /api/projects/{project_id}`) a project owned by User B, logged in as User A.
6. Attempt to update deployment status (`POST /api/projects/{project_id}/deployment-status`) for a project owned by User B, logged in as User A (unless called by deployment script/monitor).
7. Attempt to upload files (`POST /api/projects/{project_id}/upload`) to a project owned by User B, logged in as User A.
**Expected Results**:
- Unauthorized requests (no header, invalid header, insufficient roles) receive 401 or 403 errors.
- Users can only list, view details of, upload to, and delete projects they have appropriate permissions for (e.g., 'owner' role).
- Deployment status updates might have different authorization logic (e.g., allowed from internal deployment processes).
- Security logs (if configured) record access attempts and denials.

### 7.2 Resource Isolation
**Objective**: Verify resource isolation between projects
**Steps**:
1. Create multiple projects
2. Attempt cross-project access
**Expected Results**:
- Resources properly isolated
- No cross-project access possible
- Security policies enforced

## 8. Monitoring and Logging

### 8.1 Application and Deployment Logging
**Objective**: Verify comprehensive logging for diagnostics and auditing.
**Steps**:
1. Create a new project.
2. Simulate a deployment failure.
3. Delete a project.
4. Check application logs (e.g., console output, Application Insights if configured).
**Expected Results**:
- Key events logged: Project creation request, start/end of Azure resource creation steps (initial API call + background process), status updates, errors, rollback attempts, project deletion request, resource deletion steps.
- Logs include relevant context: Project ID, User ID (where applicable), resource names, timestamps.
- Error logs contain detailed stack traces and context.
- Logs generated by the background deployment process (`deploy_project_resources.py`) are captured.

### 8.2 Status Audit Trail (CosmosDB)
**Objective**: Verify that the `deployment_status` field in CosmosDB provides an audit trail.
**Steps**:
1. Create a new project.
2. Periodically retrieve the project document from CosmosDB during deployment.
3. Observe changes to the `deployment_status` field (status, message, details, updated_at).
**Expected Results**:
- The `deployment_status` field reflects the progression of the deployment.
- `updated_at` timestamp is updated with each significant status change.
- `details` object shows the completion status of individual resource groups.
- Final status ('completed' or 'failed') and associated message are recorded correctly.

## 9. Specific Scenario Tests (Updates)

### 9.1 Event Grid Subscription Linking
**Objective**: Verify Event Grid subscription correctly links storage to the deployed function.
**Steps**:
1. Create a new project.
2. After deployment, upload a file to the project's 'uploads' container.
3. Check Function App logs (e.g., Application Insights) for the Event Grid triggered function (e.g., `maturity_assessment`).
4. Verify the function processed the uploaded file.
**Expected Results**:
- Event Grid trigger fires in the Function App upon blob creation.
- Function logs indicate successful processing of the event.
- (If applicable) Downstream effects of the function are observed (e.g., new entry in CosmosDB, output blob created).

### 9.2 Deployment with Configuration Errors
**Objective**: Verify system behavior with missing or incorrect deployment configuration.
**Test Cases**:
1. Attempt deployment with an invalid/non-existent ACR image tag specified in `config.yaml` or environment variables used by `deploy_function_app_from_acr.sh`.
2. Attempt deployment with a critical environment variable missing (e.g., `AZURE_SEARCH_KEY` if Function App relies on it directly and it's not passed via Bicep).
3. Attempt deployment with incorrect resource group or subscription ID.
**Expected Results**:
- Deployment fails at the relevant stage (e.g., Function App deployment for ACR tag, script error for missing env var).
- `deployment_status` in CosmosDB is updated to 'failed' or 'partial_success'.
- Error message in `deployment_status.message` is informative.
- Logs (`logs/` directory, Azure Portal) provide details about the configuration error.
- No unexpected resource creation or charges.

### 9.3 Recovery Mechanisms Test
**Objective**: Verify recovery mechanisms for stuck or failed deployments.
**Test Cases**:
1. **`force_update` API Parameter:**
    a. Manually set a project's `deployment_status.status` in CosmosDB to 'in_progress' but ensure all Azure resources *are* actually deployed.
    b. Call `GET /api/projects/{project_id}/deployment-status?force_update=true`.
    c. Verify the status in CosmosDB is updated to 'completed'.
2. **Recovery Scripts (e.g., `force_update_*.py`):**
    a. Simulate a scenario where a deployment is stuck (e.g., `deploy_project_resources.py` crashed mid-way, status is 'in_progress').
    b. Run the relevant recovery script (e.g., `python force_update_deployment_status.py {project_id} completed --message "Manual recovery"`).
    c. Verify the status in CosmosDB is updated as specified.
**Expected Results**:
- `force_update=true` correctly re-evaluates Azure resources and updates the status in CosmosDB.
- Recovery scripts successfully update the deployment status in CosmosDB to the desired state.
- WebSocket broadcasts the corrected status.

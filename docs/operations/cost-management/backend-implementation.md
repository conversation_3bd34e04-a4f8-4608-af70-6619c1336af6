# Cost Analytics Backend Implementation Guide

This guide outlines the implementation of backend APIs needed to support the enhanced Cost Analytics dashboard with shared resources tracking, container/indexer level cost tracking, and region-specific views.

## Overview

The enhanced Cost Analytics dashboard requires several new backend APIs to:

1. Retrieve cost data at various levels of granularity (project, service, resource, container, indexer)
2. Filter cost data by region, project, resource type, and shared/dedicated status
3. Apply role-based access control to cost data (Super Admin vs Regional Admin views)

## API Endpoints

### 1. Cost Overview API

**Endpoint:** `GET /api/cost/overview`

**Purpose:** Retrieve aggregated cost data for the overview dashboard

**Query Parameters:**
- `timeRange`: The time period for cost data (week, month, quarter, year)
- `regionId`: (Optional) Filter by region ID
- `projectId`: (Optional) Filter by project ID
- `includeShared`: (Optional) Whether to include shared resources (default: true)

**Response:**
```json
{
  "projectCosts": [
    {
      "project": "Project Name",
      "projectId": "project-uuid",
      "cost": 5200,
      "budget": 8000,
      "region": "West US 2",
      "regionId": "region-uuid"
    }
  ],
  "serviceCosts": [
    {
      "service": "Storage",
      "cost": 2150,
      "isShared": false
    }
  ],
  "regionCosts": [
    {
      "region": "West US 2",
      "regionId": "region-uuid",
      "cost": 2950
    }
  ],
  "totalCost": 6050
}
```

### 2. Resource Costs API

**Endpoint:** `GET /api/cost/resources`

**Purpose:** Retrieve cost data for Azure resources

**Query Parameters:**
- `timeRange`: The time period for cost data
- `regionId`: (Optional) Filter by region ID
- `projectId`: (Optional) Filter by project ID
- `resourceType`: (Optional) Filter by resource type (storage, search, function, etc.)
- `includeShared`: (Optional) Whether to include shared resources

**Response:**
```json
{
  "resources": [
    {
      "name": "stprojecta1234",
      "resourceId": "azure-resource-id",
      "resourceType": "storage",
      "cost": 1200,
      "isShared": false,
      "region": "West US 2",
      "regionId": "region-uuid"
    }
  ],
  "totalCost": 3500
}
```

### 3. Container Costs API

**Endpoint:** `GET /api/cost/containers`

**Purpose:** Retrieve cost data for blob containers

**Query Parameters:**
- `timeRange`: The time period for cost data
- `regionId`: (Optional) Filter by region ID
- `projectId`: (Optional) Filter by project ID
- `storageAccount`: (Optional) Filter by storage account name
- `searchQuery`: (Optional) Search by container name

**Response:**
```json
{
  "containers": [
    {
      "name": "uploads-project-a-1234",
      "storageAccount": "stprojecta1234",
      "cost": 400,
      "projectId": "project-uuid",
      "project": "Project A",
      "region": "West US 2",
      "regionId": "region-uuid"
    }
  ],
  "totalCost": 1800
}
```

### 4. Indexer Costs API

**Endpoint:** `GET /api/cost/indexers`

**Purpose:** Retrieve cost data for search indexers

**Query Parameters:**
- `timeRange`: The time period for cost data
- `regionId`: (Optional) Filter by region ID
- `projectId`: (Optional) Filter by project ID
- `searchService`: (Optional) Filter by search service name
- `searchQuery`: (Optional) Search by indexer name

**Response:**
```json
{
  "indexers": [
    {
      "name": "project-a-indexer",
      "searchService": "search-projecta1234",
      "cost": 300,
      "projectId": "project-uuid",
      "project": "Project A",
      "region": "West US 2",
      "regionId": "region-uuid"
    }
  ],
  "totalCost": 1050
}
```

## Implementation Steps

### 1. Azure Cost Management Integration

1. Create a service to interact with the Azure Cost Management API:

```python
# cost_management_service.py
import os
import logging
from datetime import datetime, timedelta
from azure.identity import DefaultAzureCredential
from azure.mgmt.costmanagement import CostManagementClient
from azure.mgmt.costmanagement.models import QueryDefinition, QueryTimePeriod, QueryDataset, QueryAggregation

class CostManagementService:
    def __init__(self):
        self.credential = DefaultAzureCredential()
        self.subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
        self.client = CostManagementClient(self.credential, self.subscription_id)
        
    def get_time_period(self, time_range):
        """Convert time_range to from/to dates for the Cost Management API."""
        end_date = datetime.utcnow()
        
        if time_range == "week":
            start_date = end_date - timedelta(days=7)
        elif time_range == "month":
            start_date = end_date.replace(day=1)
        elif time_range == "quarter":
            month = ((end_date.month - 1) // 3) * 3 + 1
            start_date = end_date.replace(month=month, day=1)
        elif time_range == "year":
            start_date = end_date.replace(month=1, day=1)
        else:
            start_date = end_date - timedelta(days=30)  # Default to 30 days
            
        return QueryTimePeriod(
            from_property=start_date.strftime("%Y-%m-%dT00:00:00Z"),
            to=end_date.strftime("%Y-%m-%dT23:59:59Z")
        )
    
    def query_cost_by_tag(self, time_range, tag_name, tag_value=None):
        """Query costs filtered by a specific tag."""
        time_period = self.get_time_period(time_range)
        
        # Build filter for tags
        tag_filter = None
        if tag_value:
            tag_filter = f"tags['{tag_name}'] eq '{tag_value}'"
        
        # Define the query
        query = QueryDefinition(
            type="ActualCost",
            timeframe="Custom",
            time_period=time_period,
            dataset=QueryDataset(
                granularity="None",
                aggregation={
                    "totalCost": {
                        "name": "Cost",
                        "function": "Sum"
                    }
                },
                grouping=[
                    {
                        "type": "Dimension",
                        "name": "ResourceType"
                    }
                ],
                filter=tag_filter
            )
        )
        
        # Execute the query
        scope = f"/subscriptions/{self.subscription_id}"
        result = self.client.query.usage(scope=scope, parameters=query)
        
        return result
    
    def query_cost_by_resource(self, time_range, resource_group=None, resource_type=None):
        """Query costs grouped by resource."""
        time_period = self.get_time_period(time_range)
        
        # Build filter
        filters = []
        if resource_group:
            filters.append(f"ResourceGroup eq '{resource_group}'")
        if resource_type:
            filters.append(f"ResourceType eq '{resource_type}'")
            
        filter_str = " and ".join(filters) if filters else None
        
        # Define the query
        query = QueryDefinition(
            type="ActualCost",
            timeframe="Custom",
            time_period=time_period,
            dataset=QueryDataset(
                granularity="None",
                aggregation={
                    "totalCost": {
                        "name": "Cost",
                        "function": "Sum"
                    }
                },
                grouping=[
                    {
                        "type": "Dimension",
                        "name": "ResourceId"
                    }
                ],
                filter=filter_str
            )
        )
        
        # Execute the query
        scope = f"/subscriptions/{self.subscription_id}"
        result = self.client.query.usage(scope=scope, parameters=query)
        
        return result
```

### 2. Cost Allocation for Container and Indexer Level

Since Azure doesn't provide container or indexer level cost data directly, implement a custom allocation algorithm:

```python
# cost_allocation_service.py
import logging
from azure.storage.blob import BlobServiceClient
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential

class CostAllocationService:
    def __init__(self, storage_connection_string, search_service_endpoint, search_api_key):
        self.blob_service_client = BlobServiceClient.from_connection_string(storage_connection_string)
        self.search_endpoint = search_service_endpoint
        self.search_credential = AzureKeyCredential(search_api_key)
        
    def get_container_metrics(self, storage_account_name):
        """Get usage metrics for all containers in a storage account."""
        metrics = {}
        
        try:
            # Get a client for the specific storage account
            account_url = f"https://{storage_account_name}.blob.core.windows.net"
            account_client = BlobServiceClient(account_url=account_url, credential=self.blob_service_client.credential)
            
            # List all containers
            containers = account_client.list_containers()
            
            for container in containers:
                container_client = account_client.get_container_client(container.name)
                
                # Calculate total size and transaction count
                total_size_bytes = 0
                blob_count = 0
                
                for blob in container_client.list_blobs():
                    total_size_bytes += blob.size
                    blob_count += 1
                
                # Convert to GB for cost calculation
                total_size_gb = total_size_bytes / (1024 * 1024 * 1024)
                
                metrics[container.name] = {
                    "size_gb": total_size_gb,
                    "blob_count": blob_count
                }
                
        except Exception as e:
            logging.error(f"Error getting container metrics for {storage_account_name}: {str(e)}")
            
        return metrics
    
    def get_indexer_metrics(self, search_service_name):
        """Get usage metrics for all indexers in a search service."""
        metrics = {}
        
        try:
            # Use the admin client to get indexer statistics
            admin_client = SearchClient(
                endpoint=f"https://{search_service_name}.search.windows.net",
                index_name="",  # Not needed for admin operations
                credential=self.search_credential
            )
            
            # List all indexers
            indexers_response = admin_client.get_indexers()
            
            for indexer in indexers_response:
                # Get indexer statistics
                indexer_stats = admin_client.get_indexer_status(indexer.name)
                
                metrics[indexer.name] = {
                    "document_count": indexer_stats.document_count,
                    "storage_size_mb": indexer_stats.storage_size / (1024 * 1024),
                    "operation_count": len(indexer_stats.execution_history) if indexer_stats.execution_history else 0
                }
                
        except Exception as e:
            logging.error(f"Error getting indexer metrics for {search_service_name}: {str(e)}")
            
        return metrics
    
    def allocate_storage_costs(self, storage_account_name, total_cost, container_metrics):
        """Allocate storage account costs to individual containers based on usage."""
        allocated_costs = {}
        
        # Calculate total usage across all containers
        total_size_gb = sum(metrics["size_gb"] for metrics in container_metrics.values())
        total_blobs = sum(metrics["blob_count"] for metrics in container_metrics.values())
        
        # Assume 80% of cost is storage, 20% is transactions (adjust based on your usage pattern)
        storage_cost_portion = total_cost * 0.8
        transaction_cost_portion = total_cost * 0.2
        
        for container_name, metrics in container_metrics.items():
            # Allocate storage cost based on size proportion
            if total_size_gb > 0:
                storage_cost = storage_cost_portion * (metrics["size_gb"] / total_size_gb)
            else:
                storage_cost = 0
                
            # Allocate transaction cost based on blob count proportion
            if total_blobs > 0:
                transaction_cost = transaction_cost_portion * (metrics["blob_count"] / total_blobs)
            else:
                transaction_cost = 0
                
            allocated_costs[container_name] = storage_cost + transaction_cost
        
        return allocated_costs
    
    def allocate_search_costs(self, search_service_name, total_cost, indexer_metrics):
        """Allocate search service costs to individual indexers based on usage."""
        allocated_costs = {}
        
        # Calculate total usage across all indexers
        total_docs = sum(metrics["document_count"] for metrics in indexer_metrics.values())
        total_storage = sum(metrics["storage_size_mb"] for metrics in indexer_metrics.values())
        total_operations = sum(metrics["operation_count"] for metrics in indexer_metrics.values())
        
        # Assume 40% of cost is storage, 40% is query operations, 20% is indexing operations
        storage_cost_portion = total_cost * 0.4
        query_cost_portion = total_cost * 0.4
        indexing_cost_portion = total_cost * 0.2
        
        for indexer_name, metrics in indexer_metrics.items():
            # Allocate storage cost based on document count and storage size
            if total_storage > 0:
                storage_cost = storage_cost_portion * (metrics["storage_size_mb"] / total_storage)
            else:
                storage_cost = 0
                
            # Allocate query cost based on document count (as a proxy for query volume)
            if total_docs > 0:
                query_cost = query_cost_portion * (metrics["document_count"] / total_docs)
            else:
                query_cost = 0
                
            # Allocate indexing cost based on operation count
            if total_operations > 0:
                indexing_cost = indexing_cost_portion * (metrics["operation_count"] / total_operations)
            else:
                indexing_cost = 0
                
            allocated_costs[indexer_name] = storage_cost + query_cost + indexing_cost
        
        return allocated_costs
```

### 3. API Implementation

Implement the API endpoints using FastAPI:

```python
# cost_api.py
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List
from pydantic import BaseModel
from .cost_management_service import CostManagementService
from .cost_allocation_service import CostAllocationService
from .rbac_service import RbacService, get_current_user, UserRole

router = APIRouter(prefix="/api/cost", tags=["cost"])

# Models for response data
class ProjectCost(BaseModel):
    project: str
    projectId: str
    cost: float
    budget: float
    region: str
    regionId: str

class ServiceCost(BaseModel):
    service: str
    cost: float
    isShared: bool

class ResourceCost(BaseModel):
    name: str
    resourceId: str
    resourceType: str
    cost: float
    isShared: bool
    region: str
    regionId: str

class ContainerCost(BaseModel):
    name: str
    storageAccount: str
    cost: float
    projectId: str
    project: str
    region: str
    regionId: str

class IndexerCost(BaseModel):
    name: str
    searchService: str
    cost: float
    projectId: str
    project: str
    region: str
    regionId: str

class RegionCost(BaseModel):
    region: str
    regionId: str
    cost: float

class CostOverviewResponse(BaseModel):
    projectCosts: List[ProjectCost]
    serviceCosts: List[ServiceCost]
    regionCosts: List[RegionCost]
    totalCost: float

class ResourceCostsResponse(BaseModel):
    resources: List[ResourceCost]
    totalCost: float

class ContainerCostsResponse(BaseModel):
    containers: List[ContainerCost]
    totalCost: float

class IndexerCostsResponse(BaseModel):
    indexers: List[IndexerCost]
    totalCost: float

# Services
cost_management_service = CostManagementService()
cost_allocation_service = CostAllocationService(
    storage_connection_string=os.environ.get("AZURE_STORAGE_CONNECTION_STRING"),
    search_service_endpoint=os.environ.get("AZURE_SEARCH_SERVICE_ENDPOINT"),
    search_api_key=os.environ.get("AZURE_SEARCH_API_KEY")
)
rbac_service = RbacService()

# Helper function to apply RBAC filtering
def filter_by_user_role(data, user):
    """Filter cost data based on user role and region."""
    if user.role == UserRole.SUPER_ADMIN:
        return data
    
    if user.role == UserRole.REGIONAL_ADMIN and user.region_id:
        # Filter to only show data for the user's region
        return {
            key: [item for item in items if getattr(item, "regionId", None) == user.region_id]
            for key, items in data.items()
        }
    
    # Regular users don't see cost data
    return {key: [] for key in data}

@router.get("/overview", response_model=CostOverviewResponse)
async def get_cost_overview(
    time_range: str = Query("month", description="Time range for cost data"),
    region_id: Optional[str] = Query(None, description="Filter by region ID"),
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    include_shared: bool = Query(True, description="Include shared resources"),
    current_user = Depends(get_current_user)
):
    """Get cost overview data for the dashboard."""
    # Check if user has permission to view cost data
    if current_user.role == UserRole.REGULAR_USER:
        raise HTTPException(status_code=403, detail="Not authorized to view cost data")
    
    # Get cost data from Azure Cost Management
    project_costs = await get_project_costs(time_range, region_id, project_id)
    service_costs = await get_service_costs(time_range, region_id, project_id, include_shared)
    region_costs = await get_region_costs(time_range)
    
    # Apply RBAC filtering
    if current_user.role == UserRole.REGIONAL_ADMIN and current_user.region_id:
        project_costs = [p for p in project_costs if p.regionId == current_user.region_id]
        region_costs = [r for r in region_costs if r.regionId == current_user.region_id]
        # Service costs are shown but will be filtered on the frontend
    
    # Calculate total cost
    total_cost = sum(p.cost for p in project_costs)
    
    return CostOverviewResponse(
        projectCosts=project_costs,
        serviceCosts=service_costs,
        regionCosts=region_costs,
        totalCost=total_cost
    )

@router.get("/resources", response_model=ResourceCostsResponse)
async def get_resource_costs(
    time_range: str = Query("month", description="Time range for cost data"),
    region_id: Optional[str] = Query(None, description="Filter by region ID"),
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    include_shared: bool = Query(True, description="Include shared resources"),
    current_user = Depends(get_current_user)
):
    """Get cost data for Azure resources."""
    # Implementation details...
    pass

@router.get("/containers", response_model=ContainerCostsResponse)
async def get_container_costs(
    time_range: str = Query("month", description="Time range for cost data"),
    region_id: Optional[str] = Query(None, description="Filter by region ID"),
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    storage_account: Optional[str] = Query(None, description="Filter by storage account"),
    search_query: Optional[str] = Query(None, description="Search by container name"),
    current_user = Depends(get_current_user)
):
    """Get cost data for blob containers."""
    # Implementation details...
    pass

@router.get("/indexers", response_model=IndexerCostsResponse)
async def get_indexer_costs(
    time_range: str = Query("month", description="Time range for cost data"),
    region_id: Optional[str] = Query(None, description="Filter by region ID"),
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    search_service: Optional[str] = Query(None, description="Filter by search service"),
    search_query: Optional[str] = Query(None, description="Search by indexer name"),
    current_user = Depends(get_current_user)
):
    """Get cost data for search indexers."""
    # Implementation details...
    pass
```

### 4. Scheduled Cost Data Collection

To improve performance and reduce API calls to Azure Cost Management, implement a scheduled task to collect and cache cost data:

```python
# cost_collection_task.py
import asyncio
import logging
from datetime import datetime
import os
import json
from azure.cosmos import CosmosClient
from .cost_management_service import CostManagementService
from .cost_allocation_service import CostAllocationService

class CostCollectionTask:
    def __init__(self):
        # Initialize services
        self.cost_management_service = CostManagementService()
        self.cost_allocation_service = CostAllocationService(
            storage_connection_string=os.environ.get("AZURE_STORAGE_CONNECTION_STRING"),
            search_service_endpoint=os.environ.get("AZURE_SEARCH_SERVICE_ENDPOINT"),
            search_api_key=os.environ.get("AZURE_SEARCH_API_KEY")
        )
        
        # Initialize Cosmos DB client
        cosmos_endpoint = os.environ.get("COSMOSDB_ENDPOINT")
        cosmos_key = os.environ.get("COSMOSDB_KEY")
        cosmos_database = os.environ.get("COSMOSDB_DATABASE")
        
        self.cosmos_client = CosmosClient(cosmos_endpoint, cosmos_key)
        self.database = self.cosmos_client.get_database_client(cosmos_database)
        self.cost_container = self.database.get_container_client("cost_data")
        
    async def collect_and_store_cost_data(self):
        """Collect cost data from Azure and store in Cosmos DB."""
        try:
            logging.info("Starting cost data collection task")
            
            # Get current timestamp
            timestamp = datetime.utcnow().isoformat()
            
            # Query project, region, and resource group costs
            project_result = await self.cost_management_service.query_cost_by_tag(
                "month", "project-id"
            )
            region_result = await self.cost_management_service.query_cost_by_tag(
                "month", "ResourceLocation"
            )
            rg_result = await self.cost_management_service.query_cost_by_dimension(
                "month", "ResourceGroupName"
            )

            project_data = project_result.as_dict() if hasattr(project_result, "as_dict") else project_result
            region_data = region_result.as_dict() if hasattr(region_result, "as_dict") else region_result
            rg_data = rg_result.as_dict() if hasattr(rg_result, "as_dict") else rg_result

            project_costs = self.aggregate_project_costs_from_rows(project_data.get("rows", []))
            region_costs = self.aggregate_region_costs_from_rows(region_data.get("rows", []))
            rg_costs = self.aggregate_resource_group_costs_from_rows(rg_data.get("rows", []))

            # Store in Cosmos DB
            cost_data = {
                "id": f"cost-{timestamp}",
                "timestamp": timestamp,
                "rawCosts": {
                    "projects": project_data,
                    "regions": region_data,
                    "resourceGroups": rg_data
                },
                "projectCosts": project_costs,
                "regionCosts": region_costs,
                "resourceGroupCosts": rg_costs
            }
            
            await self.cost_container.upsert_item(cost_data)
            
            logging.info("Cost data collection completed successfully")
            
        except Exception as e:
            logging.error(f"Error in cost data collection task: {str(e)}")
    
    async def collect_resource_costs(self):
        """Collect cost data for all Azure resources."""
        # Implementation details...
        pass
    
    async def collect_container_costs(self, resource_costs):
        """Collect and allocate costs for blob containers."""
        # Implementation details...
        pass
    
    async def collect_indexer_costs(self, resource_costs):
        """Collect and allocate costs for search indexers."""
        # Implementation details...
        pass
    
    def aggregate_project_costs(self, resource_costs, container_costs, indexer_costs):
        """Aggregate costs by project."""
        # Implementation details...
        pass
    
    def aggregate_service_costs(self, resource_costs):
        """Aggregate costs by service type."""
        # Implementation details...
        pass
    
    def aggregate_region_costs(self, resource_costs):
        """Aggregate costs by region."""
        # Implementation details...
        pass

# Function to run the task
async def run_cost_collection_task():
    task = CostCollectionTask()
    await task.collect_and_store_cost_data()

# Schedule the task to run hourly
def schedule_cost_collection():
    import aioschedule
    import asyncio

    aioschedule.every().hour.do(run_cost_collection_task)
    
    while True:
        asyncio.run(aioschedule.run_pending())
        time.sleep(3600)  # Check every hour
```

#### Example `cost_data` Document

After each run the task stores an aggregated document in Cosmos DB. With region
and resource group aggregation enabled it looks like:

```json
{
  "id": "cost-2024-06-02T12:00:00Z",
  "timestamp": "2024-06-02T12:00:00Z",
  "rawCosts": {"projects": {...}, "regions": {...}},
  "projectCosts": [
    {"projectId": "proj-1", "cost": 2200}
  ],
  "regionCosts": [
    {"region": "West US 2", "cost": 1200}
  ],
  "resourceGroupCosts": [
    {"resourceGroup": "rg-internal-ai", "cost": 1500}
  ]
}
```

`regionCosts` sums all resource spending within each Azure region. The optional
`resourceGroupCosts` field totals costs for each resource group, calculated from
the raw resource-level data.

Both arrays are generated by helper methods (`aggregate_region_costs_from_rows`
and `aggregate_resource_group_costs_from_rows`) that iterate over the rows
returned by the Azure Cost Management queries and accumulate the cost values for
each unique key.

The returned rows follow a fixed ordering:

* `row[0]` – cost value
* `row[1]` – dimension name (region or resource group)
* `row[2]` – currency

These helpers rely on this ordering to parse the values correctly.

### 5. Integration with RBAC

Ensure the cost APIs respect the RBAC permissions:

1. Super Admins can see all cost data
2. Regional Admins can only see cost data for their region
3. Regular Users cannot access cost data

This is implemented in the API endpoints by filtering data based on the user's role and region.

## Deployment

1. Add the new services and API endpoints to your FastAPI application
2. Configure the following environment variables required by the cost collection task:
   - `AZURE_SUBSCRIPTION_ID`
   - `AZURE_STORAGE_CONNECTION_STRING`
   - `AZURE_SEARCH_SERVICE_ENDPOINT`
   - `AZURE_SEARCH_API_KEY`
   - `COSMOSDB_ENDPOINT`
   - `COSMOSDB_KEY`
   - `COSMOSDB_DATABASE`
3. Deploy the scheduled task to run hourly
4. Update the frontend to use the new APIs

## Testing

1. Test the APIs with different user roles to ensure RBAC is working correctly
2. Verify that cost data is correctly allocated to containers and indexers
3. Test filtering by region, project, and resource type
4. Verify that shared resources are correctly identified and can be filtered

## Conclusion

This implementation provides a comprehensive solution for tracking costs at various levels of granularity, including container and indexer level costs that are not directly available from Azure Cost Management. The role-based filtering ensures that users only see cost data relevant to their permissions.

## Implementation Status

The repository includes a starter implementation of the `/api/cost/overview` endpoint and placeholder services for querying Azure Cost Management and allocating container and indexer costs. These services can be extended with real Azure queries and scheduled collection tasks to complete the Cost Analytics integration.

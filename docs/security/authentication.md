# Authentication Overview

This document describes the current authentication flows used in AI Scope and lists current shortcomings.

## Frontend

- Uses **MSAL** to authenticate against Azure AD.

- Access and ID tokens are cached in `localStorage` under keys such as `accessToken`, `msalAccount`, and `isAuthenticated`.
- API requests include the token in the `Authorization` header via `authHeaderService.ts`.
- The login page uses a mix of MSAL redirects and custom code to handle the redirect URI.

## Backend

- API routes depend on the helper `get_authenticated_user` in `rbac_routes.py` which checks the environment variable `USE_ENTRA_AUTH`.
- When Entra ID is enabled it extracts the bearer token from the request and calls Microsoft Graph via `get_entra_user_with_delegated_token`.
- If Entra ID is not configured it falls back to a local mock user defined in `backend/auth/mock_auth.py`.
- Tokens are not stored server side. The backend relies entirely on the token supplied with each request and keeps a small in-memory cache for validation.


## Environment Variables

- `USE_ENTRA_AUTH` – enable Entra ID authentication.
- `ALLOW_CLIENT_CREDENTIALS_FALLBACK` – when set to `true`, missing or invalid tokens fall back to client credentials. Default is `false`.
- `RBAC_LOG_LEVEL` – controls backend RBAC logging verbosity.

## Shortcomings and Inconsistencies

1. **LocalStorage Persistence** – Storing tokens and an `isAuthenticated` flag in `localStorage` exposes them to JavaScript and potential XSS attacks. Session storage or cookies with `HttpOnly` would be safer.
2. **Mock Authentication Fallback** – When `ALLOW_CLIENT_CREDENTIALS_FALLBACK` is enabled the backend can fall back to a mock user or client credentials if no token is provided. This is now disabled by default to prevent privilege escalation.

3. **Role Source Mismatch** – The frontend defaults to a Super Admin role if user data cannot be fetched, while the backend enforces roles from Cosmos DB. This can lead to mismatched UI behaviour.
4. **Token Cache Divergence** – Tokens cached on the client may expire while the backend expects fresh tokens on each call, leading to authentication errors if renewal fails.
5. **Configuration Flags** – The `USE_ENTRA_AUTH` flag controls whether the backend validates tokens. Misconfiguration could leave the API unprotected even if the frontend believes authentication succeeded.

Proper alignment between frontend token handling and backend validation should be ensured before deploying to production.

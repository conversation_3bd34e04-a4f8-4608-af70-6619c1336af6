# Deployment Blob Storage Logging

This document describes the blob storage logging functionality for deployment logs.

## Overview

Deployment logs are now stored in Azure Blob Storage instead of just local files. This provides:
- Centralized log storage accessible from anywhere
- Persistent logs that survive container/pod restarts
- Organized folder structure by project and deployment
- Easy access to logs from Azure Web Apps

## Folder Structure

Logs are organized in blob storage with the following structure:
```
deployment-logs/
├── {project-id}/
│   ├── {deployment-id}/
│   │   └── {blob_name_suffix}_{timestamp}.log
│   ├── {deployment-id}/
│   │   └── {blob_name_suffix}_{timestamp}.log
│   └── ...
└── ...
```

Each deployment gets its own unique subfolder under the project folder.
The log file name defaults to `deployment_{timestamp}.log` but you can specify a
custom suffix using the `blob_name_suffix` parameter (e.g. `cleanup_{timestamp}.log`).

## Configuration

The blob logging is configured through environment variables:

### Required Configuration
- `DEPLOYMENT_LOG_STORAGE_ACCOUNT`: The name of your Azure Storage Account

### Optional Configuration
- `DEPLOYMENT_LOG_STORAGE_KEY`: Storage account key (if not provided, uses managed identity)
- `DEPLOYMENT_LOG_CONTAINER`: Container name (default: "deployment-logs")
- `ENABLE_BLOB_LOGGING`: Enable/disable blob logging (default: "true")
- `ENABLE_LOCAL_LOGGING`: Enable/disable local file logging (default: "true")
- `{LOGGER_NAME}_CONSOLE_LOG_LEVEL`: Override console log level for a specific logger (e.g., `RBAC_CONSOLE_LOG_LEVEL`)
- `DEFAULT_CONSOLE_LOG_LEVEL`: Fallback console log level if a logger-specific variable is not set
- `DISABLE_RBAC_HTTP_LOGS`: When `true`, suppress RBAC HTTP access logs
- `QUART_ACCESS_LOGGING`: Set to `0` to disable Quart/Hypercorn access logs

## Setup Instructions

### 1. Create Storage Account (if needed)
```bash
# Create storage account
az storage account create \
  --name yourdeploymentlogs \
  --resource-group your-rg \
  --location westeurope \
  --sku Standard_LRS

# Create container
az storage container create \
  --name deployment-logs \
  --account-name yourdeploymentlogs
```

### 2. Configure Environment Variables

#### For Local Development
Add to your `.env` file:
```env
DEPLOYMENT_LOG_STORAGE_ACCOUNT=yourdeploymentlogs
DEPLOYMENT_LOG_STORAGE_KEY=your-storage-key  # Optional, uses DefaultAzureCredential if not set
```

#### For Azure Web App
```bash
az webapp config appsettings set \
  --name your-webapp \
  --resource-group your-rg \
  --settings DEPLOYMENT_LOG_STORAGE_ACCOUNT=yourdeploymentlogs
```

### 3. Using Managed Identity (Recommended for Production)

Instead of using storage keys, you can use managed identity:

1. Enable managed identity on your Web App:
```bash
az webapp identity assign \
  --name your-webapp \
  --resource-group your-rg
```

2. Grant storage permissions:
```bash
# Get the principal ID
principalId=$(az webapp identity show --name your-webapp --resource-group your-rg --query principalId -o tsv)

# Grant Storage Blob Data Contributor role
az role assignment create \
  --assignee $principalId \
  --role "Storage Blob Data Contributor" \
  --scope /subscriptions/{subscription-id}/resourceGroups/{rg}/providers/Microsoft.Storage/storageAccounts/{storage-account}
```

## Implementation Details

### BlobStorageHandler
A custom logging handler that:
- Buffers log messages for efficient writing
- Creates blob storage containers if they don't exist
- Organizes logs by project and deployment ID
- Supports both storage key and managed identity authentication

### DeploymentLogger
A wrapper that:
- Creates both local and blob storage handlers
- Provides a unified logging interface
- Logs deployment metadata
- Returns the blob URL for easy access

## Usage in Code

The `deploy_project_resources.py` script automatically uses blob logging:

```python
# Initialize deployment logger
deployment_logger = create_deployment_logger(
    project_id=project_id,
    deployment_id=deployment_id,
    project_name=project_name
)

# Get the logger instance
logger = deployment_logger.get_logger()

# Use it like any standard logger
logger.info("Starting deployment...")

# Get the blob URL
blob_url = deployment_logger.get_blob_url()

# Always close when done to flush logs
deployment_logger.close()

# Retrieve another logger configured via environment variables
from backend.utils.logging_config import get_configured_logger

rbac_logger = get_configured_logger("rbac")
rbac_logger.info("Assigning roles...")
```

## Viewing Logs

### From Azure Portal
1. Navigate to your storage account
2. Go to Containers > deployment-logs
3. Browse to project folder > deployment folder
4. Download or view the log file

### Using Azure CLI
```bash
# List logs for a project
az storage blob list \
  --container-name deployment-logs \
  --prefix "deployment-logs/{project-id}/" \
  --account-name yourdeploymentlogs

# Download a specific log
az storage blob download \
  --container-name deployment-logs \
  --name "deployment-logs/{project-id}/{deployment-id}/{blob_name_suffix}_{timestamp}.log" \
  --file local-copy.log \
  --account-name yourdeploymentlogs
```

### Using Azure Storage Explorer
1. Connect to your storage account
2. Navigate to deployment-logs container
3. Browse the folder structure
4. Double-click to view or download logs

## Troubleshooting

### Logs Not Appearing in Blob Storage
1. Check environment variables are set correctly
2. Verify storage account exists and is accessible
3. Check managed identity permissions if not using storage key
4. Look for error messages in local logs

### Authentication Errors
- If using storage key: Verify the key is correct
- If using managed identity: Ensure the identity has "Storage Blob Data Contributor" role

### Performance Considerations
- Logs are buffered (50 messages by default) before writing
- Always call `close()` to ensure final flush
- Local logging can be disabled if only blob storage is needed

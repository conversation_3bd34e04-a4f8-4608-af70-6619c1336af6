# AI Scope Application Overview

This document provides a comprehensive overview of the AI Scope application architecture, components, and workflow.

## Application Architecture

The AI Scope application is a multi-project RAG (Retrieval-Augmented Generation) platform that allows users to create isolated projects, each with its own set of Azure resources. The application follows a modern web architecture with:

- **Frontend**: React-based single-page application with TypeScript
- **Backend**: Python-based API server using Quart/FastAPI
- **Storage**: Azure Blob Storage and Azure Cosmos DB
- **AI Services**: Azure AI Search and Azure OpenAI
- **Compute**: Azure Function Apps deployed from Azure Container Registry (ACR)

## Core Components

### Backend Components

1. **API Server (app.py)**
   - Main entry point for the application
   - Handles HTTP requests and routes them to appropriate handlers
   - Manages project creation, resource deployment, and user authentication

2. **WebSocket Service (index_blob_status.py)**
   - Provides real-time updates on blob storage and indexing status
   - Maintains connection with clients for live updates

3. **Resource Deployment (deploy_project_resources.py)**
   - Handles the deployment of Azure resources for new projects
   - Uses Bicep templates to create consistent resource configurations

4. **RBAC System (rbac_routes.py)**
   - Manages role-based access control for users and projects
   - Controls permissions for different user roles

5. **CosmosDB Client (cosmos_conversation_client.py)**
   - Manages interactions with Azure Cosmos DB
   - Stores project configurations, user data, and conversation history

### Frontend Components

1. **Project Management**
   - ProjectSelector.tsx: Lists available projects and allows navigation
   - NewProject.tsx: Interface for creating new projects
   - ProjectActionsMenu.tsx: Provides actions for managing projects

2. **Chat Interface**
   - Chat.tsx: Main chat interface for interacting with the AI
   - Conversation handling and history management

3. **File Management**
   - FileManagement.tsx: Interface for uploading and managing files
   - Handles file uploads to project-specific blob containers

4. **State Management**
   - AppProvider.tsx: Global application state management
   - UserProvider.tsx: User authentication and role management

## Data Flow

1. **User Authentication**
   - Users authenticate via Azure AD (Entra ID)
   - User roles and permissions are determined based on authentication

2. **Project Creation**
   - User initiates project creation through the UI
   - Backend creates necessary Azure resources (storage, search, function app)
   - Project configuration is stored in Cosmos DB

3. **Document Processing**
   - Users upload documents to project-specific blob containers
   - Documents are processed and indexed by Azure AI Search
   - WebSocket service provides real-time updates on processing status

4. **Chat Interaction**
   - User queries are processed by the backend
   - Queries are enriched with relevant context from the search index
   - Azure OpenAI generates responses based on the enriched queries
   - Responses are returned to the user with citations

## Deployment Architecture

The application can be deployed in two main configurations:

1. **Development Environment**
   - Local deployment using Docker containers
   - Uses Azure CLI credentials for resource access
   - Simplified deployment for testing and development

2. **Production Environment**
   - Deployed as Azure Web App or Container App
   - Uses Managed Identity for secure resource access
   - Full RBAC implementation for secure multi-tenant usage

## Security Model

The application implements a comprehensive security model:

1. **Authentication**: Azure AD (Entra ID) integration for user authentication
2. **Authorization**: Role-based access control for different user types
3. **Resource Isolation**: Each project has isolated resources to prevent data leakage
4. **Secure Communication**: HTTPS and WebSocket secure (WSS) protocols
5. **Managed Identities**: Used for secure service-to-service authentication

## Monitoring and Logging

The application includes comprehensive logging and monitoring:

1. **Application Logs**: Detailed logging of application events and errors
2. **Deployment Status**: Real-time tracking of resource deployment status
3. **Resource Usage**: Monitoring of resource consumption and limits
4. **User Activity**: Tracking of user actions and project access

# Architecture Documentation

This section provides comprehensive documentation on the AI Scope application architecture.

## Contents

- **[Overview](./overview/)**: High-level architecture and design principles
- **[Components](./components/)**: Detailed documentation of system components
- **[Data Flow](./data-flow/)**: Information about data flow within the application
- **[RBAC Architecture](./rbac-architecture.md)**: Role-Based Access Control system architecture

## Key Architecture Diagrams

### System Architecture

```mermaid
graph TD
    A[Frontend - React SPA] --> B[Backend API - Python]
    B --> C[Azure Resources]
    B --> D[CosmosDB]
    C --> E[Blob Storage]
    C --> F[AI Search]
    C --> G[Function Apps]
    B --> H[WebSocket Service]
    A --> H
```

### Multi-Tenant Architecture

The application implements a hybrid multi-tenancy model with shared infrastructure but isolated resources for each project.

See [Multi-Project Architecture](./overview/multi-project-architecture.md) for details.

### Security Architecture

The application implements a comprehensive security model with Role-Based Access Control (RBAC).

See [RBAC Architecture](./rbac-architecture.md) for details.

# RBAC Architecture

This document provides an overview of the Role-Based Access Control (RBAC) architecture in the AI Scope application.

## Overview

The RBAC system is a core component of the AI Scope application that manages user permissions and access control. It ensures that users can only access resources they are authorized to use, based on their role and explicit assignments.

## Architecture Diagram

```mermaid
graph TD
    A[Frontend] -->|RBAC API Requests| B[RBAC FastAPI App]
    B -->|Data Access| C[CosmosDB]
    C -->|Users Container| D[Users]
    C -->|Regions Container| E[Regions]
    C -->|Teams Container| F[Teams]
    C -->|Projects Container| G[Projects]
    C -->|Role Assignments Container| H[Role Assignments]
    I[Authentication] -->|User Identity| B
    B -->|Permission Check| J[Resource Access]
```

## Key Components

### 1. RBAC Models

The RBAC system defines several data models:

- **User**: Represents a user with a role (SUPER_ADMIN, REGIONAL_ADMIN, REGULAR_USER)
- **Region**: Represents a geographical or organizational region
- **Team**: Represents a group of users within a region
- **Project**: Represents a project with resources
- **Role Assignments**: Represents user-team, user-project, and team-project assignments

### 2. CosmosRbacClient

The `CosmosRbacClient` class is the core component that:

- Manages RBAC data in Cosmos DB
- Implements permission checks
- Filters data based on user roles
- Provides methods for managing users, regions, teams, projects, and role assignments

### 3. RBAC API

The RBAC API is implemented as a separate FastAPI application that:

- Provides endpoints for managing RBAC resources
- Enforces permission checks on all operations
- Returns filtered data based on user permissions

### 4. Integration with Main Application

The RBAC system is integrated with the main application through:

- A separate FastAPI app for RBAC endpoints
- Request routing for RBAC API requests
- Authentication middleware for user identification

## Data Flow

1. **User Authentication**:
   - User authenticates through Azure AD or mock authentication
   - User identity is established

2. **API Request**:
   - User makes a request to an RBAC API endpoint
   - Request is routed to the RBAC FastAPI app

3. **Permission Check**:
   - RBAC API endpoint checks if the user has the required permissions
   - If not, returns a 403 Forbidden error

4. **Data Access**:
   - If the user has permission, the request is processed
   - Data is filtered based on user permissions
   - Response is returned to the user

## Implementation Details

The RBAC system is implemented in the following files:

- `backend/models/rbac.py`: RBAC data models
- `backend/rbac/cosmosdb_rbac_service.py`: RBAC data access and permission logic
- `backend/rbac/rbac_routes.py`: RBAC API endpoints
- `app.py`: RBAC integration with main application

For more detailed information on the RBAC system, see the [RBAC documentation](../security/rbac.md).

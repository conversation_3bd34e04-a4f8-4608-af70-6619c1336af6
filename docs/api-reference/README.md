# API Reference

This section provides documentation for the APIs used in the AI Scope application.

## Contents

- [REST API](./rest-api.md): Documentation for REST API endpoints
- [WebSocket API](./websocket-api.md): Documentation for WebSocket API
- [Internal API](./internal-api.md): Documentation for internal APIs

## REST API Endpoints

### Project Management

- `GET /api/projects`: List projects
- `POST /api/projects`: Create a new project
- `GET /api/projects/{project_id}`: Get project details
- `PUT /api/projects/{project_id}`: Update project
- `DELETE /api/projects/{project_id}`: Delete project

### Deployment Status

- `GET /api/projects/{project_id}/deployment-status`: Get deployment status
- `POST /api/projects/{project_id}/deployment-status`: Update deployment status

### File Management

- `POST /api/projects/{project_id}/upload`: Upload files
- `POST /api/projects/{project_id}/upload-template`: Upload template files
- `GET /api/projects/{project_id}/files`: List files
- `DELETE /api/projects/{project_id}/files/{file_name}`: Delete file

### Chat

- `POST /api/projects/{project_id}/conversation`: Send message
- `GET /api/projects/{project_id}/conversation/{conversation_id}`: Get conversation
- `POST /api/projects/{project_id}/conversation/{conversation_id}/feedback`: Send feedback

## WebSocket API

### File Status

- `/ws/files/{project_id}`: File status updates

### Deployment Status

- `/ws/deployment-status/{project_id}`: Deployment status updates

### Chat

- `/ws/chat/{project_id}/{conversation_id}`: Real-time chat updates

## Authentication

All API endpoints require authentication:
- REST API: Bearer token in Authorization header
- WebSocket API: Token in connection URL or initial message

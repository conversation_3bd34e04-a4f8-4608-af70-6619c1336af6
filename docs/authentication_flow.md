# Authentication Flow

This document explains the authentication flow in the AI Scope App, particularly focusing on the differences between development and production environments.

## Overview

The application uses two authentication mechanisms:

1. **Azure App Service Authentication** (Entra ID) - Used in production
2. **MSAL.js** - Used primarily in development, but also as a fallback in production

## Production Authentication Flow

In production, the authentication flow is streamlined to avoid double authentication:

1. User accesses the application
2. Azure App Service authentication (Entra ID) handles the initial authentication
3. After successful authentication, the user is redirected to `/.auth/login/aad/callback` or `/auth/callback`
4. The backend redirects the user directly to the projects page (`/#/projects`)
5. The `AuthenticatedRoute` component assumes the user is authenticated in production

This approach eliminates the need for a second authentication step through MSAL.js, providing a smoother user experience.

## Development Authentication Flow

In development, the authentication flow is more complex:

1. User accesses the application
2. The root route (`/`) renders the `AutoLogin` component
3. `AutoLogin` checks if the user is authenticated:
   - If authenticated, redirects to the projects page
   - If not authenticated, redirects to the login page
4. The login page initiates authentication through MSAL.js
5. After successful authentication, the user is redirected to `/auth/callback`
6. The `AuthCallback` component handles the authentication response and redirects to the projects page

## Key Components

### AuthenticatedRoute

The `AuthenticatedRoute` component is a wrapper for protected routes that:

- In production: Assumes the user is authenticated if they've reached a protected route
- In development: Checks MSAL accounts and localStorage for authentication status

### Authentication Callbacks

The application handles two types of authentication callbacks:

1. `/.auth/login/aad/callback` - Used by Azure App Service authentication
2. `/auth/callback` - Used by MSAL.js

Both callbacks now redirect directly to the projects page in production, eliminating the need for a second authentication step.

## Implementation Details

### Root Route

The root route (`/`) behavior differs between environments:

```jsx
<Route path="/" element={
  window.location.hostname === 'localhost'
    ? <AutoLogin />
    : <Navigate to="/projects" />
} />
```

- In development (`localhost`): Renders the `AutoLogin` component
- In production: Redirects directly to the projects page

### Authentication Callbacks

Both authentication callbacks redirect directly to the projects page in production:

```python
# In production, redirect directly to projects page
return await make_response('', 302, {'Location': '/#/projects'})
```

### AuthenticatedRoute Component

The `AuthenticatedRoute` component has been simplified to assume authentication in production:

```jsx
const isAuthenticated = 
  window.location.hostname !== 'localhost' || // In production, assume authenticated
  accounts.length > 0 ||                      // MSAL authenticated
  localStorage.getItem('isAuthenticated') === 'true'; // localStorage authenticated
```

## Troubleshooting

If you encounter authentication issues:

1. **In development**:
   - Check browser console for MSAL errors
   - Verify that the redirect URI is correctly configured
   - Clear browser storage and try again

2. **In production**:
   - Verify that Azure App Service authentication is properly configured
   - Check that the redirect URIs in the Azure AD app registration match your application's URLs
   - Look for errors in the application logs related to authentication

## Future Improvements

- Consider implementing a more robust token validation mechanism
- Add support for role-based access control at the API level
- Implement token refresh logic to handle expired tokens

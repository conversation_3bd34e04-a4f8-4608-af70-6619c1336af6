# Repair Deployment Script Guide

## Overview

The `repair_deployment.py` script is a utility to repair incomplete project deployments by automatically retrieving missing resource details and credentials from Azure.

## Purpose

When a project deployment is interrupted or fails partially, the project record in Cosmos DB may be missing important information such as:
- Resource names (storage account, search service, function app)
- Access credentials (SAS tokens, API keys)
- Function keys for Azure Functions
- Event Grid configuration

This script attempts to automatically recover this information by querying Azure resources.

## Usage

```bash
python -m scripts.repair_deployment <project_id>
```

### Finding the Project ID

If you only know the project name (as shown in the UI), you can find the actual project ID:

```python
# Example: Project name "********" has ID "09e4aa3d-561d-41b5-ace5-51531689dbe8"
python -m scripts.repair_deployment 09e4aa3d-561d-41b5-ace5-51531689dbe8
```

## How It Works

1. **Resource Discovery**
   - Searches for Azure resources tagged with `project-id=<project_id>`
   - Looks for storage accounts, search services, and function apps
   - Updates the project record with found resource names

2. **Credential Recovery**
   - **Storage Account**: Generates a new SAS token valid for 1 year
   - **Search Service**: Retrieves the primary admin key
   - **Function App**: Gets keys for each function (maturity assessment, executive summary, PowerPoint generator)

3. **Safe Updates**
   - Only updates fields that are missing
   - Preserves all existing data
   - Logs all actions taken

## Example Output

### Successful Repair
```json
{
  "status": "partial_success",
  "message": "Repair complete. Actions taken: 5. Manual actions required: 1.",
  "manual_actions_required": [
    "Event Grid topic details are missing. This may require manual Bicep deployment of the event_grid.bicep module."
  ]
}
```

### Project Not Found
```json
{
  "status": "failed",
  "message": "Project not found."
}
```

### No Resources Found
```json
{
  "status": "success",
  "message": "No repair needed.",
  "manual_actions_required": [
    "Could not find resource 'Microsoft.Storage/storageAccounts' for project <id>. It may need to be (re)created manually.",
    "Could not find resource 'Microsoft.Search/searchServices' for project <id>. It may need to be (re)created manually.",
    "Could not find resource 'Microsoft.Web/sites' for project <id>. It may need to be (re)created manually."
  ]
}
```

## Prerequisites

1. **Azure CLI**: Must be installed and logged in (`az login`)
2. **Permissions**: Need appropriate permissions to:
   - List resources in the resource group
   - Generate SAS tokens for storage accounts
   - Read admin keys for search services
   - Read function keys from function apps
3. **Environment Variables**: Cosmos DB connection configured in `.env`

## Common Scenarios

### Scenario 1: Deployment Failed After Resource Creation
If resources were created but credentials weren't saved:
- Script will find the resources by tag
- Generate/retrieve all necessary credentials
- Update the project record

### Scenario 2: Deployment Failed Before Resource Creation
If no resources were created (like project ********):
- Script will report that resources need to be created manually
- Run the deployment script again to create resources

### Scenario 3: Partial Deployment
If some resources exist but others don't:
- Script will recover what it can
- Provide manual actions for missing resources

## Limitations

1. **Event Grid**: Cannot automatically recreate Event Grid subscriptions
2. **Deleted Resources**: Cannot recover resources that have been deleted
3. **Tag Dependencies**: Resources must be tagged with `project-id` to be found

## Troubleshooting

### "Project not found"
- Verify the project ID (not the name)
- Check Cosmos DB connectivity

### "Could not find resource"
- Resources may not have been created
- Resources may be missing the `project-id` tag
- Check the correct resource group

### "Failed to generate/retrieve credentials"
- Check Azure CLI authentication (`az account show`)
- Verify permissions on the resources
- Resources may be in a failed state

## Best Practices

1. **Run Early**: Run repair as soon as you notice a failed deployment
2. **Check Logs**: Review the output for manual actions required
3. **Verify Results**: Check the project in the UI after repair
4. **Complete Deployment**: If resources are missing, run the full deployment again

## Integration with Deployment Process

The repair script can be integrated into deployment retry logic:

```python
# Example: Retry deployment with repair
if deployment_failed:
    repair_result = await repair_project_deployment(project_id)
    if repair_result["status"] in ["success", "partial_success"]:
        # Retry deployment with recovered data
        retry_deployment(project_id)
```
# AI Scope Application Documentation

Welcome to the AI Scope application documentation. This repository contains comprehensive documentation for all aspects of the application.

## Documentation Structure

The documentation is organized into the following sections:

- **[Getting Started](./getting-started/)**: Quick start guides and setup instructions
- **[Architecture](./architecture/)**: System architecture and design principles
- **[Features](./features/)**: Detailed documentation of application features
- **[Azure Resources](./azure-resources/)**: Information about Azure resources used by the application
- **[Development](./development/)**: Development guides and best practices
- **[Operations](./operations/)**: Deployment, monitoring, and maintenance guides
- **[Security](./security/)**: Security implementation and best practices
  - [Role-Based Access Control (RBAC)](./security/rbac.md): Detailed documentation of the RBAC system
  - [Authentication Overview](./security/authentication.md): How the frontend and backend authenticate with Azure
- **[API Reference](./api-reference/)**: API documentation and examples
- **[Troubleshooting](./troubleshooting/)**: Common issues and solutions

## Contributing to Documentation

When contributing to this documentation:

1. Follow the established folder structure
2. Use Markdown for all documentation files
3. Include diagrams where appropriate (using Mermaid or images)
4. Keep documentation up-to-date with code changes
5. Use consistent formatting and style

## Documentation Standards

- File names should be lowercase with hyphens (e.g., `getting-started.md`)
- Use descriptive titles and headings
- Include a table of contents for longer documents
- Link related documents for easy navigation
- Include code examples where appropriate

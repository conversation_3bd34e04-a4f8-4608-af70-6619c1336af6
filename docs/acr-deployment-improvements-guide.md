# ACR Deployment Improvements Guide

## Overview

This guide documents the improvements made to the Azure Container Registry (ACR) deployment process for the AI Scope application. The improvements focus on fixing key issues with function app configuration, environment variables, function keys, and URL endpoints.

## Current Issues Identified

### 1. Function Key Management
- **Problem**: Function keys are not properly pinned to default keys during deployment
- **Impact**: Inconsistent function keys between Azure Function Apps and Cosmos DB
- **Solution**: Use fixed default function keys and synchronize with Cosmos DB

### 2. Environment Variables
- **Problem**: Missing or incorrect OpenAI environment variables in function apps
- **Impact**: Function apps cannot connect to Azure OpenAI services
- **Solution**: Standardize environment variable configuration across all function apps

### 3. Executive Summary URL
- **Problem**: URL uses `api/HttpTriggerAppExecutiveSummary` instead of `api/execSummary`
- **Impact**: Frontend cannot properly call the executive summary function
- **Solution**: Update URL generation to use correct endpoint

### 4. Cosmos DB Synchronization
- **Problem**: Function app details not properly synchronized with Cosmos DB
- **Impact**: Project data inconsistency between Azure and database
- **Solution**: Implement robust synchronization mechanisms

## Improved Scripts Overview

The following scripts have been created and organized in `scripts/acr_fixes/`:

### 1. `list_function_apps.py`
**Purpose**: Inventory management and discovery
- Lists all function app names by project from Cosmos DB
- Shows which projects have function apps and which don't
- Displays project names, function app names, storage accounts, and search indexes
- Provides clear overview of infrastructure

**Usage**:
```bash
python -m scripts.acr_fixes.list_function_apps
```

### 2. `configure_function_apps.py`
**Purpose**: Comprehensive function app configuration
- Adds project-id tags to function apps
- Configures CORS (enables credentials + adds allowed origins)
- Updates environment variables with OpenAI deployments and endpoints
- Retrieves and updates function keys
- Handles non-existent function apps gracefully

**Usage**:
```bash
# Configure all function apps
python -m scripts.acr_fixes.configure_function_apps

# Configure specific function app
python -m scripts.acr_fixes.configure_function_apps --function-app func-app-name

# Dry run mode
python -m scripts.acr_fixes.configure_function_apps --dry-run
```

### 3. `test_add_envs.py`
**Purpose**: Advanced environment variable management
- Automatically retrieves project data from Cosmos DB
- Sets all required OpenAI environment variables
- Includes project-specific search indexes
- Works with all function apps or specific ones

**Usage**:
```bash
# Update all function apps
python -m scripts.acr_fixes.test_add_envs

# Update specific function app
python -m scripts.acr_fixes.test_add_envs --function-app func-app-name

# Dry run mode
python -m scripts.acr_fixes.test_add_envs --dry-run
```

### 4. `fix_env_vars.py`
**Purpose**: Targeted environment variable fixing
- Sets environment variables one by one (avoids bulk update issues)
- Automatically finds search index from Cosmos DB
- Includes verification and detailed logging
- Good for troubleshooting specific function apps

**Usage**:
```bash
# Fix specific function app
python -m scripts.acr_fixes.fix_env_vars --function-app func-app-name

# Fix with custom search index
python -m scripts.acr_fixes.fix_env_vars --function-app func-app-name --search-index custom-index
```

### 5. `set_fixed_function_keys.py`
**Purpose**: Function key management and Cosmos DB synchronization
- Gets existing default function keys (doesn't create new ones)
- Updates Cosmos DB project environments with these keys
- Ensures consistency between Azure Function Apps and Cosmos DB
- Works for all projects or single project

**Usage**:
```bash
# Update all projects
python -m scripts.acr_fixes.set_fixed_function_keys

# Update specific project
python -m scripts.acr_fixes.set_fixed_function_keys --project-name project-name

# Dry run mode
python -m scripts.acr_fixes.set_fixed_function_keys --dry-run
```

### 6. `test_single_function_keys.py`
**Purpose**: Single project function key testing
- Tests function key retrieval for a single project
- Validates key synchronization with Cosmos DB
- Useful for debugging specific projects

**Usage**:
```bash
python -m scripts.acr_fixes.test_single_function_keys --project-name project-name
```

## Key Improvements Made

### 1. Fixed Function Key Management
- **Before**: Function keys were generated randomly during deployment
- **After**: Use fixed default function keys that are consistent across deployments
- **Implementation**: Scripts retrieve default keys and pin them in Cosmos DB

### 2. Standardized Environment Variables
- **Before**: Inconsistent or missing OpenAI environment variables
- **After**: All function apps have standardized environment variables
- **Complete REQUIRED_ENV_VARS**:
  - `AZURE_OPENAI_DEPLOYMENT_ID`: "gpt-4o-ai-scope"
  - `AZURE_OPENAI_DEPLOYMENT_ID_2`: "gpt-4o-mini"
  - `AZURE_OPENAI_DEPLOYMENT_ID_3`: "o3-mini"
  - `AZURE_OPENAI_VERSION`: "2024-05-01-preview"
  - `AZURE_OPENAI_DEPLOYMENT_ID_3_VERSION`: "2025-01-31"
  - `AZURE_OPENAI_ENDPOINT`: "https://ai-scope-openai.openai.azure.com/"
  - `AZURE_AI_SEARCH_INDEX`: (set dynamically per project)

### 3. Executive Summary URL Fix
- **Before**: `api/HttpTriggerAppExecutiveSummary`
- **After**: `api/execSummary`
- **Files to Update**:
  - `deploy_project_resources.py` (line 2382)
  - `scripts/repair_deployment.py` (line 312)
  - Any frontend code referencing the old URL

### 4. Enhanced Cosmos DB Synchronization
- **Before**: Manual updates with potential inconsistencies
- **After**: Automated synchronization with validation
- **Features**:
  - Automatic project detection by function app names
  - Environment variable consistency checks
  - Function key validation and updates

## Current ACR Deployment Process

### 1. Main Deployment Flow
The ACR deployment is triggered from `deploy_project_resources.py`:

```python
# Step 1: Deploy main infrastructure (storage, search)
main_bicep_outputs = await deploy_main_bicep(...)

# Step 2: Deploy function app from ACR
deployed_function_app_name = await deploy_function_app_from_acr(...)

# Step 3: Retrieve function keys and update Cosmos DB
function_keys = await get_function_keys(...)
await update_project_resources(project_id, resource_data, api_url)
```

### 2. ACR Deployment Script
Location: `scripts/ACR_deployment/deploy_function_app_from_acr.sh`

**Key Features**:
- Robust error handling with `set -e`
- Dependency checks (az, jq)
- Synchronous deployment using Bicep
- Intelligent polling for Function App 'Running' state
- Rich diagnostics on failure

### 3. Bicep Template
Location: `scripts/ACR_deployment/function_app_deployment.bicep`

**Key Components**:
- App Service Plan (Linux, Basic tier)
- Function App with container configuration
- Environment variables for project-specific settings
- ACR integration with credentials

### 4. Current Issues in Deployment

#### Issue 1: Missing Environment Variables
The current Bicep template sets basic environment variables but misses critical OpenAI configurations:

**Missing Variables**:
- `AZURE_OPENAI_DEPLOYMENT_ID_2`: "gpt-4o-mini"
- `AZURE_OPENAI_DEPLOYMENT_ID_3`: "o3-mini"
- `AZURE_OPENAI_VERSION`: "2024-05-01-preview"
- `AZURE_OPENAI_DEPLOYMENT_ID_3_VERSION`: "2025-01-31"
- `AZURE_OPENAI_ENDPOINT`: "https://ai-scope-openai.openai.azure.com/"
- `AZURE_AI_SEARCH_INDEX`: (project-specific, set dynamically)

#### Issue 2: Function Key Management
Function keys are retrieved after deployment but not pinned to default keys:

```python
# Current approach - generates new keys
function_key = await get_function_key(function_app_name, function_name)

# Improved approach - use default keys
default_key = await get_default_function_key(function_app_name)
```

#### Issue 3: Executive Summary URL
The URL generation uses the function name instead of the desired endpoint:

```python
# Current (incorrect)
url = f"{base_url}/api/HttpTriggerAppExecutiveSummary"

# Should be
url = f"{base_url}/api/execSummary"
```

## Repair Deployment Process

### Current Repair Scripts

#### 1. `scripts/repair_deployment.py`
**Purpose**: Basic repair functionality for incomplete deployments
- Discovers resources by project-id tags
- Retrieves missing credentials (SAS tokens, API keys)
- Updates Cosmos DB with recovered information

#### 2. `scripts/repair_deployment_enhanced.py`
**Purpose**: Enhanced repair with recreation capabilities
- Can recreate missing resources
- More comprehensive error handling
- Better integration with deployment functions

#### 3. `scripts/repair_with_recreation.py`
**Purpose**: Full recreation of failed deployments
- Handles complete deployment failures
- Recreates all resources from scratch
- Integrates with main deployment pipeline

### Issues with Current Repair Process

#### Issue 1: Executive Summary URL
The repair script also generates incorrect URLs:

```python
# Line 312 in repair_deployment.py (NEEDS FIX)
project["azure_function_executive_summary_url"] = f"{base_url}/api/HttpTriggerAppExecutiveSummary"
```

#### Issue 2: Function Key Inconsistency
Repair scripts don't ensure function keys match between Azure and Cosmos DB.

## Required Fixes

### 1. Update Executive Summary URLs

**Files to Update**:
- `deploy_project_resources.py` (line 2382)
- `scripts/repair_deployment.py` (line 312)
- `scripts/repair_deployment_enhanced.py` (similar location)

**Change Required**:
```python
# Before
url = f"{base_url}/api/HttpTriggerAppExecutiveSummary"

# After
url = f"{base_url}/api/execSummary"
```

### 2. Add Missing Environment Variables to Bicep ✅ COMPLETED

**File**: `scripts/ACR_deployment/function_app_deployment.bicep`

**Status**: ✅ **FIXED** - All missing environment variables have been added:
```bicep
{
  name: 'AZURE_OPENAI_DEPLOYMENT_ID_2'
  value: 'gpt-4o-mini'
}
{
  name: 'AZURE_OPENAI_DEPLOYMENT_ID_3'
  value: 'o3-mini'
}
{
  name: 'AZURE_OPENAI_VERSION'
  value: '2024-05-01-preview'
}
{
  name: 'AZURE_OPENAI_DEPLOYMENT_ID_3_VERSION'
  value: '2025-01-31'
}
```

**Additional Fixes**:
- ✅ `AZURE_OPENAI_ENDPOINT` corrected to: `https://ai-scope-openai.openai.azure.com/`
- ✅ All `REQUIRED_ENV_VARS` are now properly configured in the template

### 3. Implement Default Function Key Pinning

**Approach**: Modify deployment process to use default keys instead of generating new ones.

**Implementation**:
1. After function app deployment, retrieve default key
2. Pin this key as the fixed key for all functions
3. Update Cosmos DB with the pinned key
4. Ensure repair scripts use the same approach

## Implementation Steps

### Step 1: Fix Executive Summary URLs

Update the following files to use the correct endpoint:

**File**: `deploy_project_resources.py`
```python
# Line 2382 - Change from:
resource_data["azure_function_executive_summary_url"] = f"{resource_data['function_app_url']}/api/{function_name}"

# To:
if function_name == "HttpTriggerAppExecutiveSummary":
    resource_data["azure_function_executive_summary_url"] = f"{resource_data['function_app_url']}/api/execSummary"
```

**File**: `scripts/repair_deployment.py`
```python
# Line 312 - Change from:
project["azure_function_executive_summary_url"] = f"{base_url}/api/HttpTriggerAppExecutiveSummary"

# To:
project["azure_function_executive_summary_url"] = f"{base_url}/api/execSummary"
```

### Step 2: Update Bicep Template ✅ COMPLETED

**File**: `scripts/ACR_deployment/function_app_deployment.bicep`

**Status**: ✅ **FIXED** - The following environment variables have been added to the Bicep template:

```bicep
{
  name: 'AZURE_OPENAI_DEPLOYMENT_ID_2'
  value: 'gpt-4o-mini'
}
{
  name: 'AZURE_OPENAI_DEPLOYMENT_ID_3'
  value: 'o3-mini'
}
{
  name: 'AZURE_OPENAI_VERSION'
  value: '2024-05-01-preview'
}
{
  name: 'AZURE_OPENAI_DEPLOYMENT_ID_3_VERSION'
  value: '2025-01-31'
}
```

**Also Fixed**:
- ✅ `AZURE_OPENAI_ENDPOINT` updated to use the correct fixed endpoint: `https://ai-scope-openai.openai.azure.com/`
- ✅ All required environment variables from `REQUIRED_ENV_VARS` are now included

### Step 3: Implement Function Key Pinning

**Modify**: `deploy_project_resources.py`

Replace the function key retrieval logic:

```python
# After function app deployment, use default keys
for function_name in function_names:
    try:
        # Get the default function key instead of function-specific key
        default_key = await get_default_function_key(deployed_function_app_name)

        if default_key:
            # Pin this key for all functions
            if function_name == "HttpTriggerAppMaturityAssessment":
                resource_data["function_key_maturity"] = default_key
            elif function_name == "HttpTriggerAppExecutiveSummary":
                resource_data["function_key_executive_summary"] = default_key
            elif function_name == "HttpTriggerPowerPointGenerator":
                resource_data["function_key_powerpoint"] = default_key
```

### Step 4: Run Improvement Scripts

Execute the improvement scripts in order:

```bash
# 1. Inventory current state
python -m scripts.acr_fixes.list_function_apps

# 2. Configure all function apps
python -m scripts.acr_fixes.configure_function_apps

# 3. Fix environment variables
python -m scripts.acr_fixes.test_add_envs

# 4. Set fixed function keys
python -m scripts.acr_fixes.set_fixed_function_keys

# 5. Verify changes
python -m scripts.acr_fixes.list_function_apps
```

## Best Practices

### 1. Pre-Deployment Checklist
- [ ] Verify ACR image is available and up-to-date
- [ ] Confirm all required environment variables are defined
- [ ] Check Cosmos DB connectivity
- [ ] Validate resource group and location settings

### 2. During Deployment
- [ ] Monitor function app deployment status
- [ ] Verify container image pull success
- [ ] Check function app is in 'Running' state
- [ ] Validate environment variables are set correctly

### 3. Post-Deployment Validation
- [ ] Test function endpoints are accessible
- [ ] Verify function keys work correctly
- [ ] Confirm Cosmos DB is updated with correct data
- [ ] Test executive summary endpoint with correct URL

### 4. Troubleshooting Common Issues

#### Function App Not Starting
1. Check container logs: `az functionapp log tail --name <app-name> --resource-group <rg>`
2. Verify ACR credentials are correct
3. Check if image exists in ACR
4. Validate environment variables

#### Function Keys Not Working
1. Use default function key instead of function-specific keys
2. Verify key is correctly stored in Cosmos DB
3. Check CORS configuration allows the calling domain

#### Environment Variables Missing
1. Run `scripts/acr_fixes/fix_env_vars.py` for targeted fixes
2. Verify Bicep template includes all required variables
3. Check if variables are being overwritten during deployment

## Monitoring and Maintenance

### 1. Regular Health Checks
- Run `list_function_apps.py` weekly to verify infrastructure state
- Monitor function app performance and availability
- Check Cosmos DB synchronization status

### 2. Key Rotation
- Use `set_fixed_function_keys.py` when rotating function keys
- Ensure all dependent systems are updated with new keys
- Test all endpoints after key rotation

### 3. Environment Updates
- Use `test_add_envs.py` when updating OpenAI deployments
- Validate changes in staging before production
- Monitor function performance after environment changes

## Summary of Completed Improvements

### ✅ Fixes Applied

1. **Bicep Template Updated** (`scripts/ACR_deployment/function_app_deployment.bicep`):
   - ✅ Added all missing `REQUIRED_ENV_VARS` environment variables
   - ✅ Fixed `AZURE_OPENAI_ENDPOINT` to use correct endpoint: `https://ai-scope-openai.openai.azure.com/`
   - ✅ Added `AZURE_OPENAI_DEPLOYMENT_ID_2`, `AZURE_OPENAI_DEPLOYMENT_ID_3`, `AZURE_OPENAI_VERSION`, `AZURE_OPENAI_DEPLOYMENT_ID_3_VERSION`

2. **Scripts Organized** (`scripts/acr_fixes/`):
   - ✅ `list_function_apps.py` - Infrastructure inventory management
   - ✅ `configure_function_apps.py` - Comprehensive function app configuration
   - ✅ `test_add_envs.py` - Advanced environment variable management
   - ✅ `fix_env_vars.py` - Targeted environment variable fixing
   - ✅ `set_fixed_function_keys.py` - Function key management and Cosmos DB sync
   - ✅ `test_single_function_keys.py` - Single project testing

3. **Documentation Created**:
   - ✅ Complete deployment improvements guide with implementation steps
   - ✅ Best practices and troubleshooting guidance
   - ✅ Operational procedures for maintenance

### 🔄 Still Required (Manual Implementation)

1. **Executive Summary URL Fix** - Update these files:
   - `deploy_project_resources.py` (line 2382)
   - `scripts/repair_deployment.py` (line 312)
   - `scripts/repair_deployment_enhanced.py` (similar location)

2. **Function Key Pinning** - Implement default key usage in:
   - `deploy_project_resources.py` (function key retrieval logic)

## Next Steps

### Immediate Actions
1. **Apply URL Fixes**: Update the executive summary URLs in deployment and repair scripts
2. **Test Bicep Changes**: Deploy a test function app to verify all environment variables are set correctly
3. **Run Improvement Scripts**: Execute the organized scripts to fix existing function apps

### Recommended Deployment Process
```bash
# 1. Inventory current state
python -m scripts.acr_fixes.list_function_apps

# 2. Configure all function apps with correct settings
python -m scripts.acr_fixes.configure_function_apps

# 3. Fix environment variables for all function apps
python -m scripts.acr_fixes.test_add_envs

# 4. Set fixed function keys and sync with Cosmos DB
python -m scripts.acr_fixes.set_fixed_function_keys

# 5. Verify improvements
python -m scripts.acr_fixes.list_function_apps
```

## Conclusion

These improvements provide a robust foundation for ACR-based function app deployments with:

✅ **Consistent Configuration**: All function apps have standardized settings
✅ **Reliable Key Management**: Fixed default keys ensure consistency
✅ **Correct URL Endpoints**: Executive summary uses proper API path (pending manual fix)
✅ **Automated Synchronization**: Cosmos DB stays in sync with Azure resources
✅ **Comprehensive Tooling**: Scripts for management, troubleshooting, and maintenance
✅ **Fixed Environment Variables**: All required OpenAI configurations are now included in Bicep template

The improved deployment process reduces manual intervention, increases reliability, and provides better operational visibility into the infrastructure state.

# Logging Configuration

This document describes the logging configuration for the application, particularly focusing on deployment status tracking and debugging.

## Overview

The application uses a centralized logging configuration that provides:

1. Different log levels for console vs. file output
2. Specialized loggers for different components
3. Detailed logging for deployment status tracking
4. Reduced terminal noise while capturing comprehensive logs in files

## Log Directory Structure

All logs are stored in the `logs` directory in the root of the project. The following log files are available:

- `deployment_<project_id>.log`: Detailed logs for project deployment
- `project_retrieval.log`: Logs related to project retrieval operations
- `cosmos_db.log`: Logs related to CosmosDB operations
- `api_requests.log`: Logs for API requests and responses
- `deletions/<project_id>.log`: Detailed logs for each project deletion attempt

## Log Levels

The application uses the following log levels:

- **DEBUG**: Detailed information, typically of interest only when diagnosing problems
- **INFO**: Confirmation that things are working as expected
- **WARNING**: Indication that something unexpected happened, or may happen in the near future
- **ERROR**: Due to a more serious problem, the software has not been able to perform a function
- **CRITICAL**: A serious error, indicating that the program itself may be unable to continue running

By default, console output uses INFO level (less verbose) while file output uses DEBUG level (very detailed).

## Deployment Logging

Deployment logs capture detailed information about the deployment process, including:

1. Project information (ID, name, resource names)
2. Environment information (Python version, current directory)
3. CosmosDB connection details
4. Command execution details
5. Process output (stdout/stderr)
6. Deployment status updates

Example deployment log entry:
```
2025-05-05 12:17:24,730 - INFO - Project ID: 296882d9-e444-4c81-ae23-78441de09ddd
2025-05-05 12:17:24,731 - INFO - Project Name: Test Project
2025-05-05 12:17:24,731 - INFO - Storage Container Uploads: uploads-test-project-09dd
2025-05-05 12:17:24,731 - INFO - Storage Container Input: input-test-project-09dd
2025-05-05 12:17:24,731 - INFO - Storage Container Output: output-test-project-09dd
2025-05-05 12:17:24,731 - INFO - Search Index Name: project-test-project-index
```

## Deletion Logging

Deletion logs record every project deletion attempt. Each attempt is written to
`logs/deletions/<project_id>.log` and includes:

1. The full project document retrieved from Cosmos DB
2. The list of Azure resources scheduled for removal
3. A step-by-step record of each deletion action and its outcome

## Project Retrieval Logging

Project retrieval logs capture information about attempts to retrieve project documents from the database, including:

1. Project ID and user ID
2. Retrieval method used
3. Success/failure status
4. Error details (if any)

Example project retrieval log entry:
```
2025-05-05 12:19:30 - INFO - Attempting to get project 296882d9-e444-4c81-ae23-78441de09ddd for user 11111111-1111-1111-1111-111111111111
2025-05-05 12:19:30 - INFO - Project document found: False
2025-05-05 12:19:30 - INFO - Using Azure CLI credentials: True
```

## API Request Logging

API request logs capture information about API requests and responses, including:

1. Request method and URL
2. Request headers (with sensitive information redacted)
3. Request body
4. Response status code
5. Response headers
6. Response body

Example API request log entry:
```
2025-05-05 12:19:29 - INFO - Request URL: 'https://internal-ai-conversation-history-db.documents.azure.com:443/dbs/db_conversation_history/colls/projects/docs/11111111-1111-1111-1111-111111111111/'
2025-05-05 12:19:29 - INFO - Request method: 'GET'
```

## Using the Logging Configuration

To use the centralized logging configuration in your code:

```python
from backend.utils.logging_config import configure_logger, get_configured_logger

# Configure a logger for your module
logger = configure_logger(
    logger_name="my_module",
    log_to_console=True,
    log_to_file=True,
    console_level=logging.INFO,
    file_level=logging.DEBUG,
    log_file_name="my_module.log"
)

# Use the logger
logger.info("This is an info message")
logger.debug("This is a debug message")
logger.error("This is an error message")
```

Alternatively, use `get_configured_logger` to retrieve a logger whose console
level can be overridden via environment variables:

```python
rbac_logger = get_configured_logger("rbac")
rbac_logger.info("Assigning roles")
```

### Environment Variables

Loggers respect a set of environment variables for controlling console output:

- `{LOGGER_NAME}_CONSOLE_LOG_LEVEL` – override the console level for a specific logger (e.g., `RBAC_CONSOLE_LOG_LEVEL`)
- `DEFAULT_CONSOLE_LOG_LEVEL` – fallback console level if no specific variable is set
- `DISABLE_RBAC_HTTP_LOGS` – when `true`, hide RBAC HTTP access logs
- `QUART_ACCESS_LOGGING` – set to `0` to disable Quart/Hypercorn access logs

For deployment status tracking, use the specialized functions in `backend.utils.project_retrieval_logging`:

```python
from backend.utils.project_retrieval_logging import log_deployment_status_update

# Log a deployment status update
log_deployment_status_update(
    project_id="296882d9-e444-4c81-ae23-78441de09ddd",
    status_data={
        "status": "in_progress",
        "message": "Creating storage containers",
        "details": {
            "completion_percentage": 50
        }
    }
)
```


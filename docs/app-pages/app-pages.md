       # Application Pages and Functionality

This document describes the different pages in the AI Scope application and their functionality.

## Page Structure

The AI Scope application consists of the following main pages:

1. **Projects Page** (`/projects`)
   - Lists all projects accessible to the user
   - Entry point for creating new projects

2. **New Project Page** (`/new-project`)
   - Form for creating a new project
   - Deployment status tracking

3. **Project Page** (`/project/:projectId`)
   - Project-specific workspace
   - Contains chat interface and file management

4. **User Management** (Admin only)
   - User creation and role assignment
   - Permission management

## Projects Page

### Purpose
The Projects page serves as the main dashboard for users to view and access their projects.

### Components
1. **Header**
   - Application title and logo
   - User profile and settings

2. **Project List**
   - Grid or list view of available projects
   - Each project card shows:
     - Project name
     - Project icon
     - Description (if available)
     - Deployment status indicator

3. **Action Buttons**
   - "Create new" button for initiating project creation
   - View toggle (grid/list)
   - Search/filter functionality

4. **Project Actions Menu**
   - Accessible via three-dot menu on each project card
   - Options include:
     - Open Project
     - Edit Project
     - Assign Users (admin only)
     - Set Cost Limit (admin only)
     - Delete Project (admin only)

### Functionality
- **Project Listing**: Fetches projects from `/api/rbac/projects` endpoint
- **Project Selection**: Clicking a project navigates to the project page
- **Project Creation**: "Create new" button navigates to the New Project page
- **Project Management**: Actions menu provides project management options

### Role-Based Access
- **Super Admin**: Can see all projects across all regions
- **Regional Admin**: Can see all projects in their assigned region
- **Regular User**: Can see only projects they have access to

## New Project Page

### Purpose
The New Project page allows users to create a new project and track its deployment.

### Components
1. **Project Form**
   - Project Name input field
   - Region selection dropdown
   - Create/Cancel buttons

2. **Deployment Status View**
   - Overall progress indicator
   - Resource-specific status indicators
   - Error messages (if any)
   - Support ticket creation option (if deployment fails)

### Functionality
- **Form Submission**: Sends project data to `/api/rbac/projects` endpoint
- **Region Selection**: Fetches available regions from `/api/rbac/regions` endpoint
- **Deployment Tracking**: 
  - Polls `/api/projects/{projectId}/deployment-status` for updates
  - Updates UI with deployment progress
  - Shows detailed status for each resource

### Deployment Resources
The deployment status tracks the following resources:
1. Project Configuration (Cosmos DB)
2. Storage Containers
3. Search Index
4. Search Datasource
5. Search Indexer
6. Function App

## Project Page

### Purpose
The Project page serves as the workspace for a specific project, providing access to chat functionality and file management.

### Components
1. **Header**
   - Project name and navigation
   - User profile and settings

2. **Chat Interface**
   - Chat input area
   - Message history display
   - Citation display for references

3. **File Management**
   - File upload interface
   - File listing and status
   - Template file management

4. **Project Settings**
   - Project configuration options
   - Resource management

### Functionality
- **Chat**: 
  - Sends messages to `/api/projects/{projectId}/conversation` endpoint
  - Uses project-specific search index for RAG
  - Displays responses with citations

- **File Upload**:
  - Uploads files to `/api/projects/{projectId}/upload` endpoint
  - Files are stored in project-specific blob container
  - Real-time status updates via WebSocket

- **File Listing**:
  - Fetches files from project-specific blob container
  - Shows indexing status for each file
  - Allows file deletion and download

- **Template Management**:
  - Uploads template files to `/api/projects/{projectId}/upload-template` endpoint
  - Templates are stored in project-specific input container

### WebSocket Integration
The project page uses WebSocket connections for real-time updates:
- File status updates
- Indexing progress
- Deployment status changes

## User Management Pages

### Purpose
The User Management pages allow administrators to manage users, roles, and permissions.

### Components
1. **User List**
   - Table of users with roles and status
   - Search and filter options

2. **User Form**
   - User creation/editing form
   - Role assignment
   - Region assignment

3. **Team Management**
   - Team creation and editing
   - User assignment to teams

### Functionality
- **User Listing**: Fetches users from `/api/rbac/users` endpoint
- **User Creation**: Sends user data to `/api/rbac/users` endpoint
- **Role Management**: Updates user roles via `/api/rbac/users/{userId}/role` endpoint
- **Team Management**: Manages teams via `/api/rbac/teams` endpoints

### Role-Based Access
- **Super Admin**: Full access to all user management features
- **Regional Admin**: Can manage users in their assigned region
- **Regular User**: No access to user management

## Common Components

### Project Context
All project-specific pages use a shared ProjectContext that provides:
- Project metadata
- Resource configuration
- Access permissions

### Authentication
All pages integrate with the authentication system:
- User identity and roles
- Permission checks for actions
- Conditional rendering based on permissions

### Error Handling
All pages implement consistent error handling:
- Error messages for failed operations
- Retry mechanisms where appropriate
- Support ticket creation for critical errors

### Responsive Design
All pages are designed to be responsive:
- Desktop-optimized layout
- Mobile-friendly adaptations
- Consistent styling and branding

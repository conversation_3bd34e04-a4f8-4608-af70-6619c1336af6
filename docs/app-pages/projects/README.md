# Projects Documentation

This section provides detailed documentation on project management in the AI Scope application.

## Contents

- [Project Creation Process](./project-creation-process.md): End-to-end process of creating a new project

## Project Features

### Project Creation

Users can create new projects with:
- Project name and description
- Region selection (West US 2, West Europe, or Chile North Central)
- Automatic resource deployment

### Project Management

Projects can be managed through:
- Project listing and selection
- Project editing and deletion
- User assignment to projects
- Cost limit setting

### Project Resources

Each project has dedicated Azure resources:
- Storage containers (uploads, input, output)
- Search resources (index, datasource, indexer)
- Function App

## Project Lifecycle

1. **Creation**: User creates a project through the UI
2. **Resource Deployment**: Azure resources are deployed automatically
3. **Configuration**: Project is configured with resource information
4. **Usage**: Users upload documents and use chat functionality
5. **Management**: Project settings can be updated as needed
6. **Deletion**: Project and associated resources can be deleted

## Project Security

Projects implement security through:
- Role-based access control
- Resource isolation
- Azure RBAC for resource access

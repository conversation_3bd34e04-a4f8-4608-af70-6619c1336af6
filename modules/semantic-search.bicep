@description('Existing Azure AI Search service name')
param searchServiceName string

@description('Location for the deployment script')
param location string = resourceGroup().location

@description('Search index name')
param searchIndexName string

@description('Semantic configuration name for the index')
param semanticConfigName string = 'default'


@description('Resource tags')
param tags object = {}

// Reference existing search service
resource searchService 'Microsoft.Search/searchServices@2022-09-01' existing = {
  name: searchServiceName
}

// Deployment script to create semantic search index, datasource and indexer
resource semanticSearchScript 'Microsoft.Resources/deploymentScripts@2020-10-01' = {
  name: 'semantic-search-script-${uniqueString(searchService.id)}'
  location: location
  tags: tags
  kind: 'AzureCLI'
  properties: {
    azCliVersion: '2.40.0'
    timeout: 'PT30M'
    retentionInterval: 'P1D'
    forceUpdateTag: uniqueString(deployment().name)
    environmentVariables: [
      {
        name: 'SEARCH_SERVICE_NAME'
        value: searchServiceName
      }
      {
        name: 'SEARCH_API_KEY'
        value: listAdminKeys(searchService.id, '2022-09-01').primaryKey
      }
      {
        name: 'SEARCH_INDEX_NAME'
        value: searchIndexName
      }
      {
        name: 'SEMANTIC_CONFIG_NAME'
        value: semanticConfigName
      }
    ]
    scriptContent: '''
#!/bin/bash
set -e

echo "Configuring semantic search resources"

# Verify search service availability
az rest --method get \
  --uri "https://${SEARCH_SERVICE_NAME}.search.windows.net/indexes?api-version=2023-11-01" \
  --headers "api-key=${SEARCH_API_KEY}" || {
    echo "Unable to reach search service ${SEARCH_SERVICE_NAME}";
    exit 1;
  }

# Create index with semantic configuration
az rest --method put \
  --uri "https://${SEARCH_SERVICE_NAME}.search.windows.net/indexes/${SEARCH_INDEX_NAME}?api-version=2023-11-01" \
  --headers "Content-Type=application/json" "api-key=${SEARCH_API_KEY}" \
  --body "{\"name\": \"${SEARCH_INDEX_NAME}\", \"fields\": [{\"name\": \"id\", \"type\": \"Edm.String\", \"key\": true, \"searchable\": true, \"filterable\": true, \"sortable\": true, \"facetable\": false}, {\"name\": \"content\", \"type\": \"Edm.String\", \"searchable\": true, \"filterable\": false, \"sortable\": false, \"facetable\": false}, {\"name\": \"metadata_storage_name\", \"type\": \"Edm.String\", \"searchable\": true, \"filterable\": true, \"sortable\": true, \"facetable\": true}, {\"name\": \"metadata_storage_path\", \"type\": \"Edm.String\", \"searchable\": false, \"filterable\": true, \"sortable\": true, \"facetable\": false}, {\"name\": \"metadata_content_type\", \"type\": \"Edm.String\", \"searchable\": true, \"filterable\": true, \"sortable\": true, \"facetable\": true}], \"semantic\": {\"configurations\": [{\"name\": \"${SEMANTIC_CONFIG_NAME}\", \"prioritizedFields\": {\"prioritizedContentFields\": [{\"fieldName\": \"content\"}]}}]}}"

echo "Semantic search resources created"
'''
  }
}

output searchIndexName string = searchIndexName

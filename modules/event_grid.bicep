@description('Event Grid System Topic name base')
param eventGridSystemTopicName string

@description('Location for Event Grid resources')
param location string = resourceGroup().location

@description('Function App resource ID')
@minLength(1)
param functionAppResourceId string

@description('Storage account resource ID')
@minLength(1)
param storageAccountId string

@description('Resource tags')
param tags object = {}

// Event Grid System Topic for Blob Storage
resource blobStorageSystemTopic 'Microsoft.EventGrid/systemTopics@2022-06-15' = {
  name: eventGridSystemTopicName
  location: location
  tags: tags
  properties: {
    source: storageAccountId
    topicType: 'Microsoft.Storage.StorageAccounts'
  }
}

// Event Subscription for Blob Storage to Function App
resource blobEventSubscription 'Microsoft.EventGrid/systemTopics/eventSubscriptions@2022-06-15' = {
  parent: blobStorageSystemTopic
  name: 'blob-to-function-subscription'
  properties: {
    destination: {
      endpointType: 'AzureFunction'
      properties: {
        resourceId: '${functionAppResourceId}/functions/EventGridTriggerBlobIndexer'
        maxEventsPerBatch: 1
        preferredBatchSizeInKilobytes: 64
      }
    }
    filter: {
      includedEventTypes: [
        'Microsoft.Storage.BlobCreated'
        'Microsoft.Storage.BlobDeleted'
      ]
      subjectBeginsWith: '/blobServices/default/containers/'
      enableAdvancedFilteringOnArrays: true
    }
    eventDeliverySchema: 'EventGridSchema'
    retryPolicy: {
      maxDeliveryAttempts: 30
      eventTimeToLiveInMinutes: 1440
    }
  }
}
// No conditional deployment needed as parameters are now required

// Outputs
output eventGridSystemTopicName string = blobStorageSystemTopic.name
output eventGridSystemTopicId string = blobStorageSystemTopic.id
// Subscription is now always created (if functionAppResourceId is valid), so output its name directly.
// Handle potential deployment failure in the calling script if functionAppResourceId was invalid.
output eventSubscriptionName string = blobEventSubscription.name

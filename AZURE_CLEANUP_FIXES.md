# Azure Resource Cleanup Fixes

## Issues Found in the Logs

From analyzing the project deletion logs, the following issues were identified:

### 1. Project Deletion Partially Working
- **FR UAT** project (ID: 2baae374-b419-4c28-bdc6-04879e40b452) was deleted from Cosmos DB successfully
- Only the Function App was successfully deleted from Azure
- Storage Account, Search Service, and Event Grid resources failed to delete

### 2. Specific Errors Encountered

1. **Event Grid System Topic**: 
   - Error: `'SystemTopicsOperations' object has no attribute 'delete'`
   - Fixed: Changed to use `begin_delete()` which returns a poller

2. **Search Service**:
   - Error: `'ServicesOperations' object has no attribute 'begin_delete'`
   - Fixed: Changed to use `delete()` instead

3. **Storage Account**:
   - Error: `'StorageAccountsOperations' object has no attribute 'begin_delete'`
   - Fixed: Changed to use `delete()` instead

4. **Unclosed Client Session**:
   - Error: `Unclosed client session`
   - Fixed: Added proper credential cleanup in the `_cleanup_clients` method

## Fixes Applied

### 1. Updated Azure SDK Method Calls

```python
# Event Grid System Topic - Use begin_delete with poller
poller = await self.eventgrid_client.system_topics.begin_delete(
    self.resource_group_name,
    system_topic_name
)
await poller.result()

# Search Service - Use delete directly
await self.search_client.services.delete(
    self.resource_group_name,
    search_service_name
)

# Storage Account - Use delete directly
await self.storage_client.storage_accounts.delete(
    self.resource_group_name,
    storage_account_name
)
```

### 2. Enhanced Client Cleanup

Added proper cleanup for Azure SDK clients and credentials to prevent unclosed session warnings:

```python
# Close all clients
for client in clients:
    if client:
        try:
            await client.close()
        except Exception as e:
            logger.warning(f"Error closing client: {e}")

# Also close the credential
if hasattr(self.credential, 'close'):
    try:
        await self.credential.close()
    except Exception as e:
        logger.warning(f"Error closing credential: {e}")
```

## Testing the Fixes

To verify the fixes work correctly:

1. Try deleting another project and check if all resources are cleaned up
2. Monitor the logs:
   - `/logs/project_card_deletion.log` - for high-level deletion status
   - `/logs/azure_resource_cleanup.log` - for detailed cleanup operations
   - Terminal logs for any remaining errors

## Expected Behavior After Fixes

When deleting a project now, you should see:

1. Successful deletion from Cosmos DB
2. Successful deletion of all Azure resources:
   - ✅ Event Grid system topics and subscriptions
   - ✅ Function Apps and App Service Plans
   - ✅ Search Services
   - ✅ Storage Accounts
3. No unclosed session warnings
4. Proper cleanup status in the API response

## Additional Considerations

1. **Permissions**: Ensure the service principal has the necessary permissions to delete all resource types
2. **Resource Dependencies**: Some resources may take time to delete (especially storage accounts)
3. **Error Recovery**: If a resource fails to delete, the process continues with other resources
4. **Dry Run Mode**: Use `AZURE_CLEANUP_DRY_RUN=true` to test without actually deleting resources
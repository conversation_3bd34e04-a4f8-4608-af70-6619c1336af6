Update Jira Ticket DEV-125
I will now update the Jira ticket to include this new task, ensuring consistency and correctness.

Jira Ticket: DEV-125 (Revised)
Title: Fix Hardcoded Settings and Standardize on Gunicorn Web Server

Priority: Highest 🔥

Assignee: To be assigned

Reporter: Azure Mentor Copilot

Description:

The production deployment is failing due to a container startup failure. The root cause is a hardcoded "development mode" setting in app.py.

Additionally, an inconsistency was found between the local development server (Hypercorn) and the production container's server (Gunicorn). This ticket now includes the task to standardize on Gunicorn for both environments to ensure consistency and reliability.

Implementation Steps:

Add Gunicorn to Requirements:

Action: Add gunicorn to the requirements.txt file.
File: requirements.txt
Line to add: gunicorn>=20.1.0
Fix Hardcoded Session in app.py:

Action: Modify the create_app function in app.py to use a production-ready session backend when not in development mode.
File: app.py
Code to replace:
Python

# Configure session management
app.secret_key = os.getenv("QUART_SECRET_KEY", str(uuid.uuid4()))
app.config["SESSION_TYPE"] = "null"
app.config["SESSION_PERMANENT"] = True
app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(days=7)
logging.info("Using null session backend for local development")
Corrected Code:
Python

# Configure session management
app.secret_key = os.getenv("QUART_SECRET_KEY", str(uuid.uuid4()))

if os.getenv("DEVELOPMENT_MODE", "false").lower() == "true":
    app.config["SESSION_TYPE"] = "null"
    logging.info("Using null session backend for local development")
else:
    session_folder = os.path.join(os.path.dirname(os.path.abspath(__file__)), "quart_sessions")
    os.makedirs(session_folder, exist_ok=True)
    app.config["SESSION_TYPE"] = "filesystem"
    app.config["SESSION_FILE_DIR"] = session_folder
    logging.info(f"Using filesystem session backend for production at {session_folder}")

app.config["SESSION_PERMANENT"] = True
app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(days=7)
Standardize start.sh for Local Development:

Action: Modify the start.sh script to use gunicorn instead of hypercorn to match the production environment.
File: start.sh
Line to replace: ./.venv/bin/hypercorn app:app --bind 0.0.0.0:${API_PORT}
Corrected Line: ./.venv/bin/gunicorn app:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:${API_PORT}
Rebuild and Redeploy:

Action: After applying all changes, rebuild the Docker image and restart the App Service to pull the new version.
Commands:
Bash

# Rebuild and push to ACR
az acr build --registry webappaiscope --image aiscope/webapp:latest .

# Restart the App Service
az webapp restart --name ai-scope-app3 --resource-group rg-internal-ai
Acceptance Criteria:

gunicorn is added to requirements.txt.
start.sh is updated to use gunicorn.
app.py is updated with the conditional session logic.
The application runs locally without errors using ./start.sh.
The new Docker image is successfully deployed to the App Service.
The deployed application starts successfully and is accessible at its public URL.
Application logs confirm that the "filesystem session backend" is being used for production
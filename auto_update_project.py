#!/usr/bin/env python3
"""
Automatically update a project in CosmosDB with deployment summary.
This script is designed to be imported and used by other scripts that generate deployment summaries.
"""

import os
import sys
import json
import logging
from datetime import datetime, timezone
import asyncio

# Import the ProjectDataService
try:
    from backend.services.project_service import ProjectDataService
    project_service = ProjectDataService()
except ImportError:
    logging.error("Failed to import ProjectDataService. Make sure the backend module is in the Python path.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def auto_update_project_with_deployment(project_id, deployment_summary):
    """
    Automatically update a project in CosmosDB with deployment summary.
    This function is designed to be called right after a deployment summary is created.

    Args:
        project_id (str): The ID of the project
        deployment_summary (dict): Dictionary with deployment summary

    Returns:
        bool: True if update was successful, False otherwise
    """
    logger.info(f"Auto-updating project {project_id} with deployment summary")
    
    try:
        # Call the update method using asyncio since ProjectDataService is async
        success = asyncio.run(project_service.update_final_deployment_summary(project_id, deployment_summary))
        
        if success:
            logger.info(f"Successfully auto-updated project {project_id} with deployment summary")
        else:
            logger.error(f"Failed to auto-update project {project_id} with deployment summary")
        
        return success
    except Exception as e:
        logger.error(f"Error auto-updating project with deployment summary: {e}")
        return False

# If run directly, print usage information
if __name__ == "__main__":
    print("This script is designed to be imported and used by other scripts.")
    print("Usage example:")
    print("  from auto_update_project import auto_update_project_with_deployment")
    print("  auto_update_project_with_deployment(project_id, deployment_summary)")

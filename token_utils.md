"""
Token Utilities for Delegated Authentication Flow

This module provides utilities for extracting and validating tokens from request headers,
and using them for delegated authentication with Microsoft Graph API.
It also implements the On-Behalf-Of (OBO) flow for token exchange.
"""

import logging
import os
import time
from typing import Dict, Any, Optional, Tuple
import jwt
import requests
from fastapi import Request, HTTPException, status
from msal import ConfidentialClientApplication  # Add this import for OBO flow
from backend.auth.mock_auth import get_mock_user

# Cache for token validation results to avoid unnecessary API calls
_token_validation_cache = {}
# Cache expiry time in seconds
TOKEN_CACHE_EXPIRY = 300  # 5 minutes
# Consider tokens expiring within this buffer (seconds) as invalid
TOKEN_EXPIRY_BUFFER = int(os.getenv("TOKEN_EXPIRY_BUFFER", "60"))

# Azure AD configuration for OBO flow
AZURE_CLIENT_ID = os.getenv("AZURE_CLIENT_ID")
AZURE_TENANT_ID = os.getenv("AZURE_TENANT_ID")
AZURE_APP_SECRET = os.getenv("AZURE_APP_SECRET")  # This is the client secret for your backend app

# You might need to adjust the authority URL based on your tenant configuration
# For most multi-tenant or single-tenant apps, this is correct.
AUTHORITY = f"https://login.microsoftonline.com/{AZURE_TENANT_ID}" if AZURE_TENANT_ID else None

# Scopes required for Microsoft Graph API (user profile)
GRAPH_SCOPES = ["https://graph.microsoft.com/.default"]  # Or ["User.Read"] if more specific

def _is_token_expired(token: str) -> bool:
    """Return True if the token has expired or is about to expire."""
    try:
        payload = jwt.decode(token, options={"verify_signature": False})
        exp = payload.get("exp")
        if not exp:
            return True
        current = time.time()
        if exp < current + TOKEN_EXPIRY_BUFFER:
            logging.warning(
                f"TOKEN_EXTRACTION_DEBUG: Token expired or within buffer (exp={exp}, current={current})"
            )
            return True
        return False
    except Exception as e:
        logging.warning(f"TOKEN_EXTRACTION_DEBUG: Could not decode token for expiry check: {e}")
        return True

def extract_token_from_request(request: Request) -> Optional[str]:
    """
    Extract the Bearer token from the Authorization header.

    Args:
        request: The FastAPI request object

    Returns:
        The token string or None if not found
    """
    logging.info("🔍 TOKEN_EXTRACTION_DEBUG: Starting token extraction from request")

    # Log request method and path for context. Quart's Request.url is a string
    # while FastAPI's Request.url is an object with a `path` attribute. Handle
    # both to avoid attribute errors when this code runs under different
    # frameworks.
    try:
        path = request.url.path
    except AttributeError:
        path = getattr(request, "path", str(request.url))

    logging.info(
        f"🔍 TOKEN_EXTRACTION_DEBUG: Request method: {request.method}, path: {path}"
    )

    # Log all headers for debugging (excluding sensitive information)
    safe_headers = {k: v for k, v in request.headers.items()
                   if k.lower() not in ('authorization', 'cookie', 'x-api-key')}
    logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Safe request headers: {safe_headers}")

    # Check for Authorization header first
    auth_header = request.headers.get("Authorization")
    token = None
    if auth_header:
        logging.info("✅ TOKEN_EXTRACTION_DEBUG: Authorization header found")

        # Log header format (without showing the full token)
        if len(auth_header) > 20:
            logging.info(
                f"🔍 TOKEN_EXTRACTION_DEBUG: Authorization header format: {auth_header[:10]}...{auth_header[-10:]} (truncated, length: {len(auth_header)})"
            )
        else:
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Authorization header (short): {auth_header}")

        parts = auth_header.split()
        logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Header split into {len(parts)} parts")

        if len(parts) == 2 and parts[0].lower() == "bearer":
            candidate = parts[1]
            logging.info("✅ TOKEN_EXTRACTION_DEBUG: Successfully extracted Bearer token")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token preview: {candidate[:10]}...{candidate[-10:]} (length: {len(candidate)})")

            if not _is_token_expired(candidate):
                token = candidate
            else:
                logging.warning("❌ TOKEN_EXTRACTION_DEBUG: Authorization token expired or near expiry")
        else:
            logging.warning("❌ TOKEN_EXTRACTION_DEBUG: Invalid Authorization header format")
    else:
        logging.warning("❌ TOKEN_EXTRACTION_DEBUG: No Authorization header found in request")
        logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Available headers: {list(request.headers.keys())}")

    if not token:
        # Fallback to Azure-specific Entra ID headers when running on Azure Web App
        id_token_header = request.headers.get("x-ms-token-aad-id-token")
        if id_token_header and not _is_token_expired(id_token_header):
            logging.info("✅ TOKEN_EXTRACTION_DEBUG: Using x-ms-token-aad-id-token header")
            token = id_token_header
        else:
            if id_token_header:
                logging.warning("❌ TOKEN_EXTRACTION_DEBUG: x-ms-token-aad-id-token header expired")

    if not token:
        access_token_header = request.headers.get("x-ms-token-aad-access-token")
        if access_token_header and not _is_token_expired(access_token_header):
            logging.info("✅ TOKEN_EXTRACTION_DEBUG: Using x-ms-token-aad-access-token header")
            token = access_token_header
        elif access_token_header:
            logging.warning("❌ TOKEN_EXTRACTION_DEBUG: x-ms-token-aad-access-token header expired")

    if not token:
        return None

    # Basic JWT format validation
    token_parts = token.split('.')
    if len(token_parts) == 3:
        logging.info("✅ TOKEN_EXTRACTION_DEBUG: Token appears to be valid JWT format (3 parts)")
        try:
            header = jwt.decode_complete(token, options={"verify_signature": False})["header"]
            payload = jwt.decode_complete(token, options={"verify_signature": False})["payload"]
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token header: {header}")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token payload keys: {list(payload.keys())}")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token expiration: {payload.get('exp')}")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token audience: {payload.get('aud')}")
            logging.info(f"🔍 TOKEN_EXTRACTION_DEBUG: Token issuer: {payload.get('iss')}")
        except Exception as e:
            logging.warning(f"⚠️ TOKEN_EXTRACTION_DEBUG: Error decoding token for debugging: {e}")
    else:
        logging.warning(f"⚠️ TOKEN_EXTRACTION_DEBUG: Token may not be valid JWT format - has {len(token_parts)} parts instead of 3")

    return token

async def validate_token(token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Validate the token and extract its claims.

    Args:
        token: The JWT token to validate

    Returns:
        A tuple of (is_valid, claims)
    """
    logging.info("🔍 TOKEN_VALIDATION_DEBUG: Starting token validation")
    logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Token preview: {token[:10]}...{token[-10:]} (length: {len(token)})")

    # Clear cache to force fresh validation
    _token_validation_cache.clear()
    logging.info("🗑️ TOKEN_VALIDATION_DEBUG: Cache cleared, forcing fresh validation")

    try:
        # Get environment variables
        tenant_id = os.getenv("AZURE_TENANT_ID", "common")
        client_id = os.getenv("AZURE_CLIENT_ID")  # This is your backend's Client ID

        # Define accepted audiences for your API
        # It should include both the Client ID and the Application ID URI
        valid_api_audiences = [
            client_id,  # e.g., "b8e60630-b988-48cb-843a-779eaff15d94"
            f"api://{client_id}"  # e.g., "api://b8e60630-b988-48cb-843a-779eaff15d94"
        ]
        # You might also have custom Application ID URIs configured, add them if necessary:
        # valid_api_audiences.append("your-custom-app-id-uri")

        logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Using tenant_id: {tenant_id}")
        logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Backend client_id for audience check: {client_id}")
        logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Accepted API audiences: {valid_api_audiences}")

        # Get Azure AD's JWKs for token verification
        try:
            jwks_url = f'https://login.microsoftonline.com/{tenant_id}/discovery/v2.0/keys'
            logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Fetching JWKS from: {jwks_url}")

            response = requests.get(jwks_url, timeout=10)
            response.raise_for_status()
            jwks = response.json()

            logging.info(f"✅ TOKEN_VALIDATION_DEBUG: Successfully fetched JWKS with {len(jwks.get('keys', []))} keys")
            logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Available key IDs: {[k.get('kid') for k in jwks.get('keys', [])]}")

            # Extract the kid from the token header
            logging.info("🔍 TOKEN_VALIDATION_DEBUG: Extracting token header")
            token_header = jwt.get_unverified_header(token)
            kid = token_header.get('kid')

            logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Token header: {token_header}")
            logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Token kid: {kid}")

            # Find the matching key in the JWKs
            key = next((k for k in jwks.get('keys', []) if k.get('kid') == kid), None)
            if not key:
                logging.error(f"❌ TOKEN_VALIDATION_DEBUG: No matching key found for kid: {kid}")
                logging.error(f"❌ TOKEN_VALIDATION_DEBUG: Available kids: {[k.get('kid') for k in jwks.get('keys', [])]}")
                raise ValueError("No matching key found for token")

            logging.info(f"✅ TOKEN_VALIDATION_DEBUG: Found matching key for kid: {kid}")
            logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Key details: {key}")

            # Create a public key object from the JWK
            logging.info("🔍 TOKEN_VALIDATION_DEBUG: Creating public key from JWK")
            from cryptography.hazmat.primitives.asymmetric.rsa import RSAPublicNumbers

            # Convert base64url to int
            def base64url_to_int(val):
                # Add padding if needed
                missing_padding = len(val) % 4
                if missing_padding:
                    val += '=' * (4 - missing_padding)
                return int.from_bytes(jwt.utils.base64url_decode(val), 'big')

            numbers = RSAPublicNumbers(
                base64url_to_int(key['e']),
                base64url_to_int(key['n'])
            )
            public_key = numbers.public_key()

            logging.info("✅ TOKEN_VALIDATION_DEBUG: Successfully created public key")

            # Verify the token
            logging.info("🔍 TOKEN_VALIDATION_DEBUG: Verifying token signature and claims")

            # Decode token without verification first to check claims
            unverified_claims = jwt.decode(token, options={"verify_signature": False})
            logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Unverified claims: {unverified_claims}")

            token_aud_claim = unverified_claims.get('aud')

            # Determine if this is a Microsoft Graph API token (this logic might be less relevant now if OBO is used)
            # However, keeping it won't harm if you ever pass Graph tokens directly for some reason.
            is_graph_token = token_aud_claim == '00000003-0000-0000-c000-000000000000'

            current_audience_to_check = None
            if is_graph_token:
                logging.info("🔍 TOKEN_VALIDATION_DEBUG: Token appears to be a Microsoft Graph API token based on audience.")
                # For Graph tokens, audience check is typically against the Graph resource ID,
                # or sometimes skipped if other validations (like issuer and scopes) are strong.
                # PyJWT will check if token_aud_claim is in the list passed to 'audience'.
                # If we want to be lenient for graph tokens specifically, we might set it to token_aud_claim itself.
                current_audience_to_check = token_aud_claim
            else:
                logging.info("🔍 TOKEN_VALIDATION_DEBUG: Token appears to be an app token (for this API).")
                # For your own API, check against the list of valid audiences.
                current_audience_to_check = valid_api_audiences

            valid_issuers = [
                f'https://login.microsoftonline.com/{tenant_id}/v2.0',
                f'https://sts.windows.net/{tenant_id}/'
            ]
            logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Valid issuers: {valid_issuers}")
            logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Audience(s) for PyJWT check: {current_audience_to_check}")

            # Verify the token with the appropriate parameters
            try:
                claims = jwt.decode(
                    token,
                    public_key,
                    algorithms=['RS256'],
                    audience=current_audience_to_check,  # PyJWT will check if the token's 'aud' is IN this list/value
                    issuer=valid_issuers  # Pass the list of valid issuers
                )

                # The check 'if token_issuer not in valid_issuers:' is now redundant
                # as jwt.decode would have raised an error if the issuer was not in the list.
                # However, to be absolutely sure the token_issuer is one of the valid_issuers after decode,
                # we can keep a modified check if necessary, though typically not required if PyJWT handles list correctly.
                # For now, assuming PyJWT handles it, this specific check can be removed or commented.
                # If issues persist, this check can be re-evaluated:
                # if claims.get('iss') not in valid_issuers:
                #     logging.error(f"❌ TOKEN_VALIDATION_DEBUG: Issuer {claims.get('iss')} not in valid_issuers {valid_issuers} (post-decode check)")
                #     raise jwt.InvalidIssuerError("Invalid token issuer after decode.")

                logging.info("✅ TOKEN_VALIDATION_DEBUG: Token signature and claims verified successfully")
                logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Token claims: {list(claims.keys())}")
                logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Token issuer: {claims.get('iss')}")
                logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Token audience: {claims.get('aud')}")
                logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Token subject: {claims.get('sub')}")
                logging.info(f"🔍 TOKEN_VALIDATION_DEBUG: Token email: {claims.get('email', claims.get('preferred_username'))}")

            except jwt.InvalidAudienceError as e:
                logging.error(f"❌ TOKEN_VALIDATION_DEBUG: Token validation failed: Invalid Audience. Token 'aud': {token_aud_claim}, Expected: {current_audience_to_check}. Details: {e}")
                _cache_validation_result(token, False, None)
                return False, None
            except jwt.InvalidTokenError as e:
                logging.error(f"❌ TOKEN_VALIDATION_DEBUG: Token validation failed: {e}")
                _cache_validation_result(token, False, None)
                return False, None

        except Exception as e:
            logging.error(f"❌ TOKEN_VALIDATION_DEBUG: Error during token verification: {e}")
            logging.error(f"❌ TOKEN_VALIDATION_DEBUG: Exception type: {type(e).__name__}")
            logging.error(f"❌ TOKEN_VALIDATION_DEBUG: Exception details: {str(e)}")
            _cache_validation_result(token, False, None)
            return False, None

        # Check if token is expired
        if "exp" in claims:
            exp_time = claims["exp"]
            current_time = time.time()
            if exp_time < current_time + TOKEN_EXPIRY_BUFFER:
                logging.warning(
                    f"❌ TOKEN_VALIDATION_DEBUG: Token is expired or near expiry - exp: {exp_time}, current: {current_time}"
                )
                _cache_validation_result(token, False, None)
                return False, None
            else:
                logging.info(
                    f"✅ TOKEN_VALIDATION_DEBUG: Token expiration ok - exp: {exp_time}, current: {current_time}"
                )

        # Cache the result
        logging.info("✅ TOKEN_VALIDATION_DEBUG: Token validation successful, caching result")
        _cache_validation_result(token, True, claims)
        return True, claims

    except Exception as e:
        logging.error(f"❌ TOKEN_VALIDATION_DEBUG: Error during token verification (outer try-except): {e}", exc_info=True)
        _cache_validation_result(token, False, None)
        return False, None

def _cache_validation_result(token: str, is_valid: bool, claims: Optional[Dict[str, Any]]) -> None:
    """Cache the token validation result"""
    _token_validation_cache[token] = {
        "is_valid": is_valid,
        "claims": claims,
        "expires_at": time.time() + TOKEN_CACHE_EXPIRY
    }

async def get_graph_token_on_behalf_of(user_assertion_token: str) -> Optional[str]:
    """
    Acquires an access token for Microsoft Graph API on behalf of the user,
    using the provided user assertion token (the token received by this API).
    """
    if not all([AZURE_CLIENT_ID, AZURE_TENANT_ID, AZURE_APP_SECRET]):
        logging.error("OBO_FLOW_DEBUG: Missing Azure AD app configuration (Client ID, Tenant ID, or App Secret) for OBO flow.")
        return None

    if not user_assertion_token:
        logging.error("OBO_FLOW_DEBUG: No user assertion token provided for OBO flow.")
        return None

    logging.info("OBO_FLOW_DEBUG: Attempting On-Behalf-Of flow to get Graph API token.")
    logging.info(f"OBO_FLOW_DEBUG: Client ID: {AZURE_CLIENT_ID}, Authority: {AUTHORITY}")
    logging.info(f"OBO_FLOW_DEBUG: User assertion token preview: {user_assertion_token[:20]}...{user_assertion_token[-20:]}")

    try:
        # Create a confidential client application
        app = ConfidentialClientApplication(
            client_id=AZURE_CLIENT_ID,
            authority=AUTHORITY,
            client_credential=AZURE_APP_SECRET
        )

        # Acquire token on behalf of the user
        # The user_assertion is the access token your API received from the client.
        result = app.acquire_token_on_behalf_of(
            user_assertion=user_assertion_token,
            scopes=GRAPH_SCOPES
        )

        if "access_token" in result:
            logging.info(f"OBO_FLOW_DEBUG: Successfully acquired Graph API token via OBO flow. Token Expiry: {result.get('expires_in')}s")
            logging.info(f"OBO_FLOW_DEBUG: Graph token preview: {result['access_token'][:20]}...{result['access_token'][-20:]}")
            return result["access_token"]
        else:
            error_description = result.get("error_description", "No error description provided.")
            logging.error(f"OBO_FLOW_DEBUG: Failed to acquire Graph API token via OBO flow. Error: {result.get('error')}. Description: {error_description}")
            # Log more details if available
            if "correlation_id" in result:
                logging.error(f"OBO_FLOW_DEBUG: Correlation ID: {result['correlation_id']}")
            if "claims" in result:
                logging.error(f"OBO_FLOW_DEBUG: Claims: {result['claims']}")
            return None

    except Exception as e:
        logging.error(f"OBO_FLOW_DEBUG: Exception during OBO flow: {e}", exc_info=True)
        return None

async def get_user_info_from_graph(graph_access_token: str) -> Optional[Dict[str, Any]]:
    """
    Get user information from Microsoft Graph API using the delegated Graph access token.

    Args:
        graph_access_token: The delegated access token specifically for Microsoft Graph API.

    Returns:
        User information or None if the request fails
    """
    logging.info("🔍 GRAPH_API_DEBUG: Starting Microsoft Graph API call with OBO token.")

    if not graph_access_token:  # Check the graph_access_token now
        logging.warning("❌ GRAPH_API_DEBUG: No Graph access token provided to get_user_info_from_graph.")
        return None

    logging.info(f"🔍 GRAPH_API_DEBUG: Graph token preview: {graph_access_token[:10]}...{graph_access_token[-10:]} (length: {len(graph_access_token)})")

    try:
        headers = {
            "Authorization": f"Bearer {graph_access_token}",  # Use the graph_access_token
            "Content-Type": "application/json"
        }

        logging.info("🔍 GRAPH_API_DEBUG: Calling Microsoft Graph API /v1.0/me endpoint")
        logging.info(f"🔍 GRAPH_API_DEBUG: Request headers: {list(headers.keys())}")

        response = requests.get(
            "https://graph.microsoft.com/v1.0/me",
            headers=headers,
            timeout=10
        )

        logging.info(f"🔍 GRAPH_API_DEBUG: Graph API response status: {response.status_code}")

        if response.status_code == 200:
            user_data = response.json()
            logging.info(f"✅ GRAPH_API_DEBUG: Successfully retrieved user info from Graph API")
            logging.info(f"🔍 GRAPH_API_DEBUG: User display name: {user_data.get('displayName')}")
            logging.info(f"🔍 GRAPH_API_DEBUG: User principal name: {user_data.get('userPrincipalName')}")
            logging.info(f"🔍 GRAPH_API_DEBUG: User ID: {user_data.get('id')}")

            return {
                "id": user_data.get("id"),
                "name": user_data.get("displayName"),
                "email": user_data.get("userPrincipalName"),
                # Don't set a default role here - it will be set from the database
                # or defaulted to REGULAR_USER later if not found in the database
            }
        else:
            logging.error(f"❌ GRAPH_API_DEBUG: Error getting user info from Graph API: {response.status_code}")
            logging.error(f"❌ GRAPH_API_DEBUG: Response text: {response.text}")

            # Log response headers for debugging
            logging.error(f"❌ GRAPH_API_DEBUG: Response headers: {dict(response.headers)}")

            # Log more details about the graph_access_token if needed (scopes, audience etc. are less relevant here as this token IS for Graph)
            try:
                token_parts = graph_access_token.split('.')
                if len(token_parts) == 3:  # Valid JWT format
                    header = jwt.decode_complete(graph_access_token, options={"verify_signature": False})["header"]
                    payload = jwt.decode_complete(graph_access_token, options={"verify_signature": False})["payload"]

                    logging.info(f"🔍 GRAPH_API_DEBUG: Token header: {header}")
                    logging.info(f"🔍 GRAPH_API_DEBUG: Token payload aud: {payload.get('aud')}")
                    logging.info(f"🔍 GRAPH_API_DEBUG: Token payload scp: {payload.get('scp')}")
                    logging.info(f"🔍 GRAPH_API_DEBUG: Token payload iss: {payload.get('iss')}")
                    logging.info(f"🔍 GRAPH_API_DEBUG: Token payload exp: {payload.get('exp')}")

                    # Check for required scopes
                    scopes = str(payload.get('scp', ''))
                    if 'User.Read' not in scopes:
                        logging.error("❌ GRAPH_API_DEBUG: Token does not have User.Read scope")
                        logging.error(f"❌ GRAPH_API_DEBUG: Available scopes: {scopes}")
                    else:
                        logging.info("✅ GRAPH_API_DEBUG: Token has User.Read scope")

                    # Check audience
                    expected_audience = os.getenv("AZURE_CLIENT_ID")
                    actual_audience = payload.get('aud')
                    if actual_audience != expected_audience:
                        logging.error(f"❌ GRAPH_API_DEBUG: Token audience mismatch - expected: {expected_audience}, actual: {actual_audience}")
                    else:
                        logging.info("✅ GRAPH_API_DEBUG: Token audience matches expected client ID")

                else:
                    logging.warning("⚠️ GRAPH_API_DEBUG: Token is not in valid JWT format")
            except Exception as jwt_error:
                logging.error(f"❌ GRAPH_API_DEBUG: Error decoding token: {jwt_error}")

            return None
    except Exception as e:
        logging.error(f"❌ GRAPH_API_DEBUG: Error calling Microsoft Graph API: {e}")
        logging.error(f"❌ GRAPH_API_DEBUG: Exception type: {type(e).__name__}")
        return None

async def get_authenticated_user_with_token(request: Request) -> Dict[str, Any]:
    """
    Get the authenticated user using the token from the request with OBO flow.

    This function extracts the token from the request, validates it,
    uses the On-Behalf-Of flow to get a Graph API token, and then
    gets user information from Microsoft Graph API.
    If the token is invalid or missing, it falls back to the mock user.

    Args:
        request: The FastAPI request object

    Returns:
        User information
    """
    # Extract token from request
    user_assertion_token = extract_token_from_request(request)
    if not user_assertion_token:
        logging.warning("No token found in request, using mock user")
        return await get_mock_user()

    # Validate the user assertion token
    is_valid, claims = await validate_token(user_assertion_token)
    if not is_valid:
        logging.warning("Invalid token, using mock user")
        return await get_mock_user()

    # Use OBO flow to get Graph API token
    graph_token = await get_graph_token_on_behalf_of(user_assertion_token)
    if not graph_token:
        logging.warning("Failed to get Graph API token via OBO flow, using mock user")
        return await get_mock_user()

    # Get user info from Microsoft Graph API using the OBO token
    user_info = await get_user_info_from_graph(graph_token)
    if not user_info:
        logging.warning("Failed to get user info from Graph API, using mock user")
        return await get_mock_user()

    return user_info

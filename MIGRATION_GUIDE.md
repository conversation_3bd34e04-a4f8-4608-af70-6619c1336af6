# Migration Guide: From Environment Field to Root-Level Variables

## Overview
This guide documents the migration from using the `environment` field in Cosmos DB project documents to using root-level variables directly.

## Changes Made

### 1. ProjectLayout.tsx
- Replaced `projectEnv` state with `projectConfig` to store the full project configuration
- Updated ProjectContext.Provider to read values directly from `projectConfig` instead of `projectEnv`
- Added new fields to ProjectContext interface:
  - `searchIndexerName`
  - `functionMaturityUrl`
  - `functionExecutiveSummaryUrl`
  - `functionPowerPointUrl`
- Kept `projectEnv` in the context as an empty object for backward compatibility

### 2. FileManagement.tsx
- Updated all references from `projectContext.projectEnv?.SEARCH_INDEX` to `projectContext.searchIndex`
- Updated reference from `projectContext.projectEnv?.AZURE_SEARCH_KEY` to `projectContext.searchApiKey`
- Updated reference from `projectContext.projectEnv?.SEARCH_INDEXER` to `projectContext.searchIndexerName`

### 3. RightFileManagement.tsx
- Updated function URL references:
  - `projectContext.projectEnv?.AZURE_FUNCTION_MATURITY_ASSESSMENT_URL` → `projectContext.functionMaturityUrl`
  - `projectContext.projectEnv?.AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL` → `projectContext.functionExecutiveSummaryUrl`
- Updated function key references:
  - `projectContext.projectEnv?.FUNCTION_KEY_MATURITY` → `projectContext.functionKeyMaturity`
  - `projectContext.projectEnv?.FUNCTION_KEY_EXECUTIVE_SUMMARY` → `projectContext.functionKeyExecutiveSummary`
- Updated search index reference to use `projectContext.searchIndex`

### 4. PriorityPlotComponent.tsx
- Updated all `projectEnv` references to `projectContext` to use the direct context properties

## Field Mapping
The following root-level fields are now used directly:

| Old (Environment Field) | New (Root Level) | Context Property |
|------------------------|------------------|------------------|
| STORAGE_ACCOUNT_NAME | storage_account_name | storageAccountName |
| STORAGE_ACCOUNT_SAS_TOKEN | storage_account_sas_token | storageAccountSasToken |
| STORAGE_CONTAINER_UPLOADS | storage_container_uploads | storageContainerUploads |
| STORAGE_CONTAINER_INPUT | storage_container_input | storageContainerInput |
| STORAGE_CONTAINER_OUTPUT | storage_container_output | storageContainerOutput |
| AZURE_SEARCH_KEY | search_key | searchApiKey |
| SEARCH_INDEX | search_index_name | searchIndex |
| SEARCH_INDEXER | search_indexer_name | searchIndexerName |
| FUNCTION_KEY_MATURITY | function_key_maturity | functionKeyMaturity |
| FUNCTION_KEY_EXECUTIVE_SUMMARY | function_key_executive_summary | functionKeyExecutiveSummary |
| FUNCTION_KEY_POWERPOINT | function_key_powerpoint | functionKeyPowerPoint |
| AZURE_FUNCTION_MATURITY_ASSESSMENT_URL | azure_function_maturity_assessment_url | functionMaturityUrl |
| AZURE_FUNCTION_EXECUTIVE_SUMMARY_URL | azure_function_executive_summary_url | functionExecutiveSummaryUrl |
| AZURE_FUNCTION_POWERPOINT_URL | azure_function_powerpoint_url | functionPowerPointUrl |

## Benefits
1. **No data duplication**: Values are stored only once at the root level
2. **Simpler structure**: Direct access without nested environment object
3. **Better performance**: No need to spread and merge environment variables
4. **Easier debugging**: Clear, flat structure in Cosmos DB

## Backward Compatibility
- The `projectEnv` field is kept in the ProjectContext but is now always an empty object
- This ensures any code that checks for `projectContext.projectEnv` won't break
- All actual values are accessed through direct context properties

## Testing
After deployment, verify:
1. Storage operations work correctly (file upload/download)
2. Search indexing functions properly
3. Function app integrations work (maturity assessment, executive summary)
4. No console errors about missing configuration
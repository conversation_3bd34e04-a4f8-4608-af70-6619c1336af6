from app import create_app
import logging
import asyncio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

# Suppress server logs to avoid duplicate "Running on" messages
hypercorn_logger = logging.getLogger('hypercorn')
hypercorn_logger.setLevel(logging.WARNING)

# Suppress Quart's built-in logging
quart_logger = logging.getLogger('quart.serving')
quart_logger.setLevel(logging.WARNING)

app = create_app()

@app.after_serving
async def shutdown_cleanup():
    """Clean up all Azure connections and network resources"""
    logging.info("Initiating comprehensive resource cleanup")

    cleanup_tasks = []

    # Close OpenAI client with error handling
    if hasattr(app, 'azure_openai_client'):
        logging.info("Closing Azure OpenAI client")
        cleanup_tasks.append(app.azure_openai_client.close())

    # Close Cosmos client with error handling
    if hasattr(app, 'cosmos_conversation_client'):
        logging.info("Closing Cosmos DB client")
        cleanup_tasks.append(app.cosmos_conversation_client.close())

    # Close Search service and its HTTP client
    if hasattr(app, 'search_service'):
        logging.info("Closing Search service resources")
        cleanup_tasks.append(app.search_service.close())
        if hasattr(app.search_service, 'http_client'):
            logging.info("Closing Search HTTP client")
            cleanup_tasks.append(app.search_service.http_client.aclose())

    # Close any remaining aiohttp connectors
    logging.info("Closing remaining network connectors")
    results = await asyncio.gather(*cleanup_tasks, return_exceptions=True)
    for result in results:
        if isinstance(result, Exception):
            logging.error(f"Cleanup error: {str(result)}")

    # Force close all async generators
    logging.info("Finalizing async resources")
    await asyncio.sleep(0.250)  # Allow pending closes to complete

if __name__ == '__main__':
    logging.info("Starting server on port 50505 with WebSocket support")
    # Use the Quart-specific server for better WebSocket handling
    app.run(
        host='0.0.0.0',  # Listen on all interfaces to make it accessible from Docker/containers
        port=50505,
        debug=False,
        use_reloader=True,
        websocket=True  # Explicitly enable WebSocket support
    )

{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/eslint-recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/jsx-runtime",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "prettier"
  ],
  "plugins": ["@typescript-eslint", "import", "simple-import-sort", "prettier"],
  "env": {
    "browser": true,
    "jest": true
  },
  "rules": {
    // "prettier/prettier": "error",
    "prettier/prettier": [
      "error",
      {},
      {
        "usePrettierrc": false
      }
    ],
    "quote-props": [2, "as-needed"],
    "comma-dangle": "error",
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "no-debugger": "error",
    "react/prop-types": 0,
    "import/no-extraneous-dependencies": ["error", { "devDependencies": true }],
    "object-curly-newline": [
      "error",
      {
        "ObjectExpression": "always",
        "ObjectPattern": { "multiline": "as-needed" },
        "ImportDeclaration": "never",
        "ExportDeclaration": { "multiline": true, "minProperties": 3 }
      }
    ],
    "object-curly-spacing": ["error", "as-needed", { "objectsInObjects": true }],
    "no-underscore-dangle": 0,
    "jsx-a11y/click-events-have-key-events": 0,
    "jsx-a11y/anchor-is-valid": 0,
    "no-await-in-loop": 0,
    "no-unused-expressions": ["error", { "allowShortCircuit": true, "allowTernary": true }],
    "arrow-parens": ["error", "as-needed"],
    "no-unused-vars": "off", // Turn off the base rule and use @typescript-eslint/no-unused-vars
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "ignoreRestSiblings": true,
        "args": "after-used"
      }
    ],
    "import/prefer-default-export": 0,
    "react/no-find-dom-node": 0,
    "no-shadow": 0,
    "max-len": 0,
    "consistent-return": 0,
    "no-plusplus": 0,
    "spaced-comment": 0,
    "react/react-in-jsx-scope": 0,
    "react/jsx-filename-extension": 0,
    "indent": ["error", 2],
    "react/jsx-indent": ["error", 2],
    "react/jsx-indent-props": ["error", 2],
    "@typescript-eslint/no-explicit-any": "warn",
    "simple-import-sort/exports": "error",
    "simple-import-sort/imports": [
      "error",
      {
        "groups": [
          // Packages `react` related packages come first.
          ["^react", "^@?\\w"],
          // Internal packages.
          ["^(@|components)(/.*|$)"],
          // Side effect imports.
          ["^\\u0000"],
          // Parent imports. Put `..` last.
          ["^\\.\\.(?!/?$)", "^\\.\\./?$"],
          // Other relative imports. Put same-folder imports and `.` last.
          ["^\\./(?=.*/)(?!/?$)", "^\\.(?!/?$)", "^\\./?$"],
          // Style imports.
          ["^.+\\.?(css)$"]
        ]
      }
    ]
  },
  "ignorePatterns": ["node_modules/", "build/", "dist/", "public/", "*.config.ts"]
}

{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node node-polyfill.cjs && vite", "build": "node --max-old-space-size=4096 node-polyfill.cjs && tsc --noEmit && node --max-old-space-size=4096 ./node_modules/.bin/vite build", "build:fast": "node --max-old-space-size=4096 node-polyfill.cjs && node --max-old-space-size=4096 ./node_modules/.bin/vite build", "watch": "node node-polyfill.cjs && tsc && vite build --watch", "test": "jest", "lint": "npx eslint src", "lint:fix": "npx eslint --fix", "prettier": "npx prettier src --check", "prettier:fix": "npx prettier src --write", "format": "npm run prettier:fix && npm run lint:fix"}, "dependencies": {"@azure/msal-browser": "^4.12.0", "@azure/msal-react": "^3.0.12", "@azure/search-documents": "^12.1.0", "@azure/storage-blob": "^12.27.0", "@fluentui/react": "^8.105.3", "@fluentui/react-hooks": "^8.6.29", "@fluentui/react-icons": "^2.0.195", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-tabs": "^1.1.9", "@types/uuid": "^10.0.0", "axios": "^0.27.2", "chart.js": "^4.5.0", "dompurify": "^3.0.8", "dotenv": "^16.4.7", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lucide-react": "^0.469.0", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-markdown": "^7.0.1", "react-router-dom": "^6.8.1", "react-syntax-highlighter": "^15.5.0", "react-uuid": "^2.0.0", "rehype-raw": "^6.1.1", "remark-gfm": "^3.0.1", "remark-supersub": "^1.0.0", "shadcn-ui": "^0.9.4", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.0.2", "@eslint/js": "^9.1.1", "@types/dompurify": "^3.0.5", "@types/eslint-config-prettier": "^6.11.3", "@types/jest": "^29.5.12", "@types/lodash-es": "^4.17.12", "@types/mocha": "^10.0.6", "@types/node": "^20.17.11", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/react-syntax-highlighter": "^15.5.11", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-n": "^16.6.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^12.1.0", "globals": "^15.0.0", "jest": "^29.7.0", "lint-staged": "^15.2.2", "postcss": "^8.5.3", "prettier": "^3.2.5", "react-test-renderer": "^18.2.0", "string.prototype.replaceall": "^1.0.10", "tailwindcss": "^4.1.4", "terser": "^5.39.2", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^4.9.5", "vite": "^4.5.2", "vite-plugin-static-copy": "^0.17.1"}}
// Mock implementation of refractor
const refractor = {
  register: () => {},
  highlight: (code, language) => {
    return [{ type: 'text', value: code }];
  },
  // Add core property to handle imports like refractor/core
  core: {
    register: () => {},
    highlight: (code, language) => {
      return [{ type: 'text', value: code }];
    }
  }
};

// Export the mock
export default refractor;

// Export core for direct imports
export const core = refractor.core;

// Node.js polyfill script
const crypto = require('crypto');

// Add getRandomValues to the global crypto object if it doesn't exist
if (!crypto.getRandomValues) {
  crypto.getRandomValues = function getRandomValues(array) {
    const bytes = crypto.randomBytes(array.length);
    for (let i = 0; i < bytes.length; i++) {
      array[i] = bytes[i];
    }
    return array;
  };
}

// Make crypto available globally
global.crypto = crypto;

console.log('Node.js crypto polyfill applied');

/**
 * User Context Service
 *
 * This service centralizes all RBAC-related API calls to the backend.
 * Instead of making multiple RBAC API calls from the frontend,
 * this service makes a single call to get all user context information.
 */

import { UserRole, ProjectPermissions } from '../models/roles';
import { getAuthHeaders } from './authHeaderService';

// Types for user context
export interface UserContextData {
  user: {
    id: string;
    name: string;
    email: string;
    role: UserRole;
    region?: string;
    avatar?: string;
  };
  permissions: {
    canCreateProject: boolean;
    canEditProject: boolean;
    canDeleteProject: boolean;
    canAssignUsers: boolean;
    canSetCostLimits: boolean;
    canAccessAdminPanel: boolean;
    canManageUsers: boolean;
    canManageGlobalSettings: boolean;
    canCreateTeams: boolean;
    canAssignProjects: boolean;
    canAssignTeams: boolean;
    canSetupRegionalAdmins: boolean;
    canTagUsers: boolean;
    canViewAllRegions: boolean;
  };
  accessibleResources: {
    projects: any[];
    teams: any[];
    regions: any[];
    users: any[];
  };
}

export interface ProjectPermissionsData {
  projectId: string;
  userId: string;
  permissions: ProjectPermissions;
  basePermissions: {
    [key: string]: boolean;
  };
}

// Cache for user context data
let userContextCache: UserContextData | null = null;
let projectPermissionsCache: { [projectId: string]: ProjectPermissionsData } = {};
const CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes
let lastFetchTime = 0;

/**
 * Fetch user context from the backend
 * This replaces multiple RBAC API calls with a single call
 */
export async function fetchUserContext(forceRefresh = false): Promise<UserContextData | null> {
  const now = Date.now();

  // Return cached data if available and not expired, unless force refresh is requested
  if (!forceRefresh && userContextCache && (now - lastFetchTime < CACHE_EXPIRY)) {
    return userContextCache;
  }

  try {
    console.log('Fetching user context from backend');

    // Check if we have MSAL accounts first
    const msalInstance = (window as any).msalInstance;
    let currentUserId = '1'; // Default to Super Admin
    let hasMsalAccount = false;

    if (msalInstance) {
      const accounts = msalInstance.getAllAccounts();
      console.log(`MSAL has ${accounts.length} accounts`);

      if (accounts.length > 0) {
        hasMsalAccount = true;
        console.log('Using MSAL account:', accounts[0].username);

        // If we have an MSAL account, we don't need to use the stored user ID
        // The backend will identify the user from the token
        currentUserId = '';
      }
    }

    // If no MSAL account, fall back to stored user ID
    if (!hasMsalAccount) {
      try {
        const storedUser = localStorage.getItem('currentUser');
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          currentUserId = parsedUser.id;
          console.log('Using current user ID from localStorage:', currentUserId);
        }
      } catch (error) {
        console.error('Error getting current user ID from localStorage:', error);
      }
    }

    // Get authentication headers with explicit scopes for Microsoft Graph API
    console.log('Fetching user context - getting auth headers with required scopes');

    // Log authentication state
    console.log('Authentication state from localStorage:', localStorage.getItem('isAuthenticated'));
    console.log('Last token acquisition:', sessionStorage.getItem('lastTokenAcquisition'));
    console.log('Token scopes:', sessionStorage.getItem('tokenScopes'));

    const authHeaders = await getAuthHeaders(['User.Read', 'openid', 'offline_access']);

    console.log('Calling /api/user-context/me with auth headers');
    const response = await fetch('/api/user-context/me', {
      headers: {
        // Only include X-Current-User-ID if we have one and no MSAL account
        ...(currentUserId && !hasMsalAccount ? { 'X-Current-User-ID': currentUserId } : {}),
        ...authHeaders, // Add authentication headers
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch user context:', response.status, response.statusText);
      return null;
    }

    // Log the raw response text for debugging
    const responseText = await response.clone().text();
    console.log('Raw response from /api/user-context/me:', responseText);

    let data;
    try {
      data = JSON.parse(responseText);
    } catch (e) {
      console.error('Failed to parse JSON from /api/user-context/me:', e);
      console.error('Response text that failed parsing:', responseText);
      return null; // Return null if JSON parsing fails
    }

    // Basic validation of the received data structure
    if (!data || !data.user || !data.permissions || !data.accessibleResources) {
        console.error('Fetched user context is missing essential fields (user, permissions, or accessibleResources). Data:', data);
        // Optionally, clear cache if data is bad to avoid serving stale/bad data
        // clearUserContextCache();
        return null;
    }

    userContextCache = data;
    lastFetchTime = now;

    // Store user in localStorage for other parts of the app
    console.log('Storing user in localStorage with role:', data.user.role);
    localStorage.setItem('currentUser', JSON.stringify(data.user));

    // Log the user context data for debugging
    console.log('User context data from backend:', {
      userId: data.user.id,
      userName: data.user.name,
      userEmail: data.user.email,
      userRole: data.user.role,
      userRegion: data.user.region,
      permissions: data.permissions
    });

    return data;
  } catch (error) {
    console.error('Error fetching user context:', error);
    return null;
  }
}

/**
 * Fetch project-specific permissions for the current user
 */
export async function fetchProjectPermissions(projectId: string, forceRefresh = false): Promise<ProjectPermissionsData | null> {
  const now = Date.now();

  // Return cached data if available and not expired
  if (!forceRefresh && projectPermissionsCache[projectId] && (now - lastFetchTime < CACHE_EXPIRY)) {
    return projectPermissionsCache[projectId];
  }

  try {
    console.log(`Fetching project permissions for project ${projectId}`);

    // Get the current user ID from localStorage
    let currentUserId = '1'; // Default to Super Admin
    try {
      const storedUser = localStorage.getItem('currentUser');
      if (storedUser) {
        const parsedUser = JSON.parse(storedUser);
        currentUserId = parsedUser.id;
      }
    } catch (error) {
      console.error('Error getting current user ID from localStorage:', error);
    }

    // Get authentication headers with explicit scopes for Microsoft Graph API
    console.log(`Fetching project permissions for ${projectId} - getting auth headers with required scopes`);
    const authHeaders = await getAuthHeaders(['User.Read', 'openid', 'offline_access']);

    console.log(`Calling /api/user-context/project/${projectId}/permissions with auth headers`);
    const response = await fetch(`/api/user-context/project/${projectId}/permissions`, {
      headers: {
        'X-Current-User-ID': currentUserId,
        ...authHeaders, // Add authentication headers
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch project permissions:', response.status, response.statusText);
      return null;
    }

    const data = await response.json();
    projectPermissionsCache[projectId] = data;

    return data;
  } catch (error) {
    console.error('Error fetching project permissions:', error);
    return null;
  }
}

/**
 * Clear the user context cache
 */
export function clearUserContextCache(): void {
  userContextCache = null;
  projectPermissionsCache = {};
  lastFetchTime = 0;
}

// Export the service as a default object
const userContextService = {
  fetchUserContext,
  fetchProjectPermissions,
  clearCache: clearUserContextCache
};

export default userContextService;

/**
 * Authentication Header Service
 *
 * This service provides utilities for adding authentication headers to API requests.
 * It handles token acquisition, caching, and refresh.
 */

import { getAccessToken, graphScopes, msalInstance, msalReady } from '../auth/msal-config';

// Cache for access tokens to avoid unnecessary requests
interface TokenCache {
  [key: string]: {
    token: string;
    expiresAt: number;
  };
}

const tokenCache: TokenCache = {};
const TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Get authentication headers for API requests
 * @param customScopes Optional custom scopes to request
 * @returns Headers object with Authorization header
 */
export async function getAuthHeaders(customScopes?: string[]): Promise<HeadersInit> {
  console.log('Getting auth headers with scopes:', customScopes || 'default scopes');

  // Check if we're in production with Azure App Service Easy Auth
  const isProduction = window.location.hostname !== 'localhost';

  if (isProduction) {
    console.log('Production environment: Using Azure App Service Easy Auth - no explicit Authorization header needed');
    // In production with Easy Auth, the App Service automatically handles authentication
    // We don't need to add Authorization headers as Easy Auth handles this
    return {
      'Content-Type': 'application/json'
    };
  }

  // For development, use MSAL.js as before
  const scopesWithUserRead = customScopes ?
    [...new Set([...customScopes, 'User.Read', 'openid', 'offline_access'])] :
    ['User.Read', 'openid', 'offline_access']; // Always include these essential scopes

  console.log('Getting auth token with scopes:', scopesWithUserRead);
  const token = await getAuthToken(scopesWithUserRead);

  if (!token) {
    console.warn('No authentication token available');

    // Log authentication state for debugging
    console.log('Authentication state from localStorage:', localStorage.getItem('isAuthenticated'));
    console.log('MSAL accounts:', msalInstance.getAllAccounts().length);

    // Try to get a token one more time with interactive login if needed
    try {
      const accounts = msalInstance.getAllAccounts();
      if (accounts.length > 0) {
        console.log('Attempting to acquire token via popup as a last resort');
        const popupToken = await msalInstance.acquireTokenPopup({
          scopes: ['User.Read', 'openid', 'offline_access'],
          account: accounts[0]
        });

        if (popupToken && popupToken.accessToken) {
          console.log('Successfully acquired token via popup as last resort');
          return {
            'Authorization': `Bearer ${popupToken.accessToken}`,
            'Content-Type': 'application/json'
          };
        }
      }
    } catch (popupError) {
      console.error('Error acquiring token via popup as last resort:', popupError);
    }

    return {
      'Content-Type': 'application/json'
    };
  }

  // Log token details for debugging
  console.log(`Adding Authorization header with token: ${token.substring(0, 10)}...${token.substring(token.length - 10)}`);

  try {
    // Log token payload for debugging (without verification)
    const tokenParts = token.split('.');
    if (tokenParts.length === 3) {
      const payload = JSON.parse(atob(tokenParts[1]));
      console.log('Token scopes:', payload.scp || 'No scopes found in token');
      console.log('Token audience:', payload.aud || 'No audience found in token');
      console.log('Token expires:', new Date((payload.exp || 0) * 1000).toISOString());
    }
  } catch (e) {
    console.warn('Could not decode token for debugging:', e);
  }

  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };
}

/**
 * Get authentication token for API requests
 * @param customScopes Optional custom scopes to request
 * @returns Authentication token or null if not available
 */
export async function getAuthToken(customScopes?: string[]): Promise<string | null> {
  // Wait for MSAL to be fully initialized
  try {
    await msalReady;
  } catch (error) {
    console.error('MSAL initialization failed:', error);
    return null;
  }

  // Use specific scopes only (not .default) as per Azure Portal configuration
  // Start with the provided scopes or default to graphScopes
  let scopes = customScopes || [...graphScopes];

  // Ensure we have all the essential scopes using Set to avoid duplicates
  const essentialScopes = ['User.Read', 'openid', 'offline_access', 'profile', 'email'];
  scopes = [...new Set([...scopes, ...essentialScopes])];

  // Remove any .default scopes as they cannot be combined with resource-specific scopes
  scopes = scopes.filter(scope => !scope.endsWith('.default'));

  console.log('Final scopes for token acquisition:', scopes);

  const cacheKey = scopes.sort().join(',');

  // Check cache first
  const now = Date.now();
  const cachedToken = tokenCache[cacheKey];

  if (cachedToken && cachedToken.expiresAt > now + TOKEN_EXPIRY_BUFFER) {
    console.log('Using cached token');
    return cachedToken.token;
  }

  // Get fresh token
  try {
    console.log('Getting fresh token with scopes:', scopes);

    // Check if we have accounts
    const accounts = msalInstance.getAllAccounts();
    if (accounts.length === 0) {
      console.warn('No accounts found in MSAL');

      // Try to initiate login if no accounts are found
      try {
        console.log('No accounts found, attempting to redirect to login page');
        window.location.href = '/#/login';
      } catch (loginError) {
        console.error('Error redirecting to login:', loginError);
      }

      return null;
    }

    console.log('Using account:', accounts[0].username);

    // Try silent token acquisition first
    try {
      const silentRequest = {
        scopes: scopes,
        account: accounts[0],
        forceRefresh: false
      };

      const response = await msalInstance.acquireTokenSilent(silentRequest);
      console.log('Successfully acquired token silently');

      // Cache the token
      // Calculate expiration time - default to 1 hour if expiresOn is not available
      let expiresInMs = 60 * 60 * 1000; // Default: 1 hour in milliseconds

      if (response.expiresOn) {
        console.log('Token expiresOn value:', response.expiresOn, 'type:', typeof response.expiresOn);

        // Convert expiresOn to a number if it's not already
        const expiresOnValue = typeof response.expiresOn === 'number'
          ? response.expiresOn
          : typeof response.expiresOn === 'string'
            ? parseInt(response.expiresOn, 10)
            : null;

        console.log('Converted expiresOnValue:', expiresOnValue);

        if (expiresOnValue !== null) {
          // If expiresOn is in seconds (Unix timestamp), convert to milliseconds
          expiresInMs = expiresOnValue * 1000 - now;

          console.log('Calculated expiresInMs:', expiresInMs,
                      'which is', Math.round(expiresInMs / 1000 / 60), 'minutes from now');

          // Sanity check - if calculated value is negative or too large, use default
          if (expiresInMs <= 0 || expiresInMs > 24 * 60 * 60 * 1000) {
            console.warn('Invalid token expiration time, using default');
            expiresInMs = 60 * 60 * 1000;
          }
        }
      }

      tokenCache[cacheKey] = {
        token: response.accessToken,
        expiresAt: now + expiresInMs
      };

      return response.accessToken;
    } catch (silentError) {
      console.error('Error acquiring token silently:', silentError);

      // If silent acquisition fails, try popup
      console.log('Attempting to acquire token via popup');
      try {
        const popupRequest = {
          scopes: scopes,
          account: accounts[0]
        };

        const response = await msalInstance.acquireTokenPopup(popupRequest);
        console.log('Successfully acquired token via popup');

        // Cache the token
        // Calculate expiration time - default to 1 hour if expiresOn is not available
        let expiresInMs = 60 * 60 * 1000; // Default: 1 hour in milliseconds

        if (response.expiresOn) {
          console.log('Token expiresOn value:', response.expiresOn, 'type:', typeof response.expiresOn);

          // Convert expiresOn to a number if it's not already
          const expiresOnValue = typeof response.expiresOn === 'number'
            ? response.expiresOn
            : typeof response.expiresOn === 'string'
              ? parseInt(response.expiresOn, 10)
              : null;

          console.log('Converted expiresOnValue:', expiresOnValue);

          if (expiresOnValue !== null) {
            // If expiresOn is in seconds (Unix timestamp), convert to milliseconds
            expiresInMs = expiresOnValue * 1000 - now;

            console.log('Calculated expiresInMs:', expiresInMs,
                        'which is', Math.round(expiresInMs / 1000 / 60), 'minutes from now');

            // Sanity check - if calculated value is negative or too large, use default
            if (expiresInMs <= 0 || expiresInMs > 24 * 60 * 60 * 1000) {
              console.warn('Invalid token expiration time, using default');
              expiresInMs = 60 * 60 * 1000;
            }
          }
        }

        tokenCache[cacheKey] = {
          token: response.accessToken,
          expiresAt: now + expiresInMs
        };

        return response.accessToken;
      } catch (popupError) {
        console.error('Error acquiring token via popup:', popupError);
        return null;
      }
    }
  } catch (error) {
    console.error('Error getting authentication token:', error);
    return null;
  }
}

/**
 * Clear the token cache
 */
export function clearTokenCache(): void {
  Object.keys(tokenCache).forEach(key => {
    delete tokenCache[key];
  });
}

/**
 * Create a fetch function with authentication headers
 * @returns Authenticated fetch function
 */
export function createAuthenticatedFetch() {
  return async (url: string, options: RequestInit = {}): Promise<Response> => {
    try {
      const authHeaders = await getAuthHeaders();

      const mergedOptions: RequestInit = {
        ...options,
        headers: {
          ...options.headers,
          ...authHeaders
        }
      };

      return fetch(url, mergedOptions);
    } catch (error) {
      console.error('Error in authenticated fetch:', error);
      throw error;
    }
  };
}

// Export a pre-configured authenticated fetch function
export const authenticatedFetch = createAuthenticatedFetch();

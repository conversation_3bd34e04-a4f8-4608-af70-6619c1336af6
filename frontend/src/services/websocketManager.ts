/**
 * WebSocket Manager Service
 *
 * This service provides centralized management of WebSocket connections
 * and ensures they are properly closed when navigating between pages.
 */

// Track all active WebSocket connections by project ID
const activeConnections: Record<string, WebSocket[]> = {};

// Track the current active project ID
let currentProjectId: string | null = null;

/**
 * Register a WebSocket connection for a specific project
 *
 * @param ws The WebSocket connection to register
 * @param projectId The project ID this WebSocket is associated with (string or null, not undefined)
 * @param type The type of WebSocket (blob, index, etc.)
 */
export const registerWebSocket = (
  ws: WebSocket,
  projectId: string | null,
  type: string
): void => {
  // Ensure projectId is not undefined
  const safeProjectId = projectId || null;

  // Generate a unique key for this project
  const key = safeProjectId || 'global';

  // Initialize the array if it doesn't exist
  if (!activeConnections[key]) {
    activeConnections[key] = [];
  }

  // Add the WebSocket to the array
  activeConnections[key].push(ws);

  console.log(`WebSocket registered: ${type} for project ${key}`);
  console.log(`Total connections for project ${key}: ${activeConnections[key].length}`);
};

/**
 * Unregister a WebSocket connection
 *
 * @param ws The WebSocket connection to unregister
 * @param projectId The project ID this WebSocket is associated with (string or null, not undefined)
 */
export const unregisterWebSocket = (
  ws: WebSocket,
  projectId: string | null
): void => {
  // Ensure projectId is not undefined
  const safeProjectId = projectId || null;

  const key = safeProjectId || 'global';

  // If there are no connections for this project, return
  if (!activeConnections[key]) {
    return;
  }

  // Find the index of this WebSocket in the array
  const index = activeConnections[key].indexOf(ws);

  // If found, remove it
  if (index !== -1) {
    activeConnections[key].splice(index, 1);
    console.log(`WebSocket unregistered for project ${key}`);
    console.log(`Remaining connections for project ${key}: ${activeConnections[key].length}`);
  }

  // If there are no more connections for this project, delete the key
  if (activeConnections[key].length === 0) {
    delete activeConnections[key];
    console.log(`No more connections for project ${key}, removed from registry`);
  }
};

/**
 * Close all WebSocket connections for a specific project
 *
 * @param projectId The project ID to close connections for, or null to close all (not undefined)
 */
export const closeProjectWebSockets = (projectId: string | null): void => {
  // Ensure projectId is not undefined
  const safeProjectId = projectId || null;

  const key = safeProjectId || 'global';

  console.log(`Closing all WebSockets for project ${key}`);

  // If there are no connections for this project, return
  if (!activeConnections[key]) {
    console.log(`No connections found for project ${key}`);
    return;
  }

  // Close each WebSocket
  const connections = [...activeConnections[key]]; // Create a copy to avoid modification during iteration
  let closedCount = 0;

  connections.forEach(ws => {
    if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
      try {
        console.log(`Closing WebSocket connection for project ${key}`);
        ws.close();
        closedCount++;
      } catch (error) {
        console.error(`Error closing WebSocket for project ${key}:`, error);
      }
    }
  });

  // Clear the array
  delete activeConnections[key];

  console.log(`Closed ${closedCount} WebSocket connections for project ${key}`);
};

/**
 * Close all WebSocket connections
 */
export const closeAllWebSockets = (): void => {
  console.log('Closing all WebSocket connections');

  // Get all project keys
  const projectKeys = Object.keys(activeConnections);

  // Close connections for each project
  projectKeys.forEach(key => {
    closeProjectWebSockets(key === 'global' ? null : key);
  });
};

/**
 * Set the current active project ID
 *
 * @param projectId The current project ID, or null if not on a project page
 */
export const setCurrentProject = (projectId: string | null): void => {
  // If the project ID is changing, close connections for the previous project
  if (currentProjectId !== projectId && currentProjectId) {
    console.log(`Project changed from ${currentProjectId} to ${projectId}, closing old connections`);
    closeProjectWebSockets(currentProjectId);
  }

  currentProjectId = projectId;
};

/**
 * Get the current active project ID
 *
 * @returns The current project ID, or null if not on a project page
 */
export const getCurrentProject = (): string | null => {
  return currentProjectId;
};

/**
 * Check if WebSockets should be enabled for a specific project
 *
 * @param projectId The project ID to check (string or null, not undefined)
 * @returns True if WebSockets should be enabled, false otherwise
 */
export const shouldEnableWebSockets = (projectId: string | null): boolean => {
  // Ensure projectId is not undefined
  const safeProjectId = projectId || null;

  // Only enable WebSockets if we're on a project page and the project ID matches
  return currentProjectId !== null && currentProjectId === safeProjectId;
};

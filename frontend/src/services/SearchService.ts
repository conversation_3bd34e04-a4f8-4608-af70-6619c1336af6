export class ProjectSearchService {
  private static instance: ProjectSearchService;
  
  private constructor() {}

  public static getInstance(): ProjectSearchService {
    if (!ProjectSearchService.instance) {
      ProjectSearchService.instance = new ProjectSearchService();
    }
    return ProjectSearchService.instance;
  }

  async search(projectId: string, query: string): Promise<SearchResult[]> {
    const response = await fetch(`/api/projects/${projectId}/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      },
      body: JSON.stringify({
        searchText: query,
        facets: [],
        top: 50
      })
    });

    if (!response.ok) {
      throw new Error(`Search failed: ${response.statusText}`);
    }

    return this._formatResults(await response.json());
  }

  private _formatResults(rawResults: any[]): SearchResult[] {
    return rawResults.map(result => ({
      id: result.id,
      content: result.content,
      source: result.sourcefile,
      score: result['@search.score'],
      highlights: result['@search.highlights']
    }));
  }
}

export interface SearchResult {
  id: string;
  content: string;
  source: string;
  score: number;
  highlights?: {
    content?: string[];
  };
}

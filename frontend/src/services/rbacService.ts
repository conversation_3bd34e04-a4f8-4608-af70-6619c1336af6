// frontend/src/services/rbacService.ts
import { UserRole } from '../models/roles';
import { getAuthHeaders } from './authHeaderService';

// API response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
}

// Cache configuration
interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresIn: number; // milliseconds
}

const cache: Record<string, CacheItem<any>> = {};
const DEFAULT_CACHE_TIME = 10000; // 10 seconds

// Cache helper functions
function getCachedItem<T>(cacheKey: string): T | null {
  const item = cache[cacheKey];
  if (!item) return null;

  const now = Date.now();
  if (now - item.timestamp > item.expiresIn) {
    // Cache expired
    delete cache[cacheKey];
    return null;
  }

  return item.data;
}

function setCachedItem<T>(cacheKey: string, data: T, expiresIn: number = DEFAULT_CACHE_TIME): void {
  cache[cacheKey] = {
    data,
    timestamp: Date.now(),
    expiresIn
  };
}

function clearCache(): void {
  Object.keys(cache).forEach(key => delete cache[key]);
}

// User types
export interface UserData {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  region?: string;
  avatar?: string;
  created_at: string;
  updated_at: string;
}

// Region types
export interface RegionData {
  id: string;
  name: string;
  description?: string;
  cost_limit?: number;
  created_by: string;
  created_at: string;
  updated_at: string;
}

// Team types
export interface TeamData {
  id: string;
  name: string;
  description?: string;
  region: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

// Project types
export interface ProjectData {
  id: string;
  name: string;
  description: string;
  region: string;
  owner: string;
  storage_container_uploads: string;
  storage_container_input: string;
  storage_container_output: string;
  search_index_name: string;
  search_datasource_name: string;
  cost_limit?: number;
  environment?: Record<string, string>;
  icon?: string;
  color?: string;
  created_at: string;
  updated_at: string;
}

// Team membership types
export interface TeamMemberData {
  id: string;
  teamId: string;
  userId: string;
  role: string;
  created_by: string;
  created_at: string;
}

// Project team assignment types
export interface ProjectTeamData {
  id: string;
  projectId: string;
  teamId: string;
  created_by: string;
  created_at: string;
}

// Project user assignment types
export interface ProjectUserData {
  id: string;
  projectId: string;
  userId: string;
  role: string;
  created_by: string;
  created_at: string;
}

// Base API call function
async function apiCall<T>(
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  data?: any,
  useCache: boolean = true,
  cacheTime: number = DEFAULT_CACHE_TIME
): Promise<ApiResponse<T>> {
  // Generate a cache key based on the endpoint, method, and data
  const cacheKey = `${method}:${endpoint}:${data ? JSON.stringify(data) : ''}`;

  // For GET requests, check the cache first
  if (method === 'GET' && useCache) {
    const cachedData = getCachedItem<T>(cacheKey);
    if (cachedData !== null) {
      return { data: cachedData };
    }
  }

  try {
    console.log(`Making ${method} request to /api/rbac${endpoint}`);
    if (data) {
      console.log('Request data:', data);
    }

    // Get the current user ID from localStorage
    let currentUserId = '1'; // Default to Super Admin
    try {
      const storedUser = localStorage.getItem('currentUser');
      if (storedUser) {
        const parsedUser = JSON.parse(storedUser);
        currentUserId = parsedUser.id;
        console.log('Using current user ID from localStorage:', currentUserId);
      }
    } catch (error) {
      console.error('Error getting current user ID from localStorage:', error);
    }

    // Get authentication headers
    const authHeaders = await getAuthHeaders();

    const response = await fetch(`/api/rbac${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'X-Current-User-ID': currentUserId, // Add the current user ID to the headers
        ...authHeaders, // Add authentication headers
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    console.log(`Response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorData = await response.json();
      console.error('API error response:', errorData);
      return { error: errorData.detail || 'An error occurred' };
    }

    const responseData = await response.json();
    console.log('API response data:', responseData);

    // Cache the response for GET requests
    if (method === 'GET' && useCache) {
      setCachedItem<T>(cacheKey, responseData, cacheTime);
    } else if (method !== 'GET') {
      // Clear cache for non-GET requests as they might modify data
      clearCache();
    }

    return { data: responseData };
  } catch (error) {
    console.error('API call error:', error);
    return { error: 'Network error' };
  }
}

// User Management API
export const userApi = {
  getUsers: () => apiCall<UserData[]>('/users'),
  getUser: (userId: string) => apiCall<UserData>(`/users/${userId}`),
  createUser: (userData: Partial<UserData>) => apiCall<UserData>('/users', 'POST', userData),
  updateUser: (userId: string, userData: Partial<UserData>) =>
    apiCall<UserData>(`/users/${userId}`, 'PUT', userData),
  deleteUser: (userId: string) => {
    console.log('Calling deleteUser API with userId:', userId);
    return apiCall<void>(`/users/${userId}`, 'DELETE');
  },
  changeUserRole: (userId: string, role: UserRole) =>
    apiCall<UserData>(`/users/${userId}/role`, 'PUT', { role }),
};

// Region Management API
export const regionApi = {
  getRegions: () => apiCall<RegionData[]>('/regions'),
  getRegion: (regionId: string) => apiCall<RegionData>(`/regions/${regionId}`),
  createRegion: (regionData: Partial<RegionData>) =>
    apiCall<RegionData>('/regions', 'POST', regionData),
  updateRegion: (regionId: string, regionData: Partial<RegionData>) =>
    apiCall<RegionData>(`/regions/${regionId}`, 'PUT', regionData),
  deleteRegion: (regionId: string) => apiCall<void>(`/regions/${regionId}`, 'DELETE'),
};

// Team Management API
export const teamApi = {
  getTeams: () => apiCall<TeamData[]>('/teams'),
  getTeam: (teamId: string) => apiCall<TeamData>(`/teams/${teamId}`),
  createTeam: (teamData: Partial<TeamData>) => apiCall<TeamData>('/teams', 'POST', teamData),
  updateTeam: (teamId: string, teamData: Partial<TeamData>) =>
    apiCall<TeamData>(`/teams/${teamId}`, 'PUT', teamData),
  deleteTeam: (teamId: string) => apiCall<void>(`/teams/${teamId}`, 'DELETE'),

  // Team membership
  getTeamMembers: (teamId: string) => apiCall<TeamMemberData[]>(`/teams/${teamId}/members`),
  addTeamMember: (teamId: string, memberData: { userId: string, role: string }) =>
    apiCall<TeamMemberData>(`/teams/${teamId}/members`, 'POST', memberData),
  removeTeamMember: (teamId: string, userId: string) =>
    apiCall<void>(`/teams/${teamId}/members/${userId}`, 'DELETE'),
};

// Project Management API
export const projectApi = {
  getProjects: () => apiCall<ProjectData[]>('/projects'),
  getProject: (projectId: string) => apiCall<ProjectData>(`/projects/${projectId}`),
  createProject: (projectData: Partial<ProjectData>) =>
    apiCall<ProjectData>('/projects', 'POST', projectData),
  updateProject: (projectId: string, projectData: Partial<ProjectData>) =>
    apiCall<ProjectData>(`/projects/${projectId}`, 'PUT', projectData),
  deleteProject: (projectId: string) => apiCall<void>(`/projects/${projectId}`, 'DELETE'),

  // Project team assignments
  getProjectTeams: (projectId: string) =>
    apiCall<ProjectTeamData[]>(`/projects/${projectId}/teams`),
  assignTeamToProject: (projectId: string, teamData: { teamId: string }) =>
    apiCall<ProjectTeamData>(`/projects/${projectId}/teams`, 'POST', teamData),
  removeTeamFromProject: (projectId: string, teamId: string) =>
    apiCall<void>(`/projects/${projectId}/teams/${teamId}`, 'DELETE'),

  // Project user assignments
  getProjectUsers: (projectId: string) =>
    apiCall<ProjectUserData[]>(`/projects/${projectId}/users`),
  assignUserToProject: (projectId: string, userData: { userId: string, role: string }) =>
    apiCall<ProjectUserData>(`/projects/${projectId}/users`, 'POST', userData),
  removeUserFromProject: (projectId: string, userId: string) =>
    apiCall<void>(`/projects/${projectId}/users/${userId}`, 'DELETE'),
};

// Cache management functions for export
function invalidateCache(pattern: string): void {
  Object.keys(cache).forEach(key => {
    if (key.includes(pattern)) {
      delete cache[key];
    }
  });
}

// Export all APIs as a single object
const rbacService = {
  users: userApi,
  regions: regionApi,
  teams: teamApi,
  projects: projectApi,
  cache: {
    clear: clearCache,
    invalidate: invalidateCache
  }
};

export default rbacService;

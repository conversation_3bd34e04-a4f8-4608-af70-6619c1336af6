import { useContext } from 'react';
import { ProjectContext } from '../pages/layout/ProjectLayout';

/**
 * Hook to access project environment variables and information
 * @returns Project context containing projectId, projectName, and environment variables
 */
export const useProjectEnv = () => {
  const context = useContext(ProjectContext);
  
  if (!context) {
    throw new Error('useProjectEnv must be used within a ProjectLayout');
  }
  
  return context;
};

/**
 * Get a specific environment variable for the current project
 * @param key The environment variable key
 * @param defaultValue Optional default value if the key doesn't exist
 * @returns The environment variable value or default value
 */
export const useProjectEnvVar = (key: string, defaultValue: string = '') => {
  const { projectEnv } = useProjectEnv();
  return projectEnv[key] || defaultValue;
};

export default useProjectEnv; 
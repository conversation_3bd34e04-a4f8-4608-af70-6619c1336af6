import { useState, useEffect } from 'react';
import userContextService, { ProjectPermissionsData } from '../services/userContextService';
import { ProjectPermissions } from '../models/roles';

/**
 * Hook to get project-specific permissions for the current user
 * @param projectId The ID of the project to get permissions for
 * @returns Object containing project permissions and loading state
 */
export const useProjectPermissions = (projectId: string) => {
  const [permissions, setPermissions] = useState<ProjectPermissionsData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPermissions = async () => {
      if (!projectId) {
        setError('Project ID is required');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const projectPermissions = await userContextService.fetchProjectPermissions(projectId);

        if (!projectPermissions) {
          setError('Failed to fetch project permissions');
          return;
        }

        setPermissions(projectPermissions);
      } catch (err) {
        console.error('Error fetching project permissions:', err);
        setError('Error fetching project permissions');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPermissions();
  }, [projectId]);

  /**
   * Check if the user has a specific project permission
   * @param permissionKey The permission key to check
   * @returns Boolean indicating if the user has the permission
   */
  const hasPermission = (permissionKey: keyof ProjectPermissions) => {
    if (!permissions) return false;
    return permissions.permissions[permissionKey] === true;
  };

  /**
   * Refresh the project permissions
   */
  const refreshPermissions = async () => {
    setIsLoading(true);
    try {
      const projectPermissions = await userContextService.fetchProjectPermissions(projectId, true);
      if (projectPermissions) {
        setPermissions(projectPermissions);
      }
    } catch (err) {
      console.error('Error refreshing project permissions:', err);
      setError('Error refreshing project permissions');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    permissions,
    isLoading,
    error,
    hasPermission,
    refreshPermissions,
    projectRole: permissions?.permissions.projectRole
  };
};

export default useProjectPermissions;

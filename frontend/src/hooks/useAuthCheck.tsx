import { useEffect, useState, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { msalInstance } from '../auth/msal-config';
import { useMsal } from '@azure/msal-react';

/**
 * Hook to check authentication status and redirect if needed
 * @param redirectPath Path to redirect to if not authenticated
 * @returns Object containing authentication status
 */
export const useAuthCheck = (redirectPath: string = '/login') => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState<boolean>(true);
  const navigate = useNavigate();
  const location = useLocation();
  const hasRedirected = useRef(false);
  const { accounts, inProgress } = useMsal();

  // Prevent navigation loops by tracking if we've already redirected
  const isLoginPage = location.pathname === '/login';

  useEffect(() => {
    console.log('useAuthCheck - Current location:', location.pathname);
    console.log('useAuthCheck - MSAL accounts:', accounts);
    console.log('useAuthCheck - MSAL inProgress:', inProgress);

    const checkAuth = async () => {
      // Skip authentication check if we're already on the login page
      if (isLoginPage) {
        setIsAuthenticated(false);
        setIsCheckingAuth(false);
        return;
      }

      try {
        setIsCheckingAuth(true);

        // First check MSAL accounts from the hook
        if (accounts.length > 0) {
          console.log('useAuthCheck - User is authenticated with MSAL accounts');
          setIsAuthenticated(true);
          setIsCheckingAuth(false);
          return;
        }

        // Then check if there's an active account in MSAL directly
        const activeAccount = msalInstance.getActiveAccount();
        const msalAccounts = msalInstance.getAllAccounts();

        // If there's at least one account, consider the user authenticated
        if (activeAccount !== null || msalAccounts.length > 0) {
          console.log('useAuthCheck - User is authenticated with MSAL instance');
          setIsAuthenticated(true);
          setIsCheckingAuth(false);
          return;
        }

        // Check localStorage for development mode authentication
        const storedAuthState = localStorage.getItem('isAuthenticated');
        if (storedAuthState === 'true') {
          console.log('useAuthCheck - User is authenticated in development mode');
          setIsAuthenticated(true);
          setIsCheckingAuth(false);
          return;
        }

        // If we get here, the user is not authenticated
        console.log('useAuthCheck - User is not authenticated');
        setIsAuthenticated(false);

        // If not authenticated and we haven't redirected yet, redirect once
        if (!hasRedirected.current) {
          console.log('useAuthCheck - Redirecting to login page');
          hasRedirected.current = true;
          navigate(redirectPath);
        }
      } catch (error) {
        console.error('Authentication check failed:', error);
        setIsAuthenticated(false);

        // Only redirect if we haven't already
        if (!hasRedirected.current) {
          hasRedirected.current = true;
          navigate(redirectPath);
        }
      } finally {
        setIsCheckingAuth(false);
      }
    };

    // Reset the redirect flag when the location changes
    hasRedirected.current = false;

    // Only check auth if MSAL is not in the middle of an operation
    if (inProgress === 'none') {
      checkAuth();
    }
  }, [navigate, redirectPath, isLoginPage, accounts, inProgress, location]);

  return { isAuthenticated, isCheckingAuth };
};

export default useAuthCheck;
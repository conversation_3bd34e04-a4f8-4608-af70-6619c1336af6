import { useState, useEffect, useRef, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import * as WebSocketManager from '../services/websocketManager';

// Define types for WebSocket messages
export interface WebSocketMessage<T> {
  type: string;
  data?: T;
  message?: string;
  timestamp: string;
}

export interface BlobItem {
  name: string;
  size: number;
  type: string;
  url: string;
  lastModified: string | null;
  indexed?: boolean;
}

export interface RightPanelData {
  input: BlobItem[];
  output: BlobItem[];
}

// Type for different WebSocket update types
export type WebSocketUpdateType = 'blob' | 'index' | 'right-panel' | 'input-container' | 'output-container';

// Interface for hook options
interface UseWebSocketUpdatesOptions {
  type: WebSocketUpdateType;
  onMessage?: (data: any) => void;
  autoReconnect?: boolean;
  reconnectInterval?: number;
  pingInterval?: number; // Time in ms between pings
  connectionTimeout?: number; // Time in ms to wait for connection
  maxRetries?: number; // Maximum number of reconnection attempts
  projectId?: string | null; // Add projectId as an optional parameter
}
function convertToLocalTime(timestamp: string): Date {
  const date = new Date(timestamp);
  // Explicitly adjust for timezone if needed
  // This is a fallback in case the browser isn't handling it correctly
  return new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
}

/**
 * Custom hook for WebSocket updates for file management
 */
export const useWebSocketUpdates = <T>(options: UseWebSocketUpdatesOptions) => {
  const {
    type,
    onMessage,
    autoReconnect = true,
    reconnectInterval = 3000,
    pingInterval = 30000, // 30 seconds between pings
    connectionTimeout = 15000, // 15 seconds timeout
    maxRetries = 50, // High number of retries
    projectId: rawProjectId // Destructure projectId from options
  } = options;

  // Ensure projectId is either a string or null, not undefined
  const projectId: string | null = rawProjectId || null;

  const [data, setData] = useState<T | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  // Get current location to detect route changes
  const location = useLocation();
  const isProjectPage = location.pathname.startsWith('/project/');

  // Extract project ID from URL if we're on a project page
  const urlProjectId = isProjectPage ? location.pathname.split('/')[2] : null;

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<number | null>(null);

  // Update the current project in the WebSocket manager when the URL changes
  useEffect(() => {
    WebSocketManager.setCurrentProject(urlProjectId);
  }, [urlProjectId]);

  // Determine WebSocket URL based on type
  const getWebSocketUrl = useCallback(() => {
    // Use the same protocol (ws/wss) as the current connection (http/https)
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';

    // Use the same host:port as the current connection
    // This is critical for port forwarding scenarios , happens when you use it in VS code
    const host = window.location.host;

    console.log(`Creating WebSocket URL with protocol=${protocol} and host=${host}`);

    // Try different endpoint path patterns to be robust
    let paths = [];

    // This should match the backend endpoints
    switch(type) {
      case 'blob':
        paths = [
          `/ws/blobs`,                  // Backend direct path - this is the actual endpoint
          `/ws/blob-updates`,           // Legacy path
          `/api/search/ws/blob-updates` // Alternate path , incase we change apis
        ];
        break;
      case 'index':
        paths = [
          `/ws/indexes`,                // Backend direct path - this is the actual endpoint
          `/ws/index-updates`,
          `/api/search/ws/index-updates`
        ];
        break;
      case 'input-container':
        paths = [
          `/ws/input-container`         // Backend direct path
        ];
        break;
      case 'output-container':
        paths = [
          `/ws/output-container`        // Backend direct path
        ];
        break;
      case 'right-panel':
        paths = [
          `/ws/right-panel-updates`,
          `/api/search/ws/right-panel-updates`
        ];
        break;
      default:
        throw new Error(`Unknown WebSocket type: ${type}`);
    }

    // Use the primary path by default
    return `${protocol}//${host}${paths[0]}`;
  }, [type]);

  // Keep track of which path we're trying
  const currentPathIndexRef = useRef(0);

  // Function to connect to WebSocket with retry on different paths
  const connect = useCallback(() => {
    // Check if we should enable WebSockets for this project
    const shouldEnable = WebSocketManager.shouldEnableWebSockets(projectId);

    // If we're not on a project page or the project ID doesn't match, don't connect
    if (!shouldEnable) {
      console.log(`WebSockets disabled for ${type} - not on project page or project ID mismatch`);
      return;
    }

    try {
      // Close existing connection if any
      if (wsRef.current) {
        wsRef.current.close();
      }

      // Use the paths defined in getWebSocketUrl
      let paths: string[] = [];
      switch(type) {
        case 'blob':
          // Backend implements only /ws/blobs in index_blob_status.py
          paths = ['/ws/blobs'];
          break;
        case 'index':
          // Backend implements only /ws/indexes in index_blob_status.py
          paths = ['/ws/indexes'];
          break;
        case 'input-container':
          // Backend implements only /ws/input-container in index_blob_status.py
          paths = ['/ws/input-container'];
          break;
        case 'output-container':
          // Backend implements only /ws/output-container in index_blob_status.py
          paths = ['/ws/output-container'];
          break;
        default:
          paths = ['/ws/test'];
      }

      // Get the protocol and host
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;

      // Pick the next path to try (cycling through them)
      const pathIndex = currentPathIndexRef.current % paths.length;
      const path = paths[pathIndex];
      currentPathIndexRef.current = (pathIndex + 1) % paths.length;

      // Append projectId as a query parameter if available
      const queryParams = projectId ? `?projectId=${encodeURIComponent(projectId)}` : '';
      const wsUrl = `${protocol}//${host}${path}${queryParams}`;
      console.log(`Attempt ${pathIndex+1}/${paths.length}: Connecting to WebSocket: ${wsUrl}`);

      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      // Register this WebSocket with our manager
      WebSocketManager.registerWebSocket(ws, projectId, type);

      // Store the project ID and type on the WebSocket instance for reference
      (ws as any).projectId = projectId;
      (ws as any).type = type;

      // Add a connection timeout with a longer duration
      const connectionTimeoutId = setTimeout(() => {
        if (ws.readyState !== WebSocket.OPEN) {
          console.warn(`Connection timeout for ${wsUrl} - closing and trying another path`);
          ws.close();
          // Unregister from WebSocket manager
          WebSocketManager.unregisterWebSocket(ws, projectId);
          // Try the next path
          connect();
        }
      }, connectionTimeout); // Configurable timeout - longer to help connections establish

      // Clear the timeout when connected
      ws.addEventListener('open', () => {
        clearTimeout(connectionTimeoutId);
        console.log(`WebSocket connected successfully to ${wsUrl}`);
      });

      ws.onopen = () => {
        console.log(`WebSocket connected: ${type}`);
        setIsConnected(true);
        setError(null);
      };

      ws.onmessage = (event) => {
        try {
          // Check if the message is a simple ping response
          if (event.data === "pong") {
            console.log(`Received pong from ${type} WebSocket`);
            return;
          }

          const message: WebSocketMessage<T> = JSON.parse(event.data);

          // Check for JSON pong message
          if (message.type === "pong") {
            console.log(`Received pong from ${type} WebSocket`);
            return;
          }

          console.log(`Received ${type} update:`, message.type);

          // Handle connection established message
          if (message.type === "connection_established") {
            console.log(`WebSocket connection confirmed: ${message.message || 'connected'}`);

            // Create a unique key for this interval
            const pingIntervalKey = `ping_${type}_${Date.now()}`;

            // Start sending pings to keep the connection alive
            // Use configurable ping interval (default: 30 seconds) to prevent timeouts
            // This is longer than before to reduce network traffic
            const pingIntervalId = setInterval(() => {
              if (ws && ws.readyState === WebSocket.OPEN) {
                console.log(`Sending ping to ${type} WebSocket...`);
                ws.send(JSON.stringify({
                  type: "ping",
                  timestamp: new Date().toISOString()
                }));
              } else {
                clearInterval(pingIntervalId);
                console.log(`Cleared ping interval ${pingIntervalKey}`);
              }
            }, pingInterval);

            // Clean up interval when connection closes
            ws.addEventListener("close", () => {
              clearInterval(pingIntervalId);
              console.log(`Cleared ping interval ${pingIntervalKey} on connection close`);
            });

            return;
          }

          // Update state based on message type
          if (message.type === 'connected') {
            console.log(`Connection established to ${type} endpoint`);
            return;
          }

          // Handle the data updates based on message types from backend
          // Log the message type to help with debugging
          console.log(`Received WebSocket message type: ${message.type} for connection type: ${type}`);

          if ((type === 'blob' && message.type === 'blob_update') ||
              (type === 'index' && message.type === 'index_update') ||
              (type === 'right-panel' && (message.type === 'input_container_update' || message.type === 'output_container_update')) ||
              (type === 'input-container' && message.type === 'input_container_update') ||
              (type === 'output-container' && message.type === 'output_container_update')) {

            if (message.data) {
              setData(message.data);
              const localTime = convertToLocalTime(message.timestamp);
              setLastUpdate(localTime);

              // Call onMessage callback if provided
              if (onMessage) {
                onMessage(message.data);
              }
            } else {
              console.warn(`Received ${message.type} message without data payload`);
            }
          }
        } catch (err) {
          console.error('Error parsing WebSocket message:', err);
        }
      };

      // Store retry count in a closure to track retries for this particular connection attempt
      let retryCount = 0;
      const maxRetriesForConnection = maxRetries;

      ws.onerror = (event) => {
        console.error(`WebSocket error: ${type}`, event);
        setError('WebSocket connection error');
        setIsConnected(false);

        // Error might not always trigger close, so we'll ensure connection is closed
        if (ws.readyState !== WebSocket.CLOSED && ws.readyState !== WebSocket.CLOSING) {
          ws.close();
        }
      };

      ws.onclose = (event) => {
        console.log(`WebSocket closed: ${type} with code ${event.code}, reason: ${event.reason || 'No reason provided'}`);
        setIsConnected(false);

        // Unregister from WebSocket manager
        WebSocketManager.unregisterWebSocket(ws, projectId);

        // Attempt to reconnect if autoReconnect is enabled
        if (autoReconnect && retryCount < maxRetriesForConnection) {
          // Exponential backoff with a max delay and jitter
          const baseDelay = Math.min(reconnectInterval * Math.pow(1.5, Math.min(retryCount, 10)), 30000);
          const jitter = Math.random() * 1000; // Add up to 1s of random jitter to prevent thundering herd
          const delay = baseDelay + jitter;

          console.log(`Reconnecting WebSocket (attempt ${retryCount + 1}/${maxRetriesForConnection}) in ${Math.round(delay)}ms...`);

          // Clear any existing reconnect timeout
          if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
          }

          // Schedule a reconnect
          reconnectTimeoutRef.current = window.setTimeout(() => {
            retryCount++;
            connect();
          }, delay);
        } else if (retryCount >= maxRetriesForConnection) {
          console.warn(`Maximum reconnection attempts (${maxRetriesForConnection}) reached for ${type} WebSocket. Giving up.`);
          setError(`Connection failed after ${maxRetriesForConnection} attempts. Try refreshing the page.`);
        }
      };
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setError(`Failed to connect: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [type, getWebSocketUrl, autoReconnect, reconnectInterval, onMessage]);

  // Connect on component mount and cleanup on unmount ==> More logic on connection
  useEffect(() => {
    // Initial connection
    connect();

    // Setup periodic connection check and health monitoring
    // This helps ensure the connection stays alive even if the ping/pong fails
    const connectionHealthCheck = setInterval(() => {
      const ws = wsRef.current;

      // If we think we're connected but WebSocket is actually closed, reconnect
      if (isConnected && (!ws || ws.readyState !== WebSocket.OPEN)) {
        console.log(`Health check detected broken connection for ${type} WebSocket, reconnecting...`);
        setIsConnected(false);
        connect();
      }
    }, 45000); // Check every 45 seconds

    // Add a window event listener for page visibility changes
    // This helps reconnect when the page becomes visible again after being in background
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // If the WebSocket is closed but we're now visible, try to reconnect
        const ws = wsRef.current;
        if (!ws || ws.readyState === WebSocket.CLOSED || ws.readyState === WebSocket.CLOSING) {
          console.log(`Page became visible, reconnecting ${type} WebSocket...`);
          connect();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Also reconnect when the page is focused after being unfocused
    const handleFocus = () => {
      const ws = wsRef.current;
      if (!ws || ws.readyState === WebSocket.CLOSED || ws.readyState === WebSocket.CLOSING) {
        console.log(`Window regained focus, reconnecting ${type} WebSocket...`);
        connect();
      }
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      // Clean up WebSocket connection and timeout
      if (wsRef.current) {
        console.log(`Closing WebSocket connection for ${type} on component unmount`);
        wsRef.current.close();

        // Unregister from WebSocket manager
        WebSocketManager.unregisterWebSocket(wsRef.current, projectId);
      }

      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }

      // Clear health check interval
      clearInterval(connectionHealthCheck);

      // Remove event listeners
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [connect, type]);

  // Function to manually reconnect
  const reconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    connect();
  }, [connect]);

  return {
    data,
    isConnected,
    error,
    lastUpdate,
    reconnect,
    // Expose the WebSocket reference so components can send commands
    wsRef,
    // Add connection stats for debugging/monitoring
    connectionStats: {
      type,
      wsUrl: wsRef.current ? wsRef.current.url : null,
      wsReadyState: wsRef.current ? wsRef.current.readyState : -1,
      lastError: error,
      pingInterval,
      maxRetries
    }
  };
};

// Types for the different WebSocket data
export type BlobUpdatesData = BlobItem[];
export type IndexUpdatesData = string[];
export type RightPanelUpdatesData = RightPanelData | BlobItem[];

// Type guard to check if data is from input or output container
export const isInputContainerData = (data: any): data is BlobItem[] =>
  Array.isArray(data) && data.length > 0 && typeof data[0] === 'object';

export const isOutputContainerData = (data: any): data is BlobItem[] =>
  Array.isArray(data) && data.length > 0 && typeof data[0] === 'object';

/**
 * Utility function to close all WebSocket connections for a specific project
 * @param projectId The ID of the project to close connections for, or null to close all connections
 */
export const closeAllWebSockets = (projectId: string | null = null): void => {
  // Use the WebSocket manager to close connections
  if (projectId) {
    WebSocketManager.closeProjectWebSockets(projectId);
  } else {
    WebSocketManager.closeAllWebSockets();
  }
};

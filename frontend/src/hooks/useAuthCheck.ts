import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Hook to check if user is authenticated
 * @param redirectPath Path to redirect to if not authenticated
 * @returns Object with authentication state
 */
const useAuthCheck = (redirectPath: string = '/') => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isCheckingAuth, setIsCheckingAuth] = useState<boolean>(true);
  const navigate = useNavigate();

  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true);
      try {
        // First check local storage for demo authentication
        const storedAuthState = localStorage.getItem('isAuthenticated');
        
        if (storedAuthState === 'true') {
          setIsAuthenticated(true);
          setIsCheckingAuth(false);
          return;
        }

        // In a real app, you'd check with your authentication service here
        // For example:
        // const response = await fetch('/api/auth/check', {
        //   credentials: 'include',
        // });
        // if (response.ok) {
        //   setIsAuthenticated(true);
        // } else {
        //   setIsAuthenticated(false);
        //   navigate(redirectPath);
        // }

        // For demo purposes, redirect to login if not found in localStorage
        setIsAuthenticated(false);
        navigate(redirectPath);
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
        navigate(redirectPath);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuth();
  }, [navigate, redirectPath]);

  return { isAuthenticated, isCheckingAuth };
};

export default useAuthCheck; 
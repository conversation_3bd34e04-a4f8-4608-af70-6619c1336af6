import React from 'react';
import { BrowserRouter as Router, Routes, Route, HashRouter, Navigate } from 'react-router-dom';
import Login from './pages/Login';
import ProjectSelector from './pages/projects/ProjectSelector';
import NewProject from './pages/projects/NewProject';
import Layout from './components/Layout';
import PageNotFound from './components/PageNotFound';

function App() {
  return (
    <HashRouter>
      <Routes>
        {/* Redirect root to projects */}
        <Route path="/" element={<Navigate to="/projects" />} />
        <Route path="/login" element={<Login />} />
        <Route path="/projects" element={<ProjectSelector />} />
        <Route path="/new-project" element={<NewProject />} />

        {/* Project route - Load Layout component with project ID */}
        <Route path="/project/:projectId" element={<Layout />} />

        {/* Fallback route */}
        <Route path="*" element={<PageNotFound />} />
      </Routes>
    </HashRouter>
  );
}

export default App;
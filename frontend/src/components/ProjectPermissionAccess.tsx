import React, { ReactNode } from 'react';
import { MessageBar, MessageBarType, Text, Spinner, SpinnerSize, Stack } from '@fluentui/react';
import useProjectPermissions from '../hooks/useProjectPermissions';
import { ProjectPermissions } from '../models/roles';

interface ProjectPermissionAccessProps {
  projectId: string;
  requiredPermission: keyof ProjectPermissions;
  children: ReactNode;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
}

/**
 * Component that renders its children only if the current user has the required project permission
 */
export const ProjectPermissionAccess: React.FC<ProjectPermissionAccessProps> = ({
  projectId,
  requiredPermission,
  children,
  fallback,
  loadingComponent
}) => {
  const { permissions, isLoading, error, hasPermission } = useProjectPermissions(projectId);

  // Show loading state
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
        <Spinner size={SpinnerSize.large} label="Loading permissions..." />
      </Stack>
    );
  }

  // Show error state
  if (error) {
    return (
      <MessageBar
        messageBarType={MessageBarType.error}
        isMultiline={true}
      >
        <Text>Error loading permissions: {error}</Text>
      </MessageBar>
    );
  }

  // Check permission
  if (!hasPermission(requiredPermission)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <MessageBar
        messageBarType={MessageBarType.error}
        isMultiline={true}
      >
        <Text>You do not have permission to access this content.</Text>
      </MessageBar>
    );
  }

  // User has the required permission, render children
  return <>{children}</>;
};

export default ProjectPermissionAccess;

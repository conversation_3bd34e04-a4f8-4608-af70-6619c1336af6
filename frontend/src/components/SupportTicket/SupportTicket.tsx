import React, { useState, useCallback, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ack,
  Text,
  TextField,
  PrimaryButton,
  DefaultButton,
  MessageBar,
  MessageBarType,
  Label,
  Dropdown,
  IDropdownOption,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, Image, AlertCircle, Bug, HelpCircle, Settings } from 'lucide-react';
import { useUser } from '../../state/UserProvider';
import { getAuthHeaders } from '../../services/authHeaderService';
import styles from './SupportTicket.module.css';

interface SupportTicketProps {
  isOpen: boolean;
  onClose: () => void;
}

interface AttachedFile {
  file: File;
  preview?: string;
  id: string;
}

enum TicketPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

enum TicketCategory {
  BUG = 'bug',
  FEATURE_REQUEST = 'feature_request',
  QUESTION = 'question',
  TECHNICAL_ISSUE = 'technical_issue',
  OTHER = 'other'
}

const priorityOptions: IDropdownOption[] = [
  { key: TicketPriority.LOW, text: 'Low' },
  { key: TicketPriority.MEDIUM, text: 'Medium' },
  { key: TicketPriority.HIGH, text: 'High' },
  { key: TicketPriority.URGENT, text: 'Urgent' }
];

const categoryOptions: IDropdownOption[] = [
  { key: TicketCategory.BUG, text: 'Bug Report' },
  { key: TicketCategory.TECHNICAL_ISSUE, text: 'Technical Issue' },
  { key: TicketCategory.FEATURE_REQUEST, text: 'Feature Request' },
  { key: TicketCategory.QUESTION, text: 'Question' },
  { key: TicketCategory.OTHER, text: 'Other' }
];

const SupportTicket: React.FC<SupportTicketProps> = ({ isOpen, onClose }) => {
  const { currentUser } = useUser();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<TicketPriority>(TicketPriority.MEDIUM);
  const [category, setCategory] = useState<TicketCategory>(TicketCategory.TECHNICAL_ISSUE);
  const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{type: MessageBarType, text: string} | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Dropzone configuration
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    console.log('Files dropped:', acceptedFiles);
    console.log('Rejected files:', rejectedFiles);

    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const rejectedReasons = rejectedFiles.map(rejection => {
        const errors = rejection.errors.map((error: any) => error.message).join(', ');
        return `${rejection.file.name}: ${errors}`;
      }).join('\n');

      setSubmitMessage({
        type: MessageBarType.warning,
        text: `Some files were rejected:\n${rejectedReasons}`
      });
    }

    if (acceptedFiles.length === 0) {
      return;
    }

    const newFiles: AttachedFile[] = acceptedFiles.map(file => {
      const id = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // Create preview for images
      let preview: string | undefined;
      if (file.type.startsWith('image/')) {
        preview = URL.createObjectURL(file);
      }

      return {
        file,
        preview,
        id
      };
    });

    console.log('New files to add:', newFiles);
    setAttachedFiles(prev => {
      const updated = [...prev, ...newFiles];
      console.log('Updated attachedFiles:', updated);
      return updated;
    });
  }, []);

  const dropzoneOptions = {
    onDrop,
    multiple: true,
    noClick: false,
    noKeyboard: false,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'],
      'application/pdf': ['.pdf'],
      'text/plain': ['.txt'],
      'application/json': ['.json'],
      'text/csv': ['.csv']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone(dropzoneOptions);

  const removeFile = (fileId: string) => {
    setAttachedFiles(prev => {
      const fileToRemove = prev.find(f => f.id === fileId);
      if (fileToRemove && fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter(f => f.id !== fileId);
    });
  };

  const handleManualFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      onDrop(fileArray, []); // Reuse the onDrop logic
    }
    // Reset the input value so the same file can be selected again
    event.target.value = '';
  };

  const handleSubmit = async () => {
    if (!title.trim() || !description.trim()) {
      setSubmitMessage({
        type: MessageBarType.error,
        text: 'Please fill in both title and description.'
      });
      return;
    }

    setIsSubmitting(true);
    setSubmitMessage(null);

    try {
      const formData = new FormData();
      formData.append('title', title);
      formData.append('description', description);
      formData.append('priority', priority);
      formData.append('category', category);
      formData.append('userId', currentUser?.id || '');
      formData.append('userEmail', currentUser?.email || '');
      formData.append('userName', currentUser?.name || '');

      // Add files to FormData - backend expects 'files' parameter
      attachedFiles.forEach((attachedFile) => {
        console.log('Adding file to FormData:', attachedFile.file.name, attachedFile.file.size);
        formData.append('files', attachedFile.file);
      });
      formData.append('fileCount', attachedFiles.length.toString());

      // Debug: Log FormData contents
      console.log('FormData contents:');
      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`${key}: File(${value.name}, ${value.size} bytes)`);
        } else {
          console.log(`${key}: ${value}`);
        }
      }

      // Get proper authentication headers
      const authHeaders = await getAuthHeaders();

      // Remove Content-Type from auth headers to let browser set multipart boundary
      const headers: HeadersInit = {};
      Object.entries(authHeaders).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'content-type') {
          headers[key] = value;
        }
      });

      console.log('Sending request to /api/support/tickets with headers:', headers);

      const response = await fetch('/api/support/tickets', {
        method: 'POST',
        body: formData,
        headers
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      if (response.ok) {
        const result = await response.json();
        setSubmitMessage({
          type: MessageBarType.success,
          text: `Support ticket #${result.ticketId} has been created successfully!`
        });
        
        // Reset form after successful submission
        setTimeout(() => {
          handleClose();
        }, 2000);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create support ticket');
      }
    } catch (error) {
      console.error('Error submitting support ticket:', error);
      setSubmitMessage({
        type: MessageBarType.error,
        text: error instanceof Error ? error.message : 'Failed to create support ticket. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Clean up object URLs
    attachedFiles.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
    
    // Reset form
    setTitle('');
    setDescription('');
    setPriority(TicketPriority.MEDIUM);
    setCategory(TicketCategory.TECHNICAL_ISSUE);
    setAttachedFiles([]);
    setSubmitMessage(null);
    onClose();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Modal
      isOpen={isOpen}
      onDismiss={handleClose}
      isBlocking={false}
      containerClassName={styles.modal}
    >
      <div className={styles.header}>
        <Stack horizontal verticalAlign="center" horizontalAlign="space-between">
          <Text variant="xLarge" className={styles.title}>
            Create Support Ticket
          </Text>
          <DefaultButton
            iconProps={{ iconName: 'Cancel' }}
            onClick={handleClose}
            ariaLabel="Close"
          />
        </Stack>
      </div>

      <div className={styles.body}>
        <Stack tokens={{ childrenGap: 20 }}>
          {submitMessage && (
            <MessageBar
              messageBarType={submitMessage.type}
              onDismiss={() => setSubmitMessage(null)}
            >
              {submitMessage.text}
            </MessageBar>
          )}

          <Stack horizontal tokens={{ childrenGap: 16 }}>
            <Stack.Item grow>
              <Dropdown
                label="Category"
                options={categoryOptions}
                selectedKey={category}
                onChange={(_, option) => setCategory(option?.key as TicketCategory)}
                required
              />
            </Stack.Item>
            <Stack.Item grow>
              <Dropdown
                label="Priority"
                options={priorityOptions}
                selectedKey={priority}
                onChange={(_, option) => setPriority(option?.key as TicketPriority)}
                required
              />
            </Stack.Item>
          </Stack>

          <TextField
            label="Title"
            value={title}
            onChange={(_, newValue) => setTitle(newValue || '')}
            placeholder="Brief description of the issue..."
            required
          />

          <TextField
            label="Description"
            value={description}
            onChange={(_, newValue) => setDescription(newValue || '')}
            placeholder="Please provide detailed information about the issue, including steps to reproduce, expected behavior, and any error messages..."
            multiline
            rows={6}
            required
          />

          <div>
            <Label>Attachments (Screenshots, error logs, etc.)</Label>
            <div {...getRootProps()} className={`dropzone ${isDragActive ? 'dropzone-active' : ''}`}>
              <input {...getInputProps()} />
              <div className="upload-prompt">
                <Upload className="upload-icon" />
                <p>{isDragActive ? 'Drop files here...' : 'Drag & drop files here, or click to select'}</p>
              </div>
            </div>

            {/* Manual file selection as fallback */}
            <Stack horizontal horizontalAlign="center" tokens={{ childrenGap: 8 }} style={{ marginTop: 8 }}>
              <Text variant="small">Or</Text>
              <DefaultButton
                text="Browse Files"
                iconProps={{ iconName: 'FolderOpen' }}
                onClick={() => fileInputRef.current?.click()}
              />
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*,.pdf,.txt,.json,.csv"
                onChange={handleManualFileSelect}
                style={{ display: 'none' }}
              />
            </Stack>

            {attachedFiles.length > 0 && (
              <div className={styles.fileList}>
                <Text variant="medium" className={styles.fileListTitle}>
                  Attached Files ({attachedFiles.length})
                </Text>
                {attachedFiles.map((attachedFile) => (
                  <div key={attachedFile.id} className={styles.fileItem}>
                    <div className={styles.fileInfo}>
                      <div className={styles.fileIcon}>
                        {attachedFile.file.type.startsWith('image/') ? (
                          <Image size={16} />
                        ) : (
                          <AlertCircle size={16} />
                        )}
                      </div>
                      <div className={styles.fileDetails}>
                        <Text variant="small" className={styles.fileName}>
                          {attachedFile.file.name}
                        </Text>
                        <Text variant="tiny" className={styles.fileSize}>
                          {formatFileSize(attachedFile.file.size)}
                        </Text>
                      </div>
                    </div>
                    <DefaultButton
                      iconProps={{ iconName: 'Delete' }}
                      onClick={() => removeFile(attachedFile.id)}
                      ariaLabel="Remove file"
                      className={styles.removeButton}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className={styles.userInfo}>
            <Text variant="small" className={styles.userInfoLabel}>
              Ticket will be submitted by: {currentUser?.name} ({currentUser?.email})
            </Text>
          </div>
        </Stack>
      </div>

      <div className={styles.footer}>
        <Stack horizontal horizontalAlign="end" tokens={{ childrenGap: 8 }}>
          <DefaultButton text="Cancel" onClick={handleClose} disabled={isSubmitting} />
          <PrimaryButton
            text={isSubmitting ? 'Creating...' : 'Create Ticket'}
            onClick={handleSubmit}
            disabled={isSubmitting || !title.trim() || !description.trim()}
            iconProps={isSubmitting ? undefined : { iconName: 'Send' }}
          >
            {isSubmitting && <Spinner size={SpinnerSize.xSmall} style={{ marginRight: 8 }} />}
          </PrimaryButton>
        </Stack>
      </div>
    </Modal>
  );
};

export default SupportTicket;
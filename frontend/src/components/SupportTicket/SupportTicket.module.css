.modal {
  max-width: 800px;
  width: 90vw;
  max-height: 90vh;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.header {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #e1e1e1;
  margin-bottom: 0;
  padding-bottom: 16px;
}

.title {
  font-weight: 600;
  color: #323130;
}

.body {
  padding: 24px;
  max-height: calc(90vh - 160px);
  overflow-y: auto;
}

.footer {
  padding: 16px 24px 24px 24px;
  border-top: 1px solid #e1e1e1;
  background-color: #fafafa;
  border-radius: 0 0 8px 8px;
}

/* FileManagement-style dropzone */
:global(.dropzone) {
  padding: 8px;
  margin: 8px;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  max-width: 100%;
  background-color: #fafafa;
}

:global(.dropzone:hover) {
  border-color: #93c5fd;
  background-color: #f8fafc;
}

:global(.dropzone-active) {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

:global(.upload-icon) {
  width: 20px;
  height: 20px;
  margin: 0 auto 4px;
  color: #9ca3af;
}

:global(.upload-prompt) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

:global(.upload-prompt p) {
  margin: 0;
  font-size: 0.8rem;
  color: #6b7280;
}

.dropzoneSubtext {
  color: #666;
  font-style: italic;
}

.fileList {
  margin-top: 16px;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  background-color: #fff;
}

.fileListTitle {
  padding: 12px 16px;
  border-bottom: 1px solid #e1e1e1;
  background-color: #f8f9fa;
  margin: 0;
  font-weight: 600;
}

.fileItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f2f1;
}

.fileItem:last-child {
  border-bottom: none;
}

.fileInfo {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.fileIcon {
  margin-right: 12px;
  color: #666;
  flex-shrink: 0;
}

.fileDetails {
  min-width: 0;
  flex: 1;
}

.fileName {
  display: block;
  color: #323130;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fileSize {
  display: block;
  color: #666;
  margin-top: 2px;
}

.removeButton {
  min-width: auto;
  padding: 4px 8px;
  color: #d13438;
  border-color: transparent;
  background-color: transparent;
}

.removeButton:hover {
  background-color: #fdf2f2;
  border-color: #d13438;
}

.userInfo {
  background-color: #f8f9fa;
  padding: 12px 16px;
  border-radius: 4px;
  border-left: 4px solid #0078d4;
}

.userInfoLabel {
  color: #666;
  font-style: italic;
}

/* Preview images */
.imagePreview {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 8px;
}

/* Form validation styles */
.errorMessage {
  color: #d13438;
  font-size: 12px;
  margin-top: 4px;
}

.requiredField::after {
  content: ' *';
  color: #d13438;
}

/* Loading states */
.submitting {
  opacity: 0.7;
  pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal {
    width: 95vw;
    max-width: none;
  }
  
  .header,
  .body,
  .footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .dropzone {
    padding: 24px 12px;
  }
}
import React from 'react';
import { Dropdown, IDropdownOption, Stack } from '@fluentui/react';
import styles from './LanguageSelector.module.css';

export interface LanguageOption {
  key: string;
  text: string;
  systemPrompt: string;
}

interface LanguageSelectorProps {
  selectedLanguage: string;
  onLanguageChange: (language: string, systemPrompt: string) => void;
  languages: LanguageOption[];
  className?: string;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  selectedLanguage,
  onLanguageChange,
  languages,
  className
}) => {
  const dropdownOptions: IDropdownOption[] = languages.map(lang => ({
    key: lang.key,
    text: lang.text
  }));

  const handleLanguageChange = (
    _event: React.FormEvent<HTMLDivElement>,
    option?: IDropdownOption
  ) => {
    if (option) {
      const selectedLang = languages.find(lang => lang.key === option.key);
      if (selectedLang) {
        onLanguageChange(selectedLang.key, selectedLang.systemPrompt);
      }
    }
  };

  return (
    <div className={`${styles.languageSelectorContainer} ${className || ''}`}>
      <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 8 }}>
        <Stack.Item>
          <span className={styles.languageLabel}>Language:</span>
        </Stack.Item>
        <Stack.Item>
          <Dropdown
            selectedKey={selectedLanguage}
            onChange={handleLanguageChange}
            options={dropdownOptions}
            styles={{
              dropdown: {
                width: 120,
                border: 'none',
                selectors: {
                  ':hover': {
                    borderColor: 'transparent'
                  },
                  ':focus': {
                    borderColor: '#0078d4'
                  }
                }
              },
              root: {
                margin: 0,
                padding: 0
              },
              title: {
                border: '1px solid #e0e0e0',
                backgroundColor: 'rgba(255, 255, 255, 0.7)'
              }
            }}
            className={styles.languageDropdown}
          />
        </Stack.Item>
      </Stack>
    </div>
  );
};

export default LanguageSelector;

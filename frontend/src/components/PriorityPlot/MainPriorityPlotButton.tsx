import React from 'react';
import { BarChart2 } from 'lucide-react';
import './PriorityPlot.css';

interface MainPriorityPlotButtonProps {
  onClick: () => void;
}

export const MainPriorityPlotButton: React.FC<MainPriorityPlotButtonProps> = ({ onClick }) => {
  return (
    <button
      onClick={onClick}
      className="main-priority-plot-button"
      title="View Priority Plot"
    >
      <BarChart2 size={16} />
      <span>Priority Plot</span>
    </button>
  );
};

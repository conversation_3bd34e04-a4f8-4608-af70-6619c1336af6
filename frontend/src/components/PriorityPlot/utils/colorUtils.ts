// Color palette for different dimensions
export const dimensionColors: { [key: string]: string[] } = {
  // Red shades for Strategy
  'Strategy': [
    'rgba(220, 20, 60, 1)', // Crimson
    'rgba(178, 34, 34, 1)', // Firebrick
    'rgba(139, 0, 0, 1)',   // Dark Red
    'rgba(255, 0, 0, 1)',   // <PERSON>
    'rgba(255, 99, 71, 1)',  // <PERSON>ato
  ],
  // Green shades for Architecture
  'Architecture': [
    'rgba(0, 128, 0, 1)',   // <PERSON>
    'rgba(34, 139, 34, 1)',  // Forest Green
    'rgba(0, 100, 0, 1)',    // Dark Green
    'rgba(50, 205, 50, 1)',  // Lime Green
    'rgba(144, 238, 144, 1)', // Light Green
  ],
  // Blue shades for Literacy
  'Literacy': [
    'rgba(0, 0, 139, 1)',   // Dark Blue
    'rgba(0, 0, 205, 1)',    // Medium Blue
    'rgba(0, 0, 255, 1)',    // Blue
    'rgba(30, 144, 255, 1)', // Dodger Blue
    'rgba(135, 206, 235, 1)', // Sky Blue
  ],
  // Purple shades for Competencies
  'Competencies': [
    'rgba(128, 0, 128, 1)',  // Purple
    'rgba(148, 0, 211, 1)',   // Dark Violet
    'rgba(138, 43, 226, 1)',  // Blue Violet
    'rgba(153, 50, 204, 1)',  // Dark Orchid
    'rgba(186, 85, 211, 1)',   // Medium Orchid
  ],
  // Orange shades for Org and process
  'Org and process': [
    'rgba(255, 140, 0, 1)',  // Dark Orange
    'rgba(255, 165, 0, 1)',   // Orange
    'rgba(255, 69, 0, 1)',    // Orange Red
    'rgba(255, 127, 80, 1)',  // Coral
    'rgba(255, 99, 71, 1)',    // Tomato
  ],
  // Teal shades for Security
  'Security': [
    'rgba(0, 128, 128, 1)',  // Teal
    'rgba(32, 178, 170, 1)',  // Light Sea Green
    'rgba(0, 139, 139, 1)',   // Dark Cyan
    'rgba(0, 206, 209, 1)',   // Dark Turquoise
    'rgba(64, 224, 208, 1)',   // Turquoise
  ],
  'Organization_and_Processes': [
    'rgba(255, 140, 0, 1)',  // Dark Orange
    'rgba(255, 165, 0, 1)',   // Orange
    'rgba(255, 69, 0, 1)',    // Orange Red
    'rgba(255, 127, 80, 1)',  // Coral
    'rgba(255, 99, 71, 1)',    // Tomato
  ],
  // Default colors for other dimensions
  'default': [
    'rgba(128, 128, 128, 1)', // Gray
    'rgba(169, 169, 169, 1)', // Dark Gray
    'rgba(192, 192, 192, 1)', // Silver
    'rgba(211, 211, 211, 1)', // Light Gray
    'rgba(220, 220, 220, 1)', // Gainsboro
  ]
};

// Function to get color based on dimension and criteria
export const getDimensionColor = (dimension: string, criteria: string): string => {
  // Handle empty or undefined inputs
  if (!dimension || !criteria) {
    return dimensionColors['default'][0];
  }

  // Normalize dimension name
  const normalizedDimension = dimension.trim();

  // Handle special cases
  if (normalizedDimension.toLowerCase().includes('strategy')) {
    return dimensionColors['Strategy'][getCriteriaIndex(criteria, dimensionColors['Strategy'].length)];
  }

  if (normalizedDimension.toLowerCase().includes('architecture')) {
    return dimensionColors['Architecture'][getCriteriaIndex(criteria, dimensionColors['Architecture'].length)];
  }

  if (normalizedDimension.toLowerCase().includes('literacy')) {
    return dimensionColors['Literacy'][getCriteriaIndex(criteria, dimensionColors['Literacy'].length)];
  }

  if (normalizedDimension.toLowerCase().includes('competencies') ||
      normalizedDimension.toLowerCase().includes('Competencies')) {
    return dimensionColors['Competencies'][getCriteriaIndex(criteria, dimensionColors['Competencies'].length)];
  }

  if (normalizedDimension.toLowerCase().includes('org and process') ||
      normalizedDimension.toLowerCase().includes('organization')) {
    return dimensionColors['Org and process'][getCriteriaIndex(criteria, dimensionColors['Org and process'].length)];
  }

  if (normalizedDimension.toLowerCase().includes('security')) {
    return dimensionColors['Security'][getCriteriaIndex(criteria, dimensionColors['Security'].length)];
  }

  // If no specific match, try to find a partial match
  const matchedDimension = Object.keys(dimensionColors).find(
    key => normalizedDimension.toLowerCase().includes(key.toLowerCase())
  );

  if (matchedDimension) {
    return dimensionColors[matchedDimension][getCriteriaIndex(criteria, dimensionColors[matchedDimension].length)];
  }

  // Default fallback
  return dimensionColors['default'][getCriteriaIndex(criteria, dimensionColors['default'].length)];
};

// Helper function to get a consistent index based on criteria
function getCriteriaIndex(criteria: string, paletteLength: number): number {
  // Extract criterion number if present (e.g., "Criterion 1" -> 1)
  const criterionMatch = criteria.match(/criterion\s*(\d+)/i);
  if (criterionMatch && criterionMatch[1]) {
    const criterionNumber = parseInt(criterionMatch[1], 10);
    return (criterionNumber - 1) % paletteLength;
  }

  // Otherwise hash the string
  const hash = criteria.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return hash % paletteLength;
}

// Function to determine point fill color based on business impact
export const getPointFillColor = (businessImpact: number): string => {
  if (businessImpact >= 8) return 'rgba(255, 255, 255, 1)'; // High impact - white
  if (businessImpact >= 4) return 'rgba(255, 255, 255, 1)'; // Medium impact - white
  return 'rgba(255, 255, 255, 1)'; // Low impact - white
};

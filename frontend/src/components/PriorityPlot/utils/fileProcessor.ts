import { v4 as uuidv4 } from 'uuid';
import { BlobServiceClient } from '@azure/storage-blob';
import * as XLSX from 'xlsx';

export interface BlobFile {
  name: string;
  size: number;
  type: string;
  url: string;
  lastModified: Date;
}

export interface SavedFile {
  id: string;
  name: string;
  timestamp: Date;
  path: string;
}

export interface Initiative {
  dimension: string;
  criteria: string;
  initiative: string;
  reasoning: string;
  confScore: number;
  businessValue: number;
  feasibility: number;
  effort: number;
}

// Helper function to create a BlobServiceClient with proper error handling
export const createBlobServiceClient = async (storageAccountName: string, sasToken: string): Promise<BlobServiceClient> => {
  console.log(`Creating BlobServiceClient with storage account: ${storageAccountName}`);

  if (!storageAccountName) {
    throw new Error("Storage account name is required");
  }

  if (!sasToken) {
    throw new Error("SAS token is required");
  }

  // Log SAS token details for debugging (without revealing the full token)
  const sasTokenLength = sasToken.length;
  console.log(`SAS token length: ${sasTokenLength} characters`);
  console.log(`SAS token starts with: ${sasToken.substring(0, 10)}...`);
  console.log(`SAS token ends with: ...${sasToken.substring(sasTokenLength - 10)}`);
  console.log(`SAS token contains '?' character: ${sasToken.includes('?')}`);
  console.log(`SAS token starts with '?': ${sasToken.startsWith('?')}`);

  // Ensure SAS token starts with '?'
  const formattedSasToken = sasToken.startsWith('?') ? sasToken : `?${sasToken}`;

  try {
    // Log the URL format (without the actual SAS token for security)
    console.log(`Using URL format: https://${storageAccountName}.blob.core.windows.net${formattedSasToken.substring(0, 5)}...`);

    // Create the BlobServiceClient with proper error handling
    try {
      const blobServiceClient = new BlobServiceClient(
        `https://${storageAccountName}.blob.core.windows.net${formattedSasToken}`
      );

      console.log(`BlobServiceClient created successfully for account: ${storageAccountName}`);
      return blobServiceClient;
    } catch (clientError) {
      console.error(`Error creating BlobServiceClient: ${clientError}`);
      throw clientError;
    }
  } catch (error) {
    console.error(`Error creating BlobServiceClient: ${error}`);

    // Enhance error message with more details
    if (error instanceof Error) {
      if (error.message.includes('CORS')) {
        throw new Error(`CORS error: The storage account may not allow requests from this origin. Error: ${error.message}`);
      } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
        throw new Error(`Network error: Unable to connect to the storage account. Check your network connection and SAS token. Error: ${error.message}`);
      } else if (error.message.includes('401')) {
        throw new Error(`Authentication error: The SAS token may be invalid or expired. Error: ${error.message}`);
      } else if (error.message.includes('404')) {
        throw new Error(`Not found error: The storage account or container may not exist. Error: ${error.message}`);
      }
    }

    throw error;
  }
};

// Function to fetch files from the output container
export const fetchOutputFiles = async (
  storageAccountName: string,
  containerName: string,
  sasToken: string
): Promise<BlobFile[]> => {
  try {
    console.log(`Fetching files from output container: ${containerName}`);

    if (!containerName) {
      throw new Error("Container name is required");
    }

    if (!storageAccountName) {
      throw new Error("Storage account name is required");
    }

    if (!sasToken) {
      throw new Error("SAS token is required");
    }

    // Create a BlobServiceClient with retry logic
    let blobServiceClient;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        blobServiceClient = await createBlobServiceClient(storageAccountName, sasToken);
        break; // Success, exit the retry loop
      } catch (clientError) {
        retryCount++;
        console.error(`Error creating BlobServiceClient (attempt ${retryCount}/${maxRetries}):`, clientError);

        if (retryCount >= maxRetries) {
          throw clientError; // Max retries reached, propagate the error
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, retryCount) * 1000;
        console.log(`Retrying in ${delay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!blobServiceClient) {
      throw new Error("Failed to create BlobServiceClient after multiple attempts");
    }

    // Get a reference to the container
    const containerClient = blobServiceClient.getContainerClient(containerName);

    // Verify container exists by getting properties with retry logic
    retryCount = 0;
    while (retryCount < maxRetries) {
      try {
        await containerClient.getProperties();
        console.log(`Container access verified for: ${containerName}`);
        break; // Success, exit the retry loop
      } catch (containerError) {
        retryCount++;
        console.error(`Error accessing container (attempt ${retryCount}/${maxRetries}):`, containerError);

        if (retryCount >= maxRetries) {
          if (containerError instanceof Error) {
            if (containerError.message.includes('404')) {
              throw new Error(`Container '${containerName}' not found. Please check if the container exists.`);
            } else if (containerError.message.includes('403')) {
              throw new Error(`Access denied to container '${containerName}'. The SAS token may not have sufficient permissions.`);
            } else if (containerError.message.includes('Failed to fetch')) {
              throw new Error(`Network error accessing container '${containerName}'. This may be due to CORS issues, network connectivity, or an invalid SAS token.`);
            }
          }
          throw containerError; // Max retries reached, propagate the error
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, retryCount) * 1000;
        console.log(`Retrying container access in ${delay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // List blobs in the container with retry logic
    const outputBlobs = [];
    retryCount = 0;
    while (retryCount < maxRetries) {
      try {
        for await (const blob of containerClient.listBlobsFlat()) {
          outputBlobs.push(blob);
        }
        console.log(`Successfully listed ${outputBlobs.length} blobs in ${containerName}`);
        break; // Success, exit the retry loop
      } catch (listError) {
        retryCount++;
        console.error(`Error listing blobs (attempt ${retryCount}/${maxRetries}):`, listError);

        if (retryCount >= maxRetries) {
          if (listError instanceof Error && listError.message.includes('Failed to fetch')) {
            throw new Error(`Network error listing files in container '${containerName}'. This may be due to CORS issues, network connectivity, or an invalid SAS token.`);
          }
          throw new Error(`Failed to list files in container '${containerName}': ${listError instanceof Error ? listError.message : 'Unknown error'}`);
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, retryCount) * 1000;
        console.log(`Retrying blob listing in ${delay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    // Transform the data to match our expected format
    const fetchedOutputFiles: BlobFile[] = outputBlobs.map((blob) => ({
      name: blob.name,
      size: blob.properties.contentLength || 0,
      type: blob.properties.contentType || 'unknown',
      url: `https://${storageAccountName}.blob.core.windows.net/${containerName}/${blob.name}`,
      lastModified: blob.properties.lastModified || new Date()
    }));

    // Sort files by timestamp (newest first)
    const sortedOutputFiles = fetchedOutputFiles.sort((a, b) =>
      (b.lastModified?.getTime() || 0) - (a.lastModified?.getTime() || 0)
    );

    return sortedOutputFiles;
  } catch (error) {
    console.error('Error fetching files from output container:', error);
    throw error;
  }
};

// Helper function to convert a ReadableStream to a Blob
export const streamToBlob = async (stream: ReadableStream<Uint8Array>, size?: number): Promise<Blob> => {
  const chunks: Uint8Array[] = [];
  const reader = (stream as any).getReader();

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }
  } finally {
    reader.releaseLock();
  }

  return new Blob(chunks);
};

// Function to download a file from the output container
export const handleDownload = async (
  storageAccountName: string,
  containerName: string,
  sasToken: string,
  fileName: string
): Promise<void> => {
  try {
    console.log(`Downloading file "${fileName}" directly from Azure Storage, container: ${containerName}`);

    // Create a BlobServiceClient
    const blobServiceClient = await createBlobServiceClient(storageAccountName, sasToken);

    // Get a reference to the container and blob
    const containerClient = blobServiceClient.getContainerClient(containerName);
    const blobClient = containerClient.getBlobClient(fileName);

    // Download the blob
    const downloadResponse = await blobClient.download();

    if (!downloadResponse.readableStreamBody) {
      throw new Error("Failed to get readable stream from blob");
    }

    // Convert the stream to a blob
    const blob = await streamToBlob(downloadResponse.readableStreamBody as unknown as ReadableStream<Uint8Array>, downloadResponse.contentLength);

    // Create a download link
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    console.log(`Successfully downloaded file "${fileName}"`);
  } catch (error) {
    console.error('Error downloading file:', error);
    throw error;
  }
};

// Process Excel file directly in the browser
export const processExcelFile = async (file: BlobFile, storageAccountName: string, containerName: string, sasToken: string): Promise<Initiative[]> => {
  try {
    console.log(`Processing Excel file: ${file.name}`);

    if (!file || !file.name) {
      throw new Error("Invalid file object");
    }

    if (!file.name.toLowerCase().endsWith('.xlsx') && !file.name.toLowerCase().endsWith('.xls')) {
      throw new Error("File is not an Excel file. Only .xlsx and .xls files are supported.");
    }

    if (!storageAccountName) {
      throw new Error("Storage account name is required");
    }

    if (!containerName) {
      throw new Error("Container name is required");
    }

    if (!sasToken) {
      throw new Error("SAS token is required");
    }

    // Log SAS token details for debugging (without revealing the full token)
    const sasTokenLength = sasToken.length;
    console.log(`Excel processing - SAS token length: ${sasTokenLength} characters`);
    console.log(`Excel processing - SAS token starts with: ${sasToken.substring(0, 10)}...`);
    console.log(`Excel processing - SAS token ends with: ...${sasToken.substring(sasTokenLength - 10)}`);
    console.log(`Excel processing - SAS token contains '?' character: ${sasToken.includes('?')}`);
    console.log(`Excel processing - SAS token starts with '?': ${sasToken.startsWith('?')}`);

    // Create the URL to the blob
    const formattedSasToken = sasToken.startsWith('?') ? sasToken : `?${sasToken}`;
    const blobUrl = `https://${storageAccountName}.blob.core.windows.net/${containerName}/${encodeURIComponent(file.name)}${formattedSasToken}`;

    console.log(`Accessing Excel file at: https://${storageAccountName}.blob.core.windows.net/${containerName}/${encodeURIComponent(file.name)}${formattedSasToken.substring(0, 5)}...`);

    // Fetch the blob with retry logic
    let response;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        response = await fetch(blobUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch Excel file: ${response.status} ${response.statusText}`);
        }
        break; // Success, exit the retry loop
      } catch (fetchError) {
        retryCount++;
        console.error(`Fetch error (attempt ${retryCount}/${maxRetries}):`, fetchError);

        if (retryCount >= maxRetries) {
          if (fetchError instanceof Error) {
            if (fetchError.message.includes('Failed to fetch')) {
              throw new Error(`Network error: Unable to download the Excel file. This may be due to CORS issues, network connectivity, or an invalid SAS token.`);
            }
          }
          throw fetchError; // Max retries reached, propagate the error
        }

        // Wait before retrying (exponential backoff)
        const delay = Math.pow(2, retryCount) * 1000;
        console.log(`Retrying fetch in ${delay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    if (!response) {
      throw new Error("Failed to fetch Excel file after multiple attempts");
    }

    // Get the blob as an array buffer
    let arrayBuffer;
    try {
      arrayBuffer = await response.arrayBuffer();
      console.log(`Successfully read Excel file data, size: ${arrayBuffer.byteLength} bytes`);
    } catch (bufferError) {
      console.error('Error reading array buffer:', bufferError);
      throw new Error(`Failed to read Excel file data: ${bufferError instanceof Error ? bufferError.message : 'Unknown error'}`);
    }

    // Parse the Excel file
    let workbook;
    try {
      workbook = XLSX.read(arrayBuffer, { type: 'array' });
      console.log(`Successfully parsed Excel file with ${workbook.SheetNames.length} sheets`);
    } catch (parseError) {
      console.error('Error parsing Excel file:', parseError);
      throw new Error(`Failed to parse Excel file: The file may be corrupted or in an unsupported format. ${parseError instanceof Error ? parseError.message : ''}`);
    }

    // Log all available sheets
    console.log('Available sheets in workbook:', workbook.SheetNames);

    // Try to find a sheet that contains 'Near_Future' first (case insensitive)
    let sheetName = workbook.SheetNames[0]; // Default to first sheet

    // First priority: Look for 'Near_Future' sheet
    for (const sheet of workbook.SheetNames) {
      if (sheet.toLowerCase().includes('near_future')) {
        console.log(`Found Near_Future sheet: ${sheet}`);
        sheetName = sheet;
        break;
      }
    }

    // Second priority: Look for 'Near_Term' sheet if 'Near_Future' not found
    if (!sheetName.toLowerCase().includes('near_future')) {
      for (const sheet of workbook.SheetNames) {
        if (sheet.toLowerCase().includes('near_term')) {
          console.log(`Found Near_Term sheet: ${sheet}`);
          sheetName = sheet;
          break;
        }
      }
    }

    // If we didn't find a Near_Future or Near_Term sheet, check for other known sheets
    if (!sheetName.toLowerCase().includes('near_future') && !sheetName.toLowerCase().includes('near_term')) {
      // Try Summary sheet next as it's commonly used
      if (workbook.SheetNames.includes('Summary')) {
        console.log('Using Summary sheet as fallback');
        sheetName = 'Summary';
      } else {
        // Check for other potential sheets that might contain initiative data
        for (const sheet of workbook.SheetNames) {
          if (sheet === 'Initiatives' || sheet === 'Actions' || sheet === 'Roadmap') {
            console.log(`No Near_Future or Near_Term sheet found, trying alternative sheet: ${sheet}`);
            sheetName = sheet;
            break;
          }
        }
      }
    }

    console.log(`Using sheet: ${sheetName}`);

    // Get the worksheet
    const worksheet = workbook.Sheets[sheetName];

    // Convert to JSON, skipping the first row and using the second row as header
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { range: 1 });

    console.log('Excel data parsed:', jsonData);

    // Define possible column name variations based on the actual Excel structure
    const dimensionCols = ['Dimension', 'dimension', 'Category', 'category', 'Dimensions', 'Strategy', 'Unnamed: 0', '__EMPTY'];
    const criteriaCols = ['Criteria', 'criteria', 'Subcategory', 'subcategory', 'Criterion', 'Criterion 1', 'Unnamed: 1', '__EMPTY_1'];
    const initiativeCols = [
      'Criteria Name', 'Action Items to go from as is to be', '3 months', '3 Months', 'Initiative', 'initiative', 'Action', 'action',
      'Near Term Actions', 'Near Term Action', 'Near_Term', 'Near Term',
      'Criterion 2', 'Criterion 3', 'Criterion 4', 'Criterion 5'
    ];
    const reasoningCols = [
      'reasoning', 'Reasoning', 'Description', 'description',
      'Comments', 'Comment', 'Notes', 'Note', 'Unnamed: 7'
    ];
    const confScoreCols = [
      'Overall Conf', 'conf score', 'Conf Score', 'Confidence', 'confidence',
      'Confidence Score', 'Score'
    ];
    const businessValueCols = [
      'bus value', 'Business Value', 'business value', 'Value', 'value', 'BusinessValue',
      'Business Impact', 'Impact', 'Priority', 'Importance', 'Unnamed: 3', '__EMPTY_2'
    ];
    const feasibilityCols = [
      'feasibility', 'Feasability', 'Feasibility', 'feasability',
      'Ease of Implementation', 'Implementation Ease', 'Ease', 'Unnamed: 4', '__EMPTY_3'
    ];
    const effortCols = [
      'effort', 'Effort', 'Cost', 'Resource', 'Resources', 'Time', 'Unnamed: 5', '__EMPTY_4'
    ];

    // Find the actual column names in the data
    const columns = jsonData.length > 0 ? Object.keys(jsonData[0] as Record<string, any>) : [];

    // Check if this is the Near_Future format from the screenshot
    const isMaturityModelFormat = columns.some(col => col === '__EMPTY' || col === 'Dimension') &&
                                 columns.some(col => col === '__EMPTY_1' || col === 'Criteria') &&
                                 columns.some(col => col === 'Action Items to go from as is to be' || col === 'Criteria Name');

    console.log('Is Maturity Model format:', isMaturityModelFormat);
    console.log('Available columns:', columns);

    let dimensionCol, criteriaCol, initiativeCol, reasoningCol, confScoreCol, businessValueCol, feasibilityCol, effortCol;

    if (isMaturityModelFormat) {
      // For the maturity model format, map to the specific columns
      dimensionCol = columns.find(col => dimensionCols.includes(col));
      criteriaCol = columns.find(col => criteriaCols.includes(col));

      // Look for initiative column - could be 'Criteria Name' or 'Action Items to go from as is to be'
      initiativeCol = columns.find(col => col === 'Criteria Name' || col === 'Action Items to go from as is to be');

      // Look for confidence score column
      confScoreCol = columns.find(col => confScoreCols.includes(col));

      // Look for business value column
      businessValueCol = columns.find(col => businessValueCols.includes(col));

      // Look for feasibility column
      feasibilityCol = columns.find(col => feasibilityCols.includes(col));

      // Look for effort column
      effortCol = columns.find(col => effortCols.includes(col));

      // Look for reasoning column
      reasoningCol = columns.find(col => reasoningCols.includes(col));

      // If we can't find the columns by name, try to use the __EMPTY_X columns
      if (!dimensionCol) dimensionCol = '__EMPTY';
      if (!criteriaCol) criteriaCol = '__EMPTY_1';
      if (!initiativeCol) initiativeCol = 'Action Items to go from as is to be';
      if (!businessValueCol) businessValueCol = '__EMPTY_2';
      if (!feasibilityCol) feasibilityCol = '__EMPTY_3';
      if (!effortCol) effortCol = '__EMPTY_4';
    } else {
      // For other formats, find columns by name
      dimensionCol = columns.find(col => dimensionCols.includes(col));
      criteriaCol = columns.find(col => criteriaCols.includes(col));
      initiativeCol = columns.find(col => initiativeCols.includes(col));
      reasoningCol = columns.find(col => reasoningCols.includes(col));
      confScoreCol = columns.find(col => confScoreCols.includes(col));
      businessValueCol = columns.find(col => businessValueCols.includes(col));
      feasibilityCol = columns.find(col => feasibilityCols.includes(col));
      effortCol = columns.find(col => effortCols.includes(col));
    }

    console.log('Matched columns:', {
      dimensionCol,
      criteriaCol,
      initiativeCol,
      reasoningCol,
      confScoreCol,
      businessValueCol,
      feasibilityCol,
      effortCol
    });

    // Transform the data into initiatives
    const initiatives: Initiative[] = [];

    for (const rowData of jsonData) {
      // Cast row to Record<string, any> to fix TypeScript errors
      const row = rowData as Record<string, any>;

      // Skip completely empty rows
      if (Object.values(row).every(val => val === undefined || val === null || val === '')) {
        continue;
      }

      // Skip rows with "Action Items to go from as is to be" as the only content
      if (initiativeCol && row[initiativeCol] === "Action Items to go from as is to be") {
        continue;
      }

      // For maturity model assessment files, we need to create initiatives from dimensions and criteria
      let initiativeName = '';
      if (initiativeCol && row[initiativeCol]) {
        initiativeName = String(row[initiativeCol] || '');
      } else if (dimensionCol && criteriaCol && row[dimensionCol] && row[criteriaCol]) {
        // If we don't have a dedicated initiative column but have dimensions and criteria,
        // use the dimension and criteria to create a name
        initiativeName = `${row[dimensionCol]}: ${row[criteriaCol]}`;
      }

      // Get values from the row
      const dimension = dimensionCol && row[dimensionCol] ? String(row[dimensionCol] || '') : '';
      const criteria = criteriaCol && row[criteriaCol] ? String(row[criteriaCol] || '') : '';
      const reasoning = reasoningCol && row[reasoningCol] ? String(row[reasoningCol] || '') : '';

      // Parse numeric values, ensuring they are valid numbers
      let businessValue = 0;
      if (businessValueCol && row[businessValueCol] !== undefined && row[businessValueCol] !== null) {
        const rawValue = row[businessValueCol];
        businessValue = typeof rawValue === 'number' ? rawValue :
                       (typeof rawValue === 'string' ? parseFloat(rawValue) : 0);
      }

      let feasibility = 0;
      if (feasibilityCol && row[feasibilityCol] !== undefined && row[feasibilityCol] !== null) {
        const rawValue = row[feasibilityCol];
        feasibility = typeof rawValue === 'number' ? rawValue :
                     (typeof rawValue === 'string' ? parseFloat(rawValue) : 0);
      }

      let effort = 0;
      if (effortCol && row[effortCol] !== undefined && row[effortCol] !== null) {
        const rawValue = row[effortCol];
        effort = typeof rawValue === 'number' ? rawValue :
                (typeof rawValue === 'string' ? parseFloat(rawValue) : 0);
      }

      // Calculate confidence score if not provided
      let confScore = 0;
      if (confScoreCol && row[confScoreCol] !== undefined && row[confScoreCol] !== null) {
        const rawValue = row[confScoreCol];
        confScore = typeof rawValue === 'number' ? rawValue / 100 : // Convert percentage to 0-1 range
                   (typeof rawValue === 'string' ? parseFloat(rawValue) / 100 : 0);
      } else if (businessValue > 0 && feasibility > 0) {
        // Default confidence score calculation based on other values
        // Use a simple average of business value and feasibility, normalized to 0-1 range
        confScore = Math.min(1.0, ((businessValue + feasibility) / 200.0));
      } else {
        confScore = 0.8; // Default value if no data available
      }

      // Ensure confScore is in 0-1 range
      confScore = Math.max(0, Math.min(1, confScore));

      // Create an initiative object with the data from this row
      const initiative: Initiative = {
        dimension,
        criteria,
        initiative: initiativeName,
        reasoning,
        confScore,
        businessValue,
        feasibility,
        effort
      };

      // Skip rows that don't have the essential data
      if (!initiative.initiative || initiative.initiative.trim() === '') {
        continue;
      }

      // Skip rows with missing essential data
      if (isNaN(initiative.businessValue) || isNaN(initiative.feasibility) ||
          initiative.businessValue <= 0 || initiative.feasibility <= 0) {
        console.log('Skipping initiative due to missing data:', initiative);
        continue;
      }

      console.log('Adding initiative:', initiative);
      initiatives.push(initiative);
    }

    console.log(`Parsed ${initiatives.length} initiatives from Excel file`);
    return initiatives;
  } catch (error) {
    console.error('Error processing Excel file:', error);
    throw error;
  }
};

// Sample data for the PriorityPlot component (fallback if no files are found)
export const getSampleInitiatives = (): Initiative[] => {
  return [
    {
      dimension: 'Security',
      criteria: 'Data Security',
      initiative: 'Implement MFA',
      reasoning: 'Enhance authentication security by implementing Multi-Factor Authentication across all critical systems.',
      confScore: 0.92,
      businessValue: 90,
      feasibility: 80,
      effort: 40
    },
    {
      dimension: 'Data',
      criteria: 'Data Quality',
      initiative: 'Data Validation',
      reasoning: 'Establish automated data validation processes to ensure data accuracy and consistency.',
      confScore: 0.85,
      businessValue: 70,
      feasibility: 60,
      effort: 50
    },
    {
      dimension: 'Infrastructure',
      criteria: 'Cloud Migration',
      initiative: 'Migrate Legacy Systems',
      reasoning: 'Move on-premises legacy systems to cloud infrastructure to improve scalability and reduce maintenance costs.',
      confScore: 0.78,
      businessValue: 85,
      feasibility: 65,
      effort: 90
    },
    {
      dimension: 'Development',
      criteria: 'CI/CD',
      initiative: 'Automated Testing',
      reasoning: 'Implement comprehensive automated testing to improve code quality and reduce bugs.',
      confScore: 0.88,
      businessValue: 75,
      feasibility: 70,
      effort: 60
    },
    {
      dimension: 'Analytics',
      criteria: 'Business Intelligence',
      initiative: 'Dashboard Creation',
      reasoning: 'Create interactive dashboards for key business metrics to improve decision making.',
      confScore: 0.90,
      businessValue: 95,
      feasibility: 85,
      effort: 55
    }
  ];
};

import React, { useState, useEffect } from 'react';
import { Check, X, Plus } from 'lucide-react';
import { Initiative } from '../utils/types';
import { getDimensionColor } from '../utils/colorUtils';

// Helper function to convert rgba to hex for color input
const rgbaToHex = (rgba: string): string => {
  // Parse rgba format: rgba(r,g,b,a)
  const match = rgba.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
  if (!match) return '#000000';

  const r = parseInt(match[1]);
  const g = parseInt(match[2]);
  const b = parseInt(match[3]);

  // Convert to hex
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

interface NewInitiativeFormProps {
  onSave: (newInitiative: Initiative) => void;
  onCancel: () => void;
}

const NewInitiativeForm: React.FC<NewInitiativeFormProps> = ({ onSave, onCancel }) => {
  const [newInitiative, setNewInitiative] = useState<Initiative>({
    dimension: '',
    criteria: '',
    initiative: '',
    reasoning: '',
    confScore: 0.8,
    businessValue: 50,
    feasibility: 50,
    effort: 50,
    color: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Handle numeric values
    if (['businessValue', 'feasibility', 'effort', 'confScore'].includes(name)) {
      setNewInitiative({
        ...newInitiative,
        [name]: parseFloat(value) || 0
      });
    } else {
      setNewInitiative({
        ...newInitiative,
        [name]: value
      });
    }
  };

  // Update color when dimension or criteria changes
  useEffect(() => {
    if (newInitiative.dimension && newInitiative.criteria && !newInitiative.color) {
      const suggestedColor = getDimensionColor(newInitiative.dimension, newInitiative.criteria);
      setNewInitiative(prev => ({
        ...prev,
        color: suggestedColor
      }));
    }
  }, [newInitiative.dimension, newInitiative.criteria]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(newInitiative);
  };

  return (
    <form onSubmit={handleSubmit} className="initiative-edit-form">
      <div className="initiative-edit-fields">
        <div className="initiative-edit-field">
          <label>Initiative</label>
          <input
            type="text"
            name="initiative"
            value={newInitiative.initiative}
            onChange={handleChange}
            required
            placeholder="Enter initiative name"
          />
        </div>

        <div className="initiative-edit-row">
          <div className="initiative-edit-field">
            <label>Dimension</label>
            <input
              type="text"
              name="dimension"
              value={newInitiative.dimension}
              onChange={handleChange}
              placeholder="e.g., Strategy"
            />
          </div>

          <div className="initiative-edit-field">
            <label>Criteria</label>
            <input
              type="text"
              name="criteria"
              value={newInitiative.criteria}
              onChange={handleChange}
              placeholder="e.g., Criterion 2"
            />
          </div>
        </div>

        <div className="initiative-edit-field">
          <label>Reasoning</label>
          <textarea
            name="reasoning"
            value={newInitiative.reasoning}
            onChange={handleChange}
            rows={3}
            placeholder="Enter reasoning or description"
          />
        </div>

        <div className="initiative-edit-row">
          <div className="initiative-edit-field">
            <label>Business Value</label>
            <input
              type="number"
              name="businessValue"
              value={newInitiative.businessValue}
              onChange={handleChange}
              min="0"
              max="100"
            />
          </div>

          <div className="initiative-edit-field">
            <label>Feasibility</label>
            <input
              type="number"
              name="feasibility"
              value={newInitiative.feasibility}
              onChange={handleChange}
              min="0"
              max="100"
            />
          </div>
        </div>

        <div className="initiative-edit-row">
          <div className="initiative-edit-field">
            <label>Effort</label>
            <input
              type="number"
              name="effort"
              value={newInitiative.effort}
              onChange={handleChange}
              min="0"
              max="100"
            />
          </div>

          <div className="initiative-edit-field">
            <label>Confidence Score</label>
            <input
              type="number"
              name="confScore"
              value={newInitiative.confScore}
              onChange={handleChange}
              min="0"
              max="1"
              step="0.01"
            />
          </div>
        </div>

        <div className="initiative-edit-field">
          <label>Color</label>
          <div className="initiative-edit-color">
            <input
              type="color"
              name="color"
              value={newInitiative.color?.startsWith('rgba')
                ? rgbaToHex(newInitiative.color)
                : newInitiative.color || '#000000'}
              onChange={handleChange}
            />
            <input
              type="text"
              name="color"
              value={newInitiative.color || ''}
              onChange={handleChange}
              placeholder="rgba(0,0,0,1) or #000000"
            />
          </div>
          <p className="initiative-edit-hint">
            You can use either RGBA format (rgba(255,0,0,1)) or hex (#FF0000)
          </p>
        </div>
      </div>

      <div className="initiative-edit-actions">
        <button
          type="button"
          onClick={onCancel}
          className="initiative-edit-cancel"
          aria-label="Cancel"
        >
          <X size={18} />
        </button>
        <button
          type="submit"
          className="initiative-edit-save"
          aria-label="Save"
        >
          <Check size={18} />
        </button>
      </div>
    </form>
  );
};

// Button to show the new initiative form
export const NewInitiativeButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <button
      onClick={onClick}
      className="new-initiative-button"
    >
      <Plus size={16} />
      <span>Add New Initiative</span>
    </button>
  );
};

export default NewInitiativeForm;

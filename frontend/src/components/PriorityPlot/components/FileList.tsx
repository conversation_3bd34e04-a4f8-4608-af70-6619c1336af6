import React from 'react';
import { FileSpreadsheet, Clock, X } from 'lucide-react';

export interface SavedFile {
  id: string;
  name: string;
  timestamp: Date;
  path: string;
}

interface FileListProps {
  files: SavedFile[];
  onFileSelect: (file: SavedFile) => void;
  onFileDelete: (fileId: string) => void;
  activeFileId: string | null;
}

const FileList: React.FC<FileListProps> = ({ files, onFileSelect, onFileDelete, activeFileId }) => {
  if (files.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>No files processed yet</p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {files.map((file) => (
        <div
          key={file.id}
          className={`p-3 rounded-lg cursor-pointer transition-colors ${
            activeFileId === file.id
              ? 'bg-blue-100 border-l-4 border-blue-500'
              : 'bg-white hover:bg-gray-50 border-l-4 border-transparent'
          }`}
          onClick={() => onFileSelect(file)}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start flex-1 min-w-0 pr-2">
              <FileSpreadsheet className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 break-words">{file.name}</p>
                <div className="flex items-center mt-1">
                  <Clock className="h-3 w-3 text-gray-400 mr-1" />
                  <p className="text-xs text-gray-500">
                    {file.timestamp.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
            <button
              className="p-1 rounded-full hover:bg-gray-200 transition-colors mt-0.5"
              onClick={(e) => {
                e.stopPropagation(); // Prevent triggering the parent onClick
                onFileDelete(file.id);
              }}
              aria-label="Delete file"
            >
              <X className="h-4 w-4 text-gray-500 hover:text-red-500" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FileList;

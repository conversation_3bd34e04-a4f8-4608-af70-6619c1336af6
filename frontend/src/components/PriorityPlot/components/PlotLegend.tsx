import React from 'react';

const PlotLegend: React.FC = () => {
  return (
    <div className="plot-legend">
      <h3>Legend</h3>
      <div className="plot-legend-items">
        {/* Effort Legend - Gradient from black to white */}
        <div className="plot-legend-item">
          <div className="plot-legend-gradient">
            <div className="plot-legend-gradient-segment" style={{ opacity: 0.2 }}></div>
            <div className="plot-legend-gradient-segment" style={{ opacity: 0.4 }}></div>
            <div className="plot-legend-gradient-segment" style={{ opacity: 0.6 }}></div>
            <div className="plot-legend-gradient-segment" style={{ opacity: 0.9 }}></div>
          </div>
          <span>Effort</span>
        </div>

        {/* Impact Legend - Circle sizes */}
        <div className="plot-legend-item">
          <div className="plot-legend-circles">
            <div className="plot-legend-circle small"></div>
            <div className="plot-legend-circle medium"></div>
            <div className="plot-legend-circle large"></div>
          </div>
          <span>Impact</span>
        </div>
      </div>
    </div>
  );
};

export default PlotLegend;

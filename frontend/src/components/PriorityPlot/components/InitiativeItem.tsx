import React, { useState, useMemo } from 'react';
import { Initiative } from '../utils/types';
import { ChevronDown, ChevronUp, Edit, Trash2 } from 'lucide-react';
import { getDimensionColor } from '../utils/colorUtils';

interface InitiativeItemProps {
  initiative: Initiative;
  businessImpact: number;
  onEdit?: (initiative: Initiative) => void;
  onDelete?: (initiative: Initiative) => void;
  onHover?: (initiative: Initiative | null) => void;
  isHovered?: boolean;
  onClick?: (initiative: Initiative) => void;
}

const InitiativeItem: React.FC<InitiativeItemProps> = ({
  initiative,
  businessImpact,
  onEdit,
  onDelete,
  onHover,
  isHovered = false,
  onClick
}) => {
  const [expanded, setExpanded] = useState(false);
  // No longer need isEditing state as we're using direct callbacks
  // No longer need to store original values as we're using direct callbacks

  // Create a unique ID for this initiative based on name and dimension
  const initiativeId = `${initiative.initiative.replace(/\s+/g, '-').toLowerCase()}-${initiative.dimension.replace(/\s+/g, '-').toLowerCase()}`;

  // Get the color for this initiative based on its custom color or dimension and criteria
  const borderColor = useMemo(() => {
    return initiative.color || getDimensionColor(initiative.dimension, initiative.criteria);
  }, [initiative.dimension, initiative.criteria, initiative.color]);

  // Create a lighter version of the color for backgrounds
  const backgroundColor = useMemo(() => {
    const rgbaMatch = borderColor.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
    return rgbaMatch
      ? `rgba(${rgbaMatch[1]}, ${rgbaMatch[2]}, ${rgbaMatch[3]}, 0.2)`
      : 'rgba(200, 200, 200, 0.2)';
  }, [borderColor]);

  const toggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(initiative);
    }
  };

  // handleSave and handleCancel are no longer needed as we're using direct callbacks

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(initiative);
    }
  };

  const handleClick = () => {
    if (onClick) {
      onClick(initiative);
    }
  };

  return (
    <div
      key={initiativeId}
      id={initiativeId}
      className="initiative-item"
      style={{
        borderLeft: `4px solid ${borderColor}`,
        backgroundColor: isHovered ? 'rgba(240, 240, 240, 0.8)' : backgroundColor
      }}
      onClick={handleClick}
      onMouseEnter={() => onHover && onHover(initiative)}
      onMouseLeave={() => onHover && onHover(null)}
    >
      <div className="initiative-header">
        <h3>{initiative.initiative}</h3>
        {(onEdit || onDelete) && (
          <div className="initiative-actions">
            {onEdit && (
              <button
                onClick={handleEdit}
                className="initiative-action-button"
                aria-label="Edit initiative"
              >
                <Edit size={14} />
              </button>
            )}
            {onDelete && (
              <button
                onClick={handleDelete}
                className="initiative-action-button"
                aria-label="Delete initiative"
              >
                <Trash2 size={14} />
              </button>
            )}
            <button
              onClick={toggleExpand}
              className="initiative-action-button"
              aria-label={expanded ? "Collapse" : "Expand"}
            >
              {expanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
            </button>
          </div>
        )}
      </div>
      <div className="initiative-dimension">
        <span>Dimension:</span> {initiative.dimension} - {initiative.criteria}
      </div>
      <div className="initiative-metrics">
        <div>
          <label>Value</label>
          <div>{initiative.businessValue}</div>
        </div>
        <div>
          <label>Feas.</label>
          <div>{initiative.feasibility}</div>
        </div>
        <div>
          <label>Effort</label>
          <div>{initiative.effort}</div>
        </div>
        <div>
          <label>Impact</label>
          <div className="impact-value">
            {businessImpact.toFixed(0)}
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {expanded && (
        <div className="initiative-expanded-content">
          <div>
            <h4>Reasoning:</h4>
            <p>{initiative.reasoning || 'No reasoning provided.'}</p>
          </div>
          <div>
            <h4>Confidence Score:</h4>
            <p>{initiative.confScore.toFixed(2)}</p>
          </div>
          <div>
            <h4>Impact Score:</h4>
            <p>{businessImpact.toFixed(2)}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default InitiativeItem;

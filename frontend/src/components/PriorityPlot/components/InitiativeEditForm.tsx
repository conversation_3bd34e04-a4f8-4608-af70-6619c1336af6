import React, { useState, useEffect } from 'react';
import { Check, X } from 'lucide-react';
import { Initiative } from '../utils/types';
import { getDimensionColor } from '../utils/colorUtils';

// Helper function to convert rgba to hex for color input
const rgbaToHex = (rgba: string): string => {
  // Parse rgba format: rgba(r,g,b,a)
  const match = rgba.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
  if (!match) return '#000000';

  const r = parseInt(match[1]);
  const g = parseInt(match[2]);
  const b = parseInt(match[3]);

  // Convert to hex
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};

interface InitiativeEditFormProps {
  initiative: Initiative;
  onSave: (editedInitiative: Initiative) => void;
  onCancel: () => void;
}

const InitiativeEditForm: React.FC<InitiativeEditFormProps> = ({ initiative, onSave, onCancel }) => {
  const [editedInitiative, setEditedInitiative] = useState<Initiative>({ ...initiative });

  useEffect(() => {
    // Update the form when the initiative changes
    setEditedInitiative({ ...initiative });
  }, [initiative]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Handle numeric values
    if (['businessValue', 'feasibility', 'effort', 'confScore'].includes(name)) {
      setEditedInitiative({
        ...editedInitiative,
        [name]: parseFloat(value) || 0
      });
    } else {
      setEditedInitiative({
        ...editedInitiative,
        [name]: value
      });
    }
  };

  // Set default color if not present
  useEffect(() => {
    if (editedInitiative.dimension && editedInitiative.criteria && !editedInitiative.color) {
      const suggestedColor = getDimensionColor(editedInitiative.dimension, editedInitiative.criteria);
      setEditedInitiative(prev => ({
        ...prev,
        color: suggestedColor
      }));
    }
  }, [editedInitiative.dimension, editedInitiative.criteria]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(editedInitiative);
  };

  return (
    <form onSubmit={handleSubmit} className="initiative-edit-form">
      <div className="initiative-edit-fields">
        <div className="initiative-edit-field">
          <label>Initiative</label>
          <input
            type="text"
            name="initiative"
            value={editedInitiative.initiative}
            onChange={handleChange}
            required
          />
        </div>

        <div className="initiative-edit-row">
          <div className="initiative-edit-field">
            <label>Dimension</label>
            <input
              type="text"
              name="dimension"
              value={editedInitiative.dimension}
              onChange={handleChange}
            />
          </div>

          <div className="initiative-edit-field">
            <label>Criteria</label>
            <input
              type="text"
              name="criteria"
              value={editedInitiative.criteria}
              onChange={handleChange}
            />
          </div>
        </div>

        <div className="initiative-edit-field">
          <label>Reasoning</label>
          <textarea
            name="reasoning"
            value={editedInitiative.reasoning}
            onChange={handleChange}
            rows={3}
          />
        </div>

        <div className="initiative-edit-row">
          <div className="initiative-edit-field">
            <label>Business Value</label>
            <input
              type="number"
              name="businessValue"
              value={editedInitiative.businessValue}
              onChange={handleChange}
              min="0"
              max="100"
            />
          </div>

          <div className="initiative-edit-field">
            <label>Feasibility</label>
            <input
              type="number"
              name="feasibility"
              value={editedInitiative.feasibility}
              onChange={handleChange}
              min="0"
              max="100"
            />
          </div>
        </div>

        <div className="initiative-edit-row">
          <div className="initiative-edit-field">
            <label>Effort</label>
            <input
              type="number"
              name="effort"
              value={editedInitiative.effort}
              onChange={handleChange}
              min="0"
              max="100"
            />
          </div>

          <div className="initiative-edit-field">
            <label>Confidence Score</label>
            <input
              type="number"
              name="confScore"
              value={editedInitiative.confScore}
              onChange={handleChange}
              min="0"
              max="1"
              step="0.01"
            />
          </div>
        </div>

        <div className="initiative-edit-field">
          <label>Color</label>
          <div className="initiative-edit-color">
            <input
              type="color"
              name="color"
              value={editedInitiative.color?.startsWith('rgba')
                ? rgbaToHex(editedInitiative.color)
                : editedInitiative.color || '#000000'}
              onChange={handleChange}
            />
            <input
              type="text"
              name="color"
              value={editedInitiative.color || ''}
              onChange={handleChange}
              placeholder="rgba(0,0,0,1) or #000000"
            />
          </div>
          <p className="initiative-edit-hint">
            You can use either RGBA format (rgba(255,0,0,1)) or hex (#FF0000)
          </p>
        </div>
      </div>

      <div className="initiative-edit-actions">
        <button
          type="button"
          onClick={onCancel}
          className="initiative-edit-cancel"
          aria-label="Cancel"
        >
          <X size={18} />
        </button>
        <button
          type="submit"
          className="initiative-edit-save"
          aria-label="Save"
        >
          <Check size={18} />
        </button>
      </div>
    </form>
  );
};

export default InitiativeEditForm;

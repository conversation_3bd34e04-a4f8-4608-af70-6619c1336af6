import React from 'react';
import { X } from 'lucide-react';
import { Initiative } from '../utils/fileProcessor';
import { getDimensionColor } from '../utils/colorUtils';
import '../PriorityPlot.css';

interface InitiativeDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  initiative: Initiative | null;
  calculateBusinessImpact: (businessValue: number, feasibility: number, effort: number) => number;
}

const InitiativeDetailModal: React.FC<InitiativeDetailModalProps> = ({
  isOpen,
  onClose,
  initiative,
  calculateBusinessImpact
}) => {
  if (!isOpen || !initiative) return null;

  const businessImpact = calculateBusinessImpact(
    initiative.businessValue,
    initiative.feasibility,
    initiative.effort
  );

  // Get color based on dimension and criteria using the colorUtils
  const borderColor = getDimensionColor(initiative.dimension, initiative.criteria);

  // Create a lighter version of the color for backgrounds
  const rgbaMatch = borderColor.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
  const backgroundColor = rgbaMatch
    ? `rgba(${rgbaMatch[1]}, ${rgbaMatch[2]}, ${rgbaMatch[3]}, 0.2)`
    : 'rgba(200, 200, 200, 0.2)';

  return (
    <div className="initiative-detail-modal-overlay">
      <div className="initiative-detail-modal">
        <button className="initiative-detail-close-button" onClick={onClose} aria-label="Close">
          <X size={24} />
        </button>
        <div className="initiative-detail-header" style={{ borderBottom: `3px solid ${borderColor}` }}>
          <h2>{initiative.initiative}</h2>
        </div>
        <div className="initiative-detail-content">
          <div className="initiative-detail-section">
            <h3>Overview</h3>
            <div className="initiative-detail-grid">
              <div className="initiative-detail-field">
                <label>Dimension</label>
                <div style={{ color: borderColor }}>{initiative.dimension}</div>
              </div>
              <div className="initiative-detail-field">
                <label>Criteria</label>
                <div>{initiative.criteria}</div>
              </div>
            </div>
          </div>

          <div className="initiative-detail-section">
            <h3>Metrics</h3>
            <div className="initiative-detail-metrics">
              <div className="initiative-detail-metric" style={{ backgroundColor }}>
                <label>Business Value</label>
                <div className="initiative-detail-metric-value">{initiative.businessValue}</div>
              </div>
              <div className="initiative-detail-metric" style={{ backgroundColor }}>
                <label>Feasibility</label>
                <div className="initiative-detail-metric-value">{initiative.feasibility}</div>
              </div>
              <div className="initiative-detail-metric" style={{ backgroundColor }}>
                <label>Effort</label>
                <div className="initiative-detail-metric-value">{initiative.effort}</div>
              </div>
              <div className="initiative-detail-metric" style={{ backgroundColor }}>
                <label>Impact</label>
                <div className="initiative-detail-metric-value" style={{ color: borderColor }}>
                  {businessImpact.toFixed(0)}
                </div>
              </div>
              <div className="initiative-detail-metric" style={{ backgroundColor }}>
                <label>Confidence</label>
                <div className="initiative-detail-metric-value">
                  {(initiative.confScore * 100).toFixed(0)}%
                </div>
              </div>
            </div>
          </div>

          {initiative.reasoning && (
            <div className="initiative-detail-section">
              <h3>Reasoning</h3>
              <div className="initiative-detail-reasoning">
                {initiative.reasoning}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InitiativeDetailModal;

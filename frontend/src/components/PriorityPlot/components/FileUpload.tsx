import React, { useState, useRef } from 'react';
import { Upload, FileUp } from 'lucide-react';

interface FileUploadProps {
  onFileSelected: (file: File) => void;
  onProcessFile: () => void;
  selectedFile: File | null;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileSelected, onProcessFile, selectedFile }) => {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          file.type === 'application/vnd.ms-excel') {
        onFileSelected(file);
      } else {
        alert('Please upload an Excel file (.xlsx or .xls)');
      }
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
          file.type === 'application/vnd.ms-excel') {
        onFileSelected(file);
      } else {
        alert('Please upload an Excel file (.xlsx or .xls)');
      }
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="w-full">
      <div
        className={`border-2 border-dashed rounded-lg p-3 text-center cursor-pointer transition-colors ${
          isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleButtonClick}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileInputChange}
          accept=".xlsx,.xls"
          className="hidden"
        />
        <div className="flex items-center justify-center">
          <Upload className="h-8 w-8 text-gray-400 mr-2" />
          <div className="text-left">
            <p className="text-sm text-gray-600">
              Drag and drop an Excel file here, or click to select
            </p>
            <p className="text-xs text-gray-500">
              Supports .xlsx and .xls files
            </p>
          </div>
        </div>
      </div>

      {selectedFile && (
        <div className="mt-2">
          <div className="flex items-center justify-between bg-gray-100 p-2 rounded-lg">
            <div className="flex items-center flex-1 min-w-0 mr-2">
              <FileUp className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0" />
              <span className="text-sm font-medium text-gray-700 truncate">
                {selectedFile.name}
              </span>
            </div>
            <button
              onClick={onProcessFile}
              className="px-3 py-1 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors flex-shrink-0"
            >
              Process
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileUpload;

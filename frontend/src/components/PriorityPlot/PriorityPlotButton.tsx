import React from 'react';
import { BarChart2 } from 'lucide-react';
import './PriorityPlot.css';
import { BlobFile } from './utils/fileProcessor';

interface PriorityPlotButtonProps {
  onClick: (file: BlobFile) => void;
  file: BlobFile;
  disabled?: boolean;
}

export const PriorityPlotButton: React.FC<PriorityPlotButtonProps> = ({ onClick, file, disabled = false }) => {
  return (
    <button
      onClick={() => onClick(file)}
      className="action-button priority-plot-button"
      title="Generate Priority Plot"
      disabled={disabled}
    >
      <BarChart2 className="action-icon" />
    </button>
  );
};

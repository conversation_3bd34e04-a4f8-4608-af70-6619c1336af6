import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Download, RefreshCw, Edit, Trash2, Plus, Check, X, ChevronDown, ChevronUp } from 'lucide-react';
import { Scatter } from 'react-chartjs-2';
import InitiativeDetailModal from './components/InitiativeDetailModal';
import InitiativeItem from './components/InitiativeItem';
import InitiativeEditForm from './components/InitiativeEditForm';
import NewInitiativeForm, { NewInitiativeButton } from './components/NewInitiativeForm';
import PlotLegend from './components/PlotLegend';
import {
  Chart as ChartJS,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import {
  BlobFile,
  getSampleInitiatives,
  fetchOutputFiles,
  processExcelFile as processExcelFileUtil
} from './utils/fileProcessor';
import { Initiative } from './utils/types';
import { getDimensionColor } from './utils/colorUtils';
import { useProjectEnv } from '../../hooks/useProjectEnv';
import './PriorityPlot.css';

// Register the required chart components
ChartJS.register(
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend
);

interface PriorityPlotComponentProps {
  selectedFile?: BlobFile | null;
}

const PriorityPlotComponent: React.FC<PriorityPlotComponentProps> = ({ selectedFile }) => {
  // Get project context
  const projectContext = useProjectEnv();
  const chartRef = useRef<any>(null);

  const [initiatives, setInitiatives] = useState<Initiative[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [processingFile, setProcessingFile] = useState<boolean>(false);
  const [files, setFiles] = useState<BlobFile[]>([]);
  const [hasSyntheticData, setHasSyntheticData] = useState<boolean>(false);
  const [selectedInitiative, setSelectedInitiative] = useState<Initiative | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState<boolean>(false);
  const [isAddingInitiative, setIsAddingInitiative] = useState<boolean>(false);
  const [isEditingInitiative, setIsEditingInitiative] = useState<boolean>(false);
  const [initiativeToEdit, setInitiativeToEdit] = useState<Initiative | null>(null);
  const [hoveredInitiativeId, setHoveredInitiativeId] = useState<string | null>(null);

  // Function to fetch files from the output container
  const fetchFiles = useCallback(async () => {
    if (!projectContext?.storageAccountName || !projectContext?.storageContainerOutput || !projectContext?.storageAccountSasToken) {
      console.warn("Skipping fetchFiles: Project context incomplete.");
      setError("Project context incomplete. Cannot fetch files.");
      setHasSyntheticData(true);
      setInitiatives(getSampleInitiatives());
      setIsLoading(false);
      setRefreshing(false);
      return;
    }

    console.log("Fetching files from output container...");
    setError(null);

    try {
      // Use project-specific storage account name and SAS token from ProjectContext
      const storageAccountName = projectContext.storageAccountName;
      const sasToken = projectContext.storageAccountSasToken;
      const outputContainerName = projectContext.storageContainerOutput;

      if (!storageAccountName || !sasToken || !outputContainerName) {
        throw new Error("Missing storage account details in project context");
      }

      // Log SAS token details for debugging (without revealing the full token)
      const sasTokenLength = sasToken.length;
      console.log(`SAS token length: ${sasTokenLength} characters`);
      console.log(`SAS token starts with: ${sasToken.substring(0, 10)}...`);
      console.log(`SAS token ends with: ...${sasToken.substring(sasTokenLength - 10)}`);
      console.log(`SAS token contains '?' character: ${sasToken.includes('?')}`);
      console.log(`SAS token starts with '?': ${sasToken.startsWith('?')}`);

      console.log(`Fetching files directly from OUTPUT container: ${outputContainerName}`);
      console.log(`Using storage account: ${storageAccountName}`);

      // Fetch files from the output container
      const fetchedOutputFiles = await fetchOutputFiles(
        storageAccountName,
        outputContainerName,
        sasToken
      );

      console.log(`Fetched ${fetchedOutputFiles.length} output files directly from Azure Storage.`);

      // Update the files state with the fetched files
      setFiles(fetchedOutputFiles);

      // If we successfully fetched files but don't have a selected file yet,
      // we'll still use sample data for the plot
      if (!selectedFile) {
        console.log('No file selected, using sample data');
        setHasSyntheticData(true);
        setInitiatives(getSampleInitiatives());
      }

    } catch (err) {
      console.error('Error fetching files via direct Azure Storage access:', err);

      // Provide more detailed error message
      let errorMessage = 'Error fetching files from output container';
      if (err instanceof Error) {
        errorMessage += `: ${err.message}`;

        // Add specific guidance for common errors
        if (err.message.includes('Failed to fetch')) {
          errorMessage += '. This may be due to network connectivity issues, CORS configuration, or an invalid SAS token.';
        } else if (err.message.includes('CORS')) {
          errorMessage += '. The storage account may not allow requests from this origin. Please check CORS settings in the Azure Portal.';
        } else if (err.message.includes('401')) {
          errorMessage += '. The SAS token may be invalid or expired.';
        } else if (err.message.includes('404')) {
          errorMessage += '. The storage account or container may not exist.';
        }
      }

      setError(errorMessage);
      setHasSyntheticData(true);

      // Use sample data as fallback
      setInitiatives(getSampleInitiatives());
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  }, [projectContext?.storageAccountName, projectContext?.storageContainerOutput, projectContext?.storageAccountSasToken]);

  // Process Excel file from blob storage
  const processExcelFile = useCallback(async (file: BlobFile) => {
    if (!projectContext?.storageAccountName || !projectContext?.storageContainerOutput || !projectContext?.storageAccountSasToken) {
      console.warn("Skipping processExcelFile: Project context incomplete.");
      setError("Project context incomplete. Cannot process Excel file.");
      return;
    }

    setProcessingFile(true);
    setError(null);
    setHasSyntheticData(false);

    try {
      console.log(`Processing Excel file: ${file.name}`);

      // Get storage account details from project context
      const storageAccountName = projectContext.storageAccountName;
      const containerName = projectContext.storageContainerOutput;
      const sasToken = projectContext.storageAccountSasToken;

      if (!storageAccountName || !containerName || !sasToken) {
        throw new Error("Missing storage account details in project context");
      }

      console.log(`Processing Excel file from storage account: ${storageAccountName}`);
      console.log(`Container: ${containerName}`);
      console.log(`SAS token starts with: ${sasToken.substring(0, 10)}...`);

      // Process the Excel file using our utility function
      const parsedInitiatives = await processExcelFileUtil(file, storageAccountName, containerName, sasToken);

      console.log(`Parsed ${parsedInitiatives.length} initiatives from Excel file`);

      if (parsedInitiatives.length > 0) {
        setInitiatives(parsedInitiatives);
        setHasSyntheticData(false);
      } else {
        // If no initiatives were parsed, use sample data
        console.warn('No valid initiatives found in Excel file, using sample data');
        setInitiatives(getSampleInitiatives());
        setHasSyntheticData(true);
        setError('No initiatives found in the Excel file. The file should contain a "Near_Term" or "Near_Future" sheet with properly formatted data. Please check the file format and try again.');
      }
    } catch (err) {
      console.error('Error processing Excel file:', err);

      // Provide more detailed error message
      let errorMessage = `Error processing Excel file`;
      if (err instanceof Error) {
        errorMessage += `: ${err.message}`;

        // Add specific guidance for common errors
        if (err.message.includes('Failed to fetch')) {
          errorMessage += '. This may be due to network connectivity issues, CORS configuration, or an invalid SAS token.';
        } else if (err.message.includes('Failed to get readable stream')) {
          errorMessage += '. The file may be corrupted or inaccessible.';
        }
      }

      setError(errorMessage);

      // Use sample data as fallback
      setInitiatives(getSampleInitiatives());
      setHasSyntheticData(true);
    } finally {
      setProcessingFile(false);
    }
  }, [projectContext?.storageAccountName, projectContext?.storageContainerOutput, projectContext?.storageAccountSasToken]);

  // Initial fetch and periodic refresh
  useEffect(() => {
    // Initial fetch
    fetchFiles();

    // Set up periodic refresh every 10 seconds
    const refreshInterval = setInterval(() => {
      console.log('Periodic refresh of file list...');
      fetchFiles();
    }, 10000);

    // Clean up interval on unmount
    return () => clearInterval(refreshInterval);
  }, [fetchFiles]);

  // Process selected file when it changes
  useEffect(() => {
    if (selectedFile && selectedFile.name.toLowerCase().endsWith('.xlsx')) {
      console.log('Selected file changed, processing:', selectedFile.name);
      processExcelFile(selectedFile);
    }
  }, [selectedFile, processExcelFile]);

  // Calculate business impact as a function of business value, feasibility and effort
  const calculateBusinessImpact = (businessValue: number, feasibility: number, effort: number) => {
    // Simple formula: higher value and feasibility with lower effort = higher impact
    // For our Excel data with higher ranges, we need to scale appropriately
    return (businessValue * feasibility) / (Math.max(1, effort / 10));
  };

  // Prepare data for the chart
  const chartData = {
    datasets: [
      {
        label: 'Initiatives',
        data: initiatives.map(i => ({
          x: i.businessValue,
          y: i.feasibility,
          r: Math.max(8, calculateBusinessImpact(i.businessValue, i.feasibility, i.effort) / 30), // Larger bubbles to match screenshot
          initiative: i.initiative,
          dimension: i.dimension,
          criteria: i.criteria,
          effort: i.effort,
          impact: calculateBusinessImpact(i.businessValue, i.feasibility, i.effort)
        })),
        backgroundColor: initiatives.map(i => {
          // Get the color from colorUtils
          const color = getDimensionColor(i.dimension, i.criteria);

          // Convert to a lighter version with 0.7 opacity
          const rgbaMatch = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
          if (rgbaMatch) {
            return `rgba(${rgbaMatch[1]}, ${rgbaMatch[2]}, ${rgbaMatch[3]}, 0.7)`;
          }

          // Fallback if color format is unexpected
          return 'rgba(128, 128, 128, 0.7)';
        }),
        borderColor: initiatives.map(i => {
          // Use the color directly from colorUtils for borders
          return getDimensionColor(i.dimension, i.criteria);
        }),
        borderWidth: 2, // Thicker border to match screenshot
        pointRadius: initiatives.map(i => Math.max(8, calculateBusinessImpact(i.businessValue, i.feasibility, i.effort) / 30)),
        pointHoverRadius: initiatives.map(i => Math.max(10, calculateBusinessImpact(i.businessValue, i.feasibility, i.effort) / 30) + 2),
        pointHoverBackgroundColor: 'rgba(108, 178, 235, 0.6)',
        pointHoverBorderColor: 'rgba(108, 178, 235, 1)',
      }
    ]
  };

  // Handle initiative click
  const handleInitiativeClick = (initiative: Initiative) => {
    setSelectedInitiative(initiative);
    setIsDetailModalOpen(true);
  };

  // Handle initiative hover
  const handleInitiativeHover = (initiative: Initiative | null) => {
    setHoveredInitiativeId(initiative ? `${initiative.initiative}-${initiative.dimension}` : null);
  };

  // Handle initiative edit
  const handleInitiativeEdit = (initiative: Initiative) => {
    setInitiativeToEdit(initiative);
    setIsEditingInitiative(true);
  };

  // Handle save edited initiative
  const handleSaveEditedInitiative = (editedInitiative: Initiative) => {
    // Update the initiative in the list
    const updatedInitiatives = initiatives.map(initiative =>
      initiative === initiativeToEdit ? editedInitiative : initiative
    );

    setInitiatives(updatedInitiatives);
    setIsEditingInitiative(false);
    setInitiativeToEdit(null);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setIsEditingInitiative(false);
    setInitiativeToEdit(null);
  };

  // Handle add new initiative
  const handleAddInitiative = () => {
    setIsAddingInitiative(true);
  };

  // Handle save new initiative
  const handleSaveNewInitiative = (newInitiative: Initiative) => {
    setInitiatives([...initiatives, newInitiative]);
    setIsAddingInitiative(false);
  };

  // Handle cancel add
  const handleCancelAdd = () => {
    setIsAddingInitiative(false);
  };

  // Handle delete initiative
  const handleDeleteInitiative = (initiativeToDelete: Initiative) => {
    const updatedInitiatives = initiatives.filter(
      initiative => initiative !== initiativeToDelete
    );
    setInitiatives(updatedInitiatives);
  };

  // Chart options
  const chartOptions: ChartOptions<'scatter'> = {
    responsive: true,
    maintainAspectRatio: false,
    onClick: (_, elements) => {
      if (elements.length > 0) {
        const index = elements[0].index;
        handleInitiativeClick(initiatives[index]);
      }
    },
    plugins: {
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#333',
        borderColor: '#ddd',
        borderWidth: 1,
        padding: 12,
        cornerRadius: 6,
        displayColors: false,
        callbacks: {
          title: (context) => {
            const dataPoint = context[0].raw as any;
            return dataPoint.initiative || 'Initiative';
          },
          label: (context) => {
            const dataPoint = context.raw as any;
            return [
              `Dimension: ${dataPoint.dimension || ''}`,
              `Criteria: ${dataPoint.criteria || ''}`,
              `Business Value: ${dataPoint.x}`,
              `Feasibility: ${dataPoint.y}`,
              `Effort: ${dataPoint.effort || 0}`,
              `Impact Score: ${dataPoint.impact ? dataPoint.impact.toFixed(1) : 0}`
            ];
          }
        }
      },
      legend: {
        display: true,
        position: 'top',
        labels: {
          color: '#333',
          font: {
            size: 12
          },
          generateLabels: () => {
            // Create a legend entry for each unique dimension
            const uniqueDimensions = [...new Set(initiatives.map(i => i.dimension))];
            return uniqueDimensions.map((dimension, index) => {
              // Get a representative color for this dimension
              // Use the first criteria found for this dimension, or a default
              const initiativeWithDimension = initiatives.find(i => i.dimension === dimension);
              const criteria = initiativeWithDimension?.criteria || 'default';

              // Get the color from colorUtils
              const color = getDimensionColor(dimension, criteria);

              // Create a lighter version for fill
              const rgbaMatch = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);
              const fillColor = rgbaMatch
                ? `rgba(${rgbaMatch[1]}, ${rgbaMatch[2]}, ${rgbaMatch[3]}, 0.7)`
                : 'rgba(128, 128, 128, 0.7)';

              return {
                text: dimension || 'Unknown',
                fillStyle: fillColor,
                strokeStyle: color,
                lineWidth: 1,
                hidden: false,
                index
              };
            });
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Business Value',
          color: '#333',
          font: {
            size: 14,
            weight: 'bold'
          }
        },
        min: 0,
        max: 110, // Match the screenshot
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          color: '#666',
          stepSize: 20
        }
      },
      y: {
        title: {
          display: true,
          text: 'Feasibility',
          color: '#333',
          font: {
            size: 14,
            weight: 'bold'
          }
        },
        min: 0,
        max: 110, // Match the screenshot
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          color: '#666',
          stepSize: 20
        }
      }
    }
  };

  // Handle file download
  const handleDownload = () => {
    if (chartRef.current) {
      const url = chartRef.current.toBase64Image();
      const link = document.createElement('a');
      link.download = 'priority-plot.png';
      link.href = url;
      link.click();
    } else {
      const canvas = document.querySelector('canvas');
      if (canvas) {
        const link = document.createElement('a');
        link.download = 'initiative-analysis.png';
        link.href = canvas.toDataURL('image/png');
        link.click();
      }
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchFiles();
  };



  return (
    <div className="priority-plot-container">
      <div className="priority-plot-header">
        <div className="priority-plot-title">
          <h1>
            Initiative Analysis
          </h1>
          {/* Removed file name display */}
        </div>
        <div className="priority-plot-actions">
          <button
            onClick={handleRefresh}
            className="refresh-button"
            disabled={refreshing || processingFile}
          >
            <RefreshCw size={18} className={(refreshing || processingFile) ? 'spinning' : ''} />
            {processingFile ? 'Processing...' : 'Refresh Data'}
          </button>
          <button
            onClick={handleDownload}
            className="download-button"
            disabled={processingFile || hasSyntheticData}
          >
            <Download size={18} />
            Download Chart
          </button>
          {files.length > 0 && (
            <div className="file-count">
              {files.length} Excel file{files.length !== 1 ? 's' : ''} available
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="error-banner">
          <div className="error-message">
            {error}
          </div>
        </div>
      )}

      {processingFile && (
        <div className="processing-overlay">
          <div className="processing-message">
            <RefreshCw size={24} className="spinning" />
            <span>Processing Excel file...</span>
          </div>
        </div>
      )}

      <div className="priority-plot-content">
        {/* Initiative Details - Left Panel */}
        <div className="priority-plot-left-panel">
          <div className="priority-plot-card">
            <h2>Initiative Details</h2>
            {isLoading || processingFile ? (
              <div className="loading-message">Loading initiative data...</div>
            ) : error ? (
              <div className="error-message">{error}</div>
            ) : (
              <div className="initiatives-list">
                {isAddingInitiative ? (
                  <NewInitiativeForm
                    onSave={handleSaveNewInitiative}
                    onCancel={handleCancelAdd}
                  />
                ) : (
                  <NewInitiativeButton onClick={handleAddInitiative} />
                )}

                {initiatives.length === 0 ? (
                  <div className="no-initiatives">
                    <h3>No initiatives to display</h3>
                    <p>No data available. Try refreshing or selecting a different file.</p>
                  </div>
                ) : (
                  <div>
                    {initiatives.map((initiative, index) => {
                      const businessImpact = calculateBusinessImpact(
                        initiative.businessValue,
                        initiative.feasibility,
                        initiative.effort
                      );

                      if (isEditingInitiative && initiativeToEdit === initiative) {
                        return (
                          <InitiativeEditForm
                            key={index}
                            initiative={initiative}
                            onSave={handleSaveEditedInitiative}
                            onCancel={handleCancelEdit}
                          />
                        );
                      }

                      return (
                        <InitiativeItem
                          key={index}
                          initiative={initiative}
                          businessImpact={businessImpact}
                          onClick={handleInitiativeClick}
                          onEdit={() => handleInitiativeEdit(initiative)}
                          onDelete={() => handleDeleteInitiative(initiative)}
                          onHover={handleInitiativeHover}
                          isHovered={hoveredInitiativeId === `${initiative.initiative}-${initiative.dimension}`}
                        />
                      );
                    })}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Chart - Right Panel */}
        <div className="priority-plot-right-panel">
          <div className="priority-plot-card chart-card">
            <h2>Priority Plot</h2>
            <div className="chart-container">
              <Scatter ref={chartRef} data={chartData} options={chartOptions} />
            </div>
          </div>
          <PlotLegend />
        </div>
      </div>

      {/* Initiative Detail Modal */}
      <InitiativeDetailModal
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        initiative={selectedInitiative}
        calculateBusinessImpact={calculateBusinessImpact}
      />
    </div>
  );
};

export default PriorityPlotComponent;

import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import './PriorityPlot.css';
import PriorityPlotComponent from './PriorityPlotComponent';
import { BlobFile } from './utils/fileProcessor';

interface PriorityPlotModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedFile?: BlobFile | null;
}

export const PriorityPlotModal: React.FC<PriorityPlotModalProps> = ({ isOpen, onClose, selectedFile }) => {
  useEffect(() => {
    if (isOpen) {
      // Prevent body scrolling when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto';
    }

    return () => {
      // Cleanup: ensure body scrolling is restored when component unmounts
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="priority-plot-modal-overlay">
      <div className="priority-plot-modal">
        <button className="priority-plot-close-button absolute-close" onClick={onClose} aria-label="Close">
          <X size={28} strokeWidth={2.5} />
        </button>
        <div className="priority-plot-modal-header">
          <h2>
            Priority Plot
            {/* Removed file name display */}
          </h2>
        </div>
        <div className="priority-plot-modal-content">
          <PriorityPlotComponent selectedFile={selectedFile} />
        </div>
      </div>
    </div>
  );
};

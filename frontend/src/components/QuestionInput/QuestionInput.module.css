/* frontend/src/components/QuestionInput/QuestionInput.module.css */
.questionInputContainer {
  height: auto;
  min-height: 100px;
  position: relative;
  width: 100%;
  background: #ffffff;
  box-shadow:
    0px 8px 16px rgba(0, 0, 0, 0.14),
    0px 0px 2px rgba(0, 0, 0, 0.12);
  border-radius: 8px;
}

.questionInputTextArea {
  width: 100%;
  line-height: 40px;
  margin-top: 10px;
  margin-bottom: 10px;
  margin-left: 12px;
  margin-right: 12px;
}

.questionInputSendButtonContainer {
  position: absolute;
  right: 24px;
  bottom: 20px;
}

.questionInputSendButton {
  width: 24px;
  height: 23px;
}

.questionInputSendButtonDisabled {
  /* opacity: 0.4; */
  width: 24px;
  height: 23px;
  background: none;
  color: #424242;
}

.questionInputBottomBorder {
  position: absolute;
  width: 100%;
  height: 4px;
  left: 0%;
  bottom: 0%;
  background: radial-gradient(106.04% 106.06% at 100.1% 90.19%, #0f6cbd 33.63%, #8dddd8 100%);
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.questionInputOptionsButton {
  cursor: pointer;
  width: 27px;
  height: 30px;
}

@media (max-width: 480px) {
  .questionInputContainer {
    left: 16.5%;
  }
}

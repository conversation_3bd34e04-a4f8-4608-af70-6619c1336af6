import React, { useEffect, useState } from 'react';
import { Stack, Text, PrimaryButton, DefaultButton } from '@fluentui/react';

interface IconTest {
  name: string;
  iconName: string;
  expectedUnicode: string;
}

const IconDiagnostic: React.FC = () => {
  const [fontLoaded, setFontLoaded] = useState<boolean>(false);
  
  const iconTests: IconTest[] = [
    { name: 'ChevronLeft', iconName: 'ChevronLeft', expectedUnicode: '\uE76B' },
    { name: 'ChevronRight', iconName: 'ChevronRight', expectedUnicode: '\uE76C' },
    { name: 'Add', iconName: 'Add', expectedUnicode: '\uE710' },
    { name: 'Delete', iconName: 'Delete', expectedUnicode: '\uE74D' },
    { name: 'Edit', iconName: 'Edit', expectedUnicode: '\uE70F' },
    { name: 'Save', iconName: 'Save', expectedUnicode: '\uE74E' }
  ];

  useEffect(() => {
    // Check if FabricMDL2Icons font is loaded
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        const loaded = document.fonts.check('16px FabricMDL2Icons');
        setFontLoaded(loaded);
        console.log('FabricMDL2Icons font loaded:', loaded);
        
        // List all loaded fonts
        const loadedFonts: string[] = [];
        document.fonts.forEach((font) => {
          loadedFonts.push(`${font.family} - ${font.status}`);
        });
        console.log('All loaded fonts:', loadedFonts);
      });
    }
  }, []);

  return (
    <Stack tokens={{ childrenGap: 20 }} style={{ padding: 20 }}>
      <Text variant="xLarge">Icon Loading Diagnostic</Text>
      
      <Stack tokens={{ childrenGap: 10 }}>
        <Text>Font Status: FabricMDL2Icons is {fontLoaded ? 'loaded' : 'not loaded'}</Text>
        
        <Text variant="large">Button Icons Test:</Text>
        <Stack horizontal tokens={{ childrenGap: 10 }}>
          <PrimaryButton 
            text="Back (ChevronLeft)" 
            iconProps={{ iconName: 'ChevronLeft' }}
          />
          <DefaultButton 
            text="Forward (ChevronRight)" 
            iconProps={{ iconName: 'ChevronRight' }}
          />
          <DefaultButton 
            text="Add" 
            iconProps={{ iconName: 'Add' }}
          />
          <DefaultButton 
            text="Delete" 
            iconProps={{ iconName: 'Delete' }}
          />
        </Stack>
        
        <Text variant="large">Raw Icon Elements:</Text>
        <Stack tokens={{ childrenGap: 5 }}>
          {iconTests.map((test) => (
            <Stack key={test.name} horizontal tokens={{ childrenGap: 10 }}>
              <i 
                className={`ms-Icon ms-Icon--${test.iconName}`}
                style={{ fontSize: 20, width: 30 }}
                aria-hidden="true"
              />
              <Text>{test.name}: Expected Unicode {test.expectedUnicode}</Text>
            </Stack>
          ))}
        </Stack>
      </Stack>
    </Stack>
  );
};

export default IconDiagnostic;
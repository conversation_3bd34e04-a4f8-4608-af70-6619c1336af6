import React from 'react';
import { CommandBar, ICommandBarItemProps } from '@fluentui/react/lib/CommandBar';
import { IButtonProps } from '@fluentui/react/lib/Button';
import styles from './Header.module.css';

interface HeaderProps {
  project?: {
    name: string;
    description?: string;
  };
}

const Header: React.FC<HeaderProps> = ({ project }) => {
  // Define the items for the settings/admin dropdown menu
  const commandBarItems: ICommandBarItemProps[] = [
    // {
    //   key: 'userManagement',
    //   text: 'User Management',
    //   iconProps: { iconName: 'People' },
    // },
    // {
    //   key: 'globalSettings',
    //   text: 'Global Settings',
    //   iconProps: { iconName: 'Settings' },
    // },
  ];

  // Define the user profile button that triggers the dropdown
  const farItems: ICommandBarItemProps[] = [
    {
      key: 'userProfile',
      text: 'User',
      iconProps: { iconName: 'Contact' },
      subMenuProps: {
        items: commandBarItems,
      },
    },
  ];

  return (
    <header className={styles.header}>
      <div className={styles.titleArea}>
        <h2>{project?.name || 'Project'}</h2>
      </div>
      <div className={styles.actions}>
        {/* We apply the .commandBar style to this container */}
        <div className={styles.commandBar}>
          <CommandBar
            items={[]}
            farItems={farItems}
            ariaLabel="User settings"
          />
        </div>
      </div>
    </header>
  );
};

export default Header;

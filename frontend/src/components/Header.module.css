.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px; /* Adjusted padding for CommandBar alignment */
  background-color: white;
  border-bottom: 1px solid #eaeaea;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 48px;
}

.titleArea h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 500;
  color: #333;
}

.actions {
  display: flex;
  align-items: center;
}

/* This is the new container for the CommandBar */
.commandBar {
  border: none; /* Removes any default border from the command bar container */
}

/* Target the buttons within our CommandBar */
.commandBar button {
  background-color: transparent !important;
  border: none !important; /* Removes the button border */
  outline: none !important; /* Removes the outline on focus */
}

/* Use :global to override Fluent UI's default dropdown menu styles */
:global(.ms-ContextualMenu-container) {
  border-radius: 8px;
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

:global(.ms-ContextualMenu-item) {
  font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

:global(.ms-ContextualMenu-item:hover) {
  background-color: #f0f0f0;
}

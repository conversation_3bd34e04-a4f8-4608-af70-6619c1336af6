// frontend/src/components/FileManagement/FileManagement.tsx
import React, { useState, useEffect, useCallback, useRef, useContext } from 'react'; // Added useContext
import { useParams } from 'react-router-dom'; // Added for projectId
import { useDropzone, DropzoneOptions } from 'react-dropzone';
import { Trash2, Upload, File, ChevronRight, RefreshCw, AlertTriangle } from 'lucide-react';
import InfoBubble from './InfoBubble';
import { BlobServiceClient, ContainerClient } from '@azure/storage-blob'; // Keep for listing/deleting if needed
import './FileManagement.css';
import axios from 'axios';
import { fetchConfig, StorageConfig } from '../../services/configService';
import styles from './FileManagement.module.css';
import { useWebSocketUpdates, BlobUpdatesData, IndexUpdatesData } from '../../hooks/websockets';
import { ProjectContext } from '../../pages/layout/ProjectLayout'; // Import ProjectContext

// Get API base URL from environment variable or use default
// Use the proxy endpoint in the main application instead of directly connecting to port 8000
const API_BASE_URL = '/api/search'

// Helper function to normalize filenames for case-insensitive comparison
const normalizeFilename = (filename: string): string => {
  if (!filename) return ''
  return filename.toLowerCase().trim()
}

// Helper function to check if a filename is in a list (case insensitive)
const isFileInList = (filename: string, filesList: string[]): boolean => {
  if (!filename || !filesList || !filesList.length) return false
  const normalizedFilename = normalizeFilename(filename)
  return filesList.some(indexedFile => normalizeFilename(indexedFile) === normalizedFilename)
}

interface FileManagementProps {
  onToggle?: (isOpen: boolean) => void
}

interface UploadedFile {
  name: string
  size: number
  type: string
  indexed?: boolean
}

interface IndexerStatus {
  lastResult?: {
    status: string
    errorMessage?: string
    itemCount: number
    failedItemCount: number
  }
}

interface IndexStatistics {
  documentCount: number
  storageSize: number
}

// Keep interface for potential future use, though state is removed
interface SearchConfig {
  searchServiceName: string
  indexName: string
  indexerName: string
  apiVersion: string
}

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (date: Date | null): string => {
  if (!date) return ''
  return date.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  })
}

const FileManagement: React.FC<FileManagementProps> = ({ onToggle }): JSX.Element => {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [uploading, setUploading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [configValid, setConfigValid] = useState<boolean>(false)
  const [isInitialized, setIsInitialized] = useState<boolean>(false)
  const [isOpen, setIsOpen] = useState(true)
  const [isDragging, setIsDragging] = useState(false)
  const [containerClient, setContainerClient] = useState<ContainerClient | null>(null)
  const [storageConfig, setStorageConfig] = useState<StorageConfig>({
    account_name: '',
    container_name: '',
    container_sas_token: ''
  })
  const [isConfigValid, setIsConfigValid] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(0);
  // const { projectId } = useParams<{ projectId: string }>(); // Remove this line
  const projectContext = useContext(ProjectContext); // Get project context
  const projectId = projectContext?.projectId; // Extract projectId from context

  // Use the WebSocket hook for real-time updates, passing projectId
  const {
    data: blobData,
    isConnected: isBlobWsConnected,
    error: blobWsError,
    lastUpdate: blobLastUpdate,
    wsRef // Get the WebSocket reference so we can send commands
  } = useWebSocketUpdates<BlobUpdatesData>({
    type: 'blob', // This connects to /ws/blobs endpoint
    projectId: projectId, // Pass projectId
    autoReconnect: true,
    maxRetries: 5
  })

  const {
    data: indexData,
    isConnected: isIndexWsConnected,
    error: indexWsError,
    lastUpdate: indexLastUpdate
  } = useWebSocketUpdates<IndexUpdatesData>({
    type: 'index',
    projectId: projectId // Pass projectId
  })

  // Combine connection status
  const isWsConnected = isBlobWsConnected || isIndexWsConnected

  // Removed global searchConfig state and searchConfigLoaded state - using projectContext instead

  // Add state for indexed files
  const [indexedFiles, setIndexedFiles] = useState<string[]>([])
  const [indexingError, setIndexingError] = useState<string | null>(null)
  const [lastIndexStatusUpdate, setLastIndexStatusUpdate] = useState<Date | null>(null)

  // Add a new state for file filtering
  const [indexFilter, setIndexFilter] = useState<'all' | 'indexed' | 'unindexed'>('all')

  // Add state for loading indicator
  const [isCheckingIndexStatus, setIsCheckingIndexStatus] = useState<boolean>(false)

  // Add state for reindexing
  const [reindexingFiles, setReindexingFiles] = useState<string[]>([])

  // Add this with your other state declarations
  const [indexedFilesCount, setIndexedFilesCount] = useState<number>(0)

  // Add a state to track refreshing status
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Add a state for indexing messages
  const [indexingMessage, setIndexingMessage] = useState<string | null>(null)
  const [indexStale, setIndexStale] = useState(false) // Track if index needs refresh

  // Removed fetchSearchConfig function and related useEffect hook

  // Notify parent component when open state changes
  useEffect(() => {
    onToggle?.(isOpen)
  }, [isOpen, onToggle])

  // Initialize configuration using project-specific storage account and SAS token
  useEffect(() => {
    const initConfig = async () => {
      try {
        // Log what we receive from ProjectContext
        console.log('FileManagement: ProjectContext state:', {
          hasProjectContext: Boolean(projectContext),
          projectId: projectContext?.projectId,
          storageAccountName: projectContext?.storageAccountName || '[missing]',
          storageAccountSasToken: projectContext?.storageAccountSasToken ? '[present]' : '[missing]',
          storageContainerUploads: projectContext?.storageContainerUploads || '[missing]'
        });

        // Use project-specific storage account and SAS token from ProjectContext
        if (projectContext && projectContext.storageAccountName && projectContext.storageAccountSasToken) {
          console.log('Using project-specific storage configuration');

          // Create a StorageConfig object using project-specific values
          const projectStorageConfig: StorageConfig = {
            account_name: projectContext.storageAccountName,
            container_name: projectContext.storageContainerUploads, // Use uploads container as default
            container_sas_token: projectContext.storageAccountSasToken
          };

          console.log('Initializing with project-specific storage config:', {
            hasAccountName: Boolean(projectStorageConfig.account_name),
            hasContainerName: Boolean(projectStorageConfig.container_name),
            hasSasToken: Boolean(projectStorageConfig.container_sas_token),
            accountName: projectStorageConfig.account_name,
            containerName: projectStorageConfig.container_name
          });

          setStorageConfig(projectStorageConfig);
          setIsInitialized(true);
          setError(null);
        } else {
          console.warn('Project-specific storage configuration not available, falling back to global config');
          
          // Log what's missing
          const missingItems = [];
          if (!projectContext) missingItems.push('ProjectContext');
          if (projectContext && !projectContext.storageAccountName) missingItems.push('storageAccountName');
          if (projectContext && !projectContext.storageAccountSasToken) missingItems.push('storageAccountSasToken');
          console.warn('Missing items:', missingItems);

          // Fall back to global config if project-specific values are not available
          const config = await fetchConfig();
          console.log('Fetched global configuration:', config);

          // Ensure we have a valid StorageConfig object
          const validStorageConfig: StorageConfig = {
            account_name: config.azure_storage?.account_name || '',
            container_name: projectContext?.storageContainerUploads || '',
            container_sas_token: config.azure_storage?.container_sas_token || ''
          };

          console.log('Initializing with global storage config as fallback:', {
            hasAccountName: Boolean(validStorageConfig.account_name),
            hasContainerName: Boolean(validStorageConfig.container_name),
            hasSasToken: Boolean(validStorageConfig.container_sas_token)
          });

          setStorageConfig(validStorageConfig);
          setIsInitialized(true);
        }
      } catch (error) {
        console.error('Error initializing storage configuration:', error);
        setError('Failed to load storage configuration');
        setIsInitialized(true);
      }
    };

    // Only initialize when ProjectContext is available and has required data
    if (projectContext && projectContext.projectId) {
      // Add a small delay to ensure context is fully populated
      const timer = setTimeout(() => {
        initConfig();
      }, 100); // 100ms delay
      
      return () => clearTimeout(timer);
    } else if (!projectContext) {
      // Set a timeout to prevent infinite loading state
      const timer = setTimeout(() => {
        if (!isInitialized) {
          console.warn('No project context available after timeout, initializing anyway');
          setIsInitialized(true);
        }
      }, 3000); // 3 seconds timeout

      return () => clearTimeout(timer);
    }
  }, [projectContext?.projectId, projectContext?.storageAccountName, projectContext?.storageAccountSasToken, isInitialized])

  // Validate configuration needed for operations (e.g., delete needs account + SAS)
  useEffect(() => {
    const validateConfig = () => {
      if (!storageConfig) {
        console.warn('Global storage configuration is not available')
        setConfigValid(false)
        // Don't set global error here, let operations check specific needs
        return
      }

      const { container_sas_token, account_name } = storageConfig

      const config = {
        hasSasToken: Boolean(container_sas_token?.trim()),
        hasStorageAccountName: Boolean(account_name?.trim())
      }

      const hasCoreConfig = config.hasSasToken && config.hasStorageAccountName

      console.log('FileManagement Core Configuration Validation:', {
        ...config,
        sasTokenLength: container_sas_token?.length || 0,
        storageAccountName: account_name || '[empty]',
      })

      setConfigValid(hasCoreConfig) // Validity now depends on account name and SAS token for delete op
      if (!hasCoreConfig) {
        const missingConfigs = []
        if (!config.hasSasToken) missingConfigs.push('SAS Token')
        if (!config.hasStorageAccountName) missingConfigs.push('Storage Account Name')
        const errorMsg = `Missing required configuration for file operations: ${missingConfigs.join(', ')}`
        console.warn('FileManagement configuration error:', errorMsg, {
          storageAccountName: account_name || '[empty]',
          sasTokenPresent: config.hasSasToken,
          containerName: storageConfig?.container_name || '[empty]'
        })
        setError(errorMsg) // Set error if core config for delete is missing
      } else {
        setError(null) // Clear error if core config is present
      }
    }

    if (isInitialized) {
      validateConfig()
    }
  }, [isInitialized, storageConfig])

  // Function to fetch indexed files list - updated to use projectContext
  const fetchIndexedFiles = useCallback(async () => {
    // Use index name and search service name from project context
    const projectIndexName = projectContext.searchIndex;
    const searchServiceName = projectContext.searchServiceName;

    if (!projectIndexName) {
      console.error('Project search index name is missing from context - cannot fetch indexed files');
      setIndexingError('Project search configuration is incomplete');
      // Return empty array to allow UI to continue loading
      setIndexedFiles([]);
      setIndexedFilesCount(0);
      return;
    }

    try {
      console.log('Fetching indexed files from project index:', projectIndexName);
      console.log('Using search service:', searchServiceName);
      setIsCheckingIndexStatus(true);

      // Get search API key from project context
      const searchApiKey = projectContext.searchApiKey;

      console.log('Search configuration:', {
        searchServiceName,
        hasApiKey: Boolean(searchApiKey),
        projectIndexName
      });

      // Check if we have the required search configuration
      if (!searchServiceName) {
        console.warn("Missing search service name in project context - using fallback");
        // Continue with fallback approach instead of throwing error
        // Return empty array to allow UI to continue loading
        setIndexedFiles([]);
        setIndexedFilesCount(0);
        setIndexingError('Missing search service configuration');
        return;
      }

      if (!searchApiKey) {
        console.warn("Missing search API key in project context - using fallback");
        // Continue with fallback approach instead of throwing error
        // Return empty array to allow UI to continue loading
        setIndexedFiles([]);
        setIndexedFilesCount(0);
        setIndexingError('Missing search API key configuration');
        return;
      }

      // Build the API URL with the index name - use the direct endpoint
      // Include the search service name if available
      let apiUrl = searchServiceName
        ? `/api/search/${searchServiceName}/list-indexed-files/${projectIndexName}`
        : `/api/search/list-indexed-files/${projectIndexName}`;

      // Add project ID as a query parameter to help the backend find the correct search service
      if (projectId) {
        apiUrl += `?projectId=${projectId}`;
      }

      console.log(`Making request to: ${apiUrl} with projectId: ${projectId}`);

      // Prepare headers - no need to include API key as the backend will handle authentication
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      try {
        const response = await axios.get(apiUrl, { headers });

        console.log('Response received:', {
          status: response.status,
          headers: response.headers,
          data: response.data
        });

        // Handle the new response format which contains {files: [...], count: number}
        if (response.data && response.data.files && Array.isArray(response.data.files)) {
          const filesList = response.data.files;
          const filesCount = response.data.count || filesList.length;

          console.log(
            `Found ${filesCount} indexed files:`,
            filesList.slice(0, 5),
            filesList.length > 5 ? `... and ${filesList.length - 5} more` : ''
          );

          // Store normalized and original filenames
          setIndexedFiles(filesList);
          setIndexedFilesCount(filesCount);
          setIndexingError(null);
          setLastIndexStatusUpdate(new Date());

          // Update existing files with their indexed status using case-insensitive comparison
          setFiles(prevFiles => {
            const updatedFiles = prevFiles.map(file => ({
              ...file,
              indexed: isFileInList(file.name, filesList)
            }));

            console.log(`Updated indexed status for ${prevFiles.length} files`);
            return updatedFiles;
          });
        } else if (response.data && response.data.error) {
          // Handle error response from backend
          console.warn('Error response from list-indexed-files endpoint:', response.data.error);

          // Set a more user-friendly error message based on the error
          const errorMsg = response.data.error;
          if (errorMsg.includes("not found")) {
            setIndexingError(`Search index not found. The application will continue to function.`);
          } else if (errorMsg.includes("Authentication error") || errorMsg.includes("API key")) {
            setIndexingError(`Search authentication error. The application will continue to function.`);
          } else {
            setIndexingError(`Server message: ${response.data.error}`);
          }

          // Return empty array to allow UI to continue loading
          setIndexedFiles([]);
          setIndexedFilesCount(0);
        } else {
          console.warn('Unexpected response format from list-indexed-files endpoint:', response.data);
          setIndexingError('Invalid response format from server');
          // Return empty array to allow UI to continue loading
          setIndexedFiles([]);
          setIndexedFilesCount(0);
        }
      } catch (axiosError) {
        // Handle Axios errors separately
        console.error('Axios error fetching indexed files:', axiosError);
        if (axios.isAxiosError(axiosError)) {
          console.error('Axios error details:', {
            message: axiosError.message,
            code: axiosError.code,
            response: axiosError.response
              ? {
                  status: axiosError.response.status,
                  data: axiosError.response.data,
                  headers: axiosError.response.headers
                }
              : 'No response',
            request: axiosError.request ? 'Request was made but no response received' : 'No request'
          });

          // Set a more user-friendly error message based on status code
          if (axiosError.code === 'ERR_NETWORK') {
            // Network error - likely server is down or unreachable
            console.log('Network error occurred, but WebSocket might still work. Continuing with empty indexed files list.');
            setIndexingError('Network error connecting to search service. WebSocket updates will still work if available.');
          } else if (axiosError.response?.status === 500) {
            // Check if there's a specific error message in the response
            const errorData = axiosError.response.data;
            if (errorData && typeof errorData === 'object' && 'error' in errorData) {
              const errorMsg = errorData.error;
              if (typeof errorMsg === 'string') {
                if (errorMsg.includes("project_id")) {
                  setIndexingError('Project ID configuration issue. The application will continue to function.');
                } else if (errorMsg.includes("not found")) {
                  setIndexingError('Search index not found. The application will continue to function.');
                } else if (errorMsg.includes("Authentication")) {
                  setIndexingError('Search authentication error. The application will continue to function.');
                } else {
                  setIndexingError('Server error while fetching indexed files. The application will continue to function.');
                }
              } else {
                setIndexingError('Server error while fetching indexed files. The application will continue to function.');
              }
            } else {
              setIndexingError('Server error while fetching indexed files. The application will continue to function.');
            }
          } else if (axiosError.response?.status === 404) {
            setIndexingError('Search index not found. The application will continue to function.');
          } else if (axiosError.response?.status === 401 || axiosError.response?.status === 403) {
            setIndexingError('Search authentication error. The application will continue to function.');
          } else {
            setIndexingError(`Network error: ${axiosError.message}`);
          }
        } else {
          setIndexingError(`Failed to fetch indexed files: ${axiosError instanceof Error ? axiosError.message : 'Unknown error'}`);
        }

        // Return empty array to allow UI to continue loading
        setIndexedFiles([]);
        setIndexedFilesCount(0);
      }
    } catch (error) {
      // Handle any other errors
      console.error('Unexpected error in fetchIndexedFiles:', error);
      if (error instanceof Error) {
        setIndexingError(`Error: ${error.message}`);
      } else {
        setIndexingError('An unknown error occurred');
      }

      // Return empty array to allow UI to continue loading
      setIndexedFiles([]);
      setIndexedFilesCount(0);
    } finally {
      setIsCheckingIndexStatus(false);
    }
  }, [projectContext.searchIndex, projectContext.searchServiceName, projectId]); // Depend on project context index name, search service name, and project ID

  // Use WebSocket data for indexed files
  useEffect(() => {
    if (indexData && Array.isArray(indexData)) {
      console.log('Received indexed files data from WebSocket:', indexData.length)

      // Update indexed files from WebSocket data
      setIndexedFiles(indexData)
      setIndexedFilesCount(indexData.length)
      setIndexingError(null)
      setLastIndexStatusUpdate(indexLastUpdate || new Date())

      // Update existing files with their indexed status
      setFiles(prevFiles => {
        const updatedFiles = prevFiles.map(file => ({
          ...file,
          indexed: isFileInList(file.name, indexData)
        }))

        console.log(`Updated indexed status for ${prevFiles.length} files via WebSocket`)
        return updatedFiles
      })
    } else if (isInitialized && configValid && !isIndexWsConnected) { // Removed projectContext.projectEnv?.SEARCH_INDEX check
      // Fallback to HTTP fetch if WebSocket is not connected
      console.log('WebSocket not connected yet, fetching indexed files via HTTP');

      // Use a timeout to prevent blocking the UI
      setTimeout(() => {
        try {
          fetchIndexedFiles().catch(err => {
            console.error('Error in delayed fetchIndexedFiles:', err);
            // Ensure we have at least empty arrays to work with
            setIndexedFiles([]);
            setIndexedFilesCount(0);

            // Show a more user-friendly error message
            if (err.message && err.message.includes('Network Error')) {
              console.log('Network error occurred, but WebSocket might still work. Continuing with empty indexed files list.');
              setIndexingError('Network error connecting to search service. WebSocket updates will still work if available.');
            }
          });
        } catch (err) {
          console.error('Exception in delayed fetchIndexedFiles:', err);
          // Ensure we have at least empty arrays to work with
          setIndexedFiles([]);
          setIndexedFilesCount(0);
        }
      }, 500);
    }
  }, [
    indexData,
    isInitialized,
    configValid,
    projectContext.searchIndex,
    projectContext.searchServiceName,
    projectId,
    isIndexWsConnected,
    indexLastUpdate,
    fetchIndexedFiles
  ]);

  // Initialize blob service only when config is valid
  // Use WebSocket blob data when available
  useEffect(() => {
    if (blobData && Array.isArray(blobData)) {
      console.log('Received blob data from WebSocket:', blobData.length, blobData)

      // Map WebSocket blob data to our file format
      const blobFiles = blobData.map(blob => ({
        name: blob.name,
        size: blob.size,
        type: blob.type || 'unknown',
        indexed: blob.indexed || isFileInList(blob.name, indexedFiles)
      }))

      console.log('Updating files state with WebSocket data:', blobFiles)

      // Only update files if we have WebSocket data or if we haven't fetched files via HTTP yet
      if (blobFiles.length > 0 || files.length === 0) {
        setFiles(blobFiles)
        setError(null)

        // Show a notification that files were updated
        setIndexingMessage(`File list updated via WebSocket (${blobFiles.length} files)`)
        setTimeout(() => setIndexingMessage(null), 3000)
      } else {
        console.log('Received empty WebSocket data but keeping existing files:', files.length)
      }
    }
  }, [blobData, indexedFiles, files.length])

  // Initialize blob service only when config is valid - fallback if WebSocket isn't working
  // This useEffect might need adjustment if listing blobs directly is still needed
  // For now, it initializes the client but doesn't list blobs if WS is connected
  useEffect(() => {
    if (!configValid || !isInitialized || !storageConfig || !projectContext.storageContainerUploads) {
      console.log('Skipping blob service initialization - configuration invalid, not initialized, or project context missing:', {
        configValid,
        isInitialized,
        hasStorageConfig: Boolean(storageConfig),
        hasProjectUploadContainer: Boolean(projectContext.storageContainerUploads),
        storageConfig: {
          hasAccountName: Boolean(storageConfig?.account_name),
          hasSasToken: Boolean(storageConfig?.container_sas_token)
        }
      })
      return
    }

    // Skip HTTP listing if we already have data from WebSocket or if WebSocket is connected
    // We'll let the WebSocket update handle the file list, even if it's empty
    if (isBlobWsConnected && blobData && Array.isArray(blobData)) {
      console.log('Already received blob data from WebSocket, skipping HTTP fetch for blob list')
      return
    }

    // Define the initialization function first
    const initializeBlobService = async (retryCount = 0): Promise<void> => {
      try {
        if (!projectId) {
          console.error('Project ID is missing, cannot fetch files');
          setError('Project ID is missing. Cannot fetch files.');
          return;
        }

        // Use project-specific storage account name and SAS token from ProjectContext
        const storageAccountName = projectContext.storageAccountName;
        const sasToken = projectContext.storageAccountSasToken;

        if (!storageAccountName || !sasToken) {
          throw new Error("Missing storage account name or SAS token in project context");
        }

        const containerName = projectContext.storageContainerUploads;

        if (!containerName) {
          throw new Error("Missing uploads container name in project context");
        }

        console.log(`Initializing blob service with project ID: ${projectId}`);
        console.log(`Connecting directly to storage account: ${storageAccountName}, container: ${containerName}`);

        try {
          // Create a BlobServiceClient
          const blobServiceClient = new BlobServiceClient(
            `https://${storageAccountName}.blob.core.windows.net${sasToken.startsWith('?') ? sasToken : '?' + sasToken}`
          );

          // Get a reference to the container
          const containerClient = blobServiceClient.getContainerClient(containerName);

          // List blobs in the container
          console.log(`Fetching blobs directly from container: ${containerName}...`);
          const blobItems = containerClient.listBlobsFlat();

          const fetchedFiles: UploadedFile[] = [];
          for await (const blob of blobItems) {
            fetchedFiles.push({
              name: blob.name,
              size: blob.properties.contentLength || 0,
              type: blob.properties.contentType || 'unknown',
              indexed: isFileInList(blob.name, indexedFiles)
            });
          }

          console.log(`Successfully fetched ${fetchedFiles.length} files directly from Azure Storage`);
          setFiles(fetchedFiles);
          setError(null);

          // Show a notification that files were updated
          setIndexingMessage(`File list updated (${fetchedFiles.length} files)`);
          setTimeout(() => setIndexingMessage(null), 3000);

          // Store container client for potential future use (like delete)
          setContainerClient(containerClient);
        } catch (error) {
          console.error('Error fetching files directly from Azure Storage:', error);
          if (retryCount < 3) {
            const delay = Math.pow(2, retryCount) * 1000;
            console.warn(`Retry ${retryCount + 1}/3 - File fetch failed, retrying in ${delay / 1000}s`);
            await new Promise(resolve => setTimeout(resolve, delay));
            return await initializeBlobService(retryCount + 1);
          }
          throw error;
        }

      } catch (error: unknown) {
        console.error('Error initializing blob service in FileManagement:', error)
        setError(`Error accessing project storage: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    // If WebSocket is connected but we haven't received data yet, wait a bit
    if (isBlobWsConnected) {
      console.log('WebSocket is connected but no data received yet, will fetch via HTTP if no data arrives')
      // Set a short timeout to wait for WebSocket data before falling back to HTTP
      setTimeout(() => {
        if (files.length === 0 && blobData === null) {
          console.log('No WebSocket data received after timeout, fetching via HTTP as fallback')
          initializeBlobService()
        }
      }, 2000) // Wait 2 seconds for WebSocket data
      return
    }

    initializeBlobService()
  }, [configValid, isInitialized, storageConfig, indexedFiles, isBlobWsConnected, blobData, projectContext.storageContainerUploads, files.length])


  // Handle file-specific events from WebSocket data
  useEffect(() => {
    // Process any file status changes from WebSocket messages
    if (blobData && Array.isArray(blobData)) {
      const currentFilenames = new Set(files.map(f => f.name.toLowerCase()))
      const incomingFilenames = new Set(blobData.map(f => f.name.toLowerCase()))

      // Check for deleted files
      const deletedFiles = [...currentFilenames].filter(name => !incomingFilenames.has(name))
      if (deletedFiles.length > 0) {
        console.log(`Detected ${deletedFiles.length} deleted files via WebSocket`)
        deletedFiles.forEach(fileName => {
          // Show deletion message
          setIndexingMessage(`File ${fileName} was deleted from storage`)
          setTimeout(() => setIndexingMessage(null), 3000)
        })
      }

      // Check for new files
      const newFiles = [...incomingFilenames].filter(name => !currentFilenames.has(name))
      if (newFiles.length > 0) {
        console.log(`Detected ${newFiles.length} new files via WebSocket`)
        if (newFiles.length === 1) {
          setIndexingMessage(`New file detected: ${newFiles[0]}`)
          setTimeout(() => setIndexingMessage(null), 3000)
        } else {
          setIndexingMessage(`${newFiles.length} new files detected`)
          setTimeout(() => setIndexingMessage(null), 3000)
        }
      }
    }
  }, [blobData, files])

  // Add function to request reindexing of a file - updated to use direct endpoint
  const requestReindex = useCallback(async (fileName: string) => {
    // Use indexer name from project context
    const projectIndexerName = projectContext.searchIndexerName;

    if (!projectIndexerName) {
      console.error('Project indexer name is missing from context - cannot request reindex');
      setError('Project search configuration is incomplete (missing indexer name)');
      return false;
    }

    try {
      console.log(`Requesting reindex for file: ${fileName} using indexer: ${projectIndexerName}`);
      setReindexingFiles(prev => [...prev, fileName]);

      // Include the search service name if available
      const apiUrl = projectContext.searchServiceName
        ? `/api/search/${projectContext.searchServiceName}/reindex-file/${projectIndexerName}`
        : `/api/search/reindex-file/${projectIndexerName}`;

      // Add project ID as a query parameter to help the backend find the correct search service
      const urlWithParams = projectId ? `${apiUrl}?projectId=${projectId}` : apiUrl;

      console.log(`Making reindex request to: ${urlWithParams} for file ${fileName}`);
      const response = await axios.post(
        urlWithParams, // Use direct endpoint with indexer name
        { filename: fileName },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )

      console.log('Reindex response:', response.data)

      // With WebSockets, we don't need to poll anymore
      // The server will send a notification when indexing is complete
      setIndexingMessage(`Reindexing ${fileName}... You'll be notified when complete.`)
      setTimeout(() => setIndexingMessage(null), 5000)

      return true
    } catch (error) {
      console.error('Error requesting reindexing:', error)
      if (axios.isAxiosError(error)) {
        console.error('Axios error details:', {
          message: error.message,
          code: error.code,
          response: error.response
            ? {
                status: error.response.status,
                data: error.response.data,
                headers: error.response.headers
              }
            : 'No response',
          request: error.request ? 'Request was made but no response received' : 'No request'
        })
      }
      setReindexingFiles(prev => prev.filter(f => f !== fileName));
      return false;
    }
  }, [projectContext.searchIndexerName, projectContext.searchServiceName, projectId]);

  // Function to manually fetch blob list from the server
  const fetchBlobs = useCallback(async () => {
    if (!configValid || !isInitialized || !storageConfig || !projectContext.storageContainerUploads) {
      console.log('Cannot fetch blobs - configuration invalid or not initialized');
      return;
    }

    try {
      const { account_name, container_sas_token } = storageConfig;
      const container_name = projectContext.storageContainerUploads;

      console.log('Manually fetching blobs with configuration:', {
        account_name,
        container_name,
        hasSasToken: Boolean(container_sas_token)
      });

      // Log SAS token details for debugging (without revealing the full token)
      const sasTokenLength = container_sas_token.length;
      console.log(`fetchBlobs: SAS token length: ${sasTokenLength} characters`);
      console.log(`fetchBlobs: SAS token starts with: ${container_sas_token.substring(0, 10)}...`);
      console.log(`fetchBlobs: SAS token ends with: ...${container_sas_token.substring(sasTokenLength - 10)}`);
      console.log(`fetchBlobs: SAS token contains '?' character: ${container_sas_token.includes('?')}`);
      console.log(`fetchBlobs: SAS token starts with '?': ${container_sas_token.startsWith('?')}`);

      // Ensure SAS token starts with '?'
      const formattedSasToken = container_sas_token.startsWith('?') ? container_sas_token : `?${container_sas_token}`;

      // Log the URL format (without the actual SAS token)
      console.log(`fetchBlobs: Using URL format: https://${account_name}.blob.core.windows.net${formattedSasToken.substring(0, 5)}...`);

      const blobServiceClient = new BlobServiceClient(
        `https://${account_name}.blob.core.windows.net${formattedSasToken}`
      );
      const specificContainerClient = blobServiceClient.getContainerClient(container_name);

      // Verify container access
      await specificContainerClient.getProperties();
      console.log(`Container access verified for: ${container_name}`);

      // Fetch blobs
      console.log(`Fetching blobs from container: ${container_name}...`);
      const blobItems = specificContainerClient.listBlobsFlat();
      const fetchedFiles: UploadedFile[] = [];
      for await (const blob of blobItems) {
        fetchedFiles.push({
          name: blob.name,
          size: blob.properties.contentLength || 0,
          type: blob.properties.contentType || 'unknown',
          indexed: isFileInList(blob.name, indexedFiles)
        });
      }
      console.log(`Successfully fetched ${fetchedFiles.length} files from ${container_name}`);
      setFiles(fetchedFiles);
      setError(null);
      return fetchedFiles;
    } catch (error: unknown) {
      console.error('Error fetching blobs:', error);
      setError(`Error accessing files: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }, [configValid, isInitialized, storageConfig, projectContext.storageContainerUploads, indexedFiles]);

  // Function to handle file uploads - Updated to use direct Azure Storage access
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (!projectId) {
        setError('Project ID is missing. Cannot upload files.');
        return;
      }
      if (acceptedFiles.length === 0) {
        return;
      }

      setUploading(true);
      setError(null);
      setIndexingMessage(`Uploading ${acceptedFiles.length} file(s)...`);

      try {
        // Use project-specific storage account name and SAS token from ProjectContext
        const storageAccountName = projectContext.storageAccountName;
        const sasToken = projectContext.storageAccountSasToken;

        if (!storageAccountName || !sasToken) {
          throw new Error("Missing storage account name or SAS token in project context");
        }

        const containerName = projectContext.storageContainerUploads;

        if (!containerName) {
          throw new Error("Missing uploads container name in project context");
        }

        console.log(`Uploading directly to Azure Storage account: ${storageAccountName}, container: ${containerName}`);

        // Create a BlobServiceClient
        const blobServiceClient = new BlobServiceClient(
          `https://${storageAccountName}.blob.core.windows.net${sasToken.startsWith('?') ? sasToken : '?' + sasToken}`
        );

        // Get a reference to the container
        const containerClient = blobServiceClient.getContainerClient(containerName);

        // Upload each file
        const uploadResults = [];
        for (const file of acceptedFiles) {
          const blobClient = containerClient.getBlockBlobClient(file.name);
          console.log(`Uploading file "${file.name}" to Azure Storage...`);

          const uploadResponse = await blobClient.uploadData(await file.arrayBuffer(), {
            blobHTTPHeaders: { blobContentType: file.type }
          });

          uploadResults.push({
            name: file.name,
            success: true,
            etag: uploadResponse.etag
          });
        }

        console.log('Upload results:', uploadResults);
        setIndexingMessage('Upload completed successfully.');

          // Manually refresh the file list after successful upload
          // This ensures we see the new files even if WebSocket updates don't arrive
          console.log('Manually refreshing file list after upload...');
          setTimeout(async () => {
            try {
              // First try to send a refresh message to the WebSocket
              if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
                console.log('Sending refresh request to WebSocket');
                wsRef.current.send('refresh');
              }

              // Then also do a direct HTTP fetch as a fallback
              await fetchBlobs();
              console.log('File list refreshed after upload');
            } catch (refreshError) {
              console.error('Error refreshing file list after upload:', refreshError);
            }
          }, 1000); // Wait 1 second after upload to refresh

          // Clear the message after some time
          setTimeout(() => {
            setIndexingMessage(null);
          }, 5000);

          // Backend upload endpoint doesn't automatically trigger indexing here.
          // Need separate logic/button to trigger indexing if required, or modify backend.
          // For now, just log this.
          console.log("Upload complete. Manual indexing trigger might be needed.");


      } catch (err) {
        console.error('Error uploading files via API:', err);
        setError(`Error uploading files: ${err instanceof Error ? err.message : 'Unknown network error'}`);
        setIndexingMessage(null); // Clear uploading message
      } finally {
        setUploading(false);
      }
    },
    [projectId, fetchBlobs] // Depend on projectId and fetchBlobs
  );

  const dropzoneOptions: DropzoneOptions = {
    onDrop,
    multiple: true,
    noClick: false,
    noKeyboard: false
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone(dropzoneOptions)

  const handleDelete = async (fileName: string) => {
    try {
      // Use project-specific storage account name and SAS token from ProjectContext
      const storageAccountName = projectContext.storageAccountName;
      const sasToken = projectContext.storageAccountSasToken;

      if (!storageAccountName || !sasToken) {
        throw new Error("Missing storage account name or SAS token in project context");
      }

      // Use project-specific container from context for deletion
      const containerName = projectContext.storageContainerUploads;

      if (!containerName) {
        throw new Error("Missing uploads container name in project context");
      }

      // Show deleting status message
      setIndexingMessage(`Deleting ${fileName}...`);

      console.log(`Deleting file "${fileName}" directly from Azure Storage, container: ${containerName}`);

      // Create a BlobServiceClient
      const blobServiceClient = new BlobServiceClient(
        `https://${storageAccountName}.blob.core.windows.net${sasToken.startsWith('?') ? sasToken : '?' + sasToken}`
      );

      // Get a reference to the container and blob
      const containerClient = blobServiceClient.getContainerClient(containerName);
      const blockBlobClient = containerClient.getBlockBlobClient(fileName);

      // Delete the blob
      await blockBlobClient.delete();
      console.log(`Successfully deleted file "${fileName}" from container ${containerName}`);

      // Update UI immediately - don't wait for WebSocket
      setFiles(prev => prev.filter(file => file.name !== fileName));

      // Show success message
      setIndexingMessage(`File ${fileName} deleted successfully`);
      setTimeout(() => setIndexingMessage(null), 3000);
    } catch (err) {
      console.error('Error deleting file:', err);
      if (err instanceof Error) {
        setError(`Error: ${err.message}`);
      } else {
        setError('An unknown error occurred');
      }
    }
  }
  // Add effect to notify parent of state changes
  useEffect(() => {
    if (onToggle) {
      onToggle(isOpen)
    }
  }, [isOpen, onToggle])

  // Removed redundant storage config initialization - this is now handled by the ProjectContext-based initialization above

  useEffect(() => {
    if (!isConfigValid) {
      console.warn('Missing required blob storage configuration')
      return
    }

    try {
      // Ensure SAS token starts with '?'
      const sasToken = storageConfig.container_sas_token;
      const formattedSasToken = sasToken.startsWith('?') ? sasToken : `?${sasToken}`;

      // Log SAS token details for debugging (without revealing the full token)
      const sasTokenLength = sasToken.length;
      console.log(`SAS token length: ${sasTokenLength} characters`);
      console.log(`SAS token starts with: ${sasToken.substring(0, 10)}...`);
      console.log(`SAS token ends with: ...${sasToken.substring(sasTokenLength - 10)}`);
      console.log(`SAS token contains '?' character: ${sasToken.includes('?')}`);
      console.log(`SAS token starts with '?': ${sasToken.startsWith('?')}`);
      console.log(`Using URL format: https://${storageConfig.account_name}.blob.core.windows.net${formattedSasToken.substring(0, 5)}...`);

      const blobServiceClient = new BlobServiceClient(
        `https://${storageConfig.account_name}.blob.core.windows.net${formattedSasToken}`
      )
      const containerClient = blobServiceClient.getContainerClient(storageConfig.container_name)
      setContainerClient(containerClient)
      setIsInitialized(true)
    } catch (error) {
      console.error('Error initializing blob service client:', error)
      setIsInitialized(false)
    }
  }, [storageConfig, isConfigValid])

  // Apply filtering to files before displaying them
  const filteredFiles = files.filter(file => {
    if (indexFilter === 'all') return true
    if (indexFilter === 'indexed') return file.indexed
    if (indexFilter === 'unindexed') return !file.indexed
    return true
  })

  // Add a function to handle manual refresh
  const handleRefreshIndexStatus = async () => {
    setIsRefreshing(true)
    try {
      await fetchIndexedFiles()
    } finally {
      setIsRefreshing(false)
    }
  }

  // Handle click on the entire collapsed panel
  const handleCollapsedPanelClick = () => {
    if (!isOpen) {
      setIsOpen(true)
    }
  }

  if (!isInitialized) {
    return <div className="file-management-loading">Loading...</div>
  }

  if (!configValid) {
    return (
      <div className="file-management-error">
        Storage configuration not available. Please check your Azure Storage settings.
      </div>
    )
  }

  return (
    <div
      ref={wrapperRef}
      className={`file-management-wrapper ${!isOpen ? 'collapsed' : ''}`}
      onClick={!isOpen ? handleCollapsedPanelClick : undefined}>
      <div className={`file-management ${!isOpen ? 'collapsed' : ''}`}>
        {/* Collapsed state elements */}
        {!isOpen && (
          <>
            {/* File icon at the top */}
            <File className="collapsed-icon" size={24} color="#0F6CBD" />
            {/* Vertical text between arrows */}
            {/* Second arrow at the bottom */}
            <span className="second-arrow">»</span>
          </>
        )}

        {/* Expanded state elements */}
        {isOpen && (
          <>
            {/* Header */}
            <div className="file-management-header" onClick={() => setIsOpen(!isOpen)}>
              <div className="header-content">
                <button
                  className={`toggle-button ${isOpen ? 'open' : ''}`}
                  onClick={e => {
                    e.stopPropagation()
                    setIsOpen(!isOpen)
                  }}>
                  <ChevronRight />
                </button>
                <h2>Input Files</h2>
                <div className="right-panel-status">
                  {isWsConnected ? (
                    <span style={{ color: '#44ef5c' }} className="pulsating" title="Live updates connected">
                      ●
                    </span>
                  ) : (
                    <span style={{ color: '#ef4444' }} title="Live updates disconnected">
                      ●
                    </span>
                  )}
                </div>
                <InfoBubble
                  text="Documents uploaded here are indexed in a vector database. The information from these documents will be available for reference in the chat, allowing the AI to answer questions based on your uploaded content."
                />
              </div>
            </div>

            {/* Content */}
            <div className="file-management-content">
              <div {...getRootProps()} className="dropzone">
                <input {...getInputProps()} />
                <div className="upload-prompt">
                  <Upload className="upload-icon" />
                  <p>Input Client, questionnaire, interviews, internal document files</p>
                </div>
              </div>

              <div className="files-list-header">
                <div className="files-count">
                  {files.length} files uploaded
                  <button
                    onClick={handleRefreshIndexStatus}
                    className={`refresh-button ${isRefreshing ? 'loading' : ''} ${indexStale ? 'stale' : ''}`}
                    disabled={isRefreshing}
                    title="Click to check for newly indexed files">
                    <RefreshCw size={16} />
                  </button>
                </div>
                <div className="filter-controls">
                  <span>Show: </span>
                  <div className="filter-buttons">
                    <button
                      className={`filter-button ${indexFilter === 'all' ? 'active' : ''}`}
                      onClick={() => setIndexFilter('all')}>
                      All
                    </button>
                    <button
                      className={`filter-button ${indexFilter === 'indexed' ? 'active' : ''}`}
                      onClick={() => setIndexFilter('indexed')}>
                      Indexed
                    </button>
                    <button
                      className={`filter-button ${indexFilter === 'unindexed' ? 'active' : ''}`}
                      onClick={() => setIndexFilter('unindexed')}>
                      Pending
                    </button>
                  </div>
                </div>
              </div>

              <div className="files-list">
                {filteredFiles.map(file => (
                  <div key={file.name} className="file-item">
                    <div className="file-info">
                      <div
                        className="file-icon-container"
                        title={file.indexed ? 'Indexed and searchable' : 'Not yet indexed'}>
                        <File
                          className={`file-icon ${file.indexed ? 'indexed-file' : ''}`}
                          color={file.indexed ? '#22c55e' : 'currentColor'}
                        />
                        {file.indexed && (
                          <div className="indexed-badge" title="This file is indexed and searchable">
                            ✓
                          </div>
                        )}
                      </div>
                      <span className="file-name">{file.name}</span>
                    </div>
                    <button onClick={() => handleDelete(file.name)} className="delete-button">
                      <Trash2 className="delete-icon" />
                    </button>
                  </div>
                ))}

                {filteredFiles.length === 0 && (
                  <div className="no-files-message">
                    {files.length > 0
                      ? `No ${indexFilter === 'indexed' ? 'indexed' : 'unindexed'} files to display`
                      : 'No files uploaded yet'}
                  </div>
                )}
              </div>

              {error && <div className="status-message error">Error fetching files</div>}

              {/* Indexing Status */}
              <div className="indexer-status">
                <div className="indexer-status-item">{files.filter(f => f.indexed).length} files ready</div>
                <div className="indexer-status-separator">-</div>
                <div className="indexer-status-item">checked: {formatTime(indexLastUpdate)}</div>
                <div className="indexer-status-separator">-</div>
                <div className="indexer-status-item">
                  live updates:{' '}
                  {isWsConnected ? (
                    <span style={{ color: '#44ef5c' }} className="pulsating" title="Live updates connected">
                      ●
                    </span>
                  ) : (
                    <span style={{ color: '#ef4444' }} title="Live updates disconnected">
                      ●
                    </span>
                  )}
                </div>
              </div>

              {indexingMessage && <div className="status-message">{indexingMessage}</div>}
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default FileManagement

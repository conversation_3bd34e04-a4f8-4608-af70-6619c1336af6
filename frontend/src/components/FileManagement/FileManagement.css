/* src/components/FileManagement/FileManagement.css */
/* Add CSS custom properties at the root */
:root {
  --left-panel-width: 100%;
  --right-panel-width: 100%;
}

.file-management-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
  overflow: hidden;
  box-sizing: border-box;
}

/* Right panel specific styles */
.file-management-wrapper.right-panel {
  width: 100%;
}

.file-management-wrapper.collapsed {
  width: 64px !important;
  min-width: 64px !important;
  max-width: 64px !important;
  overflow: hidden;
  cursor: pointer;
}

.file-management {
  width: 100%;
  height: 100%;
  background-color: var(--background-color);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.file-management.collapsed {
  width: 64px !important;
  min-width: 64px !important;
  max-width: 64px !important;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100%;
  cursor: pointer;
}

.file-management-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.file-management.collapsed .file-management-content {
  width: 64px !important;
  overflow: hidden;
}

.resize-handle {
  display: none;
}

.file-management-header {
  padding: 8px;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
  width: 100%;
  cursor: pointer;
  box-sizing: border-box;
}

.file-management.collapsed .file-management-header {
  width: 64px;
  overflow: hidden;
  padding: 8px 4px;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.file-management.collapsed .header-content {
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 64px;
  padding: 0;
}

.file-management.collapsed .header-content .icon-button-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  cursor: pointer;
}

.file-management.collapsed .header-content h2 {
  font-size: 0.75rem;
  text-align: center;
  white-space: pre-line;
  word-wrap: break-word;
  line-height: 1.2;
  margin: 4px 0;
  width: 100%;
}

/* Update scrollbar visibility based on collapsed state */
.file-management.collapsed .files-list::-webkit-scrollbar {
  width: 0;
  display: none;
}

.file-management.collapsed .file-management-content::-webkit-scrollbar {
  width: 0;
  display: none;
}

.collapsed-file-icon {
  width: 24px;
  height: 24px;
  color: #0f6cbd;
  margin: 0 auto;
  display: block;
  position: relative;
  z-index: 5; /* Ensure icon is above the double arrow */
}

.toggle-button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition:
    transform 0.3s ease,
    background-color 0.2s ease;
  min-width: 32px;
  min-height: 32px;
}

.toggle-button.open {
  transform: rotate(90deg);
}

.file-management.collapsed .toggle-button {
  padding: 6px;
  min-width: 28px;
  min-height: 28px;
  transform: rotate(0deg);
}

.toggle-button:hover {
  background-color: #f3f4f6;
}

.toggle-button svg {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.file-management-header h2 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.dropzone {
  padding: 8px;
  margin: 8px;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-sizing: border-box;
  max-width: 100%;
}

.dropzone:hover {
  border-color: #93c5fd;
  background-color: #f8fafc;
}

.dropzone-active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.upload-icon {
  width: 20px;
  height: 20px;
  margin: 0 auto 4px;
  color: #9ca3af;
}

.upload-prompt p {
  margin: 0;
  font-size: 0.8rem;
  color: #6b7280;
}

.dropzone-text {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.files-list {
  flex-grow: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 12px;
}

/* Original file-item class replaced by the one below */

/* Hover effect removed */

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
  flex: 1;
  background: transparent;
}

.file-icon {
  width: 18px;
  height: 18px;
  color: #6b7280;
}

.file-icon.excel {
  color: #217346; /* Microsoft Excel green */
}

.file-icon.word {
  color: #2b579a; /* Microsoft Word blue */
}

.file-name {
  font-size: 0.875rem;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.delete-button {
  padding: 4px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.delete-button:hover {
  background-color: #fee2e2;
}

.delete-icon {
  width: 16px;
  height: 16px;
  color: #ef4444;
}

.status-message {
  padding: 8px;
  margin-bottom: 10px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

.status-message.uploading {
  background-color: #f0f0f0;
  color: #666;
}

.status-message.error {
  background-color: #fde7e9;
  color: #d13438;
}

.status-message.warning {
  background-color: #fff4ce;
  color: #605e5c;
  border: 1px solid #f9d6a5;
}

/* Add styling for file-management-error */
.file-management-error {
  padding: 8px;
  margin: 10px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  background-color: #fde7e9;
  color: #d13438;
  border: 1px solid #f1707b;
}

/* Add styling for file-management-loading */
.file-management-loading {
  padding: 8px;
  margin: 10px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  background-color: #f0f0f0;
  color: #666;
}

/* Add scrollbar styling */
.files-list::-webkit-scrollbar {
  width: 6px;
}

.files-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.files-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.files-list::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
  .file-management-container {
    width: 100%;
  }
}

.indexer-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: linear-gradient(to right, #e0e0e0, #d5d5d5, #e0e0e0);
  border-radius: 0 0 4px 4px;
  font-size: 14px;
  color: #333333;
  gap: 8px;
  margin-top: auto;
}

.indexer-status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.indexer-status-separator {
  color: #999999;
  margin: 0 4px;
}

.indexer-status .ws-status {
  margin: 0;
  display: flex;
  align-items: center;
}

.ws-status-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-left: 4px;
  position: relative;
  top: 1px;
}

.ws-status-indicator.connected {
  background-color: #22c55e;
}

.ws-status-indicator.disconnected {
  background-color: #ef4444;
}

.pulsating {
  animation: pulse 1.5s infinite;
}

.indexer-stats {
  display: grid;
  gap: 0.5rem;
}

.indexer-stats div {
  font-size: 0.9rem;
  color: #666;
}

.indexer-details {
  margin-top: 10px;
}

.indexer-details > div {
  margin: 5px 0;
}

.error-message {
  color: #dc3545;
  margin-top: 5px;
}

.file-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-button {
  padding: 4px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background-color: #f3f4f6;
}

.download-button:hover {
  background-color: #e5f0ff;
}

.action-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.download-button:hover .action-icon {
  color: #2563eb;
}

.section-container {
  padding: 16px;
  /* Removed border-bottom */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.view-priority-plot-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 2px;
  border: none;
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #0f6cbd 33.63%, #2d87c3 70.31%, #8dddd8 100%);
  color: white;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.1s ease;
  min-height: 32px;
}

.view-priority-plot-button:hover {
  opacity: 0.9;
  color: white;
}

.view-priority-plot-button:active {
  opacity: 0.8;
  color: white;
}

.section-container:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.actions-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: stretch;
}

.action-button-large {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 2px;
  border: none;
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #0f6cbd 33.63%, #2d87c3 70.31%, #8dddd8 100%);
  color: white;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.1s ease;
  min-height: 32px;
  user-select: none;
}

.action-button-large:hover {
  opacity: 0.9;
  color: white;
}

.action-button-large:active {
  opacity: 0.8;
  color: white;
}

.action-button-large:disabled {
  background: rgb(243, 242, 241);
  border: 1px solid rgb(243, 242, 241);
  color: rgb(161, 159, 157);
  cursor: not-allowed;
  opacity: 1;
}

.action-button-large.loading {
  background: rgb(243, 242, 241);
  border: 1px solid rgb(243, 242, 241);
  color: rgb(161, 159, 157);
  cursor: wait;
  opacity: 1;
}

.action-button-large .action-icon {
  width: 16px;
  height: 16px;
  color: inherit;
}

.dropdown-container {
  position: relative;
  display: flex;
  align-items: center;
  height: 36px;
}

.dropdown-icon {
  margin-left: 8px;
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.limit-icon {
  margin-left: 8px;
  width: 16px;
  height: 16px;
  color: #f59e0b;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgb(255, 255, 255);
  border: 1px solid rgb(138, 136, 134);
  border-radius: 2px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 2px;
}

.dropdown-item {
  padding: 8px 16px;
  font-size: 14px;
  color: rgb(50, 49, 48);
  cursor: pointer;
}

.dropdown-item:hover {
  background: rgb(243, 242, 241);
  color: rgb(32, 31, 30);
}

.dropdown-item.disabled {
  color: rgb(161, 159, 157);
  cursor: not-allowed;
}

.usage-counter {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #f0f0f0;
  color: #333;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: bold;
  border: 1px solid #ccc;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button-large.limit-reached,
.view-powerpoint-button.limit-reached {
  background: #f8f0e3;
  border: 1px solid #f8f0e3;
  color: #d97706;
  cursor: not-allowed;
  opacity: 1;
}

.action-button-large.limit-reached .action-icon,
.view-powerpoint-button.limit-reached .button-icon {
  color: #d97706;
}

.file-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.indexed-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background-color: #22c55e;
  color: white;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}

.indexed-file {
  filter: drop-shadow(0 0 2px rgba(34, 197, 94, 0.4));
}

/* Add a hover effect to make the tooltip more intuitive */
.file-icon-container:hover {
  cursor: pointer;
}

/* Add a section for indexed vs non-indexed files summary */
.indexing-summary {
  margin-top: 10px;
  padding: 8px;
  font-size: 12px;
  background-color: #f9fafb;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
}

.indexing-summary .count {
  font-weight: bold;
}

.indexing-summary .indexed-count {
  color: #22c55e;
}

.indexing-summary .unindexed-count {
  color: #9ca3af;
}

.refresh-button {
  background: transparent !important;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 3px;
  margin-left: 0;
  border-radius: 3px;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.refresh-button:hover {
  background-color: transparent !important;
  color: #4b5563;
}

.refresh-button:active {
  transform: scale(0.95);
}

.refresh-button.loading {
  opacity: 0.6;
  pointer-events: none;
  animation: spin 1.2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.files-list-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 12px;
  text-align: center;
  padding: 0 8px;
}

.files-count {
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.filter-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: #6b7280;
}

.filter-buttons {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  margin: 0 auto;
}

.filter-button {
  background: none;
  border: none;
  padding: 4px 10px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 60px;
  text-align: center;
}

.filter-button:hover {
  background-color: #f3f4f6;
}

.filter-button.active {
  background-color: #e5e7eb;
  font-weight: 500;
  color: #4b5563;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.no-files-message {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
  font-style: italic;
  font-size: 13px;
}

.reindex-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 3px;
  margin-left: 5px;
  border-radius: 3px;
  color: #f59e0b; /* Amber/warning color */
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.reindex-button:hover {
  background-color: #fffbeb;
  color: #d97706;
}

.reindex-button:active {
  transform: scale(0.95);
}

.reindex-button.loading {
  opacity: 0.6;
  pointer-events: none;
  animation: spin 1.2s linear infinite;
  color: #6b7280;
}

/* Add stale state styling */
.refresh-button.stale {
  animation: pulse 1.5s infinite;
  border-color: #ffc107;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.collapsed-icon {
  width: 24px;
  height: 24px;
  color: #0f6cbd;
  margin: 0 auto;
  display: block;
  position: absolute;
  z-index: 5; /* Ensure icon is above the double arrow */
  top: 5%; /* Position at the very top with just a small margin */
  left: 50%;
  transform: translateX(-50%);
}

.second-arrow {
  position: absolute;
  bottom: 30%; /* Match the position in Chat.module.css */
  left: 0;
  width: 100%;
  text-align: center;
  font-size: 24px;
  color: #0f6cbd;
  font-weight: bold;
  opacity: 0.7;
  z-index: 1;
}

.progress-bar-container {
  width: calc(100% - 50px); /* Make room for the percentage */
  height: 6px;
  background-color: #f3f2f1;
  border-radius: 3px;
  margin-bottom: 10px;
  position: relative;
  display: flex;
  align-items: center;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #0f6cbd, #8dddd8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-percentage {
  position: absolute;
  right: -45px; /* Position it to the right of the progress bar */
  font-size: 12px;
  font-weight: 600;
  color: #0f6cbd;
  white-space: nowrap;
}

.ws-status {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.upload-section {
  margin-bottom: 1.5rem;
}

.subsection-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.subsection-title {
  font-size: 0.9rem;
  color: #444;
  font-weight: 500;
  margin: 0;
  flex: 1;
  padding: 0.5rem 0;
}

.upload-icon-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.4rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0.5rem;
  transition: all 0.2s ease;
}

.upload-icon-button:hover {
  background-color: #f0f9ff;
}

.upload-icon-button:hover .upload-icon {
  color: #0f6cbd;
}

.upload-icon-button .upload-icon {
  color: #666;
  width: 20px;
  height: 20px;
}

/* Section separator moved to file-item section */

.dropzone {
  border: 2px dashed #ccc;
  border-radius: 4px;
  padding: 0.6rem;
  text-align: center;
  background-color: #fafafa;
  cursor: pointer;
  transition: border-color 0.3s ease;
  margin-bottom: 0.8rem;
  margin-left: 0.2rem;
  margin-right: 0.2rem;
  width: calc(100% - 0.4rem);
}

/* Specific styling for the RightFileManagement component dropzone */
.right-panel .dropzone {
  padding: 1rem;
}

.dropzone:hover {
  border-color: #0f6cbd;
  background-color: #f0f9ff;
}

.dropzone .upload-prompt {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.8rem;
  padding-left: 1.2rem;
  text-align: left;
}

/* Specific styling for the RightFileManagement component */
.right-panel .upload-section:first-child .dropzone .upload-prompt {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
  padding-left: 3rem;
}

.dropzone .upload-icon {
  color: #666;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.dropzone:hover .upload-icon {
  color: #0f6cbd;
}

.dropzone p {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
  text-align: left;
}

/* Override any file-item styles to ensure no borders or backgrounds */
.file-item,
.files-list .file-item,
.section-container .files-list .file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.6rem 0;
  margin-bottom: 0.2rem;
  width: 100%;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  outline: none !important;
}

/* Add a very subtle separator line between sections */
.section-separator {
  height: 1px;
  background-color: #f0f0f0;
  margin: 1.5rem 0;
  width: 100%;
}

/* Removed hover background effect */

.file-info {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  overflow: hidden;
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 0.85rem;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.file-actions {
  display: flex;
  gap: 0.3rem;
  flex-shrink: 0;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.2rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background-color: #f0f0f0;
}

.download-button {
  color: #0f6cbd;
}

.delete-button {
  color: #d32f2f;
}

.powerpoint-button {
  color: #B7472A;
}

.action-icon {
  width: 16px;
  height: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.section-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
  height: 44px;
}

.view-priority-plot-button, .view-powerpoint-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.1s ease;
  color: white;
  height: 36px;
  width: 130px;
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #0f6cbd 33.63%, #2d87c3 70.31%, #8dddd8 100%);
  box-sizing: border-box;
  vertical-align: middle;
  line-height: 1;
}

.view-priority-plot-button:hover, .view-powerpoint-button:hover {
  opacity: 0.9;
}

.button-icon, .dropdown-icon {
  width: 18px !important;
  height: 18px !important;
  vertical-align: middle;
  flex-shrink: 0;
  position: relative;
  top: 0;
}

/* Ensure the dropdown icon is properly aligned */
.dropdown-icon {
  margin-left: 2px;
  width: 16px !important;
  height: 16px !important;
}

/* Validation Error Modal Styles */
.validation-error-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.validation-error-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column;
}

.validation-error-close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  color: #605e5c;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.validation-error-close-button:hover {
  color: #e41e3f;
  background-color: #fde7e9;
}

.validation-error-modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fde7e9;
}

.validation-error-modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #d13438;
}

.validation-error-modal-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.validation-error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.validation-error-message {
  font-size: 16px;
  color: #323130;
  margin-bottom: 16px;
  line-height: 1.5;
  text-align: left;
  width: 100%;
}

.validation-error-message p {
  margin: 0 0 10px 0;
}

.validation-error-message .validation-error-item {
  margin: 0 0 8px 0;
  padding-left: 10px;
  font-size: 14px;
  line-height: 1.4;
}

.validation-error-help {
  font-size: 14px;
  color: #605e5c;
  margin-bottom: 16px;
}

.validation-error-modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: center;
}

.validation-error-button {
  padding: 8px 16px;
  background-color: #d13438;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.validation-error-button:hover {
  background-color: #c41e3f;
}

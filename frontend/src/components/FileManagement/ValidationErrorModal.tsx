import React, { useEffect } from 'react';
import { X } from 'lucide-react';
import './FileManagement.css';

interface ValidationErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  errorMessage: string;
}

const ValidationErrorModal: React.FC<ValidationErrorModalProps> = ({
  isOpen,
  onClose,
  errorMessage
}) => {
  useEffect(() => {
    if (isOpen) {
      // Prevent body scrolling when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto';
    }

    return () => {
      // Cleanup: ensure body scrolling is restored when component unmounts
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  // Determine the header text based on the error message
  const getHeaderText = () => {
    if (errorMessage.includes('Excel file structure validation failed')) {
      return 'Excel Validation Error';
    } else if (errorMessage.includes('Document structure validation failed')) {
      return 'Document Validation Error';
    } else {
      return 'Validation Error';
    }
  };

  return (
    <div className="validation-error-modal-overlay">
      <div className="validation-error-modal">
        <button className="validation-error-close-button" onClick={onClose} aria-label="Close">
          <X size={24} strokeWidth={2.5} />
        </button>
        <div className="validation-error-modal-header">
          <h2>{getHeaderText()}</h2>
        </div>
        <div className="validation-error-modal-content">
          <div className="validation-error-icon">⚠️</div>
          <div className="validation-error-message">
            {errorMessage.split('-').map((error, index) => {
              // Skip the first empty item if the message starts with a dash
              if (index === 0 && error.trim() === '') return null;

              // For the first item that contains the main error message
              if (index === 0) {
                return <p key={index}>{error.trim()}</p>;
              }

              // For all other items that are individual error points
              return (
                <p key={index} className="validation-error-item">
                  • {error.trim()}
                </p>
              );
            })}
          </div>
          <p className="validation-error-help">Please upload a valid template file and try again.</p>
        </div>
        <div className="validation-error-modal-footer">
          <button className="validation-error-button" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ValidationErrorModal;

import React, { useState, useRef } from 'react';
import { Info } from 'lucide-react';
import { Text, DirectionalHint, Callout, mergeStyles } from '@fluentui/react';
import './InfoBubble.css';

interface InfoBubbleProps {
  text: string;
}

const InfoBubble: React.FC<InfoBubbleProps> = ({ text }) => {
  const [isCalloutVisible, setIsCalloutVisible] = useState(false);
  const iconRef = useRef<HTMLSpanElement>(null);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsCalloutVisible(!isCalloutVisible);
  };

  // Using Fluent UI's mergeStyles for consistent styling
  const calloutClassName = mergeStyles({
    maxWidth: 300,
    padding: 12,
    backgroundColor: 'white',
    border: '1px solid #e0e0e0',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
    borderRadius: 4
  });

  // Text styles for the content
  const textStyles = {
    root: {
      fontSize: '13px',
      lineHeight: '1.5',
      color: '#333',
      fontWeight: 'normal'
    }
  };

  return (
    <div className="info-bubble-container" onClick={(e) => e.stopPropagation()}>
      <span ref={iconRef} onClick={handleClick}>
        <Info className="info-icon" size={16} />
      </span>
      {isCalloutVisible && (
        <Callout
          target={iconRef.current}
          onDismiss={() => setIsCalloutVisible(false)}
          className={calloutClassName}
          isBeakVisible={true}
          beakWidth={10}
          gapSpace={10}
          directionalHint={DirectionalHint.rightCenter}
          calloutMaxHeight={500}
          calloutMinWidth={200}
        >
          <div className="info-tooltip-content">
            <Text styles={textStyles}>
              {text}
            </Text>
          </div>
        </Callout>
      )}
    </div>
  );
};

export default InfoBubble;

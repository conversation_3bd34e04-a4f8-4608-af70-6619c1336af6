.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
}

.content {
  text-align: center;
  padding: 40px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  max-width: 500px;
}

.title {
  font-size: 72px;
  margin: 0;
  color: #d13438;
}

.subtitle {
  font-size: 24px;
  margin: 10px 0 20px;
  color: #333;
}

.message {
  color: #666;
  margin-bottom: 30px;
}

.button {
  padding: 10px 24px;
  background-color: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: #106ebe;
} 
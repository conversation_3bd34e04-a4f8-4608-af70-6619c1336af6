import React, { useState, useMemo, useEffect } from 'react';
import {
  Stack,
  IconButton,
  IButtonStyles,
  Persona,
  PersonaSize,
  PersonaPresence,
  Callout,
  DirectionalHint,
  Text,
  Link,
  DefaultButton,
  IStackTokens,
  Dialog,
  DialogType
} from '@fluentui/react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../state/UserProvider';
import { UserRole, getRolePermissions } from '../models/roles';
import SupportTicket from './SupportTicket';
import dropdownStyles from './RoleBasedHeader.module.css';

// Styles
const stackTokens: IStackTokens = { childrenGap: 10 };

const iconButtonStyles: IButtonStyles = {
  root: {
    color: '#333',
    marginLeft: 'auto',
  },
  rootHovered: {
    color: '#0078d4',
  },
};


const roleColorMap = {
  [UserRole.SUPER_ADMIN]: '#e81123', // Red for Super Admin
  [UserRole.REGIONAL_ADMIN]: '#ff8c00', // Orange for Regional Admin
  [UserRole.REGULAR_USER]: '#107c10', // Green for Regular User
};

// Utility function to get initials from name
const getInitialsFromName = (name: string): string => {
  if (!name) return '';

  console.log('Getting initials for name:', name);

  // Split the name by spaces
  const parts = name.split(' ');
  console.log('Name parts:', parts);

  // If we have at least two parts (first and last name)
  if (parts.length >= 2) {
    const initials = (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
    console.log('Returning initials from first and last name:', initials);
    return initials;
  }

  // If only one part, use first two letters or just first letter
  const singleNameInitial = name.length > 1 ? name.substring(0, 2).toUpperCase() : name[0].toUpperCase();
  console.log('Returning initials from single name:', singleNameInitial);
  return singleNameInitial;
};

interface RoleBasedHeaderProps {
  logo?: string;
  title?: string;
}

const RoleBasedHeader: React.FC<RoleBasedHeaderProps> = ({
  logo = 'https://via.placeholder.com/40x40',
  title = 'AI Scope Project Management'
}) => {
  const navigate = useNavigate();
  const { currentUser, logout } = useUser();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState<HTMLElement | null>(null);
  const [isDevelopmentMode, setIsDevelopmentMode] = useState(false);
  const [showSupportTicket, setShowSupportTicket] = useState(false);

  // Check development mode from environment variable
  useEffect(() => {
    // Check if DEVELOPMENT_MODE is true (case insensitive)
    const devMode = import.meta.env.DEVELOPMENT_MODE?.toLowerCase() === 'true' ||
                   import.meta.env.DEV === true ||
                   window.location.hostname === 'localhost';
    setIsDevelopmentMode(devMode);
  }, []);

  // Get permissions based on user role
  console.log('RoleBasedHeader - Current user role:', currentUser?.role);
  console.log('RoleBasedHeader - Current user permissions:', currentUser?.permissions);

  // Use permissions from the user context if available, otherwise fall back to role-based permissions
  const permissions = currentUser?.permissions ||
    (currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER));

  // Get user initials from name
  const userInitials = useMemo(() => {
    if (currentUser?.name) {
      // Log the current user name for debugging
      console.log('Current user name:', currentUser.name);

      // If name is "Super Admin", try to use email to generate a more personalized initial
      if (currentUser.name === 'Super Admin' && currentUser.email) {
        const emailParts = currentUser.email.split('@')[0].split('.');
        console.log('Email parts:', emailParts);
        if (emailParts.length >= 2) {
          const initials = (emailParts[0][0] + emailParts[1][0]).toUpperCase();
          console.log('Generated initials from email:', initials);
          return initials;
        }
      }

      // Get initials from name
      const initials = getInitialsFromName(currentUser.name);
      console.log('Generated initials from name:', initials);
      return initials;
    }
    return '';
  }, [currentUser?.name, currentUser?.email]);

  const handleUserMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
    setShowUserMenu(!showUserMenu);
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const handleAdminPanel = () => {
    navigate('/admin');
    setShowUserMenu(false);
  };

  const handleGlobalSettings = () => {
    navigate('/admin/global-settings');
    setShowUserMenu(false);
  };

  const handleUserManagement = () => {
    navigate('/admin/users');
    setShowUserMenu(false);
  };

  const handleDevTools = () => {
    navigate('/dev');
    setShowUserMenu(false);
  };

  const handleSupportTicket = () => {
    setShowSupportTicket(true);
    setShowUserMenu(false);
  };

  const getRoleLabel = (role: UserRole): string => {
    switch (role) {
      case UserRole.SUPER_ADMIN:
        return 'Super Admin';
      case UserRole.REGIONAL_ADMIN:
        return 'Regional Admin';
      case UserRole.REGULAR_USER:
        return 'Regular User';
      default:
        return 'User';
    }
  };

  return (
    <Stack
      horizontal
      verticalAlign="center"
      styles={{
        root: {
          padding: '8px 20px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
          backgroundColor: '#fff',
          position: 'relative',
          zIndex: 100,
        }
      }}
    >
      {/* Logo and Title */}
      <Stack horizontal verticalAlign="center" tokens={{ childrenGap: 12 }}>
        <img
          src={logo}
          alt="Company Logo"
          style={{ height: 40, width: 'auto' }}
        />
        <Text variant="large" styles={{ root: { fontWeight: 600 } }}>
          {title}
        </Text>
      </Stack>

      {/* Spacer */}
      <div style={{ flexGrow: 1 }} />

      {/* Role Indicator */}
      {currentUser && (
        <>
          {console.log('Rendering role indicator with role:', currentUser.role)}
          <div style={{
            backgroundColor: roleColorMap[currentUser.role] || '#666',
            color: 'white',
            padding: '4px 12px',
            borderRadius: 16,
            fontSize: 12,
            fontWeight: 600,
            marginRight: 16
          }}>
            {getRoleLabel(currentUser.role)}
          </div>
        </>
      )}

      {/* User Profile - Custom Avatar */}
      <div
        ref={ref => setUserMenuAnchor(ref)}
        onClick={handleUserMenuClick}
        style={{
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          backgroundColor: currentUser?.role === UserRole.SUPER_ADMIN ? '#b7472a' :
                          currentUser?.role === UserRole.REGIONAL_ADMIN ? '#ff8c00' : '#107c10',
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '16px',
          fontWeight: 'bold',
          cursor: 'pointer',
          position: 'relative'
        }}
      >
        {userInitials || "JD"}
        {/* Online indicator */}
        <div style={{
          position: 'absolute',
          bottom: '0',
          right: '0',
          width: '10px',
          height: '10px',
          borderRadius: '50%',
          backgroundColor: '#92c353',
          border: '2px solid white'
        }} />
      </div>

      {/* User Menu Callout */}
      {showUserMenu && userMenuAnchor && (
        <Callout
          target={userMenuAnchor}
          onDismiss={() => setShowUserMenu(false)}
          directionalHint={DirectionalHint.bottomRightEdge}
          className={dropdownStyles.dropdownMenu}
          isBeakVisible={true}
        >
          <Stack tokens={stackTokens}>
            <Text variant="large" styles={{ root: { fontWeight: 600 } }}>
              {currentUser?.name === 'Super Admin' && currentUser.email
                ? currentUser.email.split('@')[0].split('.').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join(' ')
                : currentUser?.name || 'User'}
            </Text>
            <Text variant="medium" styles={{ root: { color: '#666' } }}>
              {currentUser?.email || '<EMAIL>'}
            </Text>

            <Stack tokens={{ childrenGap: 8 }} style={{ marginTop: 16 }}>
              {/* Admin Panel - Only for Super Admin and Regional Admin */}
              {permissions.canAccessAdminPanel && (
                <DefaultButton
                  text="Admin Panel"
                  onClick={handleAdminPanel}
                  className={dropdownStyles.dropdownItem}
                />
              )}

              {/*
              // Global Settings - Only for Super Admin
              {permissions.canManageGlobalSettings && (
                <DefaultButton
                  text="Global Settings"
                  onClick={handleGlobalSettings}
                  className={dropdownStyles.dropdownItem}
                />
              )}

              // User Management - For Super Admin and Regional Admin
              {permissions.canManageUsers && (
                <DefaultButton
                  text="User Management"
                  onClick={handleUserManagement}
                  className={dropdownStyles.dropdownItem}
                />
              )}
              */}

              {/* Support Ticket */}
              <DefaultButton
                text="Create Support Ticket"
                onClick={handleSupportTicket}
                className={dropdownStyles.dropdownItem}
              />

              {/* Dev Tools - Only visible in development */}
              {isDevelopmentMode && (
                <DefaultButton
                  text="Dev Tools"
                  onClick={handleDevTools}
                  className={dropdownStyles.dropdownItem}
                  styles={{
                    root: {
                      marginTop: 8,
                      borderTop: '1px solid #eee',
                    }
                  }}
                />
              )}

              {/* Logout Button */}
              <DefaultButton
                text="Logout"
                onClick={handleLogout}
                className={dropdownStyles.dropdownItem}
                styles={{
                  root: {
                    marginTop: 8,
                    borderTop: '1px solid #eee',
                  }
                }}
              />
            </Stack>
          </Stack>
        </Callout>
      )}

      {/* Support Ticket Dialog */}
      <Dialog
        hidden={!showSupportTicket}
        onDismiss={() => setShowSupportTicket(false)}
        dialogContentProps={{
          type: DialogType.largeHeader,
          title: 'Create Support Ticket',
        }}
        modalProps={{
          isBlocking: false,
          styles: { main: { maxWidth: 600 } },
        }}
      >
        <SupportTicket isOpen={showSupportTicket} onClose={() => setShowSupportTicket(false)} />
      </Dialog>
    </Stack>
  );
};

export default RoleBasedHeader;

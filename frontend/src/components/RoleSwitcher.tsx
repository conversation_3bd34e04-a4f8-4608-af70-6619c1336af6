import React, { useState, useEffect } from 'react';
import {
  Dropdown,
  IDropdownOption,
  Stack,
  Text,
  PrimaryButton,
  MessageBar,
  MessageBarType,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useUser } from '../state/UserProvider';
import { UserRole } from '../models/roles';
import userContextService from '../services/userContextService';

// Role options for dropdown
const roleOptions: IDropdownOption[] = [
  { key: UserRole.SUPER_ADMIN, text: 'Super Admin' },
  { key: UserRole.REGIONAL_ADMIN, text: 'Regional Admin' },
  { key: UserRole.REGULAR_USER, text: 'Regular User' }
];

const RoleSwitcher: React.FC = () => {
  const { currentUser, setCurrentUser } = useUser();
  const [selectedRole, setSelectedRole] = useState<UserRole>(
    currentUser?.role || UserRole.SUPER_ADMIN
  );
  const [selectedRegion, setSelectedRegion] = useState<string>(
    currentUser?.region || ''
  );
  const [showSuccess, setShowSuccess] = useState(false);
  const [regionOptions, setRegionOptions] = useState<IDropdownOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Load regions from RBAC API
  useEffect(() => {
    loadRegions();
  }, []);

  const loadRegions = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Use the user context service to get regions
      const userContext = await userContextService.fetchUserContext();

      if (!userContext || !userContext.accessibleResources.regions) {
        console.error('Error loading regions from user context');
        setErrorMessage('Failed to load regions. Using default values.');
        // Set some default regions as fallback
        setRegionOptions([
          { key: 'North America', text: 'North America' },
          { key: 'Europe', text: 'Europe' },
          { key: 'Asia Pacific', text: 'Asia Pacific' },
          { key: 'Latin America', text: 'Latin America' },
          { key: 'Middle East', text: 'Middle East' }
        ]);
        return;
      }

      // Convert regions from user context to IDropdownOption format
      const options = userContext.accessibleResources.regions.map((region) => ({
        key: region.id,
        text: region.name
      }));

      setRegionOptions(options);

      // If user has a region but it's not in the options, select the first option
      if (currentUser?.region && !options.find(option => option.key === currentUser.region)) {
        if (options.length > 0) {
          setSelectedRegion(options[0].key as string);
        }
      }
    } catch (error) {
      console.error('Error fetching regions:', error);
      setErrorMessage('Failed to load regions. Using default values.');
      // Set some default regions as fallback
      setRegionOptions([
        { key: 'North America', text: 'North America' },
        { key: 'Europe', text: 'Europe' },
        { key: 'Asia Pacific', text: 'Asia Pacific' },
        { key: 'Latin America', text: 'Latin America' },
        { key: 'Middle East', text: 'Middle East' }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setSelectedRole(option.key as UserRole);

      // If changing to Super Admin, clear region
      if (option.key === UserRole.SUPER_ADMIN) {
        setSelectedRegion('');
      } else if (!selectedRegion && regionOptions.length > 0) {
        // If changing to a role that needs a region and no region is selected, select the first one
        setSelectedRegion(regionOptions[0].key as string);
      }
    }
  };

  const handleRegionChange = (event: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setSelectedRegion(option.key as string);
    }
  };

  const handleApply = async () => {
    if (!currentUser) return;

    // Validate that a region is selected for roles that require it
    if (selectedRole !== UserRole.SUPER_ADMIN && !selectedRegion) {
      setErrorMessage('Please select a region for this role.');
      return;
    }

    const updatedUser = {
      ...currentUser,
      role: selectedRole,
      region: selectedRole !== UserRole.SUPER_ADMIN ? selectedRegion : undefined,
      // Update the name based on the role for better UX
      name: selectedRole === UserRole.SUPER_ADMIN ? 'Super Admin' :
            selectedRole === UserRole.REGIONAL_ADMIN ? 'Regional Admin' : 'Regular User'
    };

    setCurrentUser(updatedUser);
    localStorage.setItem('currentUser', JSON.stringify(updatedUser));

    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
    }, 3000);
  };

  if (!currentUser) {
    return <Text>No user logged in</Text>;
  }

  return (
    <Stack tokens={{ childrenGap: 16 }} styles={{ root: { padding: 20, maxWidth: 400 } }}>
      <Text variant="xLarge">Role Switcher (Development Only)</Text>
      <Text>Current role: {currentUser.role}</Text>
      {currentUser.region && (
        <Text>
          Current region: {
            // Find the region name from the region ID
            regionOptions.find(r => r.key === currentUser.region)?.text || currentUser.region
          }
        </Text>
      )}

      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          onDismiss={() => setErrorMessage(null)}
        >
          {errorMessage}
        </MessageBar>
      )}

      {showSuccess && (
        <MessageBar
          messageBarType={MessageBarType.success}
          isMultiline={false}
          onDismiss={() => setShowSuccess(false)}
        >
          User role updated successfully! Refresh the page to see changes.
        </MessageBar>
      )}

      <Dropdown
        label="Select Role"
        selectedKey={selectedRole}
        options={roleOptions}
        onChange={handleRoleChange}
      />

      {selectedRole !== UserRole.SUPER_ADMIN && (
        isLoading ? (
          <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 10 } }}>
            <Spinner size={SpinnerSize.small} label="Loading regions..." />
          </Stack>
        ) : (
          <Dropdown
            label="Select Region"
            selectedKey={selectedRegion}
            options={regionOptions}
            onChange={handleRegionChange}
            required
          />
        )
      )}

      <PrimaryButton text="Apply Changes" onClick={handleApply} disabled={isLoading} />
    </Stack>
  );
};

export default RoleSwitcher;

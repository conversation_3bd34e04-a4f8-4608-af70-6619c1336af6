import React from 'react';
import { MessageBar, MessageBarType, Stack, Text, PrimaryButton } from '@fluentui/react';
import { CostWarning } from '../services/costService';

interface CostDataWarningBannerProps {
  warning: CostWarning;
  onCollectNow?: () => void;
  isCollecting?: boolean;
  showCollectButton?: boolean;
}

const CostDataWarningBanner: React.FC<CostDataWarningBannerProps> = ({
  warning,
  onCollectNow,
  isCollecting = false,
  showCollectButton = true
}) => {
  const getWarningMessage = () => {
    if (warning.hoursOld && warning.hoursOld > 0) {
      return `${warning.message} If this problem persists after 2 hours, contact the admin.`;
    }
    return `${warning.message} If this problem persists after 2 hours, contact the admin.`;
  };

  const getWarningType = (): MessageBarType => {
    if (!warning.hoursOld) {
      return MessageBarType.warning;
    }
    
    // Show different severity based on how old the data is
    if (warning.hoursOld > 6) {
      return MessageBarType.error; // Very stale data
    } else if (warning.hoursOld > 2) {
      return MessageBarType.severeWarning; // Moderately stale
    } else {
      return MessageBarType.warning; // Recently stale
    }
  };

  const getTimeInfo = () => {
    if (warning.hoursOld && warning.hoursOld > 0) {
      if (warning.hoursOld < 1) {
        const minutes = Math.round(warning.hoursOld * 60);
        return `Data is ${minutes} minutes old`;
      } else if (warning.hoursOld < 24) {
        // Format hours without decimal places for cleaner display
        const hours = Math.round(warning.hoursOld);
        return `Data is ${hours} ${hours === 1 ? 'hour' : 'hours'} old`;
      } else {
        const days = Math.round(warning.hoursOld / 24);
        return `Data is ${days} day${days > 1 ? 's' : ''} old`;
      }
    }
    return null;
  };

  return (
    <MessageBar
      messageBarType={getWarningType()}
      isMultiline={true}
      dismissButtonAriaLabel="Close"
      styles={{
        root: {
          marginBottom: 16,
        },
      }}
      actions={
        showCollectButton && onCollectNow ? (
          <div>
            <PrimaryButton
              text={isCollecting ? "Collecting..." : "Collect Now"}
              onClick={onCollectNow}
              disabled={isCollecting}
              styles={{
                root: {
                  marginLeft: 8,
                },
              }}
            />
          </div>
        ) : undefined
      }
    >
      <Stack tokens={{ childrenGap: 4 }}>
        <Text variant="medium" styles={{ root: { fontWeight: 600 } }}>
          Cost Data Collection Issue
        </Text>
        <Text variant="small">
          {getWarningMessage()}
        </Text>
        {getTimeInfo() && (
          <Text variant="small" styles={{ root: { fontStyle: 'italic', color: '#605e5c' } }}>
            {getTimeInfo()}
          </Text>
        )}
      </Stack>
    </MessageBar>
  );
};

export default CostDataWarningBanner;

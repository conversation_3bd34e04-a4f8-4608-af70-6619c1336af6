.layoutContainer {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.mainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.contentArea {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.projectContent {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  font-size: 18px;
  color: #333;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  color: #d13438;
}

.errorContainer button {
  margin-top: 20px;
  padding: 8px 16px;
  background-color: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.errorContainer button:hover {
  background-color: #106ebe;
} 
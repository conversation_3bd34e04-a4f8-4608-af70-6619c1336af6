import React from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './Sidebar.module.css';

interface Project {
  id: string;
  name: string;
  // Add other fields as needed
}

interface SidebarProps {
  project: Project;
}

const Sidebar: React.FC<SidebarProps> = ({ project }) => {
  const navigate = useNavigate();

  const handleBackToProjects = () => {
    navigate('/projects');
  };

  const navItems = [
    { label: 'Overview', icon: '📊' },
    { label: 'Data Sources', icon: '📁' },
    { label: 'Analysis', icon: '📈' }, 
    { label: 'Reports', icon: '📝' },
    { label: 'Settings', icon: '⚙️' },
  ];

  return (
    <div className={styles.sidebar}>
      <div className={styles.projectInfo}>
        <div className={styles.backButton} onClick={handleBackToProjects}>
          ← Back to Projects
        </div>
        <div className={styles.projectName}>
          <h3>{project.name}</h3>
        </div>
      </div>
      
      <nav className={styles.navigation}>
        <ul>
          {navItems.map((item, index) => (
            <li key={index} className={index === 0 ? styles.active : ''}>
              <span className={styles.icon}>{item.icon}</span>
              <span className={styles.label}>{item.label}</span>
            </li>
          ))}
        </ul>
      </nav>
      
      <div className={styles.sidebarFooter}>
        <div className={styles.helpButton}>
          <span className={styles.icon}>❓</span>
          <span>Help & Resources</span>
        </div>
      </div>
    </div>
  );
};

export default Sidebar; 
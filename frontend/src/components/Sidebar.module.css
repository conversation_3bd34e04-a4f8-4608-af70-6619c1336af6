.sidebar {
  width: 250px;
  background-color: #f8f8f8;
  border-right: 1px solid #eaeaea;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.projectInfo {
  padding: 20px;
  border-bottom: 1px solid #eaeaea;
}

.backButton {
  font-size: 14px;
  color: #0078d4;
  cursor: pointer;
  margin-bottom: 15px;
}

.backButton:hover {
  text-decoration: underline;
}

.projectName h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.navigation {
  flex: 1;
  padding: 10px 0;
}

.navigation ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.navigation li {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.navigation li:hover {
  background-color: #f0f0f0;
}

.navigation li.active {
  background-color: #e6f2fc;
  border-left: 3px solid #0078d4;
}

.icon {
  margin-right: 12px;
  font-size: 16px;
}

.label {
  font-size: 14px;
}

.sidebarFooter {
  padding: 20px;
  border-top: 1px solid #eaeaea;
}

.helpButton {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #555;
}

.helpButton:hover {
  color: #0078d4;
} 
import { initializeIcons as fluentInitializeIcons } from '@fluentui/react/lib/Icons';
import { registerIcons } from '@fluentui/react/lib/Styling';

// Custom icon initialization with local fonts for Azure deployment
export const initializeAppIcons = () => {
  try {
    console.log('Initializing Fluent UI icons with local font files...');
    
    // Initialize with local fonts
    const baseUrl = window.location.origin;
    const fontsPath = `${baseUrl}/fonts/`;
    
    console.log(`Loading fonts from: ${fontsPath}`);
    
    // Initialize Fluent UI icons with local font path
    // This tells Fluent UI where to find the font files
    fluentInitializeIcons(fontsPath, { disableWarnings: true });
    
    console.log('Fluent UI icons initialized successfully');
    
    // Verify fonts are loading after a short delay
    if ('fonts' in document) {
      setTimeout(() => {
        document.fonts.ready.then(() => {
          const fabricFonts = [...document.fonts].filter(f => f.family.includes('FabricMDL2Icons'));
          console.log(`Fluent UI icon fonts loaded: ${fabricFonts.length} font families`);
          
          // Log each loaded font
          fabricFonts.forEach(font => {
            console.log(`  - ${font.family}: ${font.status}`);
          });
          
          // Test if a common icon renders correctly
          const testIcon = document.createElement('i');
          testIcon.className = 'ms-Icon ms-Icon--Add';
          testIcon.style.fontFamily = 'FabricMDL2Icons';
          document.body.appendChild(testIcon);
          const rect = testIcon.getBoundingClientRect();
          const isRendering = rect.width > 0 && rect.height > 0;
          document.body.removeChild(testIcon);
          
          if (isRendering) {
            console.log('Icon font test successful - icons are rendering correctly');
          } else {
            console.warn('Icon font test failed - icons may not display correctly');
          }
        }).catch(err => {
          console.error('Error checking font loading status:', err);
        });
      }, 1000);
    }
    
  } catch (error) {
    console.error('Error initializing Fluent UI icons:', error);
    
    // Fallback: Try to initialize with default CDN
    try {
      console.log('Attempting fallback initialization with CDN...');
      fluentInitializeIcons();
      console.log('Fallback initialization successful');
    } catch (fallbackError) {
      console.error('Fallback initialization also failed:', fallbackError);
    }
  }
};
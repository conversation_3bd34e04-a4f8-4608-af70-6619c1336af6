// Cost cache utility to improve loading performance and reduce API calls
import { CostData, CostFilterOptions } from '../services/costService';

interface CostCache {
  data: CostData;
  timestamp: number;
  filters: Partial<CostFilterOptions>;
  userRole: string;
  userRegionId?: string;
}

const CACHE_KEY_PREFIX = 'ai_scope_cost_cache';
const CACHE_TTL = 60 * 60 * 1000; // 1 hour in milliseconds

/**
 * Generate a cache key based on filters and user context
 */
const generateCacheKey = (
  timeRange: string,
  userRole: string,
  userRegionId?: string,
  filters: Partial<CostFilterOptions> = {}
): string => {
  const keyParts = [
    CACHE_KEY_PREFIX,
    timeRange,
    userRole,
    userRegionId || 'no-region',
    filters.regionId || 'no-filter-region',
    filters.projectId || 'no-project',
    filters.resourceType || 'no-resource-type',
    filters.showSharedResources ? 'shared' : 'no-shared'
  ];
  return keyParts.join('_');
};

/**
 * Save cost data to local storage cache
 */
export const cacheCostData = (
  costData: CostData,
  timeRange: string,
  userRole: string,
  userRegionId?: string,
  filters: Partial<CostFilterOptions> = {}
): void => {
  try {
    const cacheKey = generateCacheKey(timeRange, userRole, userRegionId, filters);
    const cacheData: CostCache = {
      data: costData,
      timestamp: Date.now(),
      filters,
      userRole,
      userRegionId
    };
    
    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
    console.log(`Cached cost data for key: ${cacheKey}`);
  } catch (error) {
    console.error('Failed to cache cost data:', error);
  }
};

/**
 * Get cost data from local storage cache
 * Returns null if cache is expired or not available
 */
export const getCachedCostData = (
  timeRange: string,
  userRole: string,
  userRegionId?: string,
  filters: Partial<CostFilterOptions> = {}
): CostData | null => {
  try {
    const cacheKey = generateCacheKey(timeRange, userRole, userRegionId, filters);
    const cachedData = localStorage.getItem(cacheKey);
    
    if (!cachedData) {
      return null;
    }

    const cache: CostCache = JSON.parse(cachedData);
    const now = Date.now();
    
    // Check if cache is expired
    if (now - cache.timestamp > CACHE_TTL) {
      console.log('Cost cache expired');
      localStorage.removeItem(cacheKey);
      return null;
    }

    // Verify cache matches current context
    if (cache.userRole !== userRole || cache.userRegionId !== userRegionId) {
      console.log('Cost cache context mismatch');
      localStorage.removeItem(cacheKey);
      return null;
    }

    const ageInMinutes = Math.round((now - cache.timestamp) / (1000 * 60));
    console.log(`📦 Retrieved cost data from cache (age: ${ageInMinutes} minutes)`);
    
    return cache.data;
  } catch (error) {
    console.error('Failed to retrieve cached cost data:', error);
    return null;
  }
};

/**
 * Clear all cost-related cache entries
 * This is called when "Collect Now" is triggered to force fresh data
 */
export const clearCostCache = (): void => {
  try {
    // Get all keys in localStorage
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(CACHE_KEY_PREFIX)) {
        keys.push(key);
      }
    }
    
    // Remove all cost-related cache items
    keys.forEach(key => localStorage.removeItem(key));
    console.log(`Cleared ${keys.length} cost cache items`);
  } catch (error) {
    console.error('Failed to clear cost cache:', error);
  }
};

/**
 * Clear cache for specific filters (useful for partial invalidation)
 */
export const clearCostCacheForFilters = (
  timeRange: string,
  userRole: string,
  userRegionId?: string,
  filters: Partial<CostFilterOptions> = {}
): void => {
  try {
    const cacheKey = generateCacheKey(timeRange, userRole, userRegionId, filters);
    localStorage.removeItem(cacheKey);
    console.log(`Cleared cost cache for key: ${cacheKey}`);
  } catch (error) {
    console.error('Failed to clear specific cost cache:', error);
  }
};

/**
 * Get cache statistics for debugging
 */
export const getCostCacheStats = (): { totalEntries: number; totalSize: number; entries: string[] } => {
  try {
    const entries = [];
    let totalSize = 0;
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith(CACHE_KEY_PREFIX)) {
        const value = localStorage.getItem(key);
        if (value) {
          entries.push(key);
          totalSize += value.length;
        }
      }
    }
    
    return {
      totalEntries: entries.length,
      totalSize,
      entries
    };
  } catch (error) {
    console.error('Failed to get cost cache stats:', error);
    return { totalEntries: 0, totalSize: 0, entries: [] };
  }
};

/**
 * Get cache age in minutes
 */
export const getCostCacheAge = (
  timeRange: string,
  userRole: string,
  userRegionId?: string,
  filters: Partial<CostFilterOptions> = {}
): number | null => {
  try {
    const cacheKey = generateCacheKey(timeRange, userRole, userRegionId, filters);
    const cachedData = localStorage.getItem(cacheKey);
    
    if (!cachedData) {
      return null;
    }

    const cache: CostCache = JSON.parse(cachedData);
    const now = Date.now();
    const ageInMinutes = Math.round((now - cache.timestamp) / (1000 * 60));
    
    return ageInMinutes;
  } catch (error) {
    console.error('Failed to get cache age:', error);
    return null;
  }
};

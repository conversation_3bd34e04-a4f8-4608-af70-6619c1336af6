// Project cache utility to improve loading performance

interface Project {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  search_index: string;
  storage_container: string;
  role: 'owner' | 'contributor' | 'viewer';
  environment: Record<string, string>;
  color?: string;
  icon?: string;
  deploymentStatus?: 'pending' | 'in_progress' | 'completed' | 'failed';
}

interface ProjectCache {
  projects: Project[];
  timestamp: number;
}

const CACHE_KEY = 'ai_scope_projects_cache';
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

/**
 * Save projects to local storage cache
 */
export const cacheProjects = (projects: Project[]): void => {
  try {
    const cacheData: ProjectCache = {
      projects,
      timestamp: Date.now()
    };
    localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
    console.log(`Cached ${projects.length} projects`);
  } catch (error) {
    console.error('Failed to cache projects:', error);
  }
};

/**
 * Get projects from local storage cache
 * Returns null if cache is expired or not available
 */
export const getCachedProjects = (): Project[] | null => {
  try {
    const cachedData = localStorage.getItem(CACHE_KEY);
    if (!cachedData) {
      return null;
    }

    const cache: ProjectCache = JSON.parse(cachedData);
    const now = Date.now();
    
    // Check if cache is expired
    if (now - cache.timestamp > CACHE_TTL) {
      console.log('Project cache expired');
      return null;
    }

    console.log(`Retrieved ${cache.projects.length} projects from cache (age: ${Math.round((now - cache.timestamp) / 1000)}s)`);
    return cache.projects;
  } catch (error) {
    console.error('Failed to retrieve cached projects:', error);
    return null;
  }
};

/**
 * Cache deployment status for a specific project
 */
export const cacheProjectStatus = (projectId: string, status: 'pending' | 'in_progress' | 'completed' | 'failed'): void => {
  try {
    const statusCacheKey = `${CACHE_KEY}_status_${projectId}`;
    const statusData = {
      status,
      timestamp: Date.now()
    };
    localStorage.setItem(statusCacheKey, JSON.stringify(statusData));
  } catch (error) {
    console.error(`Failed to cache status for project ${projectId}:`, error);
  }
};

/**
 * Get cached deployment status for a specific project
 */
export const getCachedProjectStatus = (projectId: string): 'pending' | 'in_progress' | 'completed' | 'failed' | null => {
  try {
    const statusCacheKey = `${CACHE_KEY}_status_${projectId}`;
    const cachedData = localStorage.getItem(statusCacheKey);
    if (!cachedData) {
      return null;
    }

    const statusCache = JSON.parse(cachedData);
    const now = Date.now();
    
    // Status cache has a shorter TTL (2 minutes)
    if (now - statusCache.timestamp > 2 * 60 * 1000) {
      return null;
    }

    return statusCache.status;
  } catch (error) {
    console.error(`Failed to retrieve cached status for project ${projectId}:`, error);
    return null;
  }
};

/**
 * Clear all project-related cache
 */
export const clearProjectCache = (): void => {
  try {
    // Get all keys in localStorage
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key === CACHE_KEY || key.startsWith(`${CACHE_KEY}_status_`))) {
        keys.push(key);
      }
    }
    
    // Remove all project-related cache items
    keys.forEach(key => localStorage.removeItem(key));
    console.log(`Cleared ${keys.length} project cache items`);
  } catch (error) {
    console.error('Failed to clear project cache:', error);
  }
};

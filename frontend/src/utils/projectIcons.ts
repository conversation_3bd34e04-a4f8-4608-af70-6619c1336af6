// Utility for managing project icons

/**
 * Curated list of business-appropriate emoji icons
 * These icons represent different business concepts and are suitable for project representation
 */
export const businessIcons = [
  '📊', // Bar chart
  '📈', // Chart increasing
  '📉', // Chart decreasing
  '📋', // Clipboard
  '📝', // Memo
  '📑', // Bookmark tabs
  '📚', // Books
  '📁', // Folder
  '📂', // Open folder
  '🗂️', // Card index dividers
  '📒', // Ledger
  '📓', // Notebook
  '📔', // Notebook with decorative cover
  '📕', // Closed book
  '📗', // Green book
  '📘', // Blue book
  '📙', // Orange book
  '💼', // Briefcase
  '🗄️', // File cabinet
  '🖥️', // Desktop computer
  '💻', // Laptop
  '🖨️', // Printer
  '📱', // Mobile phone
  '📞', // Telephone
  '📧', // Email
  '🔍', // Magnifying glass
  '🔎', // Magnifying glass tilted right
  '🔬', // Microscope
  '🔭', // Telescope
  '📡', // Satellite antenna
  '💡', // Light bulb
  '⚙️', // <PERSON>
  '🔧', // Wrench
  '🔨', // <PERSON>
  '🛠️', // <PERSON> and wrench
  '📌', // Pushpin
  '📍', // Round pushpin
  '✂️', // Scissors
  '📏', // Straight ruler
  '📐', // Triangular ruler
  '🧮', // Abacus
  '🌐', // Globe with meridians
  '🏢', // Office building
  '🏭', // Factory
  '🏗️', // Building construction
  '🚀', // Rocket
  '⏱️', // Stopwatch
  '📅', // Calendar
  '📆', // Tear-off calendar
  '🗓️', // Spiral calendar
  '⏰', // Alarm clock
  '🧠', // Brain
  '👥', // Busts in silhouette
  '🤝', // Handshake
  '🔐', // Locked with key
  '🔑', // Key
  '📊', // Bar chart
  '📈', // Chart increasing
  '📉', // Chart decreasing
];

/**
 * Get a random business icon from the curated list
 * @returns A random business emoji icon
 */
export const getRandomBusinessIcon = (): string => {
  return businessIcons[Math.floor(Math.random() * businessIcons.length)];
};

/**
 * Validates if an icon is in the approved business icons list
 * @param icon The icon to validate
 * @returns True if the icon is in the approved list, false otherwise
 */
export const isValidBusinessIcon = (icon: string): boolean => {
  return businessIcons.includes(icon);
};

/**
 * Gets a fallback icon if the provided icon is invalid
 * @param icon The icon to check
 * @returns The original icon if valid, or a random business icon if invalid
 */
export const getValidIconOrFallback = (icon: string | undefined): string => {
  if (icon && isValidBusinessIcon(icon)) {
    return icon;
  }
  return getRandomBusinessIcon();
};

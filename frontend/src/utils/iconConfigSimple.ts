import { initializeIcons as fluentInitializeIcons } from '@fluentui/react/lib/Icons';

// Simple icon initialization that just uses the default CDN
export const initializeAppIcons = () => {
  try {
    console.log('Initializing icons with default Fluent UI settings');
    
    // Initialize with no parameters - this uses the default CDN
    fluentInitializeIcons();
    
    console.log('Icons initialized with default settings');
    
    // Add a simple test after a delay
    setTimeout(() => {
      console.log('Checking font loading...');
      if ('fonts' in document) {
        document.fonts.ready.then(() => {
          const allFonts = [...document.fonts];
          console.log('Total fonts loaded:', allFonts.length);
          allFonts.forEach(font => {
            if (font.family.includes('Fabric') || font.family.includes('MDL2')) {
              console.log(`Icon font: ${font.family} - ${font.status}`);
            }
          });
        });
      }
    }, 3000);
  } catch (error) {
    console.error('Error initializing icons:', error);
  }
};
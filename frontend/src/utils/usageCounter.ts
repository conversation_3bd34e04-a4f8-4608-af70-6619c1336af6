// frontend/src/utils/usageCounter.ts
/**
 * Utility for tracking and limiting user actions with a daily quota
 */

// Constants
const STORAGE_KEY_PREFIX = 'ai_scope_usage_counter_';
const DEFAULT_DAILY_LIMIT = 3;
const MS_PER_DAY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Interface for the counter data stored in localStorage
interface UsageCounterData {
  count: number;
  lastReset: number; // timestamp when the counter was last reset
}

/**
 * Get the current usage count for a specific action
 * @param actionId Unique identifier for the action being tracked
 * @returns Current count and remaining uses
 */
export const getUsageCount = (actionId: string): { 
  count: number; 
  remaining: number;
  isLimitReached: boolean;
  lastReset: number;
} => {
  const storageKey = `${STORAGE_KEY_PREFIX}${actionId}`;
  const now = Date.now();
  
  try {
    // Get current data from localStorage
    const storedData = localStorage.getItem(storageKey);
    let data: UsageCounterData;
    
    if (storedData) {
      data = JSON.parse(storedData);
      
      // Check if we need to reset the counter (new day)
      const timeSinceReset = now - data.lastReset;
      if (timeSinceReset >= MS_PER_DAY) {
        // Reset counter if it's been more than 24 hours
        data = {
          count: 0,
          lastReset: now
        };
        localStorage.setItem(storageKey, JSON.stringify(data));
      }
    } else {
      // Initialize counter if it doesn't exist
      data = {
        count: 0,
        lastReset: now
      };
      localStorage.setItem(storageKey, JSON.stringify(data));
    }
    
    const remaining = Math.max(0, DEFAULT_DAILY_LIMIT - data.count);
    return {
      count: data.count,
      remaining,
      isLimitReached: data.count >= DEFAULT_DAILY_LIMIT,
      lastReset: data.lastReset
    };
  } catch (error) {
    console.error('Error accessing usage counter:', error);
    // Return default values if there's an error
    return {
      count: 0,
      remaining: DEFAULT_DAILY_LIMIT,
      isLimitReached: false,
      lastReset: now
    };
  }
};

/**
 * Increment the usage counter for a specific action
 * @param actionId Unique identifier for the action being tracked
 * @returns Updated count information
 */
export const incrementUsageCount = (actionId: string): { 
  count: number; 
  remaining: number;
  isLimitReached: boolean;
  lastReset: number;
} => {
  const storageKey = `${STORAGE_KEY_PREFIX}${actionId}`;
  const now = Date.now();
  
  try {
    // Get current counter state
    const currentState = getUsageCount(actionId);
    
    // Don't increment if limit is already reached
    if (currentState.isLimitReached) {
      return currentState;
    }
    
    // Increment the counter
    const newCount = currentState.count + 1;
    const data: UsageCounterData = {
      count: newCount,
      lastReset: currentState.lastReset
    };
    
    // Save updated counter
    localStorage.setItem(storageKey, JSON.stringify(data));
    
    const remaining = Math.max(0, DEFAULT_DAILY_LIMIT - newCount);
    return {
      count: newCount,
      remaining,
      isLimitReached: newCount >= DEFAULT_DAILY_LIMIT,
      lastReset: currentState.lastReset
    };
  } catch (error) {
    console.error('Error incrementing usage counter:', error);
    // Return default values if there's an error
    return {
      count: 0,
      remaining: DEFAULT_DAILY_LIMIT,
      isLimitReached: false,
      lastReset: now
    };
  }
};

/**
 * Reset the usage counter for a specific action
 * @param actionId Unique identifier for the action being tracked
 */
export const resetUsageCount = (actionId: string): void => {
  const storageKey = `${STORAGE_KEY_PREFIX}${actionId}`;
  const now = Date.now();
  
  try {
    const data: UsageCounterData = {
      count: 0,
      lastReset: now
    };
    localStorage.setItem(storageKey, JSON.stringify(data));
  } catch (error) {
    console.error('Error resetting usage counter:', error);
  }
};

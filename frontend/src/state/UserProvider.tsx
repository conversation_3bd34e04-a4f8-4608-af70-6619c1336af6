import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { UserProfile, UserRole } from '../models/roles';
import userContextService, { UserContextData } from '../services/userContextService';

// Define the context shape
interface UserContextType {
  currentUser: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  setCurrentUser: (user: UserProfile | null) => void;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

// Create the context with default values
const UserContext = createContext<UserContextType | undefined>(undefined);

// Custom hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

interface UserProviderProps {
  children: ReactNode;
}

// Import MSAL instance for authentication
import { msalInstance, getAccessToken, msalReady } from '../auth/msal-config';
import * as WebSocketManager from '../services/websocketManager';
import { performLogout } from '../services/authService';

// Function to fetch user data from the user context API
const fetchUserData = async (userId: string = ''): Promise<UserProfile> => {
  try {
    // First, check if we have an authenticated user from MSAL
    const accounts = msalInstance.getAllAccounts();
    const activeAccount = msalInstance.getActiveAccount();

    // If we have an active account, use it to get user info
    if (activeAccount || accounts.length > 0) {
      console.log('Found authenticated user in MSAL');

      // Get the access token for API calls
      const accessToken = await getAccessToken();

      // Use the account that's active or the first one
      const account = activeAccount || accounts[0];

      // Try to get user data from the backend with the access token
      try {
        // Fetch the user context from the backend
        const userContext = await userContextService.fetchUserContext(true);

        if (userContext && userContext.user) {
          console.log('Successfully fetched user context from backend');

          // Convert the user context data to our UserProfile format
          return {
            id: userContext.user.id,
            name: userContext.user.name || account.name || 'User',
            email: userContext.user.email || account.username || '<EMAIL>',
            role: userContext.user.role as UserRole,
            region: userContext.user.region,
            avatar: userContext.user.avatar,
            // Store permissions in the user object for easy access
            permissions: userContext.permissions,
            accessibleResources: userContext.accessibleResources
          };
        }
      } catch (apiError) {
        console.error('Error fetching user context from backend:', apiError);
        // Fall through to use MSAL account info
      }

      // If we couldn't get user data from the backend, use MSAL account info
      console.log('Using MSAL account info for user data');

      // Create a user profile from MSAL account info
      // Default to Super Admin role for now - in a real app, this would come from claims
      return {
        id: account.localAccountId || account.homeAccountId || '1',
        name: account.name || 'User',
        email: account.username || '<EMAIL>',
        role: UserRole.SUPER_ADMIN, // Default role - should be determined by claims
        avatar: 'https://via.placeholder.com/150',
        permissions: {
          canCreateProject: true,
          canEditProject: true,
          canDeleteProject: true,
          canAssignUsers: true,
          canSetCostLimits: true,
          canAccessAdminPanel: true,
          canManageUsers: true,
          canManageGlobalSettings: true,
          canCreateTeams: true,
          canAssignProjects: true,
          canAssignTeams: true,
          canSetupRegionalAdmins: true,
          canTagUsers: true,
          canViewAllRegions: true
        },
        accessibleResources: { projects: [], teams: [], regions: [], users: [] }
      };
    }

    // If no MSAL account, try to get user data from the backend
    console.log('No MSAL account found, trying to get user data from backend');

    // Check if we have a stored user ID
    const storedUser = localStorage.getItem('currentUser');
    let userIdToUse = userId;

    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        userIdToUse = parsedUser.id || userId;
      } catch (parseError) {
        console.error('Error parsing stored user:', parseError);
      }
    }

    // If we have a user ID, store it for the user context service
    if (userIdToUse) {
      const tempUser = { id: userIdToUse };
      localStorage.setItem('currentUser', JSON.stringify(tempUser));
    }

    // Try to fetch user context from the backend
    const userContext = await userContextService.fetchUserContext(true);

    if (userContext && userContext.user) {
      console.log('Successfully fetched user context from backend');

      // Convert the user context data to our UserProfile format
      return {
        id: userContext.user.id,
        name: userContext.user.name,
        email: userContext.user.email,
        role: userContext.user.role as UserRole,
        region: userContext.user.region,
        avatar: userContext.user.avatar,
        permissions: userContext.permissions,
        accessibleResources: userContext.accessibleResources
      };
    }

    // If all else fails, use a fallback mock user
    console.warn('Using fallback mock user data');

    return {
      id: '1',
      name: 'Super Admin',
      email: '<EMAIL>',
      role: UserRole.SUPER_ADMIN,
      avatar: 'https://via.placeholder.com/150',
      permissions: {
        canCreateProject: true,
        canEditProject: true,
        canDeleteProject: true,
        canAssignUsers: true,
        canSetCostLimits: true,
        canAccessAdminPanel: true,
        canManageUsers: true,
        canManageGlobalSettings: true,
        canCreateTeams: true,
        canAssignProjects: true,
        canAssignTeams: true,
        canSetupRegionalAdmins: true,
        canTagUsers: true,
        canViewAllRegions: true
      },
      accessibleResources: { projects: [], teams: [], regions: [], users: [] }
    };
  } catch (error) {
    console.error('Error in fetchUserData:', error);

    // Final fallback
    return {
      id: '1',
      name: 'Super Admin',
      email: '<EMAIL>',
      role: UserRole.SUPER_ADMIN,
      avatar: 'https://via.placeholder.com/150',
      permissions: {
        canCreateProject: true,
        canEditProject: true,
        canDeleteProject: true,
        canAssignUsers: true,
        canSetCostLimits: true,
        canAccessAdminPanel: true,
        canManageUsers: true,
        canManageGlobalSettings: true,
        canCreateTeams: true,
        canAssignProjects: true,
        canAssignTeams: true,
        canSetupRegionalAdmins: true,
        canTagUsers: true,
        canViewAllRegions: true
      },
      accessibleResources: { projects: [], teams: [], regions: [], users: [] }
    };
  }
};

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUser = async () => {
      try {
        setIsLoading(true);

        // Wait for MSAL to be fully initialized
        console.log('UserProvider: Waiting for MSAL initialization...');
        await msalReady;
        console.log('UserProvider: MSAL initialized');

        // Check if we have MSAL accounts
        const accounts = msalInstance.getAllAccounts();
        console.log('UserProvider: MSAL accounts:', accounts);

        // If we have MSAL accounts, always prioritize getting fresh user data from the backend
        if (accounts.length > 0) {
          console.log('UserProvider: Found MSAL accounts, fetching fresh user data from backend');

          try {
            // Force refresh user data from backend using the token
            const userContext = await userContextService.fetchUserContext(true);

            if (userContext && userContext.user) {
              console.log('UserProvider: Successfully fetched fresh user data from backend');

              // Convert the user context data to our UserProfile format
              const userProfile: UserProfile = {
                id: userContext.user.id,
                name: userContext.user.name || accounts[0].name || 'User',
                email: userContext.user.email || accounts[0].username || '<EMAIL>',
                role: userContext.user.role as UserRole,
                region: userContext.user.region,
                avatar: userContext.user.avatar,
                permissions: userContext.permissions,
                accessibleResources: userContext.accessibleResources
              };

              console.log('UserProvider: Setting user with role from backend:', userProfile.role);
              setCurrentUser(userProfile);
              localStorage.setItem('currentUser', JSON.stringify(userProfile));
              return;
            }
          } catch (backendError) {
            console.error('UserProvider: Error fetching user data from backend:', backendError);
            // Continue to fallback mechanisms
          }
        }

        // Check if we have a stored user in localStorage as a fallback
        const storedUser = localStorage.getItem('currentUser');
        console.log('UserProvider: Stored user:', storedUser);

        if (storedUser) {
          console.log('UserProvider: Found stored user, fetching latest data');
          const parsedUser = JSON.parse(storedUser);

          try {
            // Always try to get fresh data from the backend
            console.log('UserProvider: Fetching latest user data for ID:', parsedUser.id);
            const userData = await fetchUserData(parsedUser.id);
            console.log('UserProvider: Fetched user data:', userData);

            // Update the stored user with the latest data
            const updatedUser = {
              ...userData,
              // Only keep the region from stored user if it's not provided by backend
              region: userData.region || parsedUser.region,
              // Preserve the name and email if not provided by backend
              name: userData.name || parsedUser.name,
              email: userData.email || parsedUser.email
            };

            console.log('UserProvider: Using role from backend:', userData.role);
            console.log('UserProvider: Setting current user to:', updatedUser);
            setCurrentUser(updatedUser);
            localStorage.setItem('currentUser', JSON.stringify(updatedUser));
          } catch (fetchError) {
            // If we can't fetch the latest data, use the stored user as last resort
            console.warn('UserProvider: Failed to fetch latest user data, using stored user', fetchError);
            console.log('UserProvider: Setting current user to stored user:', parsedUser);
            setCurrentUser(parsedUser);
          }
        } else {
          // If no stored user and no MSAL accounts, fetch default user
          console.log('UserProvider: No stored user or MSAL accounts, fetching default user');
          const userData = await fetchUserData();
          console.log('UserProvider: Fetched default user data:', userData);
          setCurrentUser(userData);
          localStorage.setItem('currentUser', JSON.stringify(userData));
        }
      } catch (err) {
        setError('Failed to load user data');
        console.error('UserProvider: Error loading user:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  const logout = async () => {
    try {
      setCurrentUser(null);
      localStorage.removeItem('currentUser');
      localStorage.removeItem('isAuthenticated');

      WebSocketManager.closeAllWebSockets();
      await performLogout();
    } catch (error) {
      console.error('Error during logout:', error);
      window.location.reload();
    }
  };

  const refreshUser = async () => {
    try {
      console.log('UserProvider: Refreshing user data...');
      
      // Clear cache first
      userContextService.clearCache();
      
      // Fetch fresh user data
      const userContext = await userContextService.fetchUserContext(true);
      
      if (userContext && userContext.user) {
        const userProfile: UserProfile = {
          id: userContext.user.id,
          name: userContext.user.name || currentUser?.name || 'User',
          email: userContext.user.email || currentUser?.email || '<EMAIL>',
          role: userContext.user.role as UserRole,
          region: userContext.user.region,
          avatar: userContext.user.avatar,
          permissions: userContext.permissions,
          accessibleResources: userContext.accessibleResources
        };
        
        setCurrentUser(userProfile);
        localStorage.setItem('currentUser', JSON.stringify(userProfile));
        console.log('UserProvider: User data refreshed successfully');
      }
    } catch (error) {
      console.error('UserProvider: Error refreshing user:', error);
      setError('Failed to refresh user data');
    }
  };

  return (
    <UserContext.Provider
      value={{
        currentUser,
        isLoading,
        error,
        setCurrentUser,
        logout,
        refreshUser
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

import React from 'react';
import { Stack, Text, Link, Separator } from '@fluentui/react';
import { useNavigate } from 'react-router-dom';
import RoleSwitcher from '../components/RoleSwitcher';
import RoleBasedHeader from '../components/RoleBasedHeader';
import IconDiagnostic from '../components/IconDiagnostic';

const DevTools: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <RoleBasedHeader />

      <Stack styles={{ root: { flex: 1, padding: '20px 40px' } }}>
        <Text variant="xxLarge" styles={{ root: { marginBottom: 20 } }}>
          Development Tools
        </Text>

        <Separator />

        <Stack horizontal tokens={{ childrenGap: 40 }} styles={{ root: { marginTop: 20 } }}>
          <Stack styles={{ root: { flex: 1 } }}>
            <RoleSwitcher />
          </Stack>

          <Stack styles={{ root: { flex: 1 } }}>
            <Text variant="xLarge">Quick Navigation</Text>
            <Stack tokens={{ childrenGap: 8 }} styles={{ root: { marginTop: 16 } }}>
              <Link onClick={() => navigate('/projects')}>Projects Dashboard</Link>
              <Link onClick={() => navigate('/admin')}>Admin Panel</Link>
              <Link onClick={() => navigate('/admin/users')}>User Management</Link>
              <Link onClick={() => navigate('/admin/global-settings')}>Global Settings</Link>
              <Link onClick={() => navigate('/admin/project-settings')}>Project Settings</Link>
              <Link onClick={() => navigate('/admin/teams')}>Team Management</Link>
              <Link onClick={() => navigate('/admin/tags')}>User Tags</Link>
              <Link onClick={() => navigate('/admin/regional-admins')}>Regional Admins</Link>
              <Link onClick={() => navigate('/admin/regions')}>Regions</Link>
              <Link onClick={() => navigate('/admin/cost-analytics')}>Cost Analytics</Link>
            </Stack>
          </Stack>
        </Stack>

        <Separator styles={{ root: { marginTop: 40, marginBottom: 20 } }} />

        <Stack>
          <Text variant="xLarge" styles={{ root: { marginBottom: 20 } }}>
            Icon Diagnostics
          </Text>
          <IconDiagnostic />
        </Stack>
      </Stack>
    </div>
  );
};

export default DevTools;

import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { navigateToProjects } from '../utils/navigation';

/**
 * AuthDone component
 * 
 * This component handles the Azure App Service authentication completion page.
 * It automatically redirects the user to the projects page after successful authentication
 * without requiring a second login through MSAL.
 */
const AuthDone: React.FC = () => {
  const navigate = useNavigate();
  const { instance } = useMsal();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthDone = async () => {
      try {
        console.log('Auth done page loaded, redirecting directly to projects');
        
        // Set authentication state in localStorage
        localStorage.setItem('isAuthenticated', 'true');
        
        // Try to get the current account from MSAL if available
        const accounts = instance.getAllAccounts();
        if (accounts.length > 0) {
          console.log('Found MSAL account, setting as active');
          instance.setActiveAccount(accounts[0]);
        } else {
          console.log('No MSAL account found, but proceeding as authenticated via Azure App Service');
        }
        
        // Redirect directly to projects page
        navigateToProjects();
      } catch (err) {
        console.error('Error handling auth done:', err);
        setError(`Authentication error: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setLoading(false);
      }
    };

    handleAuthDone();
  }, [instance, navigate]);

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh',
        padding: '20px',
        textAlign: 'center'
      }}>
        <div style={{ color: 'red', marginBottom: '20px' }}>
          <h2>Authentication Error</h2>
          <p>{error}</p>
          <button
            onClick={() => navigateToProjects()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#0078d4',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginTop: '20px'
            }}
          >
            Try Going to Projects
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center', 
      height: '100vh',
      padding: '20px',
      textAlign: 'center'
    }}>
      <h2>Authentication Complete</h2>
      <p>You have successfully signed in. Redirecting to projects...</p>
      <div style={{
        width: '50px',
        height: '50px',
        border: '5px solid #f3f3f3',
        borderTop: '5px solid #3498db',
        borderRadius: '50%',
        animation: 'spin 2s linear infinite',
        marginTop: '20px'
      }} />
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default AuthDone;

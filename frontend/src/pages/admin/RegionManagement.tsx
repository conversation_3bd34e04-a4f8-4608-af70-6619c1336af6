import React, { useState, useEffect } from 'react';
import {
  DetailsList,
  DetailsListLayoutMode,
  SelectionMode,
  IColumn,
  PrimaryButton,
  CommandBar,
  ICommandBarItemProps,
  Stack,
  TextField,
  Dialog,
  DialogType,
  DialogFooter,
  DefaultButton,
  MessageBar,
  MessageBarType,
  Text,
  SearchBox,
  IconButton,
  Spinner,
  SpinnerSize,
  IIconProps,
  TooltipHost,
  ITooltipHostStyles,
  DirectionalHint,
  SpinButton,
  ISpinButtonStyles
} from '@fluentui/react';
import { useUser } from '../../state/UserProvider';
import { UserRole } from '../../models/roles';
import { RegionData, ApiResponse } from '../../services/rbacService'; // Import types

// Mock data for development/testing
const mockRegions: RegionData[] = [
  {
    id: 'north-america',
    name: 'North America',
    description: 'United States and Canada',
    cost_limit: 10000,
    created_by: '1',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 'europe',
    name: 'Europe',
    description: 'European Union countries',
    cost_limit: 8000,
    created_by: '1',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  },
  {
    id: 'asia-pacific',
    name: 'Asia Pacific',
    description: 'Asia and Pacific countries',
    cost_limit: 7000,
    created_by: '1',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z'
  }
];

// Icon for tooltip
const infoIcon: IIconProps = { iconName: 'Info' };

// Tooltip styles
const tooltipStyles: Partial<ITooltipHostStyles> = {
  root: { display: 'inline-block', marginLeft: 8 }
};

// SpinButton styles
const spinButtonStyles: Partial<ISpinButtonStyles> = {
  root: { width: 200 }
};

const RegionManagement: React.FC = () => {
  const { currentUser } = useUser();
  const [regions, setRegions] = useState<RegionData[]>([]);
  const [filteredRegions, setFilteredRegions] = useState<RegionData[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isAddRegionDialogOpen, setIsAddRegionDialogOpen] = useState(false);
  const [isEditRegionDialogOpen, setIsEditRegionDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState<RegionData | null>(null);
  const [newRegion, setNewRegion] = useState<Partial<RegionData>>({
    name: '',
    description: '',
    cost_limit: 5000
  });
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Columns for the DetailsList
  const columns: IColumn[] = [
    {
      key: 'name',
      name: 'Region Name',
      fieldName: 'name',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'description',
      name: 'Description',
      fieldName: 'description',
      minWidth: 200,
      maxWidth: 300,
      isResizable: true
    },
    {
      key: 'cost_limit',
      name: 'Cost Limit (€)',
      fieldName: 'cost_limit',
      minWidth: 100,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: RegionData) => (
        <span>{item.cost_limit ? `€${item.cost_limit.toLocaleString()}` : 'N/A'}</span>
      )
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 100,
      maxWidth: 100,
      isResizable: false,
      onRender: (item: RegionData) => (
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <IconButton
            iconProps={{ iconName: 'Edit' }}
            title="Edit"
            ariaLabel="Edit"
            onClick={() => handleEditRegion(item)}
          />
        </Stack>
      )
    }
  ];

  // Command bar items
  const commandItems: ICommandBarItemProps[] = [
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => {
        void loadRegions(); // Use void operator to ignore the Promise
        return; // Return void explicitly
      }
    }
  ];

  // Load regions on component mount
  useEffect(() => {
    loadRegions();
  }, []);

  // Filter regions when search text changes
  useEffect(() => {
    if (searchText) {
      const filtered = regions.filter(region =>
        region.name.toLowerCase().includes(searchText.toLowerCase()) ||
        (region.description && region.description.toLowerCase().includes(searchText.toLowerCase()))
      );
      setFilteredRegions(filtered);
    } else {
      setFilteredRegions(regions);
    }
  }, [searchText, regions]);

  // Load regions from RBAC API
  const loadRegions = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);
      console.log('Fetching regions with rbacService');

      // Call the API to get regions using rbacService
      const response = await rbacService.regions.getRegions();

      if (!response || !response.data) {
        setErrorMessage(response?.error || 'Error loading regions: Unknown error');
        setRegions(mockRegions);
        setFilteredRegions(mockRegions);
        return;
      }

      setRegions(response.data);
      setFilteredRegions(response.data);
    } catch (error) {
      console.error('Error fetching regions:', error);
      setErrorMessage('Failed to load regions. Using mock data for development.');
      // Fallback to mock data for development
      setRegions(mockRegions);
      setFilteredRegions(mockRegions);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle adding a new region
  const handleAddRegion = async () => {
    // Validate form
    if (!newRegion.name) {
      setErrorMessage('Region name is required.');
      return;
    }

    try {
      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);
      console.log('Creating region with rbacService');

      // Call API to create region using rbacService
      const response = await rbacService.regions.createRegion({
        name: newRegion.name,
        description: newRegion.description,
        cost_limit: newRegion.cost_limit,
      });

      if (!response || response.error) {
        setErrorMessage(response?.error || 'Failed to create region: Unknown error');
        return;
      }

      // Refresh regions list
      await loadRegions();

      setIsAddRegionDialogOpen(false);
      setNewRegion({
        name: '',
        description: '',
        cost_limit: 5000
      });
      setErrorMessage(null);

      // Show success message
      setSuccessMessage(`Region "${newRegion.name}" created successfully.`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error creating region:', error);
      setErrorMessage('Failed to create region. Please try again later.');
    }
  };

  // Handle editing a region
  const handleEditRegion = (region: RegionData) => {
    setSelectedRegion(region);
    setNewRegion({
      name: region.name,
      description: region.description,
      cost_limit: region.cost_limit
    });
    setIsEditRegionDialogOpen(true);
  };

  // Handle saving edited region
  const handleSaveEdit = async () => {
    if (!selectedRegion) return;

    // Validate form
    if (!newRegion.name) {
      setErrorMessage('Region name is required.');
      return;
    }

    try {
      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);
      console.log('Updating region with rbacService');

      // Call API to update region using rbacService
      const response = await rbacService.regions.updateRegion(selectedRegion.id, {
        name: newRegion.name,
        description: newRegion.description,
        cost_limit: newRegion.cost_limit,
      });

      if (!response || response.error) {
        setErrorMessage(response?.error || 'Failed to update region: Unknown error');
        return;
      }

      // Refresh regions list
      await loadRegions();

      setIsEditRegionDialogOpen(false);
      setSelectedRegion(null);
      setNewRegion({
        name: '',
        description: '',
        cost_limit: 5000
      });
      setErrorMessage(null);

      // Show success message
      setSuccessMessage(`Region "${newRegion.name}" updated successfully.`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error updating region:', error);
      setErrorMessage('Failed to update region. Please try again later.');
    }
  };

  // Handle delete click
  const handleDeleteClick = (region: RegionData) => {
    setSelectedRegion(region);
    setIsDeleteDialogOpen(true);
  };

  // Handle deleting a region
  const handleDeleteRegion = async () => {
    if (!selectedRegion) return;

    try {
      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);
      console.log('Deleting region with rbacService');

      // Call API to delete region using rbacService
      const response = await rbacService.regions.deleteRegion(selectedRegion.id);

      console.log('Delete region response:', response);

      if (!response || response.error) {
        const errorMsg = response?.error || 'Failed to delete region: Unknown error';
        console.error('Delete region error:', errorMsg);
        setErrorMessage(errorMsg);
        return;
      }

      // Refresh regions list
      await loadRegions();

      setIsDeleteDialogOpen(false);
      setSelectedRegion(null);

      // Show success message
      setSuccessMessage(`Region "${selectedRegion.name}" deleted successfully.`);
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error deleting region:', error);
      setErrorMessage('Failed to delete region. Please try again later.');
    }
  };

  // Check if user is authorized to manage regions
  if (currentUser?.role !== UserRole.SUPER_ADMIN) {
    return (
      <MessageBar
        messageBarType={MessageBarType.error}
        isMultiline={false}
      >
        You do not have permission to manage regions. Only Super Admins can access this page.
      </MessageBar>
    );
  }

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          onDismiss={() => setErrorMessage(null)}
          dismissButtonAriaLabel="Close"
          styles={{ root: { marginBottom: 10 } }}
        >
          {errorMessage}
        </MessageBar>
      )}

      {successMessage && (
        <MessageBar
          messageBarType={MessageBarType.success}
          isMultiline={false}
          onDismiss={() => setSuccessMessage(null)}
          dismissButtonAriaLabel="Close"
          styles={{ root: { marginBottom: 10 } }}
        >
          {successMessage}
        </MessageBar>
      )}

      <Stack horizontal horizontalAlign="space-between" verticalAlign="center" styles={{ root: { marginBottom: 16 } }}>
        <Text variant="xLarge">Region Management</Text>
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <SearchBox
            placeholder="Search regions..."
            onChange={(_, newValue) => setSearchText(newValue || '')}
            styles={{ root: { width: 300 } }}
          />
          <CommandBar items={commandItems} />
        </Stack>
      </Stack>

      {isLoading ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading regions..." />
        </Stack>
      ) : filteredRegions.length === 0 ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Text>No regions found. {errorMessage ? 'Error loading regions.' : ''}</Text>
        </Stack>
      ) : (
        <DetailsList
          items={filteredRegions}
          columns={columns}
          setKey="set"
          layoutMode={DetailsListLayoutMode.justified}
          selectionMode={SelectionMode.none}
          isHeaderVisible={true}
        />
      )}

      {/* Add Region Dialog */}
      <Dialog
        hidden={!isAddRegionDialogOpen}
        onDismiss={() => {
          setIsAddRegionDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Add New Region',
          subText: 'Fill in the details to add a new region.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField
            label="Region Name"
            required
            value={newRegion.name}
            onChange={(_, value) => setNewRegion({ ...newRegion, name: value })}
          />
          <TextField
            label="Description"
            multiline
            rows={3}
            value={newRegion.description}
            onChange={(_, value) => setNewRegion({ ...newRegion, description: value })}
          />
          <Stack horizontal verticalAlign="center">
            <SpinButton
              label="Cost Limit (€)"
              labelPosition={0}
              min={0}
              max={100000}
              step={1000}
              value={newRegion.cost_limit?.toString()}
              onChange={(_, value) => setNewRegion({ ...newRegion, cost_limit: Number(value) })}
              incrementButtonAriaLabel="Increase value"
              decrementButtonAriaLabel="Decrease value"
              styles={spinButtonStyles}
            />
            <TooltipHost
              content="Maximum cost limit for projects in this region"
              styles={tooltipStyles}
              directionalHint={DirectionalHint.rightCenter}
            >
              <IconButton iconProps={infoIcon} aria-label="Info" />
            </TooltipHost>
          </Stack>
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleAddRegion} text="Add" />
          <DefaultButton onClick={() => {
            setIsAddRegionDialogOpen(false);
            setErrorMessage(null);
            setNewRegion({
              name: '',
              description: '',
              cost_limit: 5000
            });
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Edit Region Dialog */}
      <Dialog
        hidden={!isEditRegionDialogOpen}
        onDismiss={() => {
          setIsEditRegionDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Edit Region',
          subText: 'Update region details.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField
            label="Region Name"
            required
            value={newRegion.name}
            onChange={(_, value) => setNewRegion({ ...newRegion, name: value })}
          />
          <TextField
            label="Description"
            multiline
            rows={3}
            value={newRegion.description}
            onChange={(_, value) => setNewRegion({ ...newRegion, description: value })}
          />
          <Stack horizontal verticalAlign="center">
            <SpinButton
              label="Cost Limit (€)"
              labelPosition={0}
              min={0}
              max={100000}
              step={1000}
              value={newRegion.cost_limit?.toString()}
              onChange={(_, value) => setNewRegion({ ...newRegion, cost_limit: Number(value) })}
              incrementButtonAriaLabel="Increase value"
              decrementButtonAriaLabel="Decrease value"
              styles={spinButtonStyles}
            />
            <TooltipHost
              content="Maximum cost limit for projects in this region"
              styles={tooltipStyles}
              directionalHint={DirectionalHint.rightCenter}
            >
              <IconButton iconProps={infoIcon} aria-label="Info" />
            </TooltipHost>
          </Stack>
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveEdit} text="Save" />
          <DefaultButton onClick={() => {
            setIsEditRegionDialogOpen(false);
            setErrorMessage(null);
            setSelectedRegion(null);
            setNewRegion({
              name: '',
              description: '',
              cost_limit: 5000
            });
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        hidden={!isDeleteDialogOpen}
        onDismiss={() => setIsDeleteDialogOpen(false)}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Confirm Delete',
          subText: `Are you sure you want to delete the region "${selectedRegion?.name}"? This action cannot be undone and may affect users and projects assigned to this region.`
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <DialogFooter>
          <PrimaryButton onClick={handleDeleteRegion} text="Delete" />
          <DefaultButton onClick={() => setIsDeleteDialogOpen(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default RegionManagement;

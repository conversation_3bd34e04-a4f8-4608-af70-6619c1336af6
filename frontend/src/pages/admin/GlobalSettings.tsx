import React, { useState } from 'react';
import {
  Stack,
  Text,
  TextField,
  PrimaryButton,
  DefaultButton,
  Dropdown,
  IDropdownOption,
  Toggle,
  Separator,
  MessageBar,
  MessageBarType,
  IStackTokens,
  SpinButton,
  Label,
  ISpinButtonStyles
} from '@fluentui/react';
import { useUser } from '../../state/UserProvider';
import { UserRole, getRolePermissions } from '../../models/roles';

// Stack tokens
const stackTokens: IStackTokens = { childrenGap: 15 };
const sectionStackTokens: IStackTokens = { childrenGap: 25 };

// SpinButton styles
const spinButtonStyles: Partial<ISpinButtonStyles> = {
  root: { width: 200 }
};

// Mock data for global settings
const initialSettings = {
  defaultCostLimit: 1000,
  defaultAzureFunctionLimit: 500,
  defaultStorageQuota: 5,
  enableCostAlerts: true,
  costAlertThreshold: 80,
  defaultRegion: 'North America',
  allowUserRegistration: true,
  requireRegionalApproval: true
};

// Region options
const regionOptions: IDropdownOption[] = [
  { key: 'North America', text: 'North America' },
  { key: 'Europe', text: 'Europe' },
  { key: 'Asia Pacific', text: 'Asia Pacific' },
  { key: 'Latin America', text: 'Latin America' },
  { key: 'Middle East', text: 'Middle East' }
];

const GlobalSettings: React.FC = () => {
  const { currentUser } = useUser();
  const [settings, setSettings] = useState(initialSettings);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // Check if user has permission to manage global settings
  const permissions = currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER);
  
  if (!permissions.canManageGlobalSettings) {
    return (
      <MessageBar
        messageBarType={MessageBarType.error}
        isMultiline={true}
      >
        <Text variant="medium">
          You do not have permission to manage global settings. 
          This feature is only available to Super Admins.
        </Text>
      </MessageBar>
    );
  }
  
  // Handle saving settings
  const handleSave = () => {
    setIsLoading(true);
    setErrorMessage(null);
    
    // Simulate API call
    setTimeout(() => {
      // In a real app, this would be an API call to save settings
      setIsLoading(false);
      setIsSaved(true);
      
      // Reset saved message after 3 seconds
      setTimeout(() => {
        setIsSaved(false);
      }, 3000);
    }, 1000);
  };
  
  // Handle reset to defaults
  const handleReset = () => {
    setSettings(initialSettings);
  };
  
  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setSettings({
      ...settings,
      [field]: value
    });
  };
  
  return (
    <Stack tokens={sectionStackTokens}>
      <Text variant="xLarge">Global Settings</Text>
      
      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          onDismiss={() => setErrorMessage(null)}
          dismissButtonAriaLabel="Close"
        >
          {errorMessage}
        </MessageBar>
      )}
      
      {isSaved && (
        <MessageBar
          messageBarType={MessageBarType.success}
          isMultiline={false}
        >
          Settings saved successfully.
        </MessageBar>
      )}
      
      {/* Default Project Settings */}
      <Stack tokens={stackTokens}>
        <Text variant="large" styles={{ root: { fontWeight: 600 } }}>Default Project Settings</Text>
        <Separator />
        
        <Stack horizontal tokens={{ childrenGap: 40 }}>
          <Stack tokens={stackTokens} styles={{ root: { minWidth: 300 } }}>
            <Label>Default Cost Limit ($)</Label>
            <SpinButton
              value={settings.defaultCostLimit.toString()}
              min={0}
              max={10000}
              step={100}
              incrementButtonAriaLabel="Increase value"
              decrementButtonAriaLabel="Decrease value"
              onChange={(_, value) => handleInputChange('defaultCostLimit', Number(value))}
              styles={spinButtonStyles}
            />
            
            <Label>Default Azure Function Daily Limit</Label>
            <SpinButton
              value={settings.defaultAzureFunctionLimit.toString()}
              min={0}
              max={5000}
              step={50}
              incrementButtonAriaLabel="Increase value"
              decrementButtonAriaLabel="Decrease value"
              onChange={(_, value) => handleInputChange('defaultAzureFunctionLimit', Number(value))}
              styles={spinButtonStyles}
            />
            
            <Label>Default Storage Quota (GB)</Label>
            <SpinButton
              value={settings.defaultStorageQuota.toString()}
              min={1}
              max={100}
              step={1}
              incrementButtonAriaLabel="Increase value"
              decrementButtonAriaLabel="Decrease value"
              onChange={(_, value) => handleInputChange('defaultStorageQuota', Number(value))}
              styles={spinButtonStyles}
            />
          </Stack>
          
          <Stack tokens={stackTokens} styles={{ root: { minWidth: 300 } }}>
            <Toggle
              label="Enable Cost Alerts"
              checked={settings.enableCostAlerts}
              onChange={(_, checked) => handleInputChange('enableCostAlerts', checked)}
            />
            
            {settings.enableCostAlerts && (
              <>
                <Label>Cost Alert Threshold (%)</Label>
                <SpinButton
                  value={settings.costAlertThreshold.toString()}
                  min={50}
                  max={100}
                  step={5}
                  incrementButtonAriaLabel="Increase value"
                  decrementButtonAriaLabel="Decrease value"
                  onChange={(_, value) => handleInputChange('costAlertThreshold', Number(value))}
                  styles={spinButtonStyles}
                />
              </>
            )}
            
            <Dropdown
              label="Default Region"
              selectedKey={settings.defaultRegion}
              options={regionOptions}
              onChange={(_, option) => handleInputChange('defaultRegion', option?.key)}
            />
          </Stack>
        </Stack>
      </Stack>
      
      {/* User Registration Settings */}
      <Stack tokens={stackTokens}>
        <Text variant="large" styles={{ root: { fontWeight: 600 } }}>User Registration Settings</Text>
        <Separator />
        
        <Stack tokens={stackTokens}>
          <Toggle
            label="Allow User Self-Registration"
            checked={settings.allowUserRegistration}
            onChange={(_, checked) => handleInputChange('allowUserRegistration', checked)}
          />
          
          {settings.allowUserRegistration && (
            <Toggle
              label="Require Regional Admin Approval for New Users"
              checked={settings.requireRegionalApproval}
              onChange={(_, checked) => handleInputChange('requireRegionalApproval', checked)}
            />
          )}
        </Stack>
      </Stack>
      
      {/* Action Buttons */}
      <Stack horizontal tokens={{ childrenGap: 10 }} horizontalAlign="end">
        <DefaultButton
          text="Reset to Defaults"
          onClick={handleReset}
          disabled={isLoading}
        />
        <PrimaryButton
          text={isLoading ? 'Saving...' : 'Save Settings'}
          onClick={handleSave}
          disabled={isLoading}
        />
      </Stack>
    </Stack>
  );
};

export default GlobalSettings;

import React, { useContext, useEffect, useState } from 'react';
import {
  Stack,
  Text,
  Nav,
  INavLink,
  IStackTokens,
  MessageBar,
  MessageBarType,
  PrimaryButton,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUser } from '../../state/UserProvider';
import { UserRole, getRolePermissions } from '../../models/roles';
import RoleBasedHeader from '../../components/RoleBasedHeader';
import { AppStateContext } from '../../state/AppProvider';
import CompanyLogo from '../../assets/keyrus-2.svg';
import UserManagement from './UserManagement';
import GlobalSettings from './GlobalSettings';
import ProjectSettings from './ProjectSettings';
import TeamManagement from './TeamManagement';
import UserTagManagement from './UserTagManagement';
import RegionalAdminManagement from './RegionalAdminManagement';
import RegionManagement from './RegionManagement';
import CostAnalytics from './CostAnalytics';

const stackTokens: IStackTokens = { childrenGap: 20 };

const AdminPanel: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser, isLoading: isUserLoading } = useUser();
  const permissions = currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER);
  const appStateContext = useContext(AppStateContext);
  const ui = appStateContext?.state.frontendSettings?.ui;
  const [logo, setLogo] = useState<string>(CompanyLogo);

  useEffect(() => {
    if (!appStateContext?.state.isLoading) {
      setLogo(ui?.logo || CompanyLogo);
    }
  }, [appStateContext?.state.isLoading, ui?.logo]);

  // Determine which tab should be active based on the URL
  const getSelectedKey = () => {
    if (location.pathname.includes('/admin/users')) return 'users';
    if (location.pathname.includes('/admin/global-costs')) return 'global-costs';
    if (location.pathname.includes('/admin/projects')) return 'projects';
    if (location.pathname.includes('/admin/teams')) return 'teams';
    if (location.pathname.includes('/admin/regional-costs')) return 'regional-costs';
    if (location.pathname.includes('/admin/cost-analytics')) return 'cost-analytics';
    if (location.pathname.includes('/admin/regional-admins')) return 'regional-admins';
    return 'users'; // Default
  };

  // Handle navigation link clicks
  const handleNavClick = (ev?: React.MouseEvent<HTMLElement>, item?: INavLink) => {
    if (!item) return;

    switch (item.key) {
      case 'users':
        navigate('/admin/users');
        break;
      case 'global-costs':
        navigate('/admin/global-costs');
        break;
      case 'projects':
        navigate('/admin/projects');
        break;
      case 'teams':
        navigate('/admin/teams');
        break;
      case 'regional-costs':
        navigate('/admin/regional-costs');
        break;
      case 'cost-analytics':
        navigate('/admin/cost-analytics');
        break;
      case 'regional-admins':
        navigate('/admin/regional-admins');
        break;
      default:
        navigate('/admin');
    }
  };

  const navLinkGroups = [
    {
      name: 'User Management',
      links: [
        { name: 'Users', key: 'users', url: '', iconProps: { iconName: 'People' }, disabled: !permissions.canManageUsers },
        { name: 'Teams', key: 'teams', url: '', iconProps: { iconName: 'Group' }, disabled: !permissions.canCreateTeams },
        { name: 'Projects', key: 'projects', url: '', iconProps: { iconName: 'ProjectCollection' } },
        { name: 'Regional Admins', key: 'regional-admins', url: '', iconProps: { iconName: 'Contact' }, disabled: !permissions.canSetupRegionalAdmins }
      ]
    },
    {
      name: 'Cost Management',
      links: [
        { name: 'Global Costs', key: 'global-costs', url: '', iconProps: { iconName: 'Money' }, disabled: !permissions.canManageGlobalSettings },
        { name: 'Regional Costs', key: 'regional-costs', url: '', iconProps: { iconName: 'MapPin' }, disabled: !permissions.canSetupRegionalAdmins },
        { name: 'Cost Analytics', key: 'cost-analytics', url: '', iconProps: { iconName: 'BarChart4' } }
      ]
    }
  ];

  // Show a loading spinner while user information is being fetched
  if (isUserLoading) {
    return (
      <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <RoleBasedHeader logo={logo} />
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { flex: 1, padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading user..." />
        </Stack>
      </div>
    );
  }

  // If user doesn't have admin permissions, show access denied
  if (!permissions.canAccessAdminPanel) {
    return (
      <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <RoleBasedHeader logo={logo} />
        <Stack
          horizontalAlign="center"
          verticalAlign="center"
          styles={{ root: { flex: 1, padding: 20 } }}
        >
          <MessageBar
            messageBarType={MessageBarType.error}
            isMultiline={true}
            styles={{ root: { maxWidth: 600 } }}
          >
            <Text variant="large" styles={{ root: { marginBottom: 10 } }}>
              Access Denied
            </Text>
            <Text>
              You do not have permission to access the Admin Panel.
              Please contact your administrator if you believe this is an error.
            </Text>
          </MessageBar>
        </Stack>
      </div>
    );
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <RoleBasedHeader logo={logo} />

      <Stack styles={{ root: { flex: 1, padding: '20px 40px' } }} tokens={stackTokens}>
        <Stack horizontal horizontalAlign="space-between" verticalAlign="center">
          <Text variant="xxLarge" styles={{ root: { fontWeight: 600 } }}>
            Admin Panel
          </Text>
          <Stack.Item>
            <PrimaryButton
              text="Back to Projects"
              iconProps={{ iconName: 'ChevronLeft' }}
              onClick={() => navigate('/projects')}
              styles={{
                root: {
                  marginBottom: 10,
                  backgroundColor: '#0078d4',
                  borderRadius: '4px'
                },
                rootHovered: {
                  backgroundColor: '#106ebe'
                }
              }}
            />
          </Stack.Item>
        </Stack>

        <Stack horizontal styles={{ root: { flex: 1 } }} tokens={{ childrenGap: 20 }}>
          <Nav
            groups={navLinkGroups}
            selectedKey={getSelectedKey()}
            onLinkClick={handleNavClick}
            styles={{ root: { width: 220 } }}
          />

          <Stack grow>
            {getSelectedKey() === 'users' && <UserManagement />}
            {getSelectedKey() === 'global-costs' && permissions.canManageGlobalSettings && <GlobalSettings />}
            {getSelectedKey() === 'projects' && <ProjectSettings />}
            {getSelectedKey() === 'teams' && permissions.canCreateTeams && <TeamManagement />}
            {getSelectedKey() === 'regional-costs' && permissions.canSetupRegionalAdmins && <RegionManagement />}
            {getSelectedKey() === 'regional-admins' && permissions.canSetupRegionalAdmins && <RegionalAdminManagement />}
            {getSelectedKey() === 'cost-analytics' && <CostAnalytics />}
          </Stack>
        </Stack>
      </Stack>
    </div>
  );
};

export default AdminPanel;

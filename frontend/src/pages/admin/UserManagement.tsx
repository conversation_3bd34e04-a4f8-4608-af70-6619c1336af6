import React, { useState, useEffect } from 'react';
import './UserManagement.css';
import {
  DetailsList,
  DetailsListLayoutMode,
  SelectionMode,
  IColumn,
  PrimaryButton,
  CommandBar,
  ICommandBarItemProps,
  Stack,
  TextField,
  Dropdown,
  IDropdownOption,
  Dialog,
  DialogType,
  DialogFooter,
  DefaultButton,
  MessageBar,
  MessageBarType,
  Text,
  SearchBox,
  IIconProps,
  IconButton,
  TooltipHost,
  ITooltipHostStyles,
  DirectionalHint,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useUser } from '../../state/UserProvider';
import { UserRole, UserProfile } from '../../models/roles';
import rbacService, { UserData, RegionData } from '../../services/rbacService';
import userContextService from '../../services/userContextService';

// Mock data for users
const mockUsers: UserProfile[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: UserRole.SUPER_ADMIN,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: UserRole.REGIONAL_ADMIN,
    region: 'North America'
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: UserRole.REGIONAL_ADMIN,
    region: 'Europe'
  },
  {
    id: '4',
    name: 'Alice Brown',
    email: '<EMAIL>',
    role: UserRole.REGULAR_USER,
    region: 'North America'
  },
  {
    id: '5',
    name: 'Charlie Wilson',
    email: '<EMAIL>',
    role: UserRole.REGULAR_USER,
    region: 'Europe'
  },
  {
    id: '6',
    name: 'Diana Miller',
    email: '<EMAIL>',
    role: UserRole.REGULAR_USER,
    region: 'Asia Pacific'
  }
];

// Mock regions for fallback
const mockRegions: IDropdownOption[] = [
  { key: 'North America', text: 'North America' },
  { key: 'Europe', text: 'Europe' },
  { key: 'Asia Pacific', text: 'Asia Pacific' },
  { key: 'Latin America', text: 'Latin America' },
  { key: 'Middle East', text: 'Middle East' }
];

// Role options for dropdown
const roleOptions: IDropdownOption[] = [
  { key: UserRole.SUPER_ADMIN, text: 'Super Admin' },
  { key: UserRole.REGIONAL_ADMIN, text: 'Regional Admin' },
  { key: UserRole.REGULAR_USER, text: 'Regular User' }
];

// Icon for info tooltip
const infoIcon: IIconProps = { iconName: 'Info' };
const tooltipStyles: Partial<ITooltipHostStyles> = { root: { display: 'inline-block', marginLeft: 5 } };

const UserManagement: React.FC = () => {
  const { currentUser } = useUser();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isChangeRoleDialogOpen, setIsChangeRoleDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [newUser, setNewUser] = useState<Partial<UserProfile> & { role: UserRole }>({
    name: '',
    email: '',
    role: UserRole.REGULAR_USER,
    region: ''
  });
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [regions, setRegions] = useState<IDropdownOption[]>([]);


  // Columns for the DetailsList
  const columns: IColumn[] = [
    {
      key: 'name',
      name: 'Name',
      fieldName: 'name',
      minWidth: 100,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'email',
      name: 'Email',
      fieldName: 'email',
      minWidth: 200,
      maxWidth: 300,
      isResizable: true
    },
    {
      key: 'role',
      name: 'Role',
      fieldName: 'role',
      minWidth: 100,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: UserProfile) => {
        switch (item.role) {
          case UserRole.SUPER_ADMIN:
            return <span style={{ color: '#e81123', fontWeight: 'bold' }}>Super Admin</span>;
          case UserRole.REGIONAL_ADMIN:
            return <span style={{ color: '#ff8c00', fontWeight: 'bold' }}>Regional Admin</span>;
          case UserRole.REGULAR_USER:
            return <span style={{ color: '#107c10' }}>Regular User</span>;
          default:
            return <span>Unknown</span>;
        }
      }
    },
    {
      key: 'region',
      name: 'Region',
      fieldName: 'region',
      minWidth: 100,
      maxWidth: 150,
      isResizable: true,
      onRender: (item: UserProfile) => {
        // Find the region name from the region ID
        const regionOption = regions.find(r => r.key === item.region);
        return <span>{regionOption ? regionOption.text : item.region}</span>;
      }
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 100,
      maxWidth: 100,
      isResizable: false,
      onRender: (item: UserProfile) => {
        // Debug: Log the IDs to check if they match
        console.log('Current user ID:', currentUser?.id);
        console.log('Item user ID:', item.id);
        console.log('Are they equal?', item.id === currentUser?.id);

        // Check if this is the current user
        const isCurrentUser = item.id === currentUser?.id;

        // Check if this is a Super Admin and current user is not a Super Admin
        const isSuperAdminAndCurrentUserIsNot =
          item.role === UserRole.SUPER_ADMIN &&
          currentUser?.role !== UserRole.SUPER_ADMIN;

        return (
          <Stack horizontal tokens={{ childrenGap: 8 }}>
            <IconButton
              iconProps={{ iconName: 'Edit' }}
              title="Edit"
              ariaLabel="Edit"
              onClick={() => handleEditUser(item)}
              disabled={isCurrentUser || isSuperAdminAndCurrentUserIsNot}
            />

            {/* Change Role button - only show if not current user */}
            {!isCurrentUser && (
              <IconButton
                iconProps={{ iconName: 'Permissions' }}
                title="Change Role"
                ariaLabel="Change Role"
                onClick={() => handleChangeRoleClick(item)}
                disabled={isSuperAdminAndCurrentUserIsNot}
              />
            )}

            {/* Delete button with tooltip for current user */}
            {!isCurrentUser ? (
              <IconButton
                iconProps={{ iconName: 'Delete' }}
                title="Delete"
                ariaLabel="Delete"
                onClick={() => handleDeleteClick(item)}
                disabled={isSuperAdminAndCurrentUserIsNot}
              />
            ) : (
              <TooltipHost
                content="You cannot delete your own account"
                styles={tooltipStyles}
              >
                <IconButton
                  iconProps={{ iconName: 'Delete' }}
                  title="Delete"
                  ariaLabel="Delete"
                  disabled={true}
                />
              </TooltipHost>
            )}
          </Stack>
        );
      }
    }
  ];

  // Command bar items
  const commandItems: ICommandBarItemProps[] = [
    {
      key: 'addUser',
      text: 'Add User',
      iconProps: { iconName: 'AddFriend' },
      onClick: () => setIsAddUserDialogOpen(true)
    },
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => loadUsers()
    }
  ];

  // Load users and regions on component mount
  useEffect(() => {
    loadUsers();
    loadRegions();
  }, []);

  // Load regions from user context or using rbacService
  const loadRegions = async () => {
    try {
      // Option 1: If regions are part of general user context
      const userContext = await userContextService.fetchUserContext();
      if (userContext && userContext.accessibleResources && userContext.accessibleResources.regions) {
        const regionOptions = userContext.accessibleResources.regions.map((region: { id: string, name: string }) => ({
          key: region.id,
          text: region.name
        }));
        setRegions(regionOptions);
        return;
      }

      // Option 2: Use rbacService to get regions
      const response = await rbacService.regions.getRegions();
      if (response.error) {
        throw new Error(response.error);
      }

      if (!response.data) {
        throw new Error('No region data returned from API');
      }

      const regionOptions = response.data.map((region) => ({
        key: region.id,
        text: region.name
      }));
      setRegions(regionOptions);

    } catch (error) {
      console.error('Error fetching regions:', error);
      setErrorMessage('Failed to load regions. Using mock data for development.');
      setRegions(mockRegions); // Fallback to mock regions
    }
  };

  // Filter users when search text changes
  useEffect(() => {
    if (searchText) {
      const filtered = users.filter(user =>
        user.name.toLowerCase().includes(searchText.toLowerCase()) ||
        user.email.toLowerCase().includes(searchText.toLowerCase()) ||
        (user.region && user.region.toLowerCase().includes(searchText.toLowerCase()))
      );
      setFilteredUsers(filtered);
    } else {
      setFilteredUsers(users);
    }
  }, [searchText, users]);

  // Load users from RBAC API
  const loadUsers = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Debug: Log current user before loading users
      console.log('Current user when loading users:', currentUser);

      // Call the API to get users using rbacService
      const response = await rbacService.users.getUsers();

      if (response.error) {
        console.error('Error loading users:', response.error);
        setErrorMessage(`Error loading users: ${response.error}`);
        setUsers(mockUsers);
        setFilteredUsers(mockUsers);
        return;
      }

      if (!response.data) {
        console.error('No user data returned from API');
        setErrorMessage('No user data returned from API');
        setUsers(mockUsers);
        setFilteredUsers(mockUsers);
        return;
      }

      console.log('API users response:', response.data);

      // Convert API response to UserProfile format
      let apiUsers: UserProfile[] = response.data.map((userData: UserData) => ({
        id: userData.id,
        name: userData.name,
        email: userData.email,
        role: userData.role as UserRole,
        region: userData.region,
        avatar: userData.avatar
      }));

      // Debug: Log the converted users
      console.log('Converted API users:', apiUsers);
      console.log('Current user ID:', currentUser?.id);

      // Additional frontend filtering for safety
      if (currentUser?.role === UserRole.REGIONAL_ADMIN && currentUser?.region) {
        // Regional admins should only see users in their region and not super admins
        console.log('Filtering users for Regional Admin');
        console.log('Before filtering:', apiUsers.length, 'users');

        apiUsers = apiUsers.filter(user => {
          // Filter out Super Admins
          if (user.role === UserRole.SUPER_ADMIN) {
            return false;
          }

          // Include users in the same region or the current user
          return user.region === currentUser.region || user.id === currentUser.id;
        });

        console.log('After filtering:', apiUsers.length, 'users');
      }

      setUsers(apiUsers);
      setFilteredUsers(apiUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      setErrorMessage('Failed to load users. Using mock data for development.');
      // Fallback to mock data for development
      setUsers(mockUsers);
      setFilteredUsers(mockUsers);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle adding a new user
  const handleAddUser = async () => {
    // Validate form
    if (!newUser.name || !newUser.email || !newUser.role) {
      setErrorMessage('Please fill in all required fields.');
      return;
    }

    // If current user is a Regional Admin, enforce their region and role restrictions
    if (currentUser?.role === UserRole.REGIONAL_ADMIN && currentUser?.region) {
      // Regional admins can only add users to their region
      newUser.region = currentUser.region;

      // Regional admins cannot add Super Admins
      if (newUser.role === UserRole.SUPER_ADMIN) {
        setErrorMessage('Regional Admins cannot create Super Admin accounts.');
        return;
      }
    }

    if (newUser.role !== UserRole.SUPER_ADMIN && !newUser.region) {
      setErrorMessage('Region is required for Regional Admins and Regular Users.');
      return;
    }

    try {
      // Call API to create user using rbacService
      const response = await rbacService.users.createUser({
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
        region: newUser.region,
      });

      if (response.error) {
        setErrorMessage(`Failed to create user: ${response.error}`);
        return;
      }

      // Refresh users list
      await loadUsers();

      setIsAddUserDialogOpen(false);
      setNewUser({
        name: '',
        email: '',
        role: UserRole.REGULAR_USER,
        region: ''
      });
      setErrorMessage(null);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'success-message';
      successMessage.textContent = `User "${newUser.name}" created successfully.`;
      document.body.appendChild(successMessage);
      setTimeout(() => document.body.removeChild(successMessage), 3000);
    } catch (error) {
      console.error('Error creating user:', error);
      setErrorMessage('Failed to create user. Please try again later.');
    }
  };

  // Handle editing a user
  const handleEditUser = (user: UserProfile) => {
    setSelectedUser(user);
    setNewUser({
      name: user.name,
      email: user.email,
      role: user.role,
      region: user.region
    });
    setIsEditUserDialogOpen(true);
  };

  // Handle saving edited user
  const handleSaveEdit = async () => {
    if (!selectedUser) return;

    // Validate form
    if (!newUser.name || !newUser.email || !newUser.role) {
      setErrorMessage('Please fill in all required fields.');
      return;
    }

    // If current user is a Regional Admin, enforce their region and role restrictions
    if (currentUser?.role === UserRole.REGIONAL_ADMIN && currentUser?.region) {
      // Regional admins can only edit users in their region
      newUser.region = currentUser.region;

      // Regional admins cannot change users to Super Admins
      if (newUser.role === UserRole.SUPER_ADMIN) {
        setErrorMessage('Regional Admins cannot create or modify Super Admin accounts.');
        return;
      }
    }

    if (newUser.role !== UserRole.SUPER_ADMIN && !newUser.region) {
      setErrorMessage('Region is required for Regional Admins and Regular Users.');
      return;
    }

    try {
      // Call API to update user using rbacService
      const response = await rbacService.users.updateUser(selectedUser.id, {
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
        region: newUser.region,
      });

      if (response.error) {
        setErrorMessage(`Failed to update user: ${response.error}`);
        return;
      }

      // Refresh users list
      await loadUsers();

      setIsEditUserDialogOpen(false);
      setSelectedUser(null);
      setNewUser({
        name: '',
        email: '',
        role: UserRole.REGULAR_USER,
        region: ''
      });
      setErrorMessage(null);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'success-message';
      successMessage.textContent = `User "${newUser.name}" updated successfully.`;
      document.body.appendChild(successMessage);
      setTimeout(() => document.body.removeChild(successMessage), 3000);
    } catch (error) {
      console.error('Error updating user:', error);
      setErrorMessage('Failed to update user. Please try again later.');
    }
  };

  // Handle delete click
  const handleDeleteClick = (user: UserProfile) => {
    // Debug logging
    console.log('Delete clicked for user:', user);
    console.log('Current user:', currentUser);
    console.log('IDs match?', user.id === currentUser?.id);

    // Additional safety check to prevent deleting yourself
    if (user.id === currentUser?.id) {
      console.log('Preventing self-deletion - IDs match');
      setErrorMessage('You cannot delete your own account.');
      return;
    }

    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  // Handle deleting a user
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    // Debug logging
    console.log('Attempting to delete user:', selectedUser);
    console.log('Current user:', currentUser);
    console.log('IDs match?', selectedUser.id === currentUser?.id);

    // Additional safety check to prevent deleting yourself
    if (selectedUser.id === currentUser?.id) {
      console.log('Preventing self-deletion in handleDeleteUser - IDs match');
      setErrorMessage('You cannot delete your own account.');
      setIsDeleteDialogOpen(false);
      return;
    }

    try {
      // Log the request being sent
      console.log('Sending delete request for user ID:', selectedUser.id);

      // Call API to delete user using rbacService
      const response = await rbacService.users.deleteUser(selectedUser.id);

      console.log('Delete user response:', response);

      if (response.error) {
        console.error('Delete user error:', response.error);
        setErrorMessage(`Failed to delete user: ${response.error}`);
        return;
      }

      // Refresh users list
      await loadUsers();

      setIsDeleteDialogOpen(false);
      setSelectedUser(null);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'success-message';
      successMessage.textContent = `User "${selectedUser.name}" deleted successfully.`;
      document.body.appendChild(successMessage);
      setTimeout(() => document.body.removeChild(successMessage), 3000);
    } catch (error) {
      console.error('Error deleting user:', error);
      setErrorMessage('Failed to delete user. Please try again later.');
    }
  };

  // Handle role change in dialogs
  const handleRoleChange = (_: React.FormEvent<HTMLDivElement>, option?: IDropdownOption) => {
    if (option) {
      setNewUser({
        ...newUser,
        role: option.key as UserRole,
        // Clear region if Super Admin is selected
        region: option.key === UserRole.SUPER_ADMIN ? undefined : newUser.region
      });
    }
  };

  // Handle change role button click
  const handleChangeRoleClick = (user: UserProfile) => {
    setSelectedUser(user);
    setNewUser({
      ...newUser,
      role: user.role
    });
    setIsChangeRoleDialogOpen(true);
  };

  // Handle saving role change
  const handleSaveRoleChange = async () => {
    if (!selectedUser) return;

    // Validate role selection
    if (!newUser.role) {
      setErrorMessage('Please select a role.');
      return;
    }

    // If current user is a Regional Admin, enforce role restrictions
    if (currentUser?.role === UserRole.REGIONAL_ADMIN) {
      // Regional admins cannot change users to Super Admins
      if (newUser.role === UserRole.SUPER_ADMIN) {
        setErrorMessage('Regional Admins cannot promote users to Super Admin.');
        return;
      }

      // Regional admins cannot change Super Admins
      if (selectedUser.role === UserRole.SUPER_ADMIN) {
        setErrorMessage('Regional Admins cannot change Super Admin roles.');
        return;
      }
    }

    try {
      // Call API to change user role using rbacService
      const response = await rbacService.users.changeUserRole(selectedUser.id, newUser.role);

      if (response.error) {
        setErrorMessage(`Failed to change user role: ${response.error}`);
        return;
      }

      // Refresh users list
      await loadUsers();

      setIsChangeRoleDialogOpen(false);
      setSelectedUser(null);
      setErrorMessage(null);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'success-message';
      successMessage.textContent = `${selectedUser.name}'s role changed successfully.`;
      document.body.appendChild(successMessage);
      setTimeout(() => document.body.removeChild(successMessage), 3000);
    } catch (error) {
      console.error('Error changing user role:', error);
      setErrorMessage('Failed to change user role. Please try again later.');
    }
  };

  // Get available role options based on current user's role
  const getAvailableRoleOptions = (): IDropdownOption[] => {
    if (currentUser?.role === UserRole.SUPER_ADMIN) {
      // Super Admin can create any role
      return roleOptions;
    } else if (currentUser?.role === UserRole.REGIONAL_ADMIN) {
      // Regional Admin can only create Regional Admins and Regular Users
      return roleOptions.filter(option =>
        option.key === UserRole.REGIONAL_ADMIN ||
        option.key === UserRole.REGULAR_USER
      );
    } else {
      // Regular users shouldn't be able to create users, but just in case
      return roleOptions.filter(option =>
        option.key === UserRole.REGULAR_USER
      );
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          onDismiss={() => setErrorMessage(null)}
          dismissButtonAriaLabel="Close"
          styles={{ root: { marginBottom: 10 } }}
        >
          {errorMessage}
        </MessageBar>
      )}

      <Stack horizontal horizontalAlign="space-between" verticalAlign="center" styles={{ root: { marginBottom: 16 } }}>
        <Text variant="xLarge">User Management</Text>
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <SearchBox
            placeholder="Search users..."
            onChange={(_, newValue) => setSearchText(newValue || '')}
            styles={{ root: { width: 300 } }}
          />
          <CommandBar items={commandItems} />
        </Stack>
      </Stack>

      {isLoading ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading users..." />
        </Stack>
      ) : filteredUsers.length === 0 ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Text>No users found. {errorMessage ? 'Error loading users.' : ''}</Text>
        </Stack>
      ) : (
        <DetailsList
          items={filteredUsers}
          columns={columns}
          setKey="set"
          layoutMode={DetailsListLayoutMode.justified}
          selectionMode={SelectionMode.none}
          isHeaderVisible={true}
        />
      )}

      {/* Add User Dialog */}
      <Dialog
        hidden={!isAddUserDialogOpen}
        onDismiss={() => {
          setIsAddUserDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Add New User',
          subText: 'Fill in the details to add a new user.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField
            label="Name"
            required
            value={newUser.name}
            onChange={(_, value) => setNewUser({ ...newUser, name: value })}
          />
          <TextField
            label="Email"
            required
            value={newUser.email}
            onChange={(_, value) => setNewUser({ ...newUser, email: value })}
          />
          <Stack horizontal verticalAlign="center">
            <Dropdown
              label="Role"
              required
              selectedKey={newUser.role}
              options={getAvailableRoleOptions()}
              onChange={handleRoleChange}
              styles={{ root: { width: '100%' } }}
            />
            <TooltipHost
              content="Super Admin: Full access to all regions and settings. Regional Admin: Manages projects and users in their region. Regular User: Can only access assigned projects."
              styles={tooltipStyles}
              directionalHint={DirectionalHint.rightCenter}
            >
              <IconButton iconProps={infoIcon} aria-label="Info" />
            </TooltipHost>
          </Stack>

          {newUser.role !== UserRole.SUPER_ADMIN && (
            <Dropdown
              label="Region"
              required
              selectedKey={currentUser?.role === UserRole.REGIONAL_ADMIN ? currentUser.region : newUser.region}
              options={regions}
              onChange={(_, option) => setNewUser({ ...newUser, region: option?.key as string })}
              disabled={currentUser?.role === UserRole.REGIONAL_ADMIN}
              // Add a note if the dropdown is disabled
              onRenderLabel={props => (
                <Stack>
                  <Text>{props?.label}</Text>
                  {currentUser?.role === UserRole.REGIONAL_ADMIN && (
                    <Text variant="small" styles={{ root: { color: '#666', fontStyle: 'italic' } }}>
                      (Fixed to your region)
                    </Text>
                  )}
                </Stack>
              )}
            />
          )}
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleAddUser} text="Add" />
          <DefaultButton onClick={() => {
            setIsAddUserDialogOpen(false);
            setErrorMessage(null);
            setNewUser({
              name: '',
              email: '',
              role: UserRole.REGULAR_USER,
              region: ''
            });
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog
        hidden={!isEditUserDialogOpen}
        onDismiss={() => {
          setIsEditUserDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Edit User',
          subText: 'Update user details.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField
            label="Name"
            required
            value={newUser.name}
            onChange={(_, value) => setNewUser({ ...newUser, name: value })}
          />
          <TextField
            label="Email"
            required
            value={newUser.email}
            onChange={(_, value) => setNewUser({ ...newUser, email: value })}
          />
          <Stack horizontal verticalAlign="center">
            <Dropdown
              label="Role"
              required
              selectedKey={newUser.role}
              options={getAvailableRoleOptions()}
              onChange={handleRoleChange}
              disabled={selectedUser?.id === currentUser?.id}
              styles={{ root: { width: '100%' } }}
            />
            <TooltipHost
              content="Super Admin: Full access to all regions and settings. Regional Admin: Manages projects and users in their region. Regular User: Can only access assigned projects."
              styles={tooltipStyles}
              directionalHint={DirectionalHint.rightCenter}
            >
              <IconButton iconProps={infoIcon} aria-label="Info" />
            </TooltipHost>
          </Stack>

          {newUser.role !== UserRole.SUPER_ADMIN && (
            <Dropdown
              label="Region"
              required
              selectedKey={currentUser?.role === UserRole.REGIONAL_ADMIN ? currentUser.region : newUser.region}
              options={regions}
              onChange={(_, option) => setNewUser({ ...newUser, region: option?.key as string })}
              disabled={currentUser?.role === UserRole.REGIONAL_ADMIN}
              // Add a note if the dropdown is disabled
              onRenderLabel={props => (
                <Stack>
                  <Text>{props?.label}</Text>
                  {currentUser?.role === UserRole.REGIONAL_ADMIN && (
                    <Text variant="small" styles={{ root: { color: '#666', fontStyle: 'italic' } }}>
                      (Fixed to your region)
                    </Text>
                  )}
                </Stack>
              )}
            />
          )}
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveEdit} text="Save" />
          <DefaultButton onClick={() => {
            setIsEditUserDialogOpen(false);
            setErrorMessage(null);
            setSelectedUser(null);
            setNewUser({
              name: '',
              email: '',
              role: UserRole.REGULAR_USER,
              region: ''
            });
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        hidden={!isDeleteDialogOpen}
        onDismiss={() => setIsDeleteDialogOpen(false)}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Confirm Delete',
          subText: `Are you sure you want to delete ${selectedUser?.name}? This action cannot be undone.`
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <DialogFooter>
          <PrimaryButton onClick={handleDeleteUser} text="Delete" />
          <DefaultButton onClick={() => setIsDeleteDialogOpen(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Change Role Dialog */}
      <Dialog
        hidden={!isChangeRoleDialogOpen}
        onDismiss={() => {
          setIsChangeRoleDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Change User Role',
          subText: `Change role for ${selectedUser?.name}`
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={{ childrenGap: 15 }}>
          <Dropdown
            label="Role"
            required
            selectedKey={newUser.role}
            options={getAvailableRoleOptions()}
            onChange={handleRoleChange}
            styles={{ root: { width: '100%' } }}
          />
          <TooltipHost
            content="Super Admin: Full access to all regions and settings. Regional Admin: Manages projects and users in their region. Regular User: Can only access assigned projects."
            styles={tooltipStyles}
            directionalHint={DirectionalHint.rightCenter}
          >
            <IconButton
              iconProps={{ iconName: 'Info' }}
              title="Role Information"
              ariaLabel="Role Information"
              styles={{ root: { marginTop: 4 } }}
            />
          </TooltipHost>
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveRoleChange} text="Save" />
          <DefaultButton onClick={() => {
            setIsChangeRoleDialogOpen(false);
            setErrorMessage(null);
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default UserManagement;

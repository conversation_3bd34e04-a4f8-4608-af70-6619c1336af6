import React, { useState, useEffect } from 'react';
import {
  DetailsList,
  DetailsListLayoutMode,
  SelectionMode,
  IColumn,
  PrimaryButton,
  CommandBar,
  ICommandBarItemProps,
  Stack,
  TextField,
  Dialog,
  DialogType,
  DialogFooter,
  DefaultButton,
  MessageBar,
  MessageBarType,
  Text,
  SearchBox,
  IconButton,
  ColorPicker,
  IColor,
  getColorFromString,
  IStackTokens
} from '@fluentui/react';
import { useUser } from '../../state/UserProvider';
import { UserRole, getRolePermissions, UserTag } from '../../models/roles';

// Stack tokens
const stackTokens: IStackTokens = { childrenGap: 15 };

// Mock data for user tags
const mockTags: UserTag[] = [
  {
    id: '1',
    name: 'Developer',
    color: '#0078d4',
    createdBy: '1'
  },
  {
    id: '2',
    name: 'Designer',
    color: '#107c10',
    createdBy: '2'
  },
  {
    id: '3',
    name: 'Product Manager',
    color: '#d83b01',
    createdBy: '2'
  },
  {
    id: '4',
    name: 'Data Scientist',
    color: '#5c2d91',
    createdBy: '3'
  }
];

const UserTagManagement: React.FC = () => {
  const { currentUser } = useUser();
  const [tags, setTags] = useState<UserTag[]>([]);
  const [filteredTags, setFilteredTags] = useState<UserTag[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isAddTagDialogOpen, setIsAddTagDialogOpen] = useState(false);
  const [isEditTagDialogOpen, setIsEditTagDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTag, setSelectedTag] = useState<UserTag | null>(null);
  const [newTag, setNewTag] = useState<Partial<UserTag>>({
    name: '',
    color: '#0078d4'
  });
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Get permissions based on user role
  const permissions = currentUser ? getRolePermissions(currentUser.role) : getRolePermissions(UserRole.REGULAR_USER);

  // Columns for the DetailsList
  const columns: IColumn[] = [
    {
      key: 'color',
      name: 'Color',
      minWidth: 50,
      maxWidth: 50,
      isResizable: false,
      onRender: (item: UserTag) => (
        <div style={{
          width: 24,
          height: 24,
          borderRadius: '50%',
          backgroundColor: item.color,
          border: '1px solid #ccc'
        }} />
      )
    },
    {
      key: 'name',
      name: 'Tag Name',
      fieldName: 'name',
      minWidth: 150,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 100,
      maxWidth: 100,
      isResizable: false,
      onRender: (item: UserTag) => (
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <IconButton
            iconProps={{ iconName: 'Edit' }}
            title="Edit Tag"
            ariaLabel="Edit Tag"
            onClick={() => handleEditTag(item)}
          />
          <IconButton
            iconProps={{ iconName: 'Delete' }}
            title="Delete Tag"
            ariaLabel="Delete Tag"
            onClick={() => handleDeleteClick(item)}
          />
        </Stack>
      )
    }
  ];

  // Command bar items
  const commandItems: ICommandBarItemProps[] = [
    {
      key: 'addTag',
      text: 'Create Tag',
      iconProps: { iconName: 'Tag' },
      onClick: () => setIsAddTagDialogOpen(true)
    },
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => loadTags()
    }
  ];

  // Load tags on component mount
  useEffect(() => {
    loadTags();
  }, []);

  // Filter tags when search text changes
  useEffect(() => {
    if (searchText) {
      const filtered = tags.filter(tag =>
        tag.name.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredTags(filtered);
    } else {
      setFilteredTags(tags);
    }
  }, [searchText, tags]);

  // Load tags (mock data for now)
  const loadTags = () => {
    // In a real app, this would be an API call
    // Filter tags based on current user's role and region
    let filteredTags = [...mockTags];

    if (currentUser?.role === UserRole.REGIONAL_ADMIN) {
      // Regional admins can only see tags they created or that are available in their region
      filteredTags = mockTags.filter(tag =>
        tag.createdBy === currentUser.id ||
        mockTags.some(t => t.createdBy === currentUser.id)
      );
    }

    setTags(filteredTags);
    setFilteredTags(filteredTags);
  };

  // Handle adding a new tag
  const handleAddTag = () => {
    // Validate form
    if (!newTag.name) {
      setErrorMessage('Tag name is required.');
      return;
    }

    // In a real app, this would be an API call
    const newTagId = (Math.max(...tags.map(t => parseInt(t.id))) + 1).toString();
    const tagToAdd: UserTag = {
      id: newTagId,
      name: newTag.name,
      color: newTag.color || '#0078d4',
      createdBy: currentUser?.id || '1'
    };

    setTags([...tags, tagToAdd]);
    setFilteredTags([...filteredTags, tagToAdd]);
    setIsAddTagDialogOpen(false);
    setNewTag({
      name: '',
      color: '#0078d4'
    });
    setSuccessMessage(`Tag "${tagToAdd.name}" created successfully.`);
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  // Handle editing a tag
  const handleEditTag = (tag: UserTag) => {
    setSelectedTag(tag);
    setNewTag({
      name: tag.name,
      color: tag.color
    });
    setIsEditTagDialogOpen(true);
  };

  // Handle saving edited tag
  const handleSaveEdit = () => {
    if (!selectedTag) return;

    // Validate form
    if (!newTag.name) {
      setErrorMessage('Tag name is required.');
      return;
    }

    // In a real app, this would be an API call
    const updatedTag: UserTag = {
      ...selectedTag,
      name: newTag.name,
      color: newTag.color || selectedTag.color
    };

    const updatedTags = tags.map(tag =>
      tag.id === selectedTag.id ? updatedTag : tag
    );

    setTags(updatedTags);
    setFilteredTags(updatedTags);
    setIsEditTagDialogOpen(false);
    setSelectedTag(null);
    setNewTag({
      name: '',
      color: '#0078d4'
    });
    setSuccessMessage(`Tag "${updatedTag.name}" updated successfully.`);
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  // Handle delete click
  const handleDeleteClick = (tag: UserTag) => {
    setSelectedTag(tag);
    setIsDeleteDialogOpen(true);
  };

  // Handle deleting a tag
  const handleDeleteTag = () => {
    if (!selectedTag) return;

    // In a real app, this would be an API call
    const updatedTags = tags.filter(tag => tag.id !== selectedTag.id);
    setTags(updatedTags);
    setFilteredTags(updatedTags);
    setIsDeleteDialogOpen(false);
    setSelectedTag(null);
    setSuccessMessage(`Tag "${selectedTag.name}" deleted successfully.`);
    setTimeout(() => setSuccessMessage(null), 3000);
  };

  // Handle color change
  const handleColorChange = (color: IColor) => {
    setNewTag({
      ...newTag,
      color: color.str
    });
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          onDismiss={() => setErrorMessage(null)}
          dismissButtonAriaLabel="Close"
          styles={{ root: { marginBottom: 10 } }}
        >
          {errorMessage}
        </MessageBar>
      )}

      {successMessage && (
        <MessageBar
          messageBarType={MessageBarType.success}
          isMultiline={false}
          onDismiss={() => setSuccessMessage(null)}
          dismissButtonAriaLabel="Close"
          styles={{ root: { marginBottom: 10 } }}
        >
          {successMessage}
        </MessageBar>
      )}

      <Stack horizontal horizontalAlign="space-between" verticalAlign="center" styles={{ root: { marginBottom: 16 } }}>
        <Text variant="xLarge">User Tags</Text>
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <SearchBox
            placeholder="Search tags..."
            onChange={(_, newValue) => setSearchText(newValue || '')}
            styles={{ root: { width: 300 } }}
          />
          <CommandBar items={commandItems} />
        </Stack>
      </Stack>

      <DetailsList
        items={filteredTags}
        columns={columns}
        setKey="set"
        layoutMode={DetailsListLayoutMode.justified}
        selectionMode={SelectionMode.none}
        isHeaderVisible={true}
      />

      {/* Add Tag Dialog */}
      <Dialog
        hidden={!isAddTagDialogOpen}
        onDismiss={() => {
          setIsAddTagDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Create New Tag',
          subText: 'Fill in the details to create a new user tag.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={stackTokens}>
          <TextField
            label="Tag Name"
            required
            value={newTag.name}
            onChange={(_, value) => setNewTag({ ...newTag, name: value })}
          />
          <Stack>
            <Text>Tag Color</Text>
            <ColorPicker
              color={getColorFromString(newTag.color || '#0078d4') || '#0078d4'}
              onChange={(_, color) => handleColorChange(color)}
              alphaSliderHidden={true}
              showPreview={true}
              styles={{ root: { maxWidth: 300 } }}
            />
          </Stack>
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleAddTag} text="Create" />
          <DefaultButton onClick={() => {
            setIsAddTagDialogOpen(false);
            setErrorMessage(null);
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Edit Tag Dialog */}
      <Dialog
        hidden={!isEditTagDialogOpen}
        onDismiss={() => {
          setIsEditTagDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Edit Tag',
          subText: 'Update tag details.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={stackTokens}>
          <TextField
            label="Tag Name"
            required
            value={newTag.name}
            onChange={(_, value) => setNewTag({ ...newTag, name: value })}
          />
          <Stack>
            <Text>Tag Color</Text>
            <ColorPicker
              color={getColorFromString(newTag.color || '#0078d4') || '#0078d4'}
              onChange={(_, color) => handleColorChange(color)}
              alphaSliderHidden={true}
              showPreview={true}
              styles={{ root: { maxWidth: 300 } }}
            />
          </Stack>
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveEdit} text="Save" />
          <DefaultButton onClick={() => {
            setIsEditTagDialogOpen(false);
            setErrorMessage(null);
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        hidden={!isDeleteDialogOpen}
        onDismiss={() => setIsDeleteDialogOpen(false)}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Confirm Delete',
          subText: `Are you sure you want to delete the tag "${selectedTag?.name}"? This action cannot be undone.`
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <DialogFooter>
          <PrimaryButton onClick={handleDeleteTag} text="Delete" />
          <DefaultButton onClick={() => setIsDeleteDialogOpen(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default UserTagManagement;

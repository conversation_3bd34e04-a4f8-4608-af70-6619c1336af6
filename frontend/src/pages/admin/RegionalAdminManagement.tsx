import React, { useState, useEffect } from 'react';
import './UserManagement.css';
import {
  DetailsList,
  DetailsListLayoutMode,
  SelectionMode,
  IColumn,
  PrimaryButton,
  CommandBar,
  ICommandBarItemProps,
  Stack,
  TextField,
  Dropdown,
  IDropdownOption,
  Dialog,
  DialogType,
  DialogFooter,
  DefaultButton,
  MessageBar,
  MessageBarType,
  Text,
  SearchBox,
  IIconProps,
  IconButton,
  TooltipHost,
  ITooltipHostStyles,
  DirectionalHint,
  Spinner,
  SpinnerSize
} from '@fluentui/react';
import { useUser } from '../../state/UserProvider';
import { UserRole, UserProfile } from '../../models/roles';
import userContextService from '../../services/userContextService'; // Added userContextService
import { UserData, RegionData } from '../../services/rbacService'; // Keep type definitions
import { ApiResponse } from '../../services/rbacService'; // Import ApiResponse type

// Mock data for development/testing
const mockUsers: UserProfile[] = [
  {
    id: '1',
    name: '<PERSON>e',
    email: '<EMAIL>',
    role: UserRole.REGIONAL_ADMIN,
    region: 'North America',
    avatar: 'https://via.placeholder.com/150'
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: UserRole.REGIONAL_ADMIN,
    region: 'Europe',
    avatar: 'https://via.placeholder.com/150'
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: UserRole.REGIONAL_ADMIN,
    region: 'Asia Pacific',
    avatar: 'https://via.placeholder.com/150'
  }
];

// Mock regions for development/testing
const mockRegions: IDropdownOption[] = [
  { key: 'North America', text: 'North America' },
  { key: 'Europe', text: 'Europe' },
  { key: 'Asia Pacific', text: 'Asia Pacific' },
  { key: 'Latin America', text: 'Latin America' },
  { key: 'Middle East', text: 'Middle East' }
];

// Icon for tooltip
const infoIcon: IIconProps = { iconName: 'Info' };

// Tooltip styles
const tooltipStyles: Partial<ITooltipHostStyles> = {
  root: { display: 'inline-block', marginLeft: 8 }
};

const RegionalAdminManagement: React.FC = () => {
  const { currentUser } = useUser();
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false);
  const [isEditUserDialogOpen, setIsEditUserDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [newUser, setNewUser] = useState<Partial<UserProfile> & { role: UserRole }>({
    name: '',
    email: '',
    role: UserRole.REGIONAL_ADMIN, // Default to Regional Admin for this component
    region: ''
  });
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [regions, setRegions] = useState<IDropdownOption[]>([]);

  // Columns for the DetailsList
  const columns: IColumn[] = [
    {
      key: 'name',
      name: 'Name',
      fieldName: 'name',
      minWidth: 100,
      maxWidth: 200,
      isResizable: true
    },
    {
      key: 'email',
      name: 'Email',
      fieldName: 'email',
      minWidth: 200,
      maxWidth: 300,
      isResizable: true
    },
    {
      key: 'region',
      name: 'Region',
      fieldName: 'region',
      minWidth: 100,
      maxWidth: 200,
      isResizable: true,
      onRender: (item: UserProfile) => {
        // Find the region name from the regions list
        const regionObj = regions.find(r => r.key === item.region);
        const regionName = regionObj ? regionObj.text : item.region;
        return <span>{regionName || 'N/A'}</span>;
      }
    },
    {
      key: 'actions',
      name: 'Actions',
      minWidth: 100,
      maxWidth: 100,
      isResizable: false,
      onRender: (item: UserProfile) => {
        // Debug: Log the IDs to check if they match
        console.log('Current user ID:', currentUser?.id);
        console.log('Item user ID:', item.id);
        console.log('Are they equal?', item.id === currentUser?.id);

        // Check if this is the current user
        const isCurrentUser = item.id === currentUser?.id;

        // Check if this is a Super Admin and current user is not a Super Admin
        const isSuperAdminAndCurrentUserIsNot =
          item.role === UserRole.SUPER_ADMIN &&
          currentUser?.role !== UserRole.SUPER_ADMIN;

        return (
          <Stack horizontal tokens={{ childrenGap: 8 }}>
            <IconButton
              iconProps={{ iconName: 'Edit' }}
              title="Edit"
              ariaLabel="Edit"
              onClick={() => handleEditUser(item)}
              disabled={isCurrentUser || isSuperAdminAndCurrentUserIsNot}
            />
            {!isCurrentUser ? (
              <IconButton
                iconProps={{ iconName: 'Delete' }}
                title="Delete"
                ariaLabel="Delete"
                onClick={() => handleDeleteClick(item)}
                disabled={isSuperAdminAndCurrentUserIsNot}
              />
            ) : (
              <TooltipHost
                content="You cannot delete your own account"
                styles={tooltipStyles}
              >
                <IconButton
                  iconProps={{ iconName: 'Delete' }}
                  title="Delete"
                  ariaLabel="Delete"
                  disabled={true}
                />
              </TooltipHost>
            )}
          </Stack>
        );
      }
    }
  ];

  // Command bar items
  const commandItems: ICommandBarItemProps[] = [
    {
      key: 'addUser',
      text: 'Add Regional Admin',
      iconProps: { iconName: 'AddFriend' },
      onClick: () => setIsAddUserDialogOpen(true)
    },
    {
      key: 'refresh',
      text: 'Refresh',
      iconProps: { iconName: 'Refresh' },
      onClick: () => loadUsers()
    }
  ];

  // Load users and regions on component mount
  useEffect(() => {
    loadUsers();
    loadRegions();
  }, []);

  // Load regions from user context or a direct API call
  const loadRegions = async () => {
    try {
      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);

      // First try to get regions from user context
      const userContext = await userContextService.fetchUserContext();
      if (userContext && userContext.accessibleResources && userContext.accessibleResources.regions) {
        const regionOptionsFromCtx = userContext.accessibleResources.regions.map((region: { id: string, name: string }) => ({
          key: region.id,
          text: region.name
        }));
        setRegions(regionOptionsFromCtx);
        return;
      }

      // Fallback to rbacService if not in userContext
      console.log('Fetching regions with rbacService');
      const response = await rbacService.regions.getRegions();

      if (!response || !response.data) {
        throw new Error('Failed to fetch regions');
      }

      const regionOptionsFromApi = response.data.map((region: RegionData) => ({
        key: region.id,
        text: region.name
      }));
      setRegions(regionOptionsFromApi);
    } catch (error) {
      console.error('Error fetching regions:', error);
      setErrorMessage('Failed to load regions. Using mock data for development.');
      setRegions(mockRegions); // Fallback to mock regions
    }
  };

  // Filter users when search text changes
  useEffect(() => {
    if (searchText) {
      const filtered = users.filter(user =>
        user.name.toLowerCase().includes(searchText.toLowerCase()) ||
        user.email.toLowerCase().includes(searchText.toLowerCase()) ||
        (user.region && user.region.toLowerCase().includes(searchText.toLowerCase()))
      );
      setFilteredUsers(filtered);
    } else {
      setFilteredUsers(users);
    }
  }, [searchText, users]);

  // Load users from RBAC API - only regional admins
  const loadUsers = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);
      console.log('Fetching users with rbacService');

      // Call the API to get users using rbacService
      const response = await rbacService.users.getUsers();

      if (!response || !response.data) {
        setErrorMessage('Error loading users: Unknown error');
        setUsers(mockUsers);
        setFilteredUsers(mockUsers);
        return;
      }

      // Convert API response to UserProfile format and filter for REGIONAL_ADMIN only
      const apiUsers: UserProfile[] = response.data
        .filter((userData: UserData) => userData.role === UserRole.REGIONAL_ADMIN)
        .map((userData: UserData) => ({
          id: userData.id,
          name: userData.name,
          email: userData.email,
          role: userData.role as UserRole,
          region: userData.region,
          avatar: userData.avatar
        }));

      setUsers(apiUsers);
      setFilteredUsers(apiUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      setErrorMessage('Failed to load users. Using mock data for development.');
      // Fallback to mock data for development
      setUsers(mockUsers);
      setFilteredUsers(mockUsers);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle adding a new user
  const handleAddUser = async () => {
    // Validate form
    if (!newUser.name || !newUser.email) {
      setErrorMessage('Please fill in all required fields.');
      return;
    }

    if (!newUser.region) {
      setErrorMessage('Region is required for Regional Admins.');
      return;
    }

    try {
      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);
      console.log('Creating user with rbacService');

      // Call API to create user using rbacService
      const response = await rbacService.users.createUser({
        name: newUser.name,
        email: newUser.email,
        role: UserRole.REGIONAL_ADMIN, // Always create as Regional Admin in this component
        region: newUser.region,
      });

      if (!response || !response.data) {
        setErrorMessage(response?.error || 'Failed to create user: Unknown error');
        return;
      }

      // Refresh users list
      await loadUsers();

      setIsAddUserDialogOpen(false);
      setNewUser({
        name: '',
        email: '',
        role: UserRole.REGIONAL_ADMIN,
        region: ''
      });
      setErrorMessage(null);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'success-message';
      successMessage.textContent = `Regional Admin "${newUser.name}" created successfully.`;
      document.body.appendChild(successMessage);
      setTimeout(() => document.body.removeChild(successMessage), 3000);
    } catch (error) {
      console.error('Error creating user:', error);
      setErrorMessage('Failed to create user. Please try again later.');
    }
  };

  // Handle editing a user
  const handleEditUser = (user: UserProfile) => {
    setSelectedUser(user);
    setNewUser({
      name: user.name,
      email: user.email,
      role: UserRole.REGIONAL_ADMIN, // Always Regional Admin in this component
      region: user.region
    });
    setIsEditUserDialogOpen(true);
  };

  // Handle saving edited user
  const handleSaveEdit = async () => {
    if (!selectedUser) return;

    // Validate form
    if (!newUser.name || !newUser.email) {
      setErrorMessage('Please fill in all required fields.');
      return;
    }

    if (!newUser.region) {
      setErrorMessage('Region is required for Regional Admins.');
      return;
    }

    try {
      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);
      console.log('Updating user with rbacService');

      // Call API to update user using rbacService
      const response = await rbacService.users.updateUser(selectedUser.id, {
        name: newUser.name,
        email: newUser.email,
        role: UserRole.REGIONAL_ADMIN, // Always Regional Admin in this component
        region: newUser.region,
      });

      if (!response || !response.data) {
        setErrorMessage(response?.error || 'Failed to update user: Unknown error');
        return;
      }

      // Refresh users list
      await loadUsers();

      setIsEditUserDialogOpen(false);
      setSelectedUser(null);
      setNewUser({
        name: '',
        email: '',
        role: UserRole.REGIONAL_ADMIN,
        region: ''
      });
      setErrorMessage(null);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'success-message';
      successMessage.textContent = `Regional Admin "${newUser.name}" updated successfully.`;
      document.body.appendChild(successMessage);
      setTimeout(() => document.body.removeChild(successMessage), 3000);
    } catch (error) {
      console.error('Error updating user:', error);
      setErrorMessage('Failed to update user. Please try again later.');
    }
  };

  // Handle delete click
  const handleDeleteClick = (user: UserProfile) => {
    // Debug logging
    console.log('Delete clicked for regional admin:', user);
    console.log('Current user:', currentUser);
    console.log('IDs match?', user.id === currentUser?.id);

    // Additional safety check to prevent deleting yourself
    if (user.id === currentUser?.id) {
      console.log('Preventing self-deletion - IDs match');
      setErrorMessage('You cannot delete your own account.');
      return;
    }

    setSelectedUser(user);
    setIsDeleteDialogOpen(true);
  };

  // Handle deleting a user
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    // Debug logging
    console.log('Attempting to delete regional admin:', selectedUser);
    console.log('Current user:', currentUser);
    console.log('IDs match?', selectedUser.id === currentUser?.id);

    // Additional safety check to prevent deleting yourself
    if (selectedUser.id === currentUser?.id) {
      console.log('Preventing self-deletion in handleDeleteUser - IDs match');
      setErrorMessage('You cannot delete your own account.');
      setIsDeleteDialogOpen(false);
      return;
    }

    try {
      // Log the request being sent
      console.log('Sending delete request for regional admin ID:', selectedUser.id);

      // Use rbacService instead of direct fetch to ensure proper authentication
      const rbacService = await import('../../services/rbacService').then(module => module.default);
      console.log('Deleting user with rbacService');

      // Call API to delete user using rbacService
      const response = await rbacService.users.deleteUser(selectedUser.id);

      console.log('Delete regional admin response:', response);

      if (!response || response.error) {
        const errorMsg = response?.error || 'Failed to delete user: Unknown error';
        console.error('Delete regional admin error:', errorMsg);
        setErrorMessage(errorMsg);
        return;
      }

      // Refresh users list
      await loadUsers();

      setIsDeleteDialogOpen(false);
      setSelectedUser(null);

      // Show success message
      const successMessage = document.createElement('div');
      successMessage.className = 'success-message';
      successMessage.textContent = `Regional Admin "${selectedUser.name}" deleted successfully.`;
      document.body.appendChild(successMessage);
      setTimeout(() => document.body.removeChild(successMessage), 3000);
    } catch (error) {
      console.error('Error deleting user:', error);
      setErrorMessage('Failed to delete user. Please try again later.');
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {errorMessage && (
        <MessageBar
          messageBarType={MessageBarType.error}
          isMultiline={false}
          onDismiss={() => setErrorMessage(null)}
          dismissButtonAriaLabel="Close"
          styles={{ root: { marginBottom: 10 } }}
        >
          {errorMessage}
        </MessageBar>
      )}

      <Stack horizontal horizontalAlign="space-between" verticalAlign="center" styles={{ root: { marginBottom: 16 } }}>
        <Text variant="xLarge">Regional Admins Management</Text>
        <Stack horizontal tokens={{ childrenGap: 8 }}>
          <SearchBox
            placeholder="Search regional admins..."
            onChange={(_, newValue) => setSearchText(newValue || '')}
            styles={{ root: { width: 300 } }}
          />
          <CommandBar items={commandItems} />
        </Stack>
      </Stack>

      {isLoading ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Spinner size={SpinnerSize.large} label="Loading regional admins..." />
        </Stack>
      ) : filteredUsers.length === 0 ? (
        <Stack horizontalAlign="center" verticalAlign="center" styles={{ root: { padding: 20 } }}>
          <Text>No regional admins found. {errorMessage ? 'Error loading users.' : ''}</Text>
        </Stack>
      ) : (
        <DetailsList
          items={filteredUsers}
          columns={columns}
          setKey="set"
          layoutMode={DetailsListLayoutMode.justified}
          selectionMode={SelectionMode.none}
          isHeaderVisible={true}
        />
      )}

      {/* Add User Dialog */}
      <Dialog
        hidden={!isAddUserDialogOpen}
        onDismiss={() => {
          setIsAddUserDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Add New Regional Admin',
          subText: 'Fill in the details to add a new regional admin.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField
            label="Name"
            required
            value={newUser.name}
            onChange={(_, value) => setNewUser({ ...newUser, name: value })}
          />
          <TextField
            label="Email"
            required
            value={newUser.email}
            onChange={(_, value) => setNewUser({ ...newUser, email: value })}
          />
          <Dropdown
            label="Region"
            required
            selectedKey={newUser.region}
            options={regions}
            onChange={(_, option) => setNewUser({ ...newUser, region: option?.key as string })}
          />
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleAddUser} text="Add" />
          <DefaultButton onClick={() => {
            setIsAddUserDialogOpen(false);
            setErrorMessage(null);
            setNewUser({
              name: '',
              email: '',
              role: UserRole.REGIONAL_ADMIN,
              region: ''
            });
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog
        hidden={!isEditUserDialogOpen}
        onDismiss={() => {
          setIsEditUserDialogOpen(false);
          setErrorMessage(null);
        }}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Edit Regional Admin',
          subText: 'Update regional admin details.'
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField
            label="Name"
            required
            value={newUser.name}
            onChange={(_, value) => setNewUser({ ...newUser, name: value })}
          />
          <TextField
            label="Email"
            required
            value={newUser.email}
            onChange={(_, value) => setNewUser({ ...newUser, email: value })}
          />
          <Dropdown
            label="Region"
            required
            selectedKey={newUser.region}
            options={regions}
            onChange={(_, option) => setNewUser({ ...newUser, region: option?.key as string })}
          />
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveEdit} text="Save" />
          <DefaultButton onClick={() => {
            setIsEditUserDialogOpen(false);
            setErrorMessage(null);
            setSelectedUser(null);
            setNewUser({
              name: '',
              email: '',
              role: UserRole.REGIONAL_ADMIN,
              region: ''
            });
          }} text="Cancel" />
        </DialogFooter>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        hidden={!isDeleteDialogOpen}
        onDismiss={() => setIsDeleteDialogOpen(false)}
        dialogContentProps={{
          type: DialogType.normal,
          title: 'Confirm Delete',
          subText: `Are you sure you want to delete ${selectedUser?.name}? This action cannot be undone.`
        }}
        modalProps={{
          isBlocking: true,
          styles: { main: { maxWidth: 450 } }
        }}
      >
        <DialogFooter>
          <PrimaryButton onClick={handleDeleteUser} text="Delete" />
          <DefaultButton onClick={() => setIsDeleteDialogOpen(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default RegionalAdminManagement;

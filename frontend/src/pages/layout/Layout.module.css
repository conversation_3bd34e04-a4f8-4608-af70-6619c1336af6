/* frontend/src/pages/layout/Layout.module.css */
.layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  /* Add this to ensure proper stacking */
  position: relative;
}

.mainContentArea {
  /* Add these properties */
  position: relative;
  flex: 1;
  overflow-y: auto;
  z-index: 1;
}

.header {
  background-color: #f2f2f2;
  position: relative;
  z-index: 1000; /* Ensure header stays on top */
}

.headerContainer {
  display: flex;
  justify-content: left;
  align-items: center;
}

.headerTitleContainer {
  display: flex;
  align-items: center;
  margin-left: 14px;
  text-decoration: none;
}

.headerTitle {
  font-style: normal;
  font-weight: 600;
  font-size: 20px;
  line-height: 28px;
  display: flex;
  align-items: flex-end;
  color: #242424;
}

.headerIcon {
  height: 32px;
  width: auto;
  margin-left: 36px;
}

.shareButtonContainer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-right: 20px;
}

.shareButton {
  color: #ffffff;
}

.shareButtonText {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  align-items: center;
  color: #ffffff;
}

.urlTextBox {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #707070;
  border: 1px solid #d1d1d1;
  border-radius: 4px;
}

.copyButtonContainer {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 5px 12px;
  gap: 4px;
  background: #ffffff;
  border: 1px solid #d1d1d1;
  border-radius: 4px;
  margin-right: 32px;
}

.copyButtonContainer:hover {
  cursor: pointer;
  background: #d1d1d1;
}

.copyButton {
  color: #424242;
}

.copyButtonText {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  display: flex;
  align-items: center;
  color: #242424;
}

.logoutButton {
  background: radial-gradient(109.81% 107.82% at 100.1% 90.19%, #ff6c6c 33.63%, #ff8787 70.31%, #ffdddd 100%);
  color: white;
  padding: 8px 16px;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.logoutButton:hover {
  background: linear-gradient(135deg, #ff6c6c 0%, #ff8787 51.04%, #ffdddd 100%);
}

@media (max-width: 768px) {
  .headerTitleContainer {
    margin-left: 4px;
  }

  .headerIcon {
    margin-left: 26px;
  }

}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.backButton {
  margin-top: 20px;
}

.projectTitle {
  margin-left: 10px;
  font-weight: 600;
}

/* Style header buttons */
.headerButtonsContainer button {
  border: none !important;
  min-width: 120px !important;
  width: auto !important;
}

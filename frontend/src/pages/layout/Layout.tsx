// frontend/src/pages/layout/Layout.tsx

import { useContext, useEffect, useState, useRef } from 'react';
import { BlobServiceClient } from '@azure/storage-blob';
import { Link, Outlet } from 'react-router-dom';
import {
  <PERSON>alog,
  Stack,
  TextField,
  DefaultButton,
  MessageBar,
  MessageBarType,
} from '@fluentui/react';
import { CopyRegular } from '@fluentui/react-icons';

import { CosmosDBStatus } from '../../api';
import CompanyLogo from '../../assets/keyrus-2.svg';
import { HistoryButton, LogoutButton } from '../../components/common/Button';
import SupportTicket from '../../components/SupportTicket';
import { AppStateContext } from '../../state/AppProvider';
import styles from './Layout.module.css';
import { getStorageConfig, StorageConfig } from '../../services/configService';
import * as WebSocketManager from '../../services/websocketManager';
import { performLogout } from '../../services/authService';

const Layout: React.FC = () => {
  // State Hooks
  const [isSharePanelOpen, setIsSharePanelOpen] = useState<boolean>(false);
  const [copyClicked, setCopyClicked] = useState<boolean>(false);
  const [copyText, setCopyText] = useState<string>('Copy URL');
  const [shareLabel, setShareLabel] = useState<string | undefined>('Share');
  const [hideHistoryLabel, setHideHistoryLabel] = useState<string>('Hide chat history');
  const [showHistoryLabel, setShowHistoryLabel] = useState<string>('Show chat history');
  const [logo, setLogo] = useState<string>(CompanyLogo);
  const [isSupportTicketOpen, setIsSupportTicketOpen] = useState<boolean>(false);
  const [storageConfig, setStorageConfig] = useState<StorageConfig>({
    account_name: '',
    container_name: '',
    container_sas_token: ''
  });
  const [uploadStatus, setUploadStatus] = useState<{ success: boolean; message?: string }>({
    success: false,
  });
  const [isBannerVisible, setIsBannerVisible] = useState<boolean>(true);

  // Context and Refs
  const appStateContext = useContext(AppStateContext);
  const ui = appStateContext?.state.frontendSettings?.ui;
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleLogoutClick = async () => {
    try {
      WebSocketManager.closeAllWebSockets();
      await performLogout();
    } catch (error) {
      console.error('Logout error:', error);
      window.location.href = '/';
    }
  };
  

  // Handler: Share Button Click
  const handleShareClick = () => {
    setIsSharePanelOpen(true);
  };

  // Handler: Share Panel Dismiss
  const handleSharePanelDismiss = () => {
    setIsSharePanelOpen(false);
    setCopyClicked(false);
    setCopyText('Copy URL');
  };

  // Handler: Copy URL Click
  const handleCopyClick = () => {
    navigator.clipboard.writeText(window.location.href).then(
      () => {
        setCopyClicked(true);
        setCopyText('Copied URL');
      },
      (err) => {
        console.error('Failed to copy URL:', err);
        setCopyText('Failed to copy');
      }
    );
  };

  // Handler: History Button Click
  const handleHistoryClick = () => {
    appStateContext?.dispatch({ type: 'TOGGLE_CHAT_HISTORY' });
  };

  const handleSupportClick = () => {
    setIsSupportTicketOpen(true);
  };

  // Effect: Set Logo on Load
  useEffect(() => {
    if (!appStateContext?.state.isLoading) {
      setLogo(ui?.logo || CompanyLogo);
    }
  }, [appStateContext?.state.isLoading, ui?.logo]);

  // Effect: Update Copy Text
  useEffect(() => {
    if (copyClicked) {
      setCopyText('Copied URL');
    }
  }, [copyClicked]);

  // Effect: Handle Window Resize for Responsive Labels
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 480) {
        setShareLabel(undefined);
        setHideHistoryLabel('Hide history');
        setShowHistoryLabel('Show history');
      } else {
        setShareLabel('Share');
        setHideHistoryLabel('Hide chat history');
        setShowHistoryLabel('Show chat history');
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initialize labels based on current window size

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const initStorageConfig = async () => {
      console.log('Layout: Initializing storage configuration');
      try {
        const config = await getStorageConfig();
        console.log('Layout: Retrieved storage config:', {
          hasConfig: Boolean(config),
          configDetails: {
            hasAccountName: Boolean(config?.account_name),
            hasContainerName: Boolean(config?.container_name),
            hasSasToken: Boolean(config?.container_sas_token),
            accountName: config?.account_name,
            containerName: config?.container_name
          }
        });

        // Validate configuration
        const validationResult = {
          hasAccountName: Boolean(config?.account_name?.trim()),
          hasContainerName: Boolean(config?.container_name?.trim()),
          hasSasToken: Boolean(config?.container_sas_token?.trim())
        };

        console.log('Layout: Storage config validation result:', validationResult);

        if (!validationResult.hasAccountName || !validationResult.hasContainerName || !validationResult.hasSasToken) {
          console.warn('Layout: Missing required blob storage configuration:', validationResult);
          const missingFields = [];
          if (!validationResult.hasAccountName) missingFields.push('account_name');
          if (!validationResult.hasContainerName) missingFields.push('container_name');
          if (!validationResult.hasSasToken) missingFields.push('container_sas_token');
          console.warn('Layout: Missing fields:', missingFields);
        }

        setStorageConfig(config);
      } catch (error) {
        console.error('Layout: Error initializing storage config:', error);
      }
    };

    initStorageConfig();
  }, []);

  return (
    <div className={styles.layout}>
      {isBannerVisible && (
        <div className={styles.banner}>
          {/* Banner content */}
        </div>
      )}

      {/* Header */}
      <header className={styles.header} role="banner">
        <Stack horizontal verticalAlign="center" horizontalAlign="space-between">
          <Stack horizontal verticalAlign="center">
            <img src={logo} className={styles.headerIcon} aria-hidden="true" alt="Logo" />
            <Link to="/" className={styles.headerTitleContainer}>
              <h1 className={styles.headerTitle}>{ui?.title}</h1>
            </Link>
          </Stack>
          <Stack horizontal tokens={{ childrenGap: 8 }} className={styles.buttonContainer}>
            {appStateContext?.state.isCosmosDBAvailable?.status !== CosmosDBStatus.NotConfigured &&
              ui?.show_chat_history_button !== false && (
                <HistoryButton
                  onClick={handleHistoryClick}
                  text={appStateContext?.state?.isChatHistoryOpen ? hideHistoryLabel : showHistoryLabel}
                />
              )}
            <DefaultButton text="Support" onClick={handleSupportClick} />
            <LogoutButton onClick={handleLogoutClick} text="Logout" />
          </Stack>
        </Stack>
      </header>

      {/* Main Content Area */}
      <div className={styles.mainContentArea}>

        {/* Content Container */}
        <div className={styles.contentContainer}>
          <Outlet />
        </div>

      </div>


      {/* Upload Status Message */}
      {uploadStatus.message && (
        <MessageBar
          messageBarType={uploadStatus.success ? MessageBarType.success : MessageBarType.error}
          onDismiss={() => setUploadStatus({ success: false })}
        >
          {uploadStatus.message}
        </MessageBar>
      )}

      {/* Share Dialog */}
      <Dialog
        onDismiss={handleSharePanelDismiss}
        hidden={!isSharePanelOpen}
        styles={{
          main: {
            maxWidth: 600,
            background: '#FFFFFF',
            boxShadow: '0px 14px 28.8px rgba(0, 0, 0, 0.24), 0px 0px 8px rgba(0, 0, 0, 0.2)',
            borderRadius: 8,
            maxHeight: 200,
            minHeight: 100,
          },
        }}
        dialogContentProps={{
          title: 'Share the web app',
          showCloseButton: true,
        }}
      >
        <Stack horizontal verticalAlign="center" style={{ gap: '8px' }}>
          <TextField className={styles.urlTextBox} defaultValue={window.location.href} readOnly />
          <div
            className={styles.copyButtonContainer}
            role="button"
            tabIndex={0}
            aria-label="Copy"
            onClick={handleCopyClick}
            onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ' ? handleCopyClick() : null)}
          >
            <CopyRegular className={styles.copyButton} />
            <span className={styles.copyButtonText}>{copyText}</span>
          </div>
        </Stack>
      </Dialog>
      <SupportTicket
        isOpen={isSupportTicketOpen}
        onClose={() => setIsSupportTicketOpen(false)}
      />
    </div>
  );
};

export default Layout;
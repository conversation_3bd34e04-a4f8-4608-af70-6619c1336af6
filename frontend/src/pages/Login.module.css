.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loginBox {
  width: 400px;
  padding: 40px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.logoContainer {
  text-align: center;
  margin-bottom: 20px;
}

.logoText {
  font-size: 32px;
  color: #0078d4;
  margin: 0;
}

.title {
  font-size: 24px;
  text-align: center;
  margin-bottom: 24px;
  color: #333;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formGroup label {
  font-size: 14px;
  font-weight: 500;
  color: #555;
}

.formGroup input {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.formGroup input:focus {
  outline: none;
  border-color: #0078d4;
}

.formActions {
  margin-top: 10px;
}

.loginButton {
  width: 100%;
  padding: 12px;
  background-color: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.loginButton:hover {
  background-color: #106ebe;
}

.loginButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.errorMessage {
  background-color: #fde7e9;
  border-left: 4px solid #d13438;
  color: #d13438;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  font-size: 14px;
}

.demoSection {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
  text-align: center;
}

.demoButton {
  background-color: transparent;
  border: 1px solid #0078d4;
  color: #0078d4;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.demoButton:hover {
  background-color: #f0f7ff;
}

.demoButton:disabled {
  border-color: #ccc;
  color: #ccc;
  cursor: not-allowed;
}

/* Loading container for MSAL interaction */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
  font-size: 18px;
  color: #0078d4;
}

/* Entra ID login section */
.entraIdSection {
  margin-bottom: 20px;
  width: 100%;
}

.entraIdButton {
  width: 100%;
  padding: 12px;
  background-color: #0078d4;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.entraIdButton:hover {
  background-color: #106ebe;
}

.entraIdButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Divider with text */
.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 20px 0;
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid #eee;
}

.divider span {
  padding: 0 10px;
  color: #666;
  font-size: 14px;
}
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { InteractionStatus, InteractionRequiredAuthError } from '@azure/msal-browser';
import styles from './Login.module.css';
import { msalInstance, graphScopes, clientId, tenantId } from '../auth/msal-config';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const navigate = useNavigate();
  const { instance, accounts, inProgress } = useMsal();

  // Check if user is already logged in
  useEffect(() => {
    console.log('Login component - Checking authentication status');
    console.log('MSAL accounts:', accounts);
    console.log('MSAL inProgress:', inProgress);
    console.log('Current URL:', window.location.href);

    // If we're in the middle of a redirect, don't do anything
    if (inProgress !== 'none') {
      console.log('MSAL operation in progress, waiting...');
      return;
    }

    // Handle redirect response if there's a code in the URL
    if (window.location.href.includes('code=')) {
      console.log('Detected authorization code in URL, handling redirect...');
      console.log('Current hash:', window.location.hash);

      // Extract the code and other parameters from the URL hash
      const hashParams = new URLSearchParams(window.location.hash.substring(window.location.hash.indexOf('code=')));
      console.log('Hash params:', Array.from(hashParams.entries()));

      // Force a manual login completion
      if (hashParams.has('code')) {
        console.log('Found authorization code, completing login manually');
        const code = hashParams.get('code');
        console.log('Authorization code:', code);

        // Set a flag to indicate we're authenticated
        localStorage.setItem('isAuthenticated', 'true');

        // Redirect to projects page
        console.log('Redirecting to projects page');
        navigate('/projects');
        return;
      }

      // Also try the standard MSAL flow
      msalInstance.handleRedirectPromise()
        .then(response => {
          console.log('Redirect handled successfully:', response);
          if (response) {
            // If we have a response, we've successfully authenticated
            console.log('Authentication successful, redirecting to projects');
            navigate('/projects');
          } else {
            console.log('No response from handleRedirectPromise, but we have a code');
            // Try manual navigation as a fallback
            localStorage.setItem('isAuthenticated', 'true');
            navigate('/projects');
          }
        })
        .catch(error => {
          console.error('Error handling redirect:', error);
          setError('Failed to complete authentication. Please try again.');

          // Even if there's an error, if we have a code, we can assume authentication succeeded
          if (hashParams.has('code')) {
            console.log('Despite error, we have a code, so proceeding with authentication');
            localStorage.setItem('isAuthenticated', 'true');
            navigate('/projects');
          }
        });
    } else if (accounts.length > 0) {
      console.log('User is already logged in with MSAL, redirecting to projects');
      // User is already logged in with MSAL, redirect to projects
      navigate('/projects');
    } else {
      // Check localStorage for development mode authentication
      const storedAuthState = localStorage.getItem('isAuthenticated');
      if (storedAuthState === 'true') {
        console.log('User is authenticated in development mode, redirecting to projects');
        navigate('/projects');
      }
    }
  }, [accounts, navigate, inProgress]);

  // Handle Entra ID login
  const handleEntraIDLogin = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // First, ensure MSAL is initialized
      try {
        console.log('Ensuring MSAL is initialized...');
        // Use the instance from useMsal() hook instead of msalInstance
        const activeAccount = instance.getActiveAccount();
        if (activeAccount) {
          console.log('MSAL instance already initialized in Login with active account:', activeAccount.username);
        } else {
          console.log('MSAL instance initialized but no active account in Login');
        }
      } catch (initError) {
        console.error('Error checking MSAL in Login:', initError);
        // Continue anyway, as we'll try the login or use the direct approach
      }

      // Use only specific scopes (not .default) as per Azure Portal configuration
      const allScopes = [...graphScopes];
      // Ensure we have the required scopes
      if (!allScopes.includes('User.Read')) allScopes.push('User.Read');
      if (!allScopes.includes('openid')) allScopes.push('openid');

      // Remove any .default scopes as they cannot be combined with resource-specific scopes
      const filteredScopes = allScopes.filter(scope => !scope.endsWith('.default'));

      console.log('Initiating Entra ID login with scopes:', filteredScopes);

      // Use dynamic redirect URI based on environment
      const redirectUri = window.location.hostname === 'localhost'
        ? import.meta.env.VITE_AZURE_REDIRECT_URI
        : (import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI || `https://ai-scope-app3.azurewebsites.net/auth/callback`);

      if (!redirectUri) {
        console.error('Login: No redirect URI configured for current environment');
        setError('Authentication configuration error. Please check environment variables.');
        setIsLoading(false);
        return;
      }

      // Try to use MSAL's loginRedirect first
      try {
        console.log('Attempting to use MSAL loginRedirect');
        await instance.loginRedirect({
          scopes: filteredScopes,
          redirectUri: redirectUri
        });
        // The page will redirect to Microsoft login, so we won't reach here
      } catch (msalError) {
        console.error('Error using MSAL loginRedirect:', msalError);
        console.log('Falling back to direct authorization URL approach');

        // Create a code verifier and challenge for PKCE
        const array = new Uint8Array(32);
        window.crypto.getRandomValues(array);
        const codeVerifier = btoa(String.fromCharCode.apply(null, [...array]))
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=+$/, '');

        // Generate code challenge
        const encoder = new TextEncoder();
        const data = encoder.encode(codeVerifier);
        const digest = await window.crypto.subtle.digest('SHA-256', data);
        const codeChallenge = btoa(String.fromCharCode.apply(null, [...new Uint8Array(digest)]))
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=+$/, '');

        // Store the code verifier in session storage for later use
        sessionStorage.setItem('codeVerifier', codeVerifier);

        // Use environment variables for Azure configuration
        console.log('Login - Using Client ID:', clientId);
        console.log('Login - Using Tenant ID:', tenantId);
        const scopes = encodeURIComponent(filteredScopes.join(' ')); // Use filtered scopes
        const encodedRedirectUri = encodeURIComponent(redirectUri); // Use dynamic redirect URI

        // Construct the authorization URL
        const authUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize?` +
          `client_id=${clientId}` +
          `&scope=${scopes}` +
          `&redirect_uri=${encodedRedirectUri}` +
          `&response_type=code` +
          `&response_mode=fragment` +
          `&code_challenge=${codeChallenge}` +
          `&code_challenge_method=S256`;

        console.log('Redirecting to Entra ID login URL:', authUrl);

        // Redirect to the Entra ID login page
        window.location.href = authUrl;
      }
    } catch (err) {
      console.error('Entra ID login error:', err);
      setError('Failed to initiate login. Please try again.');
      setIsLoading(false);
    }
  };

  // Handle form login (for development/testing)
  const handleFormLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // For demo purposes, accept any credentials and redirect to projects
      // In a real app, you would validate against a backend

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Store auth state (in a real app, this would be a token)
      localStorage.setItem('isAuthenticated', 'true');

      // Redirect to projects page
      navigate('/projects');
    } catch (err) {
      setError('Invalid credentials. Please try again.');
      console.error('Login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // In development mode, provide a quick way to bypass login
  const handleDemoLogin = () => {
    localStorage.setItem('isAuthenticated', 'true');
    navigate('/projects');
  };

  // Don't show login page if MSAL interaction is in progress
  if (inProgress !== InteractionStatus.None) {
    return <div className={styles.loadingContainer}>Loading authentication...</div>;
  }

  return (
    <div className={styles.loginContainer}>
      <div className={styles.loginBox}>
        <div className={styles.logoContainer}>
          <h1 className={styles.logoText}>Keyrus</h1>
        </div>

        <h2 className={styles.title}>Sign in to AI Scope</h2>

        {error && (
          <div className={styles.errorMessage}>
            {error}
          </div>
        )}

        {/* Entra ID Login Button */}
        <div className={styles.entraIdSection}>
          <button
            className={styles.entraIdButton}
            onClick={handleEntraIDLogin}
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign in with Microsoft Entra ID'}
          </button>
        </div>

        <div className={styles.divider}>
          <span>OR</span>
        </div>

        {/* Form Login (for development) */}
        <form onSubmit={handleFormLogin} className={styles.form}>
          <div className={styles.formGroup}>
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              disabled={isLoading}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              disabled={isLoading}
            />
          </div>

          <div className={styles.formActions}>
            <button
              type="submit"
              className={styles.loginButton}
              disabled={isLoading}
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>

        <div className={styles.demoSection}>
          <button
            className={styles.demoButton}
            onClick={handleDemoLogin}
            disabled={isLoading}
          >
            Demo Mode (Skip Login)
          </button>
        </div>
      </div>
    </div>
  );
};

export default Login;
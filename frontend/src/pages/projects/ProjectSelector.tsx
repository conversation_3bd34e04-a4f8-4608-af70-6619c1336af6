import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  Stack,
  Text,
  DefaultButton,
  IconButton,
  MessageBar,
  MessageBarType,
  Spinner,
  SpinnerSize,
  IButtonStyles,
  ContextualMenu,
  Dialog,
  DialogType,
  DialogFooter,
  PrimaryButton,
  IContextualMenuItem,
  DirectionalHint,
  Icon,
  CommandButton,
  IContextualMenuProps,
  TextField,
  ProgressIndicator,
  TooltipHost,
  Dropdown,
  IDropdownOption
} from '@fluentui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AddRegular, GridRegular, ListRegular } from '@fluentui/react-icons';
import useAuthCheck from '../../hooks/useAuthCheck';
import styles from './ProjectSelector.module.css';
import { useAppContext } from '../../state/AppProvider';
import { useUser } from '../../state/UserProvider';
import { User<PERSON>ole, RolePermissions } from '../../models/roles'; // Added RolePermissions
import CompanyLogo from '../../assets/keyrus-2.svg';
import { projectDelete } from '../../api/api';
import { getCachedProjects, cacheProjects, getCachedProjectStatus, cacheProjectStatus } from '../../utils/projectCache';
import RoleBasedHeader from '../../components/RoleBasedHeader';
import ProjectActionsMenu from '../../components/ProjectActionsMenu';
import userContextService from '../../services/userContextService';
import * as WebSocketManager from '../../services/websocketManager';
import { businessIcons } from '../../utils/projectIcons';
import costService, { CostData, ProjectCost } from '../../services/costService';
import CostDataWarningBanner from '../../components/CostDataWarningBanner';

interface Project {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  search_index: string;
  storage_container: string;
  role: 'owner' | 'contributor' | 'viewer';
  environment: Record<string, string>;
  color?: string;
  icon?: string;
  cost_limit?: number | null;
  current_cost?: number;
  budget_exceeded?: boolean;
  budget_percentage_used?: number;
  deploymentStatus?: 'pending' | 'in_progress' | 'completed' | 'failed';
  deployment_status?: {
    status?: string;
    details?: Record<string, any>;
    error?: string;
  };
  deploymentDetails?: Record<string, any>;
  deploymentMessage?: string;
  teams?: string[];
  region?: string;
  deploymentStartTime?: string;
  deploymentProgress?: number;
}

type ViewType = 'grid' | 'list';

const ProjectSelector: React.FC = () => {
  const { isCheckingAuth } = useAuthCheck('/');
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [searchText, setSearchText] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewType, setViewType] = useState<ViewType>('grid');
  const [logo, setLogo] = useState<string>(CompanyLogo);
  const [menuTarget, setMenuTarget] = useState<HTMLElement | null>(null);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showRegionFilter, setShowRegionFilter] = useState<boolean>(false);
  const [selectedRegion, setSelectedRegion] = useState<string>('all');
  const [isEditDialogOpen, setIsEditDialogOpen] = useState<boolean>(false);
  const [editedProjectName, setEditedProjectName] = useState<string>('');
  const [editedProjectDescription, setEditedProjectDescription] = useState<string>('');
  const [editedProjectIcon, setEditedProjectIcon] = useState<string>('');
  const [isCostLimitDialogOpen, setIsCostLimitDialogOpen] = useState<boolean>(false);
  const [editedCostLimit, setEditedCostLimit] = useState<string>('');
  const [regionMap, setRegionMap] = useState<{[key: string]: string}>({});
  const [isLoadingRegions, setIsLoadingRegions] = useState<boolean>(false);
  const [showNewProjectBanner, setShowNewProjectBanner] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [costDataWarning, setCostDataWarning] = useState<CostData['warning'] | null>(null);
  const [isCollectingCost, setIsCollectingCost] = useState(false);
  const [isBudgetExceededDialogOpen, setIsBudgetExceededDialogOpen] = useState(false);
  const [budgetExceededProject, setBudgetExceededProject] = useState<Project | null>(null);
  const { state: appState, appConfig } = useAppContext();
  const { currentUser, isLoading: isUserLoading } = useUser();

  const defaultPermissions: RolePermissions = {
    canCreateProject: false,
    canEditProject: false,
    canDeleteProject: false,
    canAssignUsers: false,
    canSetCostLimits: false,
    canAccessAdminPanel: false,
    canManageUsers: false,
    canManageGlobalSettings: false,
    canCreateTeams: false,
    canAssignProjects: false,
    canAssignTeams: false,
    canSetupRegionalAdmins: false,
    canTagUsers: false,
    canViewAllRegions: false
  };
  const viewPermissions: RolePermissions = currentUser?.permissions || defaultPermissions;

  const ui = appState.frontendSettings?.ui;
  const navigate = useNavigate();
  const menuButtonRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  useEffect(() => {
    if (currentUser && currentUser.accessibleResources && currentUser.accessibleResources.regions) {
      setIsLoadingRegions(true);
      const regionMapping: {[key: string]: string} = {};
      currentUser.accessibleResources.regions.forEach((region: any) => {
        regionMapping[region.id] = region.name;
      });
      setRegionMap(regionMapping);
      setIsLoadingRegions(false);
    } else if (!isUserLoading) {
      console.warn('No accessible regions found in currentUser context.');
      setIsLoadingRegions(false);
    }
    setSelectedRegion('all');
  }, [currentUser, isUserLoading]);

  useEffect(() => {
    const newProjectId = localStorage.getItem('newProjectId');
    const newProjectName = localStorage.getItem('newProjectName');

    if (newProjectId && newProjectName) {
      console.log('Found new project in localStorage:', { newProjectId, newProjectName });
      setShowNewProjectBanner(true);
      setNewProjectName(newProjectName);

      setTimeout(() => {
        localStorage.removeItem('newProjectId');
        localStorage.removeItem('newProjectName');
        localStorage.removeItem('newProjectCreatedAt');
      }, 30000);
    }
  }, []);

  const calculateDeploymentProgress = useCallback((startTime: string) => {
    const DEPLOYMENT_TIME_MS = 45 * 60 * 1000;
    const start = new Date(startTime).getTime();
    const now = new Date().getTime();
    const elapsed = now - start;

    if (elapsed >= DEPLOYMENT_TIME_MS) {
      return 100;
    }

    return Math.floor((elapsed / DEPLOYMENT_TIME_MS) * 100);
  }, []);

  useEffect(() => {
    console.log('ProjectSelector mounted - closing all WebSocket connections');
    WebSocketManager.closeAllWebSockets();
    WebSocketManager.setCurrentProject(null);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      projects.forEach(p => {
        if (p.deploymentStatus === 'pending' || p.deploymentStatus === 'in_progress') {
          fallbackToHttpPolling(p.id);
        }
      });
    }, 30000);

    return () => clearInterval(interval);
  }, [projects]);

  const mockTeams = [
    { id: '1', name: 'Marketing Team', region: 'North America' },
    { id: '2', name: 'Sales Team', region: 'Europe' },
    { id: '3', name: 'Product Team', region: 'North America' }
  ];

  useEffect(() => {
    if (!appState.isLoading) {
      setLogo(ui?.logo || CompanyLogo);
    }
  }, [appState.isLoading, ui?.logo]);

  const getRandomColor = () => {
    return 'linear-gradient(135deg, #ffffff 0%, #add8e6 100%)';
  };

  const getRandomIcon = () => {
    const icons = ['📊', '📈', '🤖', '📋', '📑', '📚', '📝', '🔍', '💡', '🌐'];
    return icons[Math.floor(Math.random() * icons.length)];
  };

  const fetchProjectsCostData = async (): Promise<ProjectCost[]> => {
    try {
      const costData: CostData = await costService.getCostData(
        'month',
        currentUser?.role || UserRole.REGULAR_USER,
        currentUser?.region
      );
      console.log('Cost data fetched:', costData);
      if (costData.warning) {
        console.warn('Cost data warning detected:', costData.warning);
        setCostDataWarning(costData.warning);
      } else {
        setCostDataWarning(null);
      }
      return costData.projectCosts || [];
    } catch (error) {
      console.error('Error fetching cost data:', error);
      // Set a warning when cost data fails to load
      setCostDataWarning({
        message: 'Unable to fetch cost data. Project budgets cannot be verified.',
        severity: 'warning',
        lastUpdate: new Date().toISOString()
      });
      return [];
    }
  };

  const handleCollectCostNow = async () => {
    try {
      setIsCollectingCost(true);
      await costService.collectCostDataNow();
      await loadProjectsWithCostData();
    } catch (error) {
      console.error('Error collecting cost data:', error);
    } finally {
      setIsCollectingCost(false);
    }
  };

  const loadProjectsWithCostData = async () => {
    if (!currentUser || !currentUser.accessibleResources || !currentUser.accessibleResources.projects) {
      return;
    }

    const projectsData = currentUser.accessibleResources.projects;
    const validProjectsData = Array.isArray(projectsData) ? projectsData : [];

    const projectsWithStatus = validProjectsData.map((project: any) => {
      const processedProject = {
        id: project.id,
        name: project.name || 'Unnamed Project',
        description: project.description || '',
        region: project.region || '',
        owner: project.owner || '',
        created_at: project.created_at || new Date().toISOString(),
        updated_at: project.updated_at || new Date().toISOString(),
        search_index: project.search_index_name || '',
        storage_container: project.storage_container_uploads || '',
        role: 'owner' as 'owner' | 'contributor' | 'viewer',
        environment: project.environment || {},
        icon: project.icon || getRandomIcon(),
        color: project.color || getRandomColor(),
        cost_limit: project.cost_limit ?? null,
        storage_container_uploads: project.storage_container_uploads || '',
        storage_container_input: project.storage_container_input || '',
        storage_container_output: project.storage_container_output || '',
        search_index_name: project.search_index_name || '',
        search_datasource_name: project.search_datasource_name || '',
        search_indexer_name: project.search_indexer_name || ''
      };

      let deploymentStatus: 'pending' | 'in_progress' | 'completed' | 'failed' = 'pending';
      let deploymentMessage = '';
      let deploymentProgress: number | undefined = undefined;

      if (project.deployment_status) {
        const ds = project.deployment_status;
        if (ds.status && ['pending', 'in_progress', 'completed', 'failed'].includes(ds.status)) {
          deploymentStatus = ds.status as 'pending' | 'in_progress' | 'completed' | 'failed';
        }
        deploymentMessage = ds.message || '';
        if (ds.details && typeof ds.details.completion_percentage === 'number') {
          deploymentProgress = ds.details.completion_percentage;
        }
      }

      return { ...processedProject, deploymentStatus, deploymentMessage, deploymentProgress };
    });

    const enhancedProjects = enhanceProjects(projectsWithStatus);
    
    const projectCosts = await fetchProjectsCostData();
    
    const projectsWithCostData = enhancedProjects.map(project => {
      const costData = projectCosts.find(pc => pc.projectId === project.id);
      if (costData) {
        const budgetPercentageUsed = project.cost_limit && project.cost_limit > 0 
          ? (costData.cost / project.cost_limit) * 100 
          : 0;
        
        return {
          ...project,
          current_cost: costData.cost,
          // Only set budget_exceeded if we have valid cost data AND cost limit is defined
          budget_exceeded: project.cost_limit !== null && project.cost_limit !== undefined && project.cost_limit > 0 && costData.cost > project.cost_limit,
          budget_percentage_used: budgetPercentageUsed
        };
      }
      // If no cost data is available, don't mark as budget exceeded
      return {
        ...project,
        budget_exceeded: false
      };
    });
    
    setProjects(projectsWithCostData);
    setFilteredProjects(projectsWithCostData);
    cacheProjects(projectsWithCostData);
  };

  const fetchWithRetry = async (url: string, options = {}, maxRetries = 5, initialDelay = 1000) => {
    let retries = 0;
    let delay = initialDelay;
    const cachedETag = localStorage.getItem(`etag:${url}`);

    while (retries < maxRetries) {
      try {
        const mergedOptions = {
          ...options,
          headers: {
            'Accept-Encoding': 'gzip, deflate',
            ...(cachedETag ? { 'If-None-Match': cachedETag } : {}),
            ...(options as any).headers
          }
        };

        const response = await fetch(url, mergedOptions);

        if (response.status === 304) {
          console.log(`304 Not Modified for ${url}, using cached data`);
          const cachedData = localStorage.getItem(`response:${url}`);
          if (cachedData) {
            return {
              ok: true,
              status: 200,
              headers: new Headers({ 'Content-Type': 'application/json' }),
              json: async () => JSON.parse(cachedData)
            } as Response;
          }
        }

        if (response.status === 503) {
          console.log(`Service unavailable (attempt ${retries + 1}/${maxRetries}), retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          retries++;
          delay *= 2;
          continue;
        }

        if (response.ok || response.status === 404) {
          if (response.ok) {
            const etag = response.headers.get('ETag');
            if (etag) {
              localStorage.setItem(`etag:${url}`, etag);
              const clonedResponse = response.clone();
              try {
                const data = await clonedResponse.json();
                localStorage.setItem(`response:${url}`, JSON.stringify(data));
              } catch (e) {
                console.error('Error caching response data:', e);
              }
            }
          }
          return response;
        }

        console.log(`Error response (${response.status}) for ${url}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        retries++;
        delay *= 2;
        continue;
      } catch (error) {
        if (retries >= maxRetries - 1) throw error;
        console.log(`Network error (attempt ${retries + 1}/${maxRetries}), retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        retries++;
        delay *= 2;
      }
    }
    throw new Error(`Failed after ${maxRetries} retries`);
  };

  const enhanceProjects = useCallback((projects: Project[]) => {
    return projects.map(project => {
      const enhancedProject = {
        ...project,
        color: project.color || getRandomColor(),
        icon: project.icon || getRandomIcon(),
        deploymentStatus: project.deploymentStatus || 'pending' as 'pending' | 'in_progress' | 'completed' | 'failed'
      };

      if (enhancedProject.deploymentStatus === 'in_progress' || enhancedProject.deploymentStatus === 'pending') {
        if (typeof enhancedProject.deploymentProgress !== 'number') {
          const deploymentStartTime = enhancedProject.environment?.deploymentStartTime;
          enhancedProject.deploymentStartTime = deploymentStartTime || enhancedProject.created_at || new Date().toISOString();
          const progress = calculateDeploymentProgress(enhancedProject.deploymentStartTime);
          enhancedProject.deploymentProgress = progress;
        } else {
          enhancedProject.deploymentProgress = Math.max(0, Math.min(100, enhancedProject.deploymentProgress));
        }
      }

      return enhancedProject;
    });
  }, [calculateDeploymentProgress]);
  
  useEffect(() => {
    const loadData = async () => {
      if (isCheckingAuth || isUserLoading) {
        setIsLoading(true);
        return;
      }
      setIsLoading(true);
      setError(null);

    if (currentUser && currentUser.accessibleResources && currentUser.accessibleResources.projects) {
      console.log('Using projects from currentUser context');
      const projectsData = currentUser.accessibleResources.projects;
      const validProjectsData = Array.isArray(projectsData) ? projectsData : [];

      const projectsWithStatus = validProjectsData.map((project: any) => {
        const processedProject = {
          id: project.id,
          name: project.name || 'Unnamed Project',
          description: project.description || '',
          region: project.region || '',
          owner: project.owner || '',
          created_at: project.created_at || new Date().toISOString(),
          updated_at: project.updated_at || new Date().toISOString(),
          search_index: project.search_index_name || '',
          storage_container: project.storage_container_uploads || '',
          role: 'owner' as 'owner' | 'contributor' | 'viewer',
          environment: project.environment || {},
          icon: project.icon || getRandomIcon(),
          color: project.color || getRandomColor(),
          cost_limit: project.cost_limit ?? null,
          storage_container_uploads: project.storage_container_uploads || '',
          storage_container_input: project.storage_container_input || '',
          storage_container_output: project.storage_container_output || '',
          search_index_name: project.search_index_name || '',
          search_datasource_name: project.search_datasource_name || '',
          search_indexer_name: project.search_indexer_name || ''
        };

        let deploymentStatus: 'pending' | 'in_progress' | 'completed' | 'failed' = 'pending';
        let deploymentMessage = '';
        let deploymentProgress: number | undefined = undefined;

        if (project.deployment_status) {
          const ds = project.deployment_status;
          if (ds.status && ['pending', 'in_progress', 'completed', 'failed'].includes(ds.status)) {
            deploymentStatus = ds.status as 'pending' | 'in_progress' | 'completed' | 'failed';
          }
          if (typeof ds.message === 'string') {
            deploymentMessage = ds.message;
          }
          if (ds.details && typeof ds.details.completion_percentage === 'number') {
            deploymentProgress = ds.details.completion_percentage;
          }
        } else if (project.deploymentStatus && ['pending', 'in_progress', 'completed', 'failed'].includes(project.deploymentStatus)) {
          deploymentStatus = project.deploymentStatus;
        }

        return { ...processedProject, deploymentStatus, deploymentMessage, deploymentProgress };
      });

      const enhancedProjects = enhanceProjects(projectsWithStatus);
      const projectCosts = await fetchProjectsCostData();
      
      const projectsWithCostData = enhancedProjects.map(project => {
        const costData = projectCosts.find(pc => pc.projectId === project.id);
        if (costData) {
          const budgetPercentageUsed = project.cost_limit && project.cost_limit > 0 
            ? (costData.cost / project.cost_limit) * 100 
            : 0;
          
          return {
            ...project,
            current_cost: costData.cost,
            // Only set budget_exceeded if we have valid cost data AND cost limit is defined
            budget_exceeded: project.cost_limit !== null && project.cost_limit !== undefined && project.cost_limit > 0 && costData.cost > project.cost_limit,
            budget_percentage_used: budgetPercentageUsed
          };
        }
        // If no cost data is available, don't mark as budget exceeded
        return {
          ...project,
          budget_exceeded: false
        };
      });
      
      setProjects(projectsWithCostData);
      setFilteredProjects(projectsWithCostData);
      cacheProjects(projectsWithCostData);
      } else if (currentUser) {
      console.warn('No accessible projects found in currentUser context.');
      setProjects([]);
      setFilteredProjects([]);
    } else {
      setError('Failed to load user data, cannot fetch projects.');
      setProjects([]);
      setFilteredProjects([]);
    }
    setIsLoading(false);
    };

    loadData();
  }, [isCheckingAuth, currentUser, isUserLoading, enhanceProjects]);

  const wsConnections = useRef<{ [key: string]: WebSocket }>({});

  const checkDeploymentStatus = async (projectId: string, forceUpdate: boolean = false) => {
    try {
      if (forceUpdate) {
        await fallbackToHttpPolling(projectId, true);
        return;
      }

      if (!wsConnections.current[projectId]) {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        const wsUrl = `${protocol}//${host}/ws/deployment-status/${projectId}`;

        console.log(`Connecting to deployment status WebSocket for project ${projectId}: ${wsUrl}`);
        const ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          console.log(`Deployment status WebSocket connected for project ${projectId}`);
        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            if (message.type === 'deployment_status_update' && message.data) {
              console.log(`Received deployment status update for project ${projectId}:`, message.data);
              const status = message.data.status || 'pending';
              const msg = message.data.message;
              const progress = message.data.details?.completion_percentage;
              updateProjectStatus(projectId, status, msg, progress);
            }
          } catch (err) {
            console.error(`Error parsing WebSocket message for project ${projectId}:`, err);
          }
        };

        ws.onerror = (error) => {
          console.error(`Deployment status WebSocket error for project ${projectId}:`, error);
          fallbackToHttpPolling(projectId);
        };

        ws.onclose = () => {
          console.log(`Deployment status WebSocket closed for project ${projectId}`);
          delete wsConnections.current[projectId];
          fallbackToHttpPolling(projectId);
        };
        wsConnections.current[projectId] = ws;
      }
      fallbackToHttpPolling(projectId);
    } catch (error) {
      console.error(`Error setting up WebSocket for project ${projectId}:`, error);
      fallbackToHttpPolling(projectId);
    }
  };

  const updateProjectStatus = (
    projectId: string,
    status: string,
    message?: string,
    progress?: number
  ) => {
    let validStatus: 'pending' | 'in_progress' | 'completed' | 'failed' = 'pending';
    if (status === 'pending' || status === 'in_progress' || status === 'completed' || status === 'failed') {
      validStatus = status as 'pending' | 'in_progress' | 'completed' | 'failed';
    }

    setProjects(prevProjects =>
      prevProjects.map(project =>
        project.id === projectId
          ? {
              ...project,
              deploymentStatus: validStatus,
              deploymentMessage: message ?? project.deploymentMessage,
              deploymentProgress:
                typeof progress === 'number'
                  ? progress
                  : project.deploymentProgress,
            }
          : project
      )
    );

    setFilteredProjects(prevProjects =>
      prevProjects.map(project =>
        project.id === projectId
          ? {
              ...project,
              deploymentStatus: validStatus,
              deploymentMessage: message ?? project.deploymentMessage,
              deploymentProgress:
                typeof progress === 'number'
                  ? progress
                  : project.deploymentProgress,
            }
          : project
      )
    );

    cacheProjectStatus(projectId, validStatus);

    const cachedUserContext = localStorage.getItem('userContext');
    if (cachedUserContext) {
      try {
        const userContext = JSON.parse(cachedUserContext);
        if (userContext.accessibleResources && userContext.accessibleResources.projects) {
          const updatedProjects = userContext.accessibleResources.projects.map((project: any) =>
            project.id === projectId
              ? {
                  ...project,
                  deploymentStatus: validStatus,
                  deployment_status: {
                    ...(project.deployment_status || {}),
                    status: validStatus,
                    message: message ?? project.deployment_status?.message,
                    details: {
                      ...(project.deployment_status?.details || {}),
                      completion_percentage:
                        typeof progress === 'number'
                          ? progress
                          : project.deployment_status?.details?.completion_percentage,
                    },
                  },
                }
              : project
          );
          userContext.accessibleResources.projects = updatedProjects;
          localStorage.setItem('userContext', JSON.stringify(userContext));
        }
      } catch (e) {
        console.error('Error updating cached user context:', e);
      }
    }
  };

  const fallbackToHttpPolling = async (projectId: string, forceUpdate: boolean = false) => {
    try {
      const url = forceUpdate
        ? `/api/projects/${projectId}/deployment-status?force_update=true`
        : `/api/projects/${projectId}/deployment-status`;

      console.log(`Checking deployment status for project ${projectId}${forceUpdate ? ' (force update)' : ''}`);
      const response = await fetchWithRetry(url, { headers: { 'Accept-Encoding': 'gzip, deflate' } });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const status = data.status || 'pending';
      const message = data.message;
      const progress = data.details?.completion_percentage;
      console.log(`Received deployment status for project ${projectId}: ${status}`);
      updateProjectStatus(projectId, status, message, progress);
    } catch (error) {
      console.error(`Error checking deployment status for project ${projectId}:`, error);
    }
  };

  const renderDeploymentStatus = (project: Project) => {
    if (project.deploymentStatus === 'completed') {
      return null;
    }

    return (
      <div className={styles.deploymentStatus}>
        <TooltipHost
          content={project.deploymentMessage}
          directionalHint={DirectionalHint.topCenter}
        >
          {renderDeploymentStatusIcon(project.deploymentStatus)}
        </TooltipHost>
        <IconButton
          iconProps={{ iconName: 'Refresh' }}
          title="Check deployment status"
          onClick={(e) => {
            e.stopPropagation();
            checkDeploymentStatus(project.id);
          }}
          className={styles.refreshButton}
        />
        {(project.deploymentStatus === 'failed' || project.deploymentStatus === 'pending') && (
          <IconButton
            iconProps={{ iconName: 'Sync' }}
            title="Force update deployment status"
            onClick={(e) => {
              e.stopPropagation();
              fallbackToHttpPolling(project.id, true);
            }}
            className={styles.forceUpdateButton}
          />
        )}
      </div>
    );
  };

  useEffect(() => {
    let filtered = [...projects];
    if (searchText) {
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(searchText.toLowerCase()) ||
        (project.description && project.description.toLowerCase().includes(searchText.toLowerCase()))
      );
    }
    if (currentUser?.role === UserRole.SUPER_ADMIN && selectedRegion !== 'all') {
      filtered = filtered.filter(project => project.region === selectedRegion);
    }
    setFilteredProjects(filtered);
  }, [searchText, projects, selectedRegion, currentUser]);

  const handleProjectSelect = (project: Project) => {
    // Only block access if we have valid cost data AND budget is exceeded
    if (project.budget_exceeded && !costDataWarning) {
      // Show a warning dialog instead of navigating
      setBudgetExceededProject(project);
      setIsBudgetExceededDialogOpen(true);
      return;
    }
    
    navigate(`/project/${project.id}`);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLDivElement>, project: Project) => {
    event.stopPropagation();
    event.preventDefault();
    setMenuTarget(event.currentTarget);
    setSelectedProject(project);
  };

  const handleMenuDismiss = () => {
    setMenuTarget(null);
  };

  const handleDeleteClick = () => {
    setIsDeleteDialogOpen(true);
  };

  const handleEditProject = (project: Project) => {
    setSelectedProject(project);
    setEditedProjectName(project.name);
    setEditedProjectDescription(project.description || '');
    setEditedProjectIcon(project.icon || businessIcons[0]);
    setIsEditDialogOpen(true);
  };

  const handleDeleteProject = (project: Project) => {
    setSelectedProject(project);
    setIsDeleteDialogOpen(true);
  };

  const handleSaveEdit = async () => {
    if (!selectedProject) return;

    try {
      const projectData = {
        name: editedProjectName,
        icon: editedProjectIcon
      };
      console.log('Updating project with partial data:', projectData);
      console.log('Selected project:', selectedProject);
      console.log('Project ID being sent:', selectedProject.id);

      const { getAuthHeaders } = await import('../../services/authHeaderService');
      const authHeaders = await getAuthHeaders();
      const response = await fetch(`/api/rbac/projects/${selectedProject.id}`, {
        method: 'PUT',
        headers: { ...authHeaders, 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to update project:', errorData);
        setError(`Failed to update project: ${errorData.error || response.statusText || 'Unknown error'}`);
        return;
      }

      const responseData = await response.json();
      console.log('Project updated successfully:', responseData);

      const updatedProject = { ...selectedProject, name: editedProjectName, icon: editedProjectIcon };
      const updatedProjects = projects.map(p => p.id === selectedProject.id ? updatedProject : p);

      setProjects(updatedProjects);
      setFilteredProjects(updatedProjects);
      setIsEditDialogOpen(false);
      setSelectedProject(null);
      setError(null);
    } catch (err) {
      console.error('Error updating project:', err);
      setError(err instanceof Error ? err.message : 'Failed to update project');
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedProject) return;

    console.log(`Starting deletion of project: ${selectedProject.id} (${selectedProject.name})`);
    setIsDeleting(true);
    try {
      console.log(`Calling projectDelete API for project ID: ${selectedProject.id}`);
      const response = await projectDelete(selectedProject.id);

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`API error response:`, errorData);
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const responseData = await response.json();
      console.log(`Project deletion API response:`, responseData);

      setProjects(prevProjects => prevProjects.filter(p => p.id !== selectedProject.id));
      setFilteredProjects(prevProjects => prevProjects.filter(p => p.id !== selectedProject.id));
      setError(null);
      console.log(`Successfully removed project ${selectedProject.id} from UI state`);
    } catch (err) {
      console.error('Error deleting project:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete project');
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      setSelectedProject(null);
      console.log(`Project deletion process completed`);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteDialogOpen(false);
    setSelectedProject(null);
  };

  const handleSetCostLimit = (project: Project) => {
    setSelectedProject(project);
    setEditedCostLimit(project.cost_limit?.toString() || '');
    setIsCostLimitDialogOpen(true);
  };

  const handleAssignUsers = (project: Project) => {
    navigate('/admin/teams');
  };

  const handleSaveCostLimit = async () => {
    if (!selectedProject) return;

    try {
      const costLimitValue = editedCostLimit.trim() === '' ? null : parseFloat(editedCostLimit);
      if (costLimitValue !== null && (isNaN(costLimitValue) || costLimitValue < 0)) {
        setError('Please enter a valid cost limit (positive number)');
        return;
      }

      const projectData = { cost_limit: costLimitValue };
      console.log('Updating project cost limit:', projectData);

      const { getAuthHeaders } = await import('../../services/authHeaderService');
      const authHeaders = await getAuthHeaders();
      const response = await fetch(`/api/rbac/projects/${selectedProject.id}`, {
        method: 'PUT',
        headers: { ...authHeaders, 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Failed to update project cost limit:', errorData);
        setError(`Failed to update cost limit: ${errorData.error || response.statusText || 'Unknown error'}`);
        return;
      }

      const responseData = await response.json();
      console.log('Project cost limit updated successfully:', responseData);

      const updatedProject = { ...selectedProject, cost_limit: costLimitValue };
      const updatedProjects = projects.map(p => p.id === selectedProject.id ? updatedProject : p);

      setProjects(updatedProjects);
      setFilteredProjects(updatedProjects);
      setIsCostLimitDialogOpen(false);
      setSelectedProject(null);
      setError(null);
    } catch (err) {
      console.error('Error updating project cost limit:', err);
      setError(err instanceof Error ? err.message : 'Failed to update cost limit');
    }
  };

  const getMenuItems = (): IContextualMenuItem[] => {
    return [
      { key: 'openProject', text: 'Open project', disabled: true, onClick: () => { if (selectedProject) { handleProjectSelect(selectedProject); } handleMenuDismiss(); } },
      { key: 'editProject', text: 'Edit project', disabled: true, onClick: () => { handleMenuDismiss(); } },
      { key: 'assignUsers', text: 'Assign users', disabled: true, onClick: () => { handleMenuDismiss(); } },
      { key: 'setCostLimit', text: 'Set cost limit', disabled: true, onClick: () => { handleMenuDismiss(); } },
      { key: 'divider1', itemType: 1 },
      { key: 'deleteProject', text: 'Delete project', iconProps: { iconName: 'Delete' }, onClick: handleDeleteClick }
    ];
  };

  const handleCreateNewProject = () => navigate('/new-project');

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return `${monthNames[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
    } catch (e) {
        console.error("Error formatting date:", dateString, e);
        return '';
    }
  };

  const renderDeploymentStatusIcon = (status?: 'pending' | 'in_progress' | 'completed' | 'failed') => {
    switch (status) {
      case 'completed': return <Icon iconName="CheckMark" className={styles.deploymentStatusCompleted} title="Deployment completed" />;
      case 'in_progress': return <Icon iconName="Sync" className={styles.deploymentStatusInProgress} title="Deployment in progress" />;
      case 'failed': return <Icon iconName="Error" className={styles.deploymentStatusFailed} title="Deployment failed" />;
      case 'pending': return <Icon iconName="Clock" className={styles.deploymentStatusPending} title="Deployment pending" />;
      default: return <Spinner size={SpinnerSize.small} className={styles.deploymentStatusLoading} title="Loading status..." />;
    }
  };

  if (isCheckingAuth) {
    return (
      <div className={styles.container}>
        <Stack horizontalAlign="center" verticalAlign="center" style={{ height: '100%' }}>
          <Spinner size={SpinnerSize.large} label="Checking authentication..." />
        </Stack>
      </div>
    );
  }

  const createButtonStyles: IButtonStyles = {
    root: { borderRadius: '16px', height: '36px', padding: '0 16px', backgroundColor: '#f0f0f0', border: 'none' },
    rootHovered: { backgroundColor: '#e0e0e0' },
    label: { fontWeight: 'normal' }
  };

  return (
    <div className={styles.container}>
      <RoleBasedHeader logo={logo} title="AI Scope Project Management" />

      <div className={styles.welcomeContainer}>
        <h1 className={styles.welcomeTitle}>Welcome to AI scope project management</h1>
      </div>

      <div className={styles.contentContainer}>
        {showNewProjectBanner && (
          <MessageBar messageBarType={MessageBarType.info} isMultiline={false} onDismiss={() => setShowNewProjectBanner(false)} dismissButtonAriaLabel="Close">
            Project "{newProjectName}" is being created. This process may take up to 45 minutes.
          </MessageBar>
        )}

        {error && (<MessageBar messageBarType={MessageBarType.error} onDismiss={() => setError(null)}>{error}</MessageBar>)}

        {costDataWarning && (
          <CostDataWarningBanner
            warning={costDataWarning}
            onCollectNow={handleCollectCostNow}
            isCollecting={isCollectingCost}
            showCollectButton={currentUser?.role !== UserRole.REGULAR_USER}
          />
        )}

        <div className={styles.projectsSection}>
          <div className={styles.sectionHeader}>
            <h2 className={styles.sectionTitle}>
              {currentUser?.role === UserRole.SUPER_ADMIN ? 'All Projects' :
               currentUser?.role === UserRole.REGIONAL_ADMIN ?
                 `${currentUser.region ? (regionMap[currentUser.region] || currentUser.region) : ''} Projects` :
               'My Projects'}
            </h2>
            <div className={styles.sectionActions}>
              {appConfig === null ? (
                <Spinner size={SpinnerSize.small} />
              ) : (
                viewPermissions.canCreateProject && appConfig.show_create_project && (
                  <DefaultButton text="Create new" onClick={handleCreateNewProject} iconProps={{ iconName: 'Add' }} styles={createButtonStyles} />
                )
              )}
              {currentUser?.role === UserRole.SUPER_ADMIN && (
                <CommandButton
                  text={selectedRegion === 'all' ? 'All Regions' : `Region: ${regionMap[selectedRegion] || selectedRegion}`}
                  iconProps={{ iconName: 'Filter' }}
                  menuProps={{
                    items: [ { key: 'all', text: 'All Regions' }, ...Object.entries(regionMap).map(([id, name]) => ({ key: id, text: name })) ],
                    onItemClick: (_, item) => item && setSelectedRegion(item.key as string)
                  }}
                  styles={{ root: { marginLeft: 8 } }}
                />
              )}
            </div>
          </div>

          <div className={styles.toolbarContainer}>
            <div className={styles.viewToggleContainer}>
              <IconButton iconProps={{ iconName: 'GridViewMedium' }} onClick={() => setViewType('grid')} className={viewType === 'grid' ? styles.viewButtonActive : styles.viewButton} title="Grid view" />
              <IconButton iconProps={{ iconName: 'BulletedList' }} onClick={() => setViewType('list')} className={viewType === 'list' ? styles.viewButtonActive : styles.viewButton} title="List view" />
            </div>
          </div>

          {isLoading ? (
            <div className={styles.loadingContainer}><Spinner size={SpinnerSize.large} label="Loading projects..." /></div>
          ) : filteredProjects.length === 0 && !error ? (
             <div className={styles.noProjects}><Text>No projects found. Click "Create new" to get started.</Text></div>
           ) : (
             <div className={viewType === 'grid' ? styles.projectsGrid : styles.projectsList}>
               {filteredProjects.map(project => (
                 <div
                   key={project.id}
                   className={`${styles.projectCard} ${(project.deploymentStatus === 'pending' || project.deploymentStatus === 'in_progress') ? styles.projectCardDeploying : ''} ${project.budget_exceeded && !costDataWarning ? styles.projectCardBudgetExceeded : ''}`}
                   style={{ background: project.color }}
                   role="button"
                   tabIndex={(project.deploymentStatus === 'pending' || project.deploymentStatus === 'in_progress') ? -1 : 0}
                 >
                  <div
                    className={styles.projectCardContent}
                    onClick={() => {
                       if (project.deploymentStatus !== 'pending' && project.deploymentStatus !== 'in_progress') {
                         handleProjectSelect(project);
                       }
                     }}
                     onKeyPress={(e) => {
                       if (project.deploymentStatus !== 'pending' && project.deploymentStatus !== 'in_progress' &&
                           (e.key === 'Enter' || e.key === ' ')) {
                         handleProjectSelect(project);
                       }
                     }}
                   >
                    <div className={styles.cardHeader}>
                      <div className={styles.headerLeft}>
                        <div className={styles.projectIcon}>{project.icon}</div>
                        <h3 className={styles.projectName}>
                          {project.name}
                          {project.budget_exceeded && !costDataWarning && (
                            <TooltipHost
                              content="Budget exceeded - Access restricted"
                              directionalHint={DirectionalHint.topCenter}
                            >
                              <Icon 
                                iconName="Warning" 
                                styles={{ 
                                  root: { 
                                    color: '#d13438', 
                                    marginLeft: '8px',
                                    fontSize: '16px'
                                  } 
                                }} 
                              />
                            </TooltipHost>
                          )}
                        </h3>
                      </div>
                      <div className={styles.headerRight} onClick={(e) => e.stopPropagation()}>
                        {renderDeploymentStatus(project)}
                        <div className={styles.projectMenuButton}>
                          <ProjectActionsMenu
                            projectId={project.id}
                            projectName={project.name}
                            onEdit={() => handleEditProject(project)}
                            onDelete={() => handleDeleteProject(project)}
                            onSetCostLimit={() => handleSetCostLimit(project)}
                            onAssignUsers={() => handleAssignUsers(project)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className={styles.cardBody}>
                       <Text className={styles.projectDescription} block>
                          {project.description}
                       </Text>
                       {project.deploymentStatus && project.deploymentStatus !== 'completed' && (
                          <div className={styles.deploymentProgressBar}>
                            <ProgressIndicator
                              percentComplete={(project.deploymentProgress || 0) / 100}
                              barHeight={8}
                              className={project.deploymentStatus === 'failed' ? styles.failedProgressBar : styles.inProgressProgressBar}
                           />
                         </div>
                       )}
                     </div>

                     <div className={styles.cardFooterContainer}>
                       {project.cost_limit != null && project.cost_limit > 0 && (
                         <div className={styles.budgetSection}>
                           <TooltipHost
                             content={`Limit: $${(project.cost_limit || 0).toFixed(2)} | Used: ${(project.budget_percentage_used || 0).toFixed(1)}%`}
                             directionalHint={DirectionalHint.topCenter}
                           >
                             <Text
                               className={`${styles.budgetStatusText} ${project.budget_exceeded ? styles.budgetExceeded : styles.budgetOk}`}
                             >
                               <Icon iconName="Money" /> ${(project.current_cost || 0).toFixed(2)} / ${project.cost_limit.toFixed(2)}
                             </Text>
                           </TooltipHost>
                         </div>
                       )}
                       
                       <div className={styles.cardFooter}>
                         {formatDate(project.created_at) && (
                           <div className={styles.footerItem}>
                             <Icon iconName="Calendar" /> {formatDate(project.created_at)}
                           </div>
                         )}
                         {currentUser?.role === UserRole.SUPER_ADMIN && project.region && (
                           <div className={styles.footerItem}>
                             <Icon iconName="Globe" /> {(regionMap[project.region] || project.region) === 'Chile North Central' ? 'Chile NC' : regionMap[project.region] || project.region}
                           </div>
                         )}
                         {project.teams && project.teams.length > 0 && (
                           <TooltipHost
                             content={`Assigned Teams: ${project.teams.map(teamId => {
                               const team = mockTeams.find(t => t.id === teamId);
                               return team ? team.name : 'Unknown';
                             }).join(', ')}`}
                             directionalHint={DirectionalHint.topCenter}
                           >
                             <div className={styles.footerItem}>
                               <Icon iconName="Group" /> {project.teams.length} {project.teams.length > 1 ? 'Teams' : 'Team'}
                             </div>
                           </TooltipHost>
                         )}
                       </div>
                     </div>
                   </div>
                 </div>
               ))}
             </div>
           )
          }
        </div>
      </div>

      {menuTarget && (<ContextualMenu items={getMenuItems()} hidden={!menuTarget} target={menuTarget} onDismiss={handleMenuDismiss} directionalHint={DirectionalHint.bottomRightEdge} />)}

      <Dialog hidden={!isEditDialogOpen} onDismiss={() => setIsEditDialogOpen(false)} dialogContentProps={{ type: DialogType.normal, title: 'Edit Project', subText: 'Update project details' }} modalProps={{ isBlocking: true, styles: { main: { maxWidth: 450 } } }}>
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField label="Project Name" value={editedProjectName} onChange={(_, value) => setEditedProjectName(value || '')} required />
          <Dropdown 
            label="Project Icon" 
            selectedKey={editedProjectIcon} 
            options={businessIcons.map(icon => ({ key: icon, text: icon }))} 
            onChange={(_, option) => option && setEditedProjectIcon(option.key as string)} 
            styles={{ 
              dropdown: { width: '100%' },
              dropdownItem: { fontFamily: "'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', 'Apple Color Emoji', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif" },
              dropdownItemSelected: { fontFamily: "'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', 'Apple Color Emoji', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif" },
              title: { fontFamily: "'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', 'Apple Color Emoji', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif" }
            }} 
          />
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveEdit} text="Save" />
          <DefaultButton onClick={() => setIsEditDialogOpen(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>

      <Dialog hidden={!isDeleteDialogOpen} onDismiss={handleDeleteCancel} dialogContentProps={{ type: DialogType.normal, title: 'Delete Project', subText: selectedProject ? `Are you sure you want to delete "${selectedProject.name}"? This will permanently delete all associated Azure resources including storage containers, search indexes, and function apps.` : 'Are you sure you want to delete this project?' }} modalProps={{ isBlocking: true, styles: { main: { maxWidth: 450 } } }}>
        <DialogFooter>
          <PrimaryButton onClick={handleDeleteConfirm} text="Delete" disabled={isDeleting} />
          <DefaultButton onClick={handleDeleteCancel} text="Cancel" />
        </DialogFooter>
      </Dialog>

      <Dialog hidden={!isCostLimitDialogOpen} onDismiss={() => setIsCostLimitDialogOpen(false)} dialogContentProps={{ type: DialogType.normal, title: 'Set Cost Limit', subText: selectedProject ? `Set the cost limit for "${selectedProject.name}". Leave empty to remove the limit.` : 'Set the cost limit for this project.' }} modalProps={{ isBlocking: true, styles: { main: { maxWidth: 450 } } }}>
        <Stack tokens={{ childrenGap: 15 }}>
          <TextField label="Cost Limit (€)" value={editedCostLimit} onChange={(_, value) => setEditedCostLimit(value || '')} placeholder="Enter cost limit in EUR (e.g., 1000)" type="number" min="0" step="0.01" description="Set a monthly cost limit for this project. Leave empty to remove any existing limit." />
          {selectedProject?.cost_limit && (<Text variant="small" styles={{ root: { color: '#666' } }}>Current limit: €{selectedProject.cost_limit}</Text>)}
        </Stack>
        <DialogFooter>
          <PrimaryButton onClick={handleSaveCostLimit} text="Save" />
          <DefaultButton onClick={() => setIsCostLimitDialogOpen(false)} text="Cancel" />
        </DialogFooter>
      </Dialog>

      <Dialog 
        hidden={!isBudgetExceededDialogOpen} 
        onDismiss={() => setIsBudgetExceededDialogOpen(false)} 
        dialogContentProps={{ 
          type: DialogType.normal, 
          title: 'Budget Exceeded', 
          subText: budgetExceededProject ? 
            `The project "${budgetExceededProject.name}" has exceeded its budget limit. Current cost: $${(budgetExceededProject.current_cost || 0).toFixed(2)}, Budget limit: $${(budgetExceededProject.cost_limit || 0).toFixed(2)}. Please contact your administrator to increase the budget limit or resolve the issue.` : 
            'This project has exceeded its budget limit.' 
        }} 
        modalProps={{ 
          isBlocking: true, 
          styles: { main: { maxWidth: 500 } } 
        }}
      >
        <DialogFooter>
          <DefaultButton onClick={() => setIsBudgetExceededDialogOpen(false)} text="OK" />
        </DialogFooter>
      </Dialog>
    </div>
  );
};

export default ProjectSelector;
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMsal } from '@azure/msal-react';
import { graphScopes, clientId, tenantId } from '../auth/msal-config';

/**
 * Auto Login Component
 *
 * This component automatically redirects to Entra ID login
 * if the user is not already authenticated.
 */
const AutoLogin: React.FC = () => {
  const navigate = useNavigate();
  const { instance, accounts, inProgress } = useMsal();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      try {
        console.log('AutoLogin - Checking authentication status');
        console.log('MSAL accounts:', accounts);
        console.log('MSAL inProgress:', inProgress);

        // First, ensure MS<PERSON> is initialized
        try {
          console.log('Ensuring MSAL is initialized...');
          // Use the instance from useMsal() hook instead of msalInstance
          const activeAccount = instance.getActiveAccount();
          if (activeAccount) {
            console.log('MSAL instance already initialized in AutoLogin with active account:', activeAccount.username);
          } else {
            console.log('MSAL instance initialized but no active account in AutoLogin');
          }
        } catch (initError) {
          console.error('Error checking MSAL in AutoLogin:', initError);
          // Continue anyway, as we'll use the direct authorization URL approach
        }

        // If we're in the middle of an operation, wait
        if (inProgress !== 'none') {
          console.log('MSAL operation in progress, waiting...');
          return;
        }

        // If already authenticated, redirect to projects
        if (accounts.length > 0) {
          console.log('User is already authenticated, redirecting to projects');
          navigate('/projects');
          return;
        }

        // Check localStorage for development mode authentication
        const storedAuthState = localStorage.getItem('isAuthenticated');
        if (storedAuthState === 'true') {
          console.log('User is authenticated in development mode, redirecting to projects');
          navigate('/projects');
          return;
        }

        // Not authenticated, redirect to Entra ID login
        console.log('User is not authenticated, redirecting to Entra ID login');

        // Use dynamic redirect URI based on environment
        const redirectUri = window.location.hostname === 'localhost'
          ? import.meta.env.VITE_AZURE_REDIRECT_URI
          : (import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI || `${window.location.origin}/.auth/login/aad/callback`);

        if (!redirectUri) {
          console.error('AutoLogin: No redirect URI configured for current environment');
          setError('Authentication configuration error. Please check environment variables.');
          setIsLoading(false);
          return;
        }

        console.log('AutoLogin - Using redirect URI:', redirectUri);

        // Use environment variables for Azure configuration
        console.log('AutoLogin - Using Client ID:', clientId);
        console.log('AutoLogin - Using Tenant ID:', tenantId);

        // Use only specific scopes (not .default) as per Azure Portal configuration
        const allScopes = [...graphScopes];
        // Ensure we have the required scopes
        if (!allScopes.includes('User.Read')) allScopes.push('User.Read');
        if (!allScopes.includes('openid')) allScopes.push('openid');

        // Remove any .default scopes as they cannot be combined with resource-specific scopes
        const filteredScopes = allScopes.filter(scope => !scope.endsWith('.default'));

        console.log('Using scopes for authentication:', filteredScopes);
        const scopes = encodeURIComponent(filteredScopes.join(' '));
        const encodedRedirectUri = encodeURIComponent(redirectUri);

        // Create a code verifier and challenge for PKCE
        const codeVerifier = generateCodeVerifier();
        const codeChallenge = await generateCodeChallenge(codeVerifier);

        // Store the code verifier in session storage for later use
        sessionStorage.setItem('codeVerifier', codeVerifier);

        // Try to use MSAL's loginRedirect first
        try {
          console.log('Attempting to use MSAL loginRedirect with scopes:', filteredScopes);
          await instance.loginRedirect({
            scopes: filteredScopes,
            redirectUri: redirectUri,
            prompt: "select_account" // Force account selection for better user experience
          });
          return; // If successful, we'll be redirected
        } catch (msalError) {
          console.error('Error using MSAL loginRedirect:', msalError);
          console.log('Falling back to direct authorization URL approach');

          // Construct the authorization URL
          const authUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize?` +
            `client_id=${clientId}` +
            `&scope=${scopes}` +
            `&redirect_uri=${encodedRedirectUri}` +
            `&response_type=code` +
            `&response_mode=fragment` +
            `&code_challenge=${codeChallenge}` +
            `&code_challenge_method=S256`;

          console.log('Redirecting to Entra ID login URL:', authUrl);

          // Redirect to the Entra ID login page
          window.location.href = authUrl;
        }

        // The page will redirect to Microsoft login, so we won't reach here
      } catch (err) {
        console.error('Error redirecting to login:', err);
        setError(`Error redirecting to login: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setIsLoading(false);
      }
    };

    // Generate a random code verifier for PKCE
    function generateCodeVerifier() {
      const array = new Uint8Array(32);
      window.crypto.getRandomValues(array);
      return base64UrlEncode(array);
    }

    // Generate a code challenge from the code verifier
    async function generateCodeChallenge(codeVerifier: string) {
      const encoder = new TextEncoder();
      const data = encoder.encode(codeVerifier);
      const digest = await window.crypto.subtle.digest('SHA-256', data);
      return base64UrlEncode(new Uint8Array(digest));
    }

    // Base64Url encode an array
    function base64UrlEncode(array: Uint8Array) {
      const base64 = btoa(String.fromCharCode.apply(null, [...array]));
      return base64
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '');
    }

    checkAuthAndRedirect();
  }, [navigate, accounts, inProgress, instance]);

  // Show a loading indicator or error message
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '100vh',
      padding: '20px',
      textAlign: 'center'
    }}>
      {error ? (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          <h2>Authentication Error</h2>
          <p>{error}</p>
          <button
            onClick={() => navigate('/login')}
            style={{
              padding: '10px 20px',
              backgroundColor: '#0078d4',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginTop: '20px'
            }}
          >
            Go to Login Page
          </button>
        </div>
      ) : (
        <>
          <h2>Redirecting to Login</h2>
          <p>Please wait while we redirect you to the login page...</p>
          <div style={{
            width: '50px',
            height: '50px',
            border: '5px solid #f3f3f3',
            borderTop: '5px solid #3498db',
            borderRadius: '50%',
            animation: 'spin 2s linear infinite',
            marginTop: '20px'
          }} />
          <style>
            {`
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `}
          </style>
        </>
      )}
    </div>
  );
};

export default AutoLogin;

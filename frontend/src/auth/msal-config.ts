import {
  PublicClientApplication,
  Configuration,
  EventType,
  EventMessage,
  AuthenticationResult,
  InteractionRequiredAuthError,
  PopupRequest,
  RedirectRequest,
  AccountInfo
} from "@azure/msal-browser";

// Get client ID and tenant ID from environment variables
// In production, these should be set by the deployment process
export const clientId = import.meta.env.VITE_AZURE_CLIENT_ID || "bb1ebfc1-47d8-4273-9206-3acc107c1e35";
export const tenantId = import.meta.env.VITE_AZURE_TENANT_ID || "ee78877a-c63a-405d-85d6-8914358aa533";

// Validate required environment variables
if (!clientId) {
  console.error('MSAL Config Error: VITE_AZURE_CLIENT_ID is required');
  throw new Error('Missing required environment variable: VITE_AZURE_CLIENT_ID');
}

if (!tenantId) {
  console.error('MSAL Config Error: VITE_AZURE_TENANT_ID is required');
  throw new Error('Missing required environment variable: VITE_AZURE_TENANT_ID');
}

console.log('MSAL Config - Using Client ID:', clientId);
console.log('MSAL Config - Using Tenant ID:', tenantId);

// Dynamic redirect URI configuration
const getRedirectUri = () => {
  const isProduction = window.location.hostname !== 'localhost';

  if (isProduction) {
    // Use the production redirect URI from environment variables
    const prodRedirectUri = import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI ||
      `https://${window.location.hostname}/.auth/login/aad/callback`;
    console.log('Using production redirect URI:', prodRedirectUri);
    return prodRedirectUri;
  } else {
    // Use development redirect URI from environment variables (no hardcoded fallback)
    const devRedirectUri = import.meta.env.VITE_AZURE_REDIRECT_URI;
    if (!devRedirectUri) {
      console.error('VITE_AZURE_REDIRECT_URI environment variable is required for development');
      throw new Error('Missing required environment variable: VITE_AZURE_REDIRECT_URI');
    }
    console.log('Using development redirect URI:', devRedirectUri);
    return devRedirectUri;
  }
};

const redirectUri = getRedirectUri();

// List of all valid redirect URIs (should match those configured in Azure Portal)
// This array is used for validation and debugging purposes
const currentHostname = window.location.hostname;
const validRedirectUris = [
  // Test URIs for development
  `http://localhost:8000/test_browser_auth.html`,
  `http://localhost:8000/test_redirect_auth.html`,
  // Production URIs - include both hardcoded and environment variable values
  `https://ai-scope-app3.azurewebsites.net/.auth/login/aad/callback`,
  // Dynamic hostname URI
  `https://${currentHostname}/.auth/login/aad/callback`,
  // Add environment variable values if they exist and are not already included
  ...(import.meta.env.VITE_AZURE_REDIRECT_URI ? [import.meta.env.VITE_AZURE_REDIRECT_URI] : []),
  ...(import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI ? [import.meta.env.VITE_AZURE_PRODUCTION_REDIRECT_URI] : [])
];

// Log the valid redirect URIs for debugging
console.log('Valid redirect URIs:', validRedirectUris);

console.log('MSAL Config - Using redirect URI:', redirectUri);
console.log('MSAL Config - Window location:', window.location.href);

const msalConfig: Configuration = {
  auth: {
    clientId: clientId,
    authority: `https://login.microsoftonline.com/${tenantId}`,
    redirectUri: redirectUri,
    postLogoutRedirectUri: redirectUri,
    navigateToLoginRequestUrl: true,
  },
  cache: {
    cacheLocation: "localStorage",
    storeAuthStateInCookie: true
  },
  system: {
    allowRedirectInIframe: true,
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (!containsPii) {
          console.log(`MSAL - ${level}: ${message}`);
        }
      },
      logLevel: 3, // Verbose logging for debugging
      piiLoggingEnabled: false
    }
  }
};

export const msalInstance = new PublicClientApplication(msalConfig);

// Make MSAL instance available globally for debugging
(window as any).msalInstance = msalInstance;

// Track initialization state
let isMsalInitialized = false;
const initPromise = msalInstance.initialize()
  .then(() => {
    console.log('MSAL initialized');
    isMsalInitialized = true;
    // Check for active account
    const activeAccount = msalInstance.getActiveAccount();
    if (activeAccount) {
      console.log('Active account found during init:', activeAccount);
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('msalAccount', JSON.stringify(activeAccount));
    }
    return true;
  })
  .catch(error => {
    console.error('MSAL initialization failed:', error);
    isMsalInitialized = false;
    return false;
  });

// Export initialization promise
export const msalReady = initPromise;

// Register redirect callbacks
msalInstance.addEventCallback((event: EventMessage) => {
  console.log('MSAL Event:', event.eventType);

  if (event.eventType === EventType.LOGIN_SUCCESS) {
    console.log('MSAL login success event:', event);

    // Set the active account
    if (event.payload && (event.payload as AuthenticationResult).account) {
      const authResult = event.payload as AuthenticationResult;
      msalInstance.setActiveAccount(authResult.account);
      console.log('Active account set:', authResult.account);

      // Clear any existing user data to ensure we get fresh data
      localStorage.removeItem('currentUser');

      // Clear user context cache
      // We'll import it dynamically to avoid circular dependencies
      import('../services/userContextService').then(module => {
        if (module.default && module.default.clearCache) {
          console.log('MSAL Config: Clearing user context cache after successful login');
          module.default.clearCache();
        }
      }).catch(err => {
        console.error('MSAL Config: Error importing userContextService:', err);
      });

      // Store authentication state
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('msalAccount', JSON.stringify(authResult.account));

      // Redirect to projects page after successful login
      if (window.location.pathname === '/' || window.location.pathname === '/login') {
        console.log('Redirecting to projects page after successful login');
        window.location.href = `${window.location.origin}/#/projects`;
      }
    }
  } else if (event.eventType === EventType.LOGIN_FAILURE) {
    console.error('MSAL login failure event:', event);
    // Clear any existing auth state on failure
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('msalAccount');
    localStorage.removeItem('currentUser');
  } else if (event.eventType === EventType.HANDLE_REDIRECT_START) {
    console.log('MSAL handle redirect start event:', event);
  } else if (event.eventType === EventType.HANDLE_REDIRECT_END) {
    console.log('MSAL handle redirect end event:', event);
  } else if (event.eventType === EventType.ACQUIRE_TOKEN_SUCCESS) {
    console.log('MSAL acquire token success event');

    // When a token is successfully acquired, we should refresh the user context
    // to ensure we have the most up-to-date user information
    import('../services/userContextService').then(module => {
      if (module.default && module.default.fetchUserContext) {
        console.log('MSAL Config: Refreshing user context after token acquisition');
        module.default.fetchUserContext(true).catch(err => {
          console.error('MSAL Config: Error refreshing user context:', err);
        });
      }
    }).catch(err => {
      console.error('MSAL Config: Error importing userContextService:', err);
    });
  }
});

// Define the scopes needed for Microsoft Graph API
// Only use specific scopes (not .default) as per Azure Portal configuration
export const graphScopes = [
  "User.Read", // For accessing user profile information
  "profile",   // Basic profile information
  "email",     // User's email address
  "openid"     // Required for OpenID Connect flows
];

// Helper function to get access token with specified scopes
export const getAccessToken = async (customScopes?: string[]): Promise<string | null> => {
  try {
    const accounts = msalInstance.getAllAccounts();
    if (accounts.length === 0) {
      console.warn("No accounts found in MSAL when getting access token");
      return null;
    }

    // Ensure we always include the required scopes
    let scopes = customScopes || [...graphScopes];
    if (!scopes.includes('User.Read')) scopes.push('User.Read');
    if (!scopes.includes('openid')) scopes.push('openid');
    if (!scopes.includes('offline_access')) scopes.push('offline_access');

    console.log("Getting access token with scopes:", scopes);
    console.log("Current account:", accounts[0]);

    const request: PopupRequest = {
      scopes: scopes,
      account: accounts[0],
      prompt: 'select_account'
    };

    try {
      const response = await msalInstance.acquireTokenSilent(request);
      console.log("Successfully acquired token with scopes:", scopes);

      // Store the token
      localStorage.setItem('accessToken', response.accessToken);

      return response.accessToken;
    } catch (error) {
      if (error instanceof InteractionRequiredAuthError) {
        console.log("Interaction required, redirecting to login");
        const redirectRequest: RedirectRequest = {
          ...request,
          redirectUri: redirectUri
        };
        await msalInstance.acquireTokenRedirect(redirectRequest);
        return null;
      }
      throw error;
    }
  } catch (error) {
    console.error("Error getting access token:", error);
    return null;
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = (): boolean => {
  const hasAccount = msalInstance.getAllAccounts().length > 0;
  const hasToken = !!localStorage.getItem('accessToken');
  const isAuth = localStorage.getItem('isAuthenticated') === 'true';

  console.log('Auth check:', { hasAccount, hasToken, isAuth });

  return hasAccount && hasToken && isAuth;
};

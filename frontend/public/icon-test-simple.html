<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Icon Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .icon-test {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .icon {
            font-size: 20px;
            margin-right: 10px;
            width: 30px;
            text-align: center;
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Simple Icon Test</h1>
    
    <div class="test-section">
        <h2>Font Loading Status</h2>
        <div id="font-status">Checking...</div>
    </div>
    
    <div class="test-section">
        <h2>Raw Icon Elements (without Fluent UI)</h2>
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Add icon" aria-hidden="true"></i>
            <span>Add Icon (should show +)</span>
        </div>
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--ChevronLeft icon" aria-hidden="true"></i>
            <span>ChevronLeft Icon (should show &lt;)</span>
        </div>
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Filter icon" aria-hidden="true"></i>
            <span>Filter Icon (should show filter symbol)</span>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console" class="console-output"></div>
    </div>

    <script>
        // Capture console output
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type}: ${args.join(' ')}\n`;
            consoleDiv.textContent += message;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('LOG', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('ERROR', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('WARN', ...args);
        };
        
        // Check font loading
        function checkFontLoading() {
            console.log('Checking font loading status...');
            
            if ('fonts' in document) {
                document.fonts.ready.then(() => {
                    const allFonts = [...document.fonts];
                    console.log(`Total fonts loaded: ${allFonts.length}`);
                    
                    const fabricFonts = allFonts.filter(f => 
                        f.family.includes('FabricMDL2Icons') || 
                        f.family.includes('Fabric')
                    );
                    
                    console.log(`Fabric icon fonts found: ${fabricFonts.length}`);
                    fabricFonts.forEach(font => {
                        console.log(`  - ${font.family}: ${font.status}`);
                    });
                    
                    // Update status display
                    const statusDiv = document.getElementById('font-status');
                    if (fabricFonts.length > 0) {
                        statusDiv.innerHTML = `✅ Found ${fabricFonts.length} Fabric icon fonts`;
                        statusDiv.style.color = 'green';
                    } else {
                        statusDiv.innerHTML = '❌ No Fabric icon fonts found';
                        statusDiv.style.color = 'red';
                    }
                    
                    // Test icon rendering
                    testIconRendering();
                }).catch(err => {
                    console.error('Error checking fonts:', err);
                });
            } else {
                console.warn('document.fonts API not available');
            }
        }
        
        function testIconRendering() {
            console.log('Testing icon rendering...');
            
            const icons = document.querySelectorAll('.ms-Icon');
            icons.forEach((icon, index) => {
                const rect = icon.getBoundingClientRect();
                const isRendering = rect.width > 0 && rect.height > 0;
                const iconName = icon.className.match(/ms-Icon--(\w+)/)?.[1] || 'unknown';
                
                console.log(`Icon ${iconName}: ${isRendering ? 'rendering' : 'not rendering'} (${rect.width}x${rect.height})`);
                
                if (!isRendering) {
                    icon.style.border = '2px solid red';
                } else {
                    icon.style.border = '2px solid green';
                }
            });
        }
        
        // Start checking after page load
        window.addEventListener('load', () => {
            console.log('Page loaded, starting font check...');
            setTimeout(checkFontLoading, 1000);
        });
        
        console.log('Script loaded');
    </script>
</body>
</html>

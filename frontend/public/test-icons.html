<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fluent UI Icon Test Page</title>
    <style>
        /* Font face declarations matching Fluent UI's structure */
        @font-face { 
            font-family: "FabricMDL2Icons"; 
            src: url("/fonts/fabric-icons-a13498cf.woff") format("woff"); 
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-0"; 
            src: url("/fonts/fabric-icons-0-467ee27f.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-1"; 
            src: url("/fonts/fabric-icons-1-4d521695.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-2"; 
            src: url("/fonts/fabric-icons-2-63c99abf.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-3"; 
            src: url("/fonts/fabric-icons-3-089e217a.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-4"; 
            src: url("/fonts/fabric-icons-4-a656cc0a.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-5"; 
            src: url("/fonts/fabric-icons-5-f95ba260.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-6"; 
            src: url("/fonts/fabric-icons-6-ef6fd590.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-7"; 
            src: url("/fonts/fabric-icons-7-2b97bb99.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-8"; 
            src: url("/fonts/fabric-icons-8-6fdf1528.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        @font-face { 
            font-family: "FabricMDL2Icons-9"; 
            src: url("/fonts/fabric-icons-9-c6162b42.woff") format("woff");
            font-weight: normal;
            font-style: normal;
        }
        
        body {
            font-family: "Segoe UI", Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        
        h1, h2 {
            color: #0078d4;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .icon-test {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            background: #f8f8f8;
        }
        
        .icon {
            font-size: 24px;
            width: 40px;
            text-align: center;
            color: #323130;
        }
        
        .icon-info {
            flex: 1;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .icon-name {
            font-weight: 600;
            color: #323130;
        }
        
        .icon-unicode {
            color: #605e5c;
            font-family: monospace;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .status.success {
            background: #dff6dd;
            border: 1px solid #57a300;
            color: #107c10;
        }
        
        .status.error {
            background: #fde7e9;
            border: 1px solid #c50e2e;
            color: #a80000;
        }
        
        .status.warning {
            background: #fff4ce;
            border: 1px solid #fcb900;
            color: #323130;
        }
        
        /* Fluent UI icon styles */
        .ms-Icon {
            display: inline-block;
            font-family: "FabricMDL2Icons";
            font-style: normal;
            font-weight: normal;
            speak: none;
        }
        
        .ms-Icon--ChevronLeft:before { content: "\E76B"; }
        .ms-Icon--ChevronRight:before { content: "\E76C"; }
        .ms-Icon--Add:before { content: "\E710"; }
        .ms-Icon--Delete:before { content: "\E74D"; }
        .ms-Icon--Edit:before { content: "\E70F"; }
        .ms-Icon--Save:before { content: "\E74E"; }
        .ms-Icon--CheckMark:before { content: "\E73E"; }
        .ms-Icon--Cancel:before { content: "\E711"; }
        .ms-Icon--Search:before { content: "\E721"; }
        .ms-Icon--Settings:before { content: "\E713"; }
        .ms-Icon--MoreVertical:before { content: "\E712"; }
        .ms-Icon--People:before { content: "\E716"; }
        .ms-Icon--Calendar:before { content: "\E787"; }
        .ms-Icon--Clock:before { content: "\E917"; }
        .ms-Icon--Home:before { content: "\E80F"; }
        .ms-Icon--GridViewMedium:before { content: "\F0E2"; font-family: "FabricMDL2Icons-9"; }
    </style>
</head>
<body>
    <h1>Fluent UI Icon Test Page</h1>
    
    <div id="font-status" class="status">Checking font loading status...</div>
    
    <h2>Common Icons</h2>
    <div class="icon-grid">
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--ChevronLeft icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">ChevronLeft</div>
                <div class="icon-unicode">U+E76B</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--ChevronRight icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">ChevronRight</div>
                <div class="icon-unicode">U+E76C</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Add icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Add</div>
                <div class="icon-unicode">U+E710</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Delete icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Delete</div>
                <div class="icon-unicode">U+E74D</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Edit icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Edit</div>
                <div class="icon-unicode">U+E70F</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Save icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Save</div>
                <div class="icon-unicode">U+E74E</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--CheckMark icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">CheckMark</div>
                <div class="icon-unicode">U+E73E</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Cancel icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Cancel</div>
                <div class="icon-unicode">U+E711</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Search icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Search</div>
                <div class="icon-unicode">U+E721</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Settings icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Settings</div>
                <div class="icon-unicode">U+E713</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--MoreVertical icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">MoreVertical</div>
                <div class="icon-unicode">U+E712</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--People icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">People</div>
                <div class="icon-unicode">U+E716</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Calendar icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Calendar</div>
                <div class="icon-unicode">U+E787</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Clock icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Clock</div>
                <div class="icon-unicode">U+E917</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--Home icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">Home</div>
                <div class="icon-unicode">U+E80F</div>
            </div>
        </div>
        
        <div class="icon-test">
            <i class="ms-Icon ms-Icon--GridViewMedium icon" aria-hidden="true"></i>
            <div class="icon-info">
                <div class="icon-name">GridViewMedium</div>
                <div class="icon-unicode">U+F0E2 (Font-9)</div>
            </div>
        </div>
    </div>
    
    <h2>Direct Unicode Test</h2>
    <div class="icon-grid">
        <div class="icon-test">
            <span class="icon" style="font-family: FabricMDL2Icons;">&#xE76B;</span>
            <div class="icon-info">
                <div class="icon-name">Direct Unicode</div>
                <div class="icon-unicode">ChevronLeft (E76B)</div>
            </div>
        </div>
        
        <div class="icon-test">
            <span class="icon" style="font-family: FabricMDL2Icons;">&#xE710;</span>
            <div class="icon-info">
                <div class="icon-name">Direct Unicode</div>
                <div class="icon-unicode">Add (E710)</div>
            </div>
        </div>
    </div>
    
    <h2>Network Requests</h2>
    <p>Open the browser's Developer Tools → Network tab to see if font files are loading correctly.</p>
    <p>You should see requests for fabric-icons-*.woff files with status 200.</p>
    
    <script>
        // Check font loading status
        function checkFontStatus() {
            const statusEl = document.getElementById('font-status');
            
            if (!('fonts' in document)) {
                statusEl.className = 'status warning';
                statusEl.textContent = 'Font loading API not supported in this browser';
                return;
            }
            
            document.fonts.ready.then(() => {
                const fabricFonts = [...document.fonts].filter(f => f.family.includes('FabricMDL2Icons'));
                const loadedCount = fabricFonts.filter(f => f.status === 'loaded').length;
                
                if (loadedCount > 0) {
                    statusEl.className = 'status success';
                    statusEl.innerHTML = `
                        <strong>Success!</strong> ${loadedCount} FabricMDL2Icons font families loaded successfully.<br>
                        <details>
                            <summary>Font Details</summary>
                            <ul>
                                ${fabricFonts.map(font => `<li>${font.family}: ${font.status}</li>`).join('')}
                            </ul>
                        </details>
                    `;
                } else {
                    statusEl.className = 'status error';
                    statusEl.innerHTML = `
                        <strong>Error:</strong> No FabricMDL2Icons fonts loaded.<br>
                        Check the Network tab for failed font requests.
                    `;
                }
                
                // Test icon rendering
                const testIcon = document.createElement('i');
                testIcon.className = 'ms-Icon ms-Icon--Add';
                testIcon.style.position = 'absolute';
                testIcon.style.visibility = 'hidden';
                document.body.appendChild(testIcon);
                
                const rect = testIcon.getBoundingClientRect();
                const isRendering = rect.width > 0 && rect.height > 0;
                document.body.removeChild(testIcon);
                
                if (!isRendering && loadedCount > 0) {
                    statusEl.innerHTML += '<br><strong>Warning:</strong> Fonts loaded but icons may not be rendering correctly.';
                }
            }).catch(err => {
                statusEl.className = 'status error';
                statusEl.textContent = 'Error checking font status: ' + err.message;
            });
        }
        
        // Check on load
        checkFontStatus();
        
        // Also check after a delay
        setTimeout(checkFontStatus, 2000);
    </script>
</body>
</html>
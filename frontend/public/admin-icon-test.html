<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel Icon Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .icon-test {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .icon {
            font-size: 20px;
            margin-right: 10px;
            width: 30px;
            text-align: center;
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        .icon-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .icon-name {
            margin-left: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Admin Panel Icon Test</h1>
    
    <div class="test-section">
        <h2>Navigation Icons</h2>
        <div class="icon-grid">
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--People icon" aria-hidden="true"></i>
                <span class="icon-name">People (Users)</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--Group icon" aria-hidden="true"></i>
                <span class="icon-name">Group (Teams)</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--ProjectCollection icon" aria-hidden="true"></i>
                <span class="icon-name">ProjectCollection (Projects)</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--Contact icon" aria-hidden="true"></i>
                <span class="icon-name">Contact (Regional Admins)</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--Money icon" aria-hidden="true"></i>
                <span class="icon-name">Money (Global Costs)</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--MapPin icon" aria-hidden="true"></i>
                <span class="icon-name">MapPin (Regional Costs)</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--BarChart4 icon" aria-hidden="true"></i>
                <span class="icon-name">BarChart4 (Cost Analytics)</span>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Action Icons</h2>
        <div class="icon-grid">
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--Edit icon" aria-hidden="true"></i>
                <span class="icon-name">Edit</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--Delete icon" aria-hidden="true"></i>
                <span class="icon-name">Delete</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--Refresh icon" aria-hidden="true"></i>
                <span class="icon-name">Refresh</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--AddFriend icon" aria-hidden="true"></i>
                <span class="icon-name">AddFriend</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--AddGroup icon" aria-hidden="true"></i>
                <span class="icon-name">AddGroup</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--Tag icon" aria-hidden="true"></i>
                <span class="icon-name">Tag</span>
            </div>
            <div class="icon-item">
                <i class="ms-Icon ms-Icon--ChevronLeft icon" aria-hidden="true"></i>
                <span class="icon-name">ChevronLeft</span>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console" class="console-output"></div>
    </div>

    <script>
        // Capture console output
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type}: ${args.join(' ')}\n`;
            consoleDiv.textContent += message;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('LOG', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('ERROR', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('WARN', ...args);
        };
        
        // Test icon rendering
        function testIconRendering() {
            console.log('Testing admin panel icon rendering...');
            
            const icons = document.querySelectorAll('.ms-Icon');
            let workingCount = 0;
            let failingCount = 0;
            
            icons.forEach((icon, index) => {
                const rect = icon.getBoundingClientRect();
                const isRendering = rect.width > 0 && rect.height > 0;
                const iconName = icon.className.match(/ms-Icon--(\w+)/)?.[1] || 'unknown';
                
                if (isRendering) {
                    workingCount++;
                    icon.style.border = '2px solid green';
                    console.log(`✓ Icon ${iconName}: rendering correctly (${rect.width}x${rect.height})`);
                } else {
                    failingCount++;
                    icon.style.border = '2px solid red';
                    console.log(`✗ Icon ${iconName}: not rendering (${rect.width}x${rect.height})`);
                }
            });
            
            console.log(`\nSummary: ${workingCount} working, ${failingCount} failing`);
            
            if (failingCount === 0) {
                console.log('🎉 All admin panel icons are rendering correctly!');
            } else {
                console.log('⚠️ Some icons are not rendering. Check the red-bordered icons above.');
            }
        }
        
        // Check font loading
        function checkFontLoading() {
            console.log('Checking font loading status...');
            
            if ('fonts' in document) {
                document.fonts.ready.then(() => {
                    const allFonts = [...document.fonts];
                    console.log(`Total fonts loaded: ${allFonts.length}`);
                    
                    const fabricFonts = allFonts.filter(f => 
                        f.family.includes('FabricMDL2Icons') || 
                        f.family.includes('Fabric')
                    );
                    
                    console.log(`Fabric icon fonts found: ${fabricFonts.length}`);
                    fabricFonts.forEach(font => {
                        console.log(`  - ${font.family}: ${font.status}`);
                    });
                    
                    // Test icon rendering after fonts are loaded
                    setTimeout(testIconRendering, 500);
                }).catch(err => {
                    console.error('Error checking fonts:', err);
                    // Still test icons even if font check fails
                    setTimeout(testIconRendering, 1000);
                });
            } else {
                console.warn('document.fonts API not available');
                setTimeout(testIconRendering, 1000);
            }
        }
        
        // Start testing after page load
        window.addEventListener('load', () => {
            console.log('Page loaded, starting icon tests...');
            setTimeout(checkFontLoading, 1000);
        });
        
        console.log('Admin panel icon test script loaded');
    </script>
</body>
</html>

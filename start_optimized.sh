# start.sh
#!/bin/bash

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

echo "Starting thorough cleanup and build process..."
echo ""

# Kill any running processes
echo "Stopping any running processes..."
if command_exists pkill; then
    pkill -f "python run.py" || true
    pkill -f "vite" || true  # Kill any running Vite processes
fi

echo "Restoring frontend npm packages"
echo ""
cd frontend

# Enhanced cleaning process
echo "Performing thorough cleaning..."
rm -rf build/        # Remove build directory
rm -rf dist/         # Remove dist directory
rm -rf node_modules/ # Remove node_modules
rm -rf .cache/       # Remove cache directory
rm -rf .parcel-cache/ # Remove parcel cache if exists
rm -f package-lock.json # Remove package lock to force fresh dependencies
rm -rf .vite/       # Remove Vite cache directory
find . -name "*.js.map" -type f -delete  # Remove source maps
find . -name "*.d.ts" -type f -delete    # Remove TypeScript declaration files

# Clear npm cache for this project
echo "Clearing npm cache..."
npm cache clean --force
npm cache verify

# Install dependencies fresh
echo "Installing dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "Failed to restore frontend npm packages"
    exit $?
fi

# Clean TypeScript compilation
echo "Cleaning TypeScript compilation..."
rm -rf tsconfig.tsbuildinfo # Remove TypeScript build info
npm run clean 2>/dev/null || true # Run clean script if it exists

# Create minimal vite.config.ts if it doesn't exist
if [ ! -f "vite.config.ts" ]; then
    echo "Creating minimal vite.config.ts..."
    cat > vite.config.ts << 'EOF'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
  },
  build: {
    sourcemap: true,
  },
})
EOF
fi

# Force rebuild with no cache
echo ""
echo "Building frontend with clean environment..."
echo ""
export VITE_FORCE_REBUILD=true # Force Vite to rebuild
export NODE_ENV=development # Ensure we're in development mode
export VITE_CLEAR_SCREEN=false # Prevent Vite from clearing the screen
export TS_NODE_TRANSPILE_ONLY=true # Speed up TypeScript compilation
export NODE_OPTIONS="--max-old-space-size=4096" # Increase Node.js heap size

# Run the memory-optimized build
echo "Running memory-optimized Vite build..."
npm run build:fast -- --force --mode development --clearScreen false
if [ $? -ne 0 ]; then
    echo "Build failed, trying with full build process..."
    npm run build -- --force --mode development --clearScreen false
    if [ $? -ne 0 ]; then
        echo "Failed to build frontend"
        exit $?
    fi
fi

# Verify the build includes the latest FileManagement.tsx
echo "Verifying build output..."
if [ -f "src/FileManagement.tsx" ]; then
    echo "Last modified time of FileManagement.tsx: $(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" src/FileManagement.tsx 2>/dev/null || stat -c "%y" src/FileManagement.tsx 2>/dev/null)"
fi

cd ..
. ./scripts/loadenv.sh

echo ""
echo "Starting backend"
echo ""
echo "Python path: $(./.venv/bin/python -c 'import sys; print(sys.executable)')"
echo "Quart version: $(./.venv/bin/pip show quart | grep Version)"
echo "Working directory: $(pwd)"
echo "App.py exists: $(ls app.py 2>/dev/null && echo 'Yes' || echo 'No')"
echo ""

# Clear Python cache files
echo "Clearing Python cache files..."
find . -type d -name "__pycache__" -exec rm -r {} + 2>/dev/null || true
find . -name "*.pyc" -delete

# Start the development server
echo "Starting development server..."
./.venv/bin/python run.py
if [ $? -ne 0 ]; then
    echo "Failed to start backend"
    exit $?
fi
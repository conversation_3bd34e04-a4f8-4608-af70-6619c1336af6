FROM node:20-alpine AS frontend
# No changes in this stage
RUN mkdir -p /home/<USER>/app/node_modules && chown -R node:node /home/<USER>/app

WORKDIR /home/<USER>/app
COPY ./frontend/package*.json ./
USER node
RUN npm ci
COPY --chown=node:node ./frontend/ ./frontend
COPY --chown=node:node ./static/ ./static
WORKDIR /home/<USER>/app/frontend
RUN NODE_OPTIONS=--max_old_space_size=8192 npm run build

# --- BEGIN MODIFICATION ---
FROM python:3.11-alpine
# Added dependencies required by the application and Azure CLI
RUN apk add --no-cache --virtual .build-deps \
    build-base \
    libffi-dev \
    openssl-dev \
    curl \
    && apk add --no-cache \
    bash \
    libpq \
    py3-pip \
    gcc \
    musl-dev \
    python3-dev

# Install Azure CLI via pip (Alpine-compatible method)
RUN pip install --no-cache-dir azure-cli
# --- <PERSON><PERSON> MODIFICATION ---

COPY requirements.txt /usr/src/app/
RUN pip install --no-cache-dir -r /usr/src/app/requirements.txt \
    && rm -rf /root/.cache

COPY . /usr/src/app/
COPY --from=frontend /home/<USER>/app/static  /usr/src/app/static/
WORKDIR /usr/src/app
EXPOSE 8000

CMD ["gunicorn", "app:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]